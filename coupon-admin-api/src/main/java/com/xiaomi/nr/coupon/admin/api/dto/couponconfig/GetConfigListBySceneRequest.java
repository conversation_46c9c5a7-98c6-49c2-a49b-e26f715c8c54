package com.xiaomi.nr.coupon.admin.api.dto.couponconfig;

import com.xiaomi.nr.coupon.admin.api.dto.BaseRequest;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 获取券配置列表入参
 * @author: he<PERSON><PERSON><PERSON>
 * @Date 2022/3/3 2:12 下午
 * @Version: 1.0
 **/
@Data
public class GetConfigListBySceneRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 8428624892408116317L;

    private List<Long> configIdList;

    /**
     * 领券场景
     */
    private String sceneCode;
}
