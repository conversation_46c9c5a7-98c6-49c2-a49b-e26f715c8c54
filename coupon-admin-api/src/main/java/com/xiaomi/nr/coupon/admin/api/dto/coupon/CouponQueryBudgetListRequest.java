package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/19
 */
@Data
public class CouponQueryBudgetListRequest implements Serializable {
    
    
    private static final long serialVersionUID = 1591189707350617200L;

    /**
     * pageNum
     */
    private Integer pageNum;

    /**
     * pageSize
     */
    private Integer pageSize;

    /**
     * 车型item
     */
    private List<Long> carItemIdList;

    /**
     * 促销类型
     */
    private Integer promotionType;

    /**
     * 模糊搜索词
     */
    private String keyword;
    
    public Pair<Boolean, String> checkParam() {
        return Pair.of(Boolean.TRUE, "");
    }
}
