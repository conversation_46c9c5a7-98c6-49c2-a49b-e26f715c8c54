package com.xiaomi.nr.coupon.admin.api.dto.coupon;


import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class WorkFlowConfigVo implements Serializable {

    private static final long serialVersionUID = 5005271995480586735L;
    /**
     * 唯一标识id
     */
    private Integer workFlowId;
    /**
     * 渠道List
     */
    private List<Integer> channel;
    /**
     * 前端展示审批流名称
     */
    private String workFlowText;
    /**
     * 是否通用（新）审批流标识：true-新；false-旧
     */
    private Boolean newFlag;



}

