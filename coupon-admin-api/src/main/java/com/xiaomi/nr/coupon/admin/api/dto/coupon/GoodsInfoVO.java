package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

/**
 * 审批商品警示信息
 */
@Data
public class GoodsInfoVO implements Comparable<GoodsInfoVO>{
    /**
     * sku或者套装id
     */
    private long goodsId;
    /**
     * 商品名称
     */
    private String  goodsName;

    /**
     * 划线价 元
     */
    private BigDecimal marketPrice;

    /**
     * 商品渠道价格
     */
    private List<GoodsChannelVO> goodsChannelVOs;

    /**
     * 可用最大折扣力度商品券信息(key: 券优惠类型)
     */
    private Map<Integer, GoodsCouponPromVO> goodsCouponMap;

    /**
     * level:sku/package/ssu
     */
    private String level;

    /**
     * ssuType
     * {@link com.xiaomi.nr.coupon.admin.enums.goods.GoodsItemTypeEnum}
     */
    private Integer ssuType;


    @Override
    public int compareTo(GoodsInfoVO o) {
        GoodsChannelVO goodsChannelVO =  o.getGoodsChannelVOs().stream().max(Comparator.comparing(GoodsChannelVO::getDiscount)).get();
        GoodsChannelVO goodsChannelVO2 =  this.getGoodsChannelVOs().stream().max(Comparator.comparing(GoodsChannelVO::getDiscount)).get();
        return goodsChannelVO2.getDiscount().compareTo(goodsChannelVO.getDiscount());
    }
}
