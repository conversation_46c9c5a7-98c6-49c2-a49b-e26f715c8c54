package com.xiaomi.nr.coupon.admin.api.service.couponadmin;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.UserCouponCodeVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.request.UserCouponCodeListRequest;
import com.xiaomi.youpin.infra.rpc.Result;

/**
 * 后台优惠码服务
 */
public interface DubboCouponCodeService {

    /**
     * 查询用户优惠码信息列表
     *
     * @param request request
     * @return response
     */
    Result<BasePageResponse<UserCouponCodeVO>> userCouponCodeList(UserCouponCodeListRequest request);
}
