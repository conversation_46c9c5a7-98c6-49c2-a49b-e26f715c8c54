package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
public class GoodsRuleVO implements Serializable {

    /**
     * 商品范围类型  1 商品券 2 分类券
     */
    private int scopeType;

    /**
     * 自动更新新品 1 是  2 否
     */
    private Integer autoUpdateGoods;

    /**
     * 商品部门
     */
    private List<Integer> goodsDepartments;

    /**
     * 可用商品列表 key： sku package ssu suit
     */
    private Map<String, List<Long>> goodsInclude;

    /**
     * 可用商品列表 key： sku package ssu
     */
    private Map<String, List<Long>> goodsExclude;

    /**
     * 三级类目Id列表
     */
    private Set<Long> categoryIds;

    /**
     * sku适用商品列表
     */
    private List<GoodsSuitableVO> goodsSuitableVOs;

    /**
     * 警示商品信息
     */
    private GoodsDiscountLevelVO goodsDiscountLevelVO;

    /**
     * 工时ssu
     */
    private Map<Long, Integer> labourHourSsu;

    /**
     * 配件ssu
     */
    private Map<Long, Integer> partsSsu;

    /**
     * 适用商品信息 key为ssuId
     */
    private Map<Long, GoodMsgVO> goodsMsg;
}
