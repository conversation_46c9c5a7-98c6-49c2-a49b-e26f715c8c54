package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/27 15:06
 */
@Data
public class GetCouponByIdResponse implements Serializable {
    private static final long serialVersionUID = 1487925596025525855L;

    /**
     * 券信息列表
     */
    @ApiDocClassDefine(value = "couponInfoList", description = "券信息列表")
    private List<CouponInfo> couponInfoList;
}
