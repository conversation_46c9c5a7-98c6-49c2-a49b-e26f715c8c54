package com.xiaomi.nr.coupon.admin.api.dto.couponconfig;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 全量刷缓存请求
 * @author: he<PERSON><PERSON><PERSON>
 * @Date 2022/1/19 11:27 上午
 * @Version: 1.0
 **/
@Deprecated
@Data
public class GenAllConfigRedisCacheRequest implements Serializable {

    private static final long serialVersionUID = 5915753887637177541L;

    private String source;

    private Boolean full;

    private List<Long> itemIds;



}
