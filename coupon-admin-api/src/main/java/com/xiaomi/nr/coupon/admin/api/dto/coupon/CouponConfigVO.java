package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import com.xiaomi.nr.coupon.admin.api.dto.BudgetInfoDto;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.store.StoreInfoVo;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class CouponConfigVO implements Serializable {
    /**
     * 优惠券id
     */
    private long id;
    /**
     * 优惠券名称
     */
    private String name;
    /**
     * 使用说明
     */
    private String couponDesc;
    /**
     * 预算池信息
     */
    private BudgetInfoDto budgetInfoDto;
    /**
     * 一级场景类别
     */
    private Integer sendSceneType;
    /**
     * 投放场景
     */
    private String sendScene;
    /**
     * 领取有效期开始时间
     */
    private Date startFetchTime;
    /**
     * 领取有效期结束时间
     */
    private Date endFetchTime;
    /**
     * 领取有效期开始时间
     */
    private Long startFetchTimeStamp;
    /**
     * 领取有效期结束时间
     */
    private Long endFetchTimeStamp;

    /**
     * 使用有效期
     */
    private UseTermVO useTermVO;

    /**
     * 使用渠道 key为渠道 1:小米商城 2:直营店 3:专卖店 4:授权店 5:堡垒店：value为商城渠道或限制门店
     */
    private Map<Integer, UseChannelVO> useChannel;

    /**
     * 指定门店列表
     */
    private List<StoreInfoVo> storeInfoVoList;

    /**
     * 优惠逻辑
     */
    private PromotionRuleVO promotionRuleVO;

    /**
     * 领取发放规则
     */
    private DistributionRuleVO distributionRuleVO;

    /**
     * 特殊规则
     */
    private ExtPropVO extProp;

    /**
     * 指定地区id
     */
    private List<Long> areaIds;

    /**
     * 适用商品信息
     */
    private GoodsRuleVO goodsRuleVO;

    /**
     * 投放目的
     */
    private int sendPurpose;

    /**
     * 成本分摊 json
     */
    private Map<Integer, Integer> costShare;

    /**
     * 预估总成本 元
     */
    private BigDecimal cost;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建人部门
     */
    private long departmentId;

    /**
     * 券状态
     */
    private int status;

    /**
     * 来源 1 老券迁移 2 乾坤创建
     */
    private Integer source;

    /**
     * 最后更新时间
     */
    private Timestamp updateTime;

    /**
     * 优惠券类型(1:商品券、2:运费券、3:超级补贴券)
     */
    private Integer couponType;

    /**
     * 履约方式 -1: 所有方式 139: 门店闪送 (这个跟交易中台统一定义)
     */
    private Integer shipmentId;

    /**
     * 投放方式 1:优惠券, 2:兑换码
     */
    private int sendMode;

    /**
     * 地区：所属区域ID
     */
    private String areaId;


    /**
     * 审批流ID
     */
    private Long workflowId;

    /**
     * uniqId
     */
    private String uniqId;
}
