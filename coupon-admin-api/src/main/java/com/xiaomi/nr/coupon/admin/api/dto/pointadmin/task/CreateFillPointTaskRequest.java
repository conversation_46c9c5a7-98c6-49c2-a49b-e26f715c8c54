package com.xiaomi.nr.coupon.admin.api.dto.pointadmin.task;

import com.xiaomi.nr.coupon.admin.api.dto.BaseRequest;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import java.util.Map;

/**
 * @description 创建灌积分任务请求参数
 * <AUTHOR>
 * @date 2024-08-14 10:04
*/
@Data
public class CreateFillPointTaskRequest extends BaseRequest {
    private static final long serialVersionUID = -288743288229455217L;

    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空")
    private String taskName;

    /**
     * 积分批次ID
     */
    @NotNull(message = "积分批次Id不能为空")
    @Min(value = 1, message = "积分批次Id有误，不能为0")
    private Long pointBatchId;

    /**
     * 发放方式 1-本地上传
     */
    @NotNull(message = "上传方式不能为空")
    @Range(min = 1, max = 2, message = "上传方式不在合法范围内")
    private Integer sendType;

    /**
     * 灌积分数据文件hdfs地址
     */
    @NotNull(message = "数据文件hdfs地址不能为空")
    private String hdfsAddress;

    /**
     * 创建人
     */
    @NotBlank(message = "灌积分任务创建人不能为空")
    private String creator;

    /**
     * 发放数量(人次，非积分总数)
     */
    private Long applyCount;

    /**
     *  计划发放积分总数
     */
    private Long applyPointCount;

    /**
     * 创建人部门
     */
    private String departmentId;

    /**
     * 积分发放场景
     */
    private String sendScene;

    /**
     * 地区id
     */
    private String areaId;

    /**
     * 审批流ID
     */
    private Long workflowId;

}
