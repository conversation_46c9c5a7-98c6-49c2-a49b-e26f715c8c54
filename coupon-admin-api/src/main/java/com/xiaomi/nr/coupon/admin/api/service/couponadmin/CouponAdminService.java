package com.xiaomi.nr.coupon.admin.api.service.couponadmin;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponConfigListVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponInfoRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponUpdateStatusRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.OperateCouponConfigRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.response.CouponDetailResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.response.SaveCouponConfigResponse;
import com.xiaomi.youpin.infra.rpc.Result;

import javax.validation.Valid;

/**
 * @description: 优惠券管理TOB服务接口
 * @author: hejiapeng
 * @Date 2022/3/3 12:44 下午
 * @Version: 1.0
 **/

public interface CouponAdminService {

    /**
     * 券配置列表
     *
     * @param request request
     * @return response
     */
    Result<BasePageResponse<CouponConfigListVO>> couponConfigList(CouponListRequest request);

    /**
     * 券配置详情
     *
     * @param request request
     * @return response
     */
    Result<CouponDetailResponse> couponConfigDetail(@Valid CouponInfoRequest request);

    /**
     * 保存券配置
     * @param request
     * @return
     */
    Result<SaveCouponConfigResponse> saveCouponConfig(@Valid OperateCouponConfigRequest request);

    /**
     * 更新券配置
     * @param request
     * @return
     */
    Result<Void> updateCouponConfig(@Valid OperateCouponConfigRequest request);

    /**
     * 修改券配置状态
     *
     * @param request request
     * @return void
     */
    Result<String> updateConfigStatus(@Valid CouponUpdateStatusRequest request);

}