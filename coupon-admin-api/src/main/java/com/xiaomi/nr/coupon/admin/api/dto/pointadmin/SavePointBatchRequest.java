package com.xiaomi.nr.coupon.admin.api.dto.pointadmin;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/12/5 14:18
 */
@Data
public class SavePointBatchRequest implements Serializable {
    private static final long serialVersionUID = 7553474172590359209L;

    /**
     * 积分批次id
     */
    private Long batchId;

    /**
     * 积分批次名称
     */
    @NotBlank(message = "积分批次名称不能为空")
    @Size(min = 1, max = 30, message = "积分批次名称长度不能超过30")
    private String batchName;

    /**
     * 预算池id
     */
    @NotNull(message = "预算池id不能为空")
    @Min(value = 1, message = "预算池id需大于0")
    private Long budgetId;

    /**
     * 预算申请单号
     */
    @NotBlank(message = "预算申请单号不能为空")
    private String budgetApplyNo;

    /**
     * 行号
     */
    @NotNull(message = "行号不能为空")
    @Min(value = 1, message = "行号需大于0")
    private Long lineNum;

    /**
     * br申请单号（唯一标识）
     */
    @NotBlank(message = "br申请单号不能为空")
    private String brApplyNo;

    /**
     * 预算单创建时间
     */
    @NotBlank(message = "预算单创建时间不能为空")
    private String budgetCreateTime;

    /**
     * 场景编码
     */
    @NotBlank(message = "场景编码不能为空")
    private String sceneCode;

    /**
     * 发放周期开始时间
     */
    @NotNull(message = "发放周期开始时间不能为空")
    @Min(value = 1, message = "发放周期开始时间需大于0")
    private Long startTime;

    /**
     * 发放周期结束时间
     */
    @NotNull(message = "发放周期结束时间不能为空")
    @Min(value = 1, message = "发放周期结束时间需大于0")
    private Long endTime;

    /**
     * 积分总额
     */
    @NotNull(message = "积分总额不能为空")
    @Min(value = 1, message = "积分总额需大于0")
    private Long applyCount;

    /**
     * 预警阈值
     */
    @NotNull(message = "预警阈值不能为空")
    private Integer warningRatio;

    /**
     * 使用时间类型 1-相对时间，2-绝对时间
     */
    @NotNull(message = "使用时间类型不能为空")
    @Range(min = 1, max = 2, message = "使用时间类型非法")
    private Integer useTimeType;

    /**
     * 操作人
     */
    @NotNull(message = "操作人不能为空")
    private String operator;
}
