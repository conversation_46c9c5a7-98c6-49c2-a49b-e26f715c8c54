package com.xiaomi.nr.coupon.admin.api.dto.pointadmin;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.optlog.response.ModifyContentVO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 优惠券日志
 * @Date: 2022.02.25 17:14
 */
@Data
public class PointBatchLogDto implements Serializable {
    /**
     * 日志id
     */
    private Long logId;
    /**
     * 操作类型
     */
    private Integer optType;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 修改内容
     */
    private List<ModifyContentVO> optContent;
    /**
     * 操作时间
     */
    private Long optTime;
}
