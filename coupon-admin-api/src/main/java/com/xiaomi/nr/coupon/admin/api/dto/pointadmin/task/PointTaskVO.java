package com.xiaomi.nr.coupon.admin.api.dto.pointadmin.task;

import lombok.Data;

import java.io.Serializable;

/**
 * @description 灌积分任务VO
 * <AUTHOR>
 * @date 2024-08-14 10:51
 * @version 1.0
*/
@Data
public class PointTaskVO implements Serializable {

    private static final long serialVersionUID = -6682323727532368794L;

    /**
     * 灌积分任务名称
     */
    private String name;

    /**
     * 积分批次名称
     */
    private String pointBatchName;

    /**
     * 计划发放数量
     */
    private Long planCount;

    /**
     * 发放方式
     */
    private Integer sendType;

    /**
     * HDFS地址
     */
    private String hdfsAddr;

    /**
     * 任务进度
     */
    private Long processRate;

    /**
     * 任务状态
     */
    private Integer status;
}
