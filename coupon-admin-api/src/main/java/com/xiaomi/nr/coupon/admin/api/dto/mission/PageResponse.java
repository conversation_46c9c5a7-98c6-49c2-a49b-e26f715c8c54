package com.xiaomi.nr.coupon.admin.api.dto.mission;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *
 * 券发放任务分页数据
 */
@Data
public class PageResponse<X> implements Serializable  {
    private static final long serialVersionUID = -1234565769795223827L;

    /**
     * 上次分页数据的最后一个任务id
     */
    public Long lastMissionId;

    /**
     * 页面大小
     */
    public Integer pageSize;

    /**
     * 总的数据量
     */
    public Integer total;

    /**
     * 分页数据
     */
    public List<X> data;

    public PageResponse(long lastMissionId, int pageSize, int total, List<X> list) {
        this.lastMissionId = lastMissionId;
        this.pageSize = pageSize;
        this.total = total;
        this.data = list;
    }
}
