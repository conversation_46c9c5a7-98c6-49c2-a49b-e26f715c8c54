package com.xiaomi.nr.coupon.admin.api.service.pointadmin;

import com.xiaomi.youpin.infra.rpc.Result;

/**
 * point定时任务
 *
 *
 * <AUTHOR>
 * @date 2023/12/12
 */
public interface DubboPointScheduleService {

    /**
     * 积分批次定时任务
     * 涉及：风险预警、预算释放
     *
     * @return      void
     */
    Result<Void> pointBatchSchedule();

    /**
     * 积分批次定时任务
     * 涉及：预算释放
     *
     * @return      void
     */
    Result<Void> budgetRelease();
}
