package com.xiaomi.nr.coupon.admin.api.service.couponadmin;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.BudgetInfoDto;
import com.xiaomi.nr.coupon.admin.api.dto.PageInfo;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponConfigListVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponQueryBudgetListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.*;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.response.CouponInfoResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.response.CouponConfigDescResponse;
import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.UpdateCouponGoodsRequest;
import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.UpdateCouponGoodsResponse;
import com.xiaomi.youpin.infra.rpc.Result;

import javax.validation.Valid;

/**
 * @description: 优惠券管理后台服务接口
 * @author: hejiapeng
 * @Date 2022/3/3 12:44 下午
 * @Version: 1.0
 **/

public interface DubboCouponAdminService {

    /**
     * 查询券配置信息列表
     *
     * @param request request
     * @return response
     */
    Result<BasePageResponse<CouponConfigListVO>> couponConfigList(CouponListRequest request);

    /**
     * 查询券配置详情(单个)
     *
     * @param request request
     * @return response
     */
    Result<CouponInfoResponse> couponConfigDetail(@Valid CouponInfoRequest request);

    /**
     * 修改券配置状态
     *
     * @param request request
     * @return void
     */
    Result<String> updateStatus(@Valid CouponUpdateStatusRequest request);

    /**
     * 优惠券描述信息
     *
     * @param request
     * @return
     */
    Result<CouponConfigDescResponse> couponConfigDesc(CouponDescRequest request);


    /**
     * 更新品类券适用商品
     *
     * @param request
     * @return
     */
    Result<UpdateCouponGoodsResponse> updateCouponGoods(UpdateCouponGoodsRequest request);

    /**
     * 查询预算池列表
     *
     * @param
     * @return
     */
    Result<PageInfo<BudgetInfoDto>> queryBudgetList(CouponQueryBudgetListRequest request);

    /**
     * 查询预算池详情
     *
     * @param
     * @return
     */
    Result<BudgetInfoDto> queryBudgetDetail(BudgetInfoDto budgetInfoDto);
}