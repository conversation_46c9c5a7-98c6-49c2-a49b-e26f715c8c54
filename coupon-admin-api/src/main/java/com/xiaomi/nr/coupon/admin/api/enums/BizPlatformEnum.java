package com.xiaomi.nr.coupon.admin.api.enums;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 优惠券所属业务平台
 *
 * <AUTHOR>
 * @date 2023/9/13
 */
@Getter
@AllArgsConstructor
public enum BizPlatformEnum {

    /**
     * -1: 自动化测试
     */
    AUTO_TEST(-1, "自动化测试", "autoTest"),

    /**
     * 0: 零售业务
     */
    RETAIL(0, "零售", "retail"),

    /**
     * 3: 汽车业务
     */
    CAR(3, "汽车", "car"),

    /**
     * 4: 汽车售后
     */
    CAR_AFTER_SALE(4, "汽车售后", "car_after_sale"),

    /**
     * 5: 车商城
     */
    CAR_SHOP(5, "车商城", "car_shop"),

    /**
     * 6: 国际新零售
     */
    GLOBAL_NEW_RETAIL(6, "国际零售通", "global_new_retail"),
    ;
    /**
     * 枚举code
     */
    private final Integer code;

    /**
     * 枚举描述
     */
    private final String desc;

    /**
     * value
     */
    private final String value;

    private static final HashMap<Integer, BizPlatformEnum> MAPPING = new HashMap<>();

    static {
        for (BizPlatformEnum e : BizPlatformEnum.values()) {
            MAPPING.put(e.getCode(), e);
        }
    }

    public static BizPlatformEnum valueOf(Integer code) {
        return MAPPING.get(code);
    }


    public static List<BizPlatformEnum> valueOf(List<Integer> codes) {
        List<BizPlatformEnum> enumList = Lists.newArrayList();
        for (Integer code : codes) {
            BizPlatformEnum anEnum = MAPPING.get(code);
            if (Objects.nonNull(anEnum)) {
                enumList.add(anEnum);
            }
        }
        return enumList;
    }


}
