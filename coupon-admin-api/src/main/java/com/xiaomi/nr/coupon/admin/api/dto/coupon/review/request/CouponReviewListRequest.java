package com.xiaomi.nr.coupon.admin.api.dto.coupon.review.request;

import com.google.common.collect.ImmutableMap;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageRequest;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class CouponReviewListRequest extends BasePageRequest {

    private Map<String,String> orderByMap = ImmutableMap.<String, String>builder()
            .put("reviewId", "id")
            .put("createTime", "create_time")
            .put("startUseTime", "start_use_time")
            .build();
    /**
     * 优惠券类型  1: 商品券 2: 运费券
     */
    private Integer couponType;

    /**
     * 审核id
     */
    private Long reviewId;

    /**
     *券配置id
     */
    private Long configId;

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     * 审核状态 1 待审核, 2 审核中, 3 审核通过, 4 已驳回 5 已撤回 6 已过期
     */
    private List<Integer> status;

    /**
     * 提交人
     */
    private String creator;

    /**
     *提交时间起
     */
    private Date startCreateTime;

    /**
     *提交时间止
     */
    private Date endCreateTime;

    /**
     * 地址区域数据
     */
    private String areaId;





}
