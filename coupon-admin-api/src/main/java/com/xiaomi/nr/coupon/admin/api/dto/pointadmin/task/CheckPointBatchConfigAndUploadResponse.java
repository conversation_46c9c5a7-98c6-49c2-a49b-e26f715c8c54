package com.xiaomi.nr.coupon.admin.api.dto.pointadmin.task;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @description 校验积分批次信息出参
 * <AUTHOR>
 * @date 2024-08-20 15:04
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CheckPointBatchConfigAndUploadResponse implements Serializable {
    private static final long serialVersionUID = 4155212213257500422L;

    /**
     * 文件保存路径
     */
    private String hdfsAddress;
}
