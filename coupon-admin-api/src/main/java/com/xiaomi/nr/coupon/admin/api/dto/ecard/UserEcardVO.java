package com.xiaomi.nr.coupon.admin.api.dto.ecard;

import lombok.Data;

import java.io.Serializable;

@Data
public class UserEcardVO implements Serializable {

    private static final long serialVersionUID = 2660622670062980355L;

    /**
     * 礼品卡ID
     */
    private Long cardId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 卡类型id
     */
    private Integer cardType;

    /**
     * 卡类型描述
     */
    private String cardTypeDesc;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 结束时间
     */
    private Long endTime;

    /**
     * 来源订单：购买礼品卡订单
     */
    private String fromOrderId;

    /**
     * 虚拟 | 实体
     */
    private int virtual;

    /**
     * 状态
     */
    private int status;

    /**
     * 状态描述
     */
    private String statusDesc;
}
