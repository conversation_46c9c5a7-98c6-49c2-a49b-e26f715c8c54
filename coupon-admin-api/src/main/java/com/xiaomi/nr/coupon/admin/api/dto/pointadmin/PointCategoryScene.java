package com.xiaomi.nr.coupon.admin.api.dto.pointadmin;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 券渠道类型VO
 * @Date: 2021.04.28 18:03
 */
@Data
public class PointCategoryScene implements Serializable {
    /**
     * 类型
     */
    private Integer type;
    /**
     * 名称
     */
    private String name;
    /**
     * 渠道信息
     */
    private List<SceneCategoryDto> sceneCategoryDtoList;

    public PointCategoryScene(int type, String name, List<SceneCategoryDto> channelVOList) {
        this.type = type;
        this.name = name;
        this.sceneCategoryDtoList = channelVOList;
    }
}
