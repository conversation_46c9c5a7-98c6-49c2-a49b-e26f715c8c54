package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
public class CouponConfigListVO extends GlobalBaseVO implements Serializable {
    private static final long serialVersionUID = -129182850054756648L;

    /**
     * 优惠券id
     */
    private Long id;

    /**
     * 优惠券名称
     */
    private String name;

    /**
     * 优惠类型 (1:满减, 2:满折, 3:N元券, 4:立减)
     */
    private Integer promotionType;

    /**
     * 优惠值 (分)
     */
    private Long promotionValue;

    /**
     * 门槛类型 (1:满元, 2:满件, 3:每满元, 4:每满件, 5:满元且满件)
     */
    private Integer bottomType;

    /**
     * 门槛值(分)
     */
    private Long bottomPrice;

    /**
     * 门槛值(件)
     */
    private Integer bottomCount;

    /**
     * 已发数量
     */
    private Long sendCount;

    /**
     * 申请数量
     */
    private Integer applyCount;

    /**
     * 领取有效期开始时间
     */
    private Date startFetchTime;

    /**
     * 领取有效期结束时间
     */
    private Date endFetchTime;

    /**
     * 最后修改时间
     */
    private Date updateTime;

    /**
     * 上下线状态( 1:上线, 2:下线, 3:终止)
     */
    private Integer status;

    /**
     * 优惠券状态(1:未开始, 2:进行中, 3:已结束, 4:已终止)
     */
    private Integer timeStatus;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建时间
     */
    private Long createTimeStamp;

    /**
     * 使用渠道
     */
    private String useChannel;

    /**
     * 渠道编码
     */
    private Integer channel;


    /**
     * 投放场景
     */
    private String sendSceneName;

    /**
     * 券配置来源 1 老券迁移 2 乾坤创建
     */
    private Integer source;

    /**
     * 优惠券类型  1: 商品券 2: 运费券
     */
    private Integer couponType;

    /**
     * 可用描述信息 查询商品可用券列表使用
     */
    private String validRemark;

    /**
     * 服务券类型
     */
    private Integer serviceType;

    /**
     * 限领类型 1限领 2不限领
     */
    private Integer fetchLimitType;

    /**
     * 开始时间（可读）
     */
    private String beginTimeShow;

    /**
     * 结束时间（可读）
     */
    private String endTimeShow;

    /**
     * 活动区域(可读)
     */
    private String areaIdShow;

    /**
     * 折扣规则国际化翻译文案
     */
    private String discountDesc;

    @Override
    protected Date fetchStartFetchTimeDate() {
        return startFetchTime;
    }

    @Override
    protected Date fetchEndFetchTimeDate() {
        return endFetchTime;
    }
}
