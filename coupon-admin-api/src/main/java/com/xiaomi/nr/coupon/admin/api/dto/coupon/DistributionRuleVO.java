package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import lombok.Data;

import java.io.Serializable;

@Data
public class DistributionRuleVO  implements Serializable {

    /**
     *发放总量
     */
   private long applyCount;

    /**
     *每人限领
     */
    private long fetchLimit;

    public DistributionRuleVO(){}
    public DistributionRuleVO(long applyCount, long fetchLimit){
        this.applyCount = applyCount;
        this.fetchLimit = fetchLimit;
    }

}
