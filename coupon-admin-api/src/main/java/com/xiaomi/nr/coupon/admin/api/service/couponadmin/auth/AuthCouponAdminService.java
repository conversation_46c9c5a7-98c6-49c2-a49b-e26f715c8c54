package com.xiaomi.nr.coupon.admin.api.service.couponadmin.auth;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponConfigListVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponInfoRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.response.CouponInfoResponse;
import com.xiaomi.youpin.infra.rpc.Result;


/**
 * @description: 优惠券管理后台服务接口
 * @author: xueqizheng
 * @Date 2025年7月22日14:59:23
 * @Version: 1.0
 **/

public interface AuthCouponAdminService {

    /**
     * 查询券配置信息列表
     *
     * @param request request
     * @return response
     */
    default Result<BasePageResponse<CouponConfigListVO>> authCouponConfigList(CouponListRequest request) {
        return null;
    }


    /**
     * 查询活动详情
     * @param request
     * @return
     */
    default Result<CouponInfoResponse> queryConfigDetail(CouponInfoRequest request) {
        return null;
    }

    /**
     * 导出到活动中心
     * @param request
     * @return
     */
    default Result<String> export2TaskCenter(CouponListRequest request) {
        return null;
    }
}