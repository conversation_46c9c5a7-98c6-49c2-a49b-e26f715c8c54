package com.xiaomi.nr.coupon.admin.api.dto.couponconfig;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 获取详情出参
 * @author: he<PERSON><PERSON><PERSON>
 * @Date 2022/3/3 2:14 下午
 * @Version: 1.0
 **/
@Data
@Accessors(chain = true)
public class BatchGetConfigInfoReponse implements Serializable {

    /**
     * 券配置基本信息
     */
    private List<CouponConfigInfoDTO> couponConfigs;
}
