package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import lombok.Data;

import java.io.Serializable;


@Data
public class PackageInfoVO implements Serializable {
    /**
     * 套装id
     */
    private Long batchedId;
    /**
     * 套装名称
     */
    private String batchedName;
    /**
     * 平台价 分
     */
    private Long marketPrice;
    /**
     * pid
     */
    private Long productId;
    /**
     * 线上上架状态
     */
    private Integer onSaleOnline;
    /**
     * 线下上架状态
     */
    private Integer onSaleOffline;

    /**
     * 授权店上架状态
     */
    private Integer sqOnSale;

    /**
     * 线上在售状态
     */
    private Integer onShelfOnline;
    /**
     * 线下在售状态
     */
    private Integer onShelfOffline;

    /**
     * 授权店货品状态
     * 0、未开售   1、开售
     */
    private Integer sqOnShelf;

    /**
     * 车商城是否上架
     */
    private Integer onSaleCarShop;

    /**
     * 0：单品，1：新套装，2：主副品，10：老套装，20：虚拟资产
     * 默认老套装
     */
    private Integer ssuType = 10;
}
