package com.xiaomi.nr.coupon.admin.api.service.couponadmin;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.optlog.request.CouponLogListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.optlog.request.LogDetailRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.optlog.response.CouponLogListResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.optlog.response.LogDetailResponse;
import com.xiaomi.youpin.infra.rpc.Result;

/**
 * @Description: 优惠券日志
 * @Date: 2022.02.25 15:06
 */
public interface DubboCouponLogService {
    /**
     * 查询修改列表
     * @param request
     * @return
     */
    Result<CouponLogListResponse> queryLogList(CouponLogListRequest request);

    /**
     * 查询修改详情
     * @param request
     * @return
     */
    Result<LogDetailResponse> queryLogDetail(LogDetailRequest request);
}
