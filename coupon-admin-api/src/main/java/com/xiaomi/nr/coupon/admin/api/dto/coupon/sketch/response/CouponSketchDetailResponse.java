package com.xiaomi.nr.coupon.admin.api.dto.coupon.sketch.response;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.*;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CouponSketchDetailResponse implements Serializable {

    /**
     * 券草稿id
     */
    private long sketchId;

    /**
     * 券信息
     */
    private CouponConfigVO couponConfigVO;

    /**
     * 商品配置信息
     */
    private GoodsRuleDetailVO goodsRuleDetailVO;

    /**
     * 申请附件地址
     */
    private List<ApplyAttachmentVO> applyAttachment;

}
