package com.xiaomi.nr.coupon.admin.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;

/**
 * 券服务类型枚举
 *
 * <AUTHOR>
 * @date 2023/12/22 14:43
 */
@Getter
@AllArgsConstructor
public enum CouponServiceTypeEnum {
    /**
     * 1: 基础保养
     */
    BASIC_MAINTENANCE(1, "基础保养"),

    /**
     * 2: 漆面修复
     */
    PAINT_REPAIR(2, "漆面修复"),

    /**
     * 3: 补胎
     */
    REPAIR_TAIR(3, "补胎"),

    /**
     * 4: 按需保养
     */
    NEED_MAINTENANCE(4, "按需保养"),

    /**
     * 5: 耗材券
     */
    CONSUMABLES(5, "耗材券"),

    /**
     * 6: 玻璃维修
      */
    GLASS_REPAIR(6, "玻璃维修"),
    ;

    /**
     * 枚举code
     */
    private final Integer code;

    /**
     * 枚举描述
     */
    private final String name;

    private static final HashMap<Integer, CouponServiceTypeEnum> MAPPING = new HashMap<>();

    static {
        for (CouponServiceTypeEnum e : CouponServiceTypeEnum.values()) {
            MAPPING.put(e.getCode(), e);
        }
    }

    public static CouponServiceTypeEnum valueOf(Integer code) {
        return MAPPING.get(code);
    }
}
