package com.xiaomi.nr.coupon.admin.api.dto.pointadmin;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/12/7 19:59
 */
@Data
public class DeleteSsuBlacklistRequest implements Serializable {
    private static final long serialVersionUID = -7279856167624967804L;

    /**
     * ssu
     */
    @NotNull(message = "ssuId不能为空")
    private Long ssuId;
}
