package com.xiaomi.nr.coupon.admin.api.dto.coupon.review.response;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 小程序回调审批详情
 */
@Data
public class CouponReviewInfoResponse implements Serializable{
    /**
     *券名称
     */
    private String name;
    /**
     *领取时间起
     */
    private String startFetchTime;
    /**
     *领取时间止
     */
    private String endFetchTime;
    /**
     * 使用有效期类型 1 固定有效,2 相对有效期
     */
    private int useTimeType;
    /**
     *开始使用时间
     */
    private String startUseTime;
    /**
     *结束使用时间
     */
    private String endUseTime;
    /**
     * 有效时长(单位小时)
     */
    private int useDuration;
    /**
     *券类型
     */
    private String promotionText;
    /**
     *门槛信息
     */
    private String bottomText;
    /**
     *发放总量
     */
    private String applyCount;
    /**
     *投放场景
     */
    private String sendSceneText;
    /**
     *投放目的
     */
    private String sendPurposeText;
    /**
     *预估总成本（元）
     */
    private BigDecimal costText;
    /**
     *分摊规则
     */
    private List<CouponCostShareVO> costShareTexts;
    /**
     *警示信息
     */
    private GoodsDiscountLevelVO goodsNotifyInfo;
    /**
     *适用商品
     */
    private List<GoodsSuitableVO> goodsSuitables;

    /**
     *套装数量
     */
    private long packageAmount;

    /**
     *申请附件list
     */
    private List<ApplyAttachmentVO> applyAttachments;
    /**
     *导出商品
     */
    private String downloadGoodsUrl;
    /**
     *系统地址
     */
    private String systemUrl;

    /**
     * 使用渠道
     */
    private List<String> useChannelTexts;

    /**
     * 优惠券类型(商品券｜运费券)
     */
    private String couponTypeText;

    /**
     * 优惠金额
     */
    private String promotionValue;

    /**
     * 适用商品价格信息
     */
    private List<GoodsPriceVO> goodsSuitableInfoList;

    /**
     * 可用货品数量
     */
    private Integer totalSku;

    /**
     * 特殊规则
     */
    private List<String> extPropTexts;

}
