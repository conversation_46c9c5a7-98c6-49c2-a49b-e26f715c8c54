package com.xiaomi.nr.coupon.admin.api.dto.couponconfig;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;
import java.util.Set;

/**
 * @description:
 * @author: heji<PERSON><PERSON>
 * @Date 2023/3/29 2:25 下午
 * @Version: 1.0
 **/
@Data
@Builder
public class GoodsConfigRelResponse implements Serializable {

    private static final long serialVersionUID = -4083811591756769792L;

    /**
     * sku -> configIds
     */
    private Map<Long, Set<Long>> skuCouponConfigIds;

    /**
     * packetId -> configIds
     */
    private Map<Long, Set<Long>> packageCouponConfigIds;

    /**
     * ssu -> configIds
     */
    private Map<Long, Set<Long>> ssuCouponConfigIds;

    /**
     * 券信息
     */
    private Map<Long, CouponConfigInfoDTO> couponConfigInfoDTOMap;
}
