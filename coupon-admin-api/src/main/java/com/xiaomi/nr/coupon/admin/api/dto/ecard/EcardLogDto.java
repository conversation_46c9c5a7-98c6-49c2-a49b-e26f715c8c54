package com.xiaomi.nr.coupon.admin.api.dto.ecard;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/8/27 16:40
 */
@Data
public class EcardLogDto implements Serializable {
    private static final long serialVersionUID = -5373834714211511712L;

    /**
     * ecard编号
     */
    private Long cardId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 退款单ID
     */
    private Long refundNo;

    /**
     * 日志类型：-1系统异常日志，0系统正常日志，1消费（含退款）
     */
    private Integer logType;

    /**
     * 金额变化
     */
    private BigDecimal income;

    /**
     * 老余额
     * 对应的表字段: oldBalance
     */
    private BigDecimal oldBalance;

    /**
     * 新余额
     */
    private BigDecimal newBalance;

    /**
     * 操作人ID，系统操作为0
     */
    private Long operatorId;

    /**
     * 添加时间
     */
    private Integer addTime;

    /**
     * 描述
     */
    private String description;

    /**
     * 最后更新时间
     */
    private String lastUpdateTime;

    /**
     * hashCode
     */
    private String hashCode;

    /**
     * 线下使用
     */
    private Integer offline;
}
