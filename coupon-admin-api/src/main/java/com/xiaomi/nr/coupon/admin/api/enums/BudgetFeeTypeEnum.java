package com.xiaomi.nr.coupon.admin.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description 预算类型
 * <AUTHOR>
 * @date 2024-09-20 09:24
*/
@Getter
@AllArgsConstructor
public enum BudgetFeeTypeEnum {

    /**
     * 车商城优惠券
     */
    CARSHOP1("CARSHOP1", "车商城优惠券"),

    ;

    private final String code;
    private final String name;

    public static BudgetFeeTypeEnum getByCode(String code) {
        for (BudgetFeeTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
