package com.xiaomi.nr.coupon.admin.api.utils;

import com.xiaomi.com.i18n.area.Area;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAccessor;
import java.time.temporal.TemporalQueries;
import java.util.Date;

/**
 * 日期时间工具类
 *
 * <AUTHOR>
 */
public class TimeUtil {

    public static final DateFormat DEFAULT_DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 默认时间格式化
     */
    public static final String DATE_TIME_FORMAT_DEFAULT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 获取当前日期格式（默认格式）
     * @return String
     */
    public static String getNowDateTime() {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(DATE_TIME_FORMAT_DEFAULT);
        LocalDateTime dt = LocalDateTime.ofInstant(Instant.ofEpochSecond(getNowUnixSecond()), ZoneId.systemDefault());
        return df.format(dt);
    }

    /**
     * 时间戳转日期格式（默认格式）
     * @param second 10位时间戳(秒）
     * @return String
     */
    public static String formatSecond(Long second) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(DATE_TIME_FORMAT_DEFAULT);
        LocalDateTime dt = LocalDateTime.ofInstant(Instant.ofEpochSecond(second), ZoneId.systemDefault());
        return df.format(dt);
    }

    /**
     * 时间戳转日期格式（默认格式）
     *
     * @param dateTime 时间
     * @return 10位时间戳(秒）
     */
    public static Long parseDateTime(String dateTime) {
        long mills = parseByPattern(dateTime, DATE_TIME_FORMAT_DEFAULT);
        return mills / 1000;
    }

    /**
     * @param mills   13位时间戳(毫秒）
     * @param pattern 格式
     * @return String
     */
    public static String formatByPattern(Long mills, String pattern) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(pattern);
        LocalDateTime dt = LocalDateTime.ofInstant(Instant.ofEpochMilli(mills), ZoneId.systemDefault());
        return df.format(dt);
    }


    /**
     * @return String
     */
    public static String formatDate(Date date) {
        if (date != null) {
            return DEFAULT_DATE_FORMAT.format(date);
        }
        return null;
    }



    /**
     * 格式化时间
     *
     * @param dateTime 时间
     * @param pattern  格式
     * @return 13位时间戳(毫秒 ）
     */
    public static Long parseByPattern(String dateTime, String pattern) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(pattern);
        TemporalAccessor temporal = df.parse(dateTime);
        LocalDateTime localDateTime = null;
        if (temporal instanceof LocalDateTime) {
            localDateTime = (LocalDateTime) temporal;
        } else if (temporal instanceof ZonedDateTime) {
            localDateTime = ((ZonedDateTime) temporal).toLocalDateTime();
        } else if (temporal instanceof OffsetDateTime) {
            localDateTime = ((OffsetDateTime) temporal).toLocalDateTime();
        } else {
            LocalDate localDate = temporal.query(TemporalQueries.localDate());
            LocalTime localTime = temporal.query(TemporalQueries.localTime());
            if (localTime == null) {
                localTime = LocalTime.MIN;
            }
            localDateTime = localDate.atTime(localTime);
        }
        Instant instant = localDateTime.atZone(ZoneId.systemDefault()).toInstant();
        return instant.toEpochMilli();
    }

    /**
     * 获取当前日期
     *
     * @return 年-月-日
     */
    public static String getNowDay() {
        Date date = new Date();
        SimpleDateFormat ft = new SimpleDateFormat("yyyy-MM-dd");
        return ft.format(date.getTime());

    }

    /**
     * 获取当前时间戳（秒）
     *
     * @return Long
     */
    public static long getNowUnixSecond() {
        return System.currentTimeMillis() / 1000;
    }

    /**
     * 获取当前时间戳（毫秒）
     *
     * @return Long
     */
    public static long getNowUnixMillis() {
        return System.currentTimeMillis();
    }

    /**
     * 获取当前时间戳(纳秒)
     *
     * @return Long
     */
    public static long getNowUnixNano() {
        return System.nanoTime();
    }

    /**
     * 计算时间差（毫秒）
     *
     * @return
     */
    public static long sinceMillis(long millis) {
        return System.currentTimeMillis() - millis;
    }

    /**
     * Date转时间戳秒
     * @param date Date
     * @return long(秒)
     */
    public static long convertDateToLong(Date date){
        return date.getTime() / 1000;
    }

    /**
     * 时间戳转Date
     * @param second long
     * @return Date
     */
    public static Date convertLongToDate(long second){
        return new Date(second * 1000);
    }


    /**
     * 两个日期间隔天数e
     */
    public static long daysBetweenDate(Date start,Date end){
        LocalDate startDate = start.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endDate = end.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return startDate.until(endDate, ChronoUnit.DAYS);
    }

    public static Timestamp getNowTimestamp(){
        return new Timestamp(getNowUnixMillis());
    }

    public static Date formatDateStr(String dateStr){
        try {
            return DEFAULT_DATE_FORMAT.parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取秒级时间戳对应的年
     */
    public static int getYearFromTimestamp(long timestamp) {
        Instant instant = Instant.ofEpochSecond(timestamp);
        ZonedDateTime zonedDateTime = instant.atZone(ZoneId.systemDefault());
        return zonedDateTime.getYear();
    }

    /**
     * 获取秒级时间戳一年后对应的Date
     */
    public static Date getOneYearLaterDate(long timestamp) {
        Instant instant = Instant.ofEpochSecond(timestamp);
        LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        LocalDateTime oneYearLater = dateTime.plusYears(1);
        return Date.from(oneYearLater.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * Formats a given timestamp into a date-time string based on the specified language.
     *
     * @param timestamp the timestamp to be formatted, in milliseconds since epoch.
     * @return the formatted date-time string.
     */
    public static  String formatDatetimeForLanguage(Long timestamp,String areaId) {
        String area = StringUtils.isEmpty(areaId) ? "ID" : areaId;
        return Area.of(area).timeFormat(timestamp * 1000);
    }

}
