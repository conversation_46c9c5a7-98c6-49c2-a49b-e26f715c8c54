package com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request;

import com.xiaomi.nr.coupon.admin.api.dto.BaseRequest;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 操作券配置入参
 * 涉及接口：保存券配置、更新券配置
 *
 * <AUTHOR>
 */
@Data
public class OperateCouponConfigRequest extends BaseRequest {

    private static final long serialVersionUID = 2740958083432898764L;

    /**
     * 优惠券配置
     */
    @NotNull(message = "优惠券配置不能为空")
    private CouponConfigDTO couponConfigDTO;

    /**
     * 优惠券所属业务平台
     */
    @NotNull(message = "业务平台不能为空")
    private Integer bizPlatform;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;

}
