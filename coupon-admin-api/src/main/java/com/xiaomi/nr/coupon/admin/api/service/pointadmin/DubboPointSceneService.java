package com.xiaomi.nr.coupon.admin.api.service.pointadmin;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.scene.request.OperateSceneRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.scene.response.SceneTypeVO;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.*;
import com.xiaomi.youpin.infra.rpc.Result;

/**
 * @Description: 优惠券场景
 * @Date: 2022.03.03 16:53
 */
public interface DubboPointSceneService {

    /**
     * 积分场景创建
     *
     * @param request request
     * @return Result<Boolean>
     */
    Result<Boolean> createOrUpdateScene(PointSceneRequest request);

    /**
     * 积分场景新建与编辑
     *
     * @param request request
     * @return Result<Boolean>
     */
    Result<Boolean> createOrUpdateSceneOpen(PointSceneOpenRequest request);

    /**
     * 上下线场景
     *
     * @param request request
     * @return Result<Boolean>
     */
    Result<Boolean> operateScene(OperateSceneRequest request);

    /**
     * 查询所有场景并且分类
     *
     * @param request request
     * @return Result<SceneWithCatResponse>
     */
    Result<SceneWithCatResponse> searchSceneWithCat(SceneWithCatRequest request);

    /**
     * 查询关联预算池
     *
     * @return Result<SearchRelationBudgetResponse>
     */
    Result<SearchRelationBudgetResponse> searchRelationBudget();

    /**
     * 分页查询所有场景并且分类
     *
     * @param request request
     * @return Result<SceneWithCatResponse>
     */
    Result<BasePageResponse<SceneTypeVO>> searchScenePage(SceneWithCatPageRequest request);
}
