package com.xiaomi.nr.coupon.admin.api.dto.ecard.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/27 16:48
 */
@Data
public class GetEcardInfoRequest implements Serializable {
    private static final long serialVersionUID = 2630370364841593472L;

    /**
     * ecard编号列表
     */
    @NotNull(message = "cardIdList不能为空")
    @Size(max = 50, message = "礼品卡id数量不能超过50")
    private List<Long> cardIdList;
}
