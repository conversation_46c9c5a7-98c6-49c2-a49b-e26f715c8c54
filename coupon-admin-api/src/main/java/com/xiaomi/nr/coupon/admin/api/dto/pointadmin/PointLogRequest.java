package com.xiaomi.nr.coupon.admin.api.dto.pointadmin;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageRequest;
import lombok.Data;
import lombok.Value;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description: 券日志列表请求参数
 * @Date: 2022.02.25 16:36
 */
@Data
public class PointLogRequest extends BasePageRequest implements Serializable {

    @NotNull
    @Min(value = 1, message = "积分批次ID必须大于0")
    private Long batchId;
}
