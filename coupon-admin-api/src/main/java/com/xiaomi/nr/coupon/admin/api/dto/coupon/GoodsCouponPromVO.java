package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 可用券信息
 *
 * <AUTHOR>
 * @date 2023-01-03
 */

@Data
@Builder
public class GoodsCouponPromVO implements Serializable {
    private static final long serialVersionUID = -4608552053022650961L;

    /**
     * 优惠信息
     */
    private PromotionRuleVO promotionRuleVO;

    /**
     * 券配置id
     */
    private long configId;

    /**
     * 使用渠道
     */
    private List<Integer> useChannel;

    /**
     * 折扣力度
     */
    private BigDecimal discountLevel;
}
