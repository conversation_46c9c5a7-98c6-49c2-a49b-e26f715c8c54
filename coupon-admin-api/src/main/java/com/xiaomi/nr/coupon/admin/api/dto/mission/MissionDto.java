package com.xiaomi.nr.coupon.admin.api.dto.mission;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class MissionDto implements Serializable {

    private static final long serialVersionUID = -1585447070420394626L;

    /**
     * 发放任务id
     */
    private Long missionId;

    /**
     * 发放任务名称
     */
    private String missionName;

    /**
     * 发放任务状态
     */
    private String status;

    /**
     * 发放任务状态描述
     */
    private String statusDesc;

    /**
     * 发放任务类型（1:接口调用发放 2:自动任务批量发放）
     */
    private String missionType;

    /**
     * 发放任务最多可发放的券数量
     */
    private Long sendNumLimit;

    /**
     * 券的真实有效期类别（section:时间段内有效 days:从发放起*天内有效）
     */
    private String timeType;

    /**
     * 券的真实有效期类别描述
     */
    private String timeTypeDesc;

    /**
     * 券的真实有效开始时间
     */
    private Long couponStartTime;

    /**
     * 券的真实有效结束时间
     */
    private Long couponEndTime;

    /**
     * 券从发放起*天内有效（真实有效时间）
     */
    private Integer couponDays;

    /**
     * 券从发放起*小时内有效（真实有效时间）
     */
    private Integer couponHours;

    /**
     * 发放任务的创建时间
     */
    private Long addTime;

    /**
     * 优惠券类型id (优惠券配置ID)
     */
    private Long couponConfigId;

    /**
     * 优惠券类型名称
     */
    private String couponConfigName;

    /**
     * 优惠券类型码 cash：现金券，discount：折扣券，deductible：抵扣券
     * 这个即将要作废
     */
    private String couponTypeCode;

    /**
     * 优惠券类型码描述 cash：现金券，discount：折扣券，deductible：抵扣券
     * 这个即将要作废
     */
    private String couponTypeCodeDesc;

    /**
     * 使用类型 cash：现金券，discount：折扣券，deductible：抵扣券
     */
    private String useType;

    /**
     * 使用类型描述 cash：现金券，discount：折扣券，deductible：抵扣券
     */
    private String useTypeDesc;

    /**
     * 优惠券的面值，100：代表满*减100元，8.5：代表满*打8.5折，空：代表是抵扣券的
     */
    private String showValue;

    /**
     * 优惠券的面额单位（现金券的单位为元，折扣券的单位为折，抵扣券的单位为空）
     */
    private String showUnit;

    /**
     * 优惠券的发放渠道，store_manager：店长券，store_order_gift 等等之类的，空：其他渠道
     */
    private String couponSendChannel;

    /**
     * 优惠券最小开始时间
     */
    private Long globalCouponStartTime;

    /**
     * 优惠券最大结束时间
     */
    private Long globalCouponEndTime;

    /**
     * 优惠券类型
     */
    private Integer couponType;

    /**
     * 履约方式
     */
    private Integer shipmentId;
}
