package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import lombok.Data;

import java.io.Serializable;

@Data
public class FillCouponDetailVO implements Serializable {

    /**
     * 用户id
     */
    private long mid;

    private int count;

    private String entity;

    /**
     * 失败原因
     */
    private String reason;

    public  FillCouponDetailVO(String entity, String reason){
        this.entity = entity;
        this.reason = reason;
    }
    public FillCouponDetailVO(String entity, int count, String reason){
        this.entity = entity;
        this.reason = reason;
    }

    public FillCouponDetailVO(String entity, int count) {
        this.entity = entity;
        this.count = count;
    }

}
