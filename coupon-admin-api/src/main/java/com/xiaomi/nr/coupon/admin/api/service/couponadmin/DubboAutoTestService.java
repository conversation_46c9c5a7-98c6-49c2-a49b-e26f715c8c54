package com.xiaomi.nr.coupon.admin.api.service.couponadmin;

import com.xiaomi.nr.coupon.admin.api.dto.autotest.request.CleanCouponDataRequest;
import com.xiaomi.nr.coupon.admin.api.dto.autotest.request.CleanEcardDataRequest;
import com.xiaomi.nr.coupon.admin.api.dto.autotest.request.CleanRedpacketDataRequest;
import com.xiaomi.nr.coupon.admin.api.dto.autotest.request.UserEcardListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.autotest.response.CouponCreateResponse;
import com.xiaomi.nr.coupon.admin.api.dto.autotest.response.UserEcardListResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponConfigVO;
import com.xiaomi.youpin.infra.rpc.Result;

/**
 * 自动化测试接口，业务需求勿用
 */
public interface DubboAutoTestService {

    /**
     * 创建券
     * @param request
     * @return
     */
    Result<CouponCreateResponse> createCoupon(CouponConfigVO request);


    /**
     * 清除券信息数据，券配置+用户券
     * @param request
     * @return
     */
    Result<Void> cleanCouponData(CleanCouponDataRequest request);


    /**
     * 清除用户红包
     * @param request
     * @return
     */
    Result<Void> cleanRedpacketData(CleanRedpacketDataRequest request);


    /**
     * 查询用户礼品卡,不传礼品卡id，查所有符合条件的有效礼品卡
     * @param request
     * @return
     */
    Result<UserEcardListResponse> queryUserEcard(UserEcardListRequest request);


    /**
     * 清除用户礼品卡
     * @param request
     * @return
     */
    Result<Void> cleanEcardData(CleanEcardDataRequest request);

}
