package com.xiaomi.nr.coupon.admin.api.dto.pointadmin;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 批次状态变更
 *
 * <AUTHOR>
 * @date 2023/12/8
 */
@Data
public class ChangePointBatchStatusRequest implements Serializable {

    private static final long serialVersionUID = 7553474172590359209L;

    /**
     * 积分批次id
     */
    @NotNull(message = "批次id不能为空")
    @Min(value = 1, message = "批次id需大于0")
    private Long batchId;

    /**
     * 操作类型
     */
    @NotNull(message = "操作类型不能为空")
    private Integer optType;

    /**
     * 操作人邮箱前缀
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;

}
