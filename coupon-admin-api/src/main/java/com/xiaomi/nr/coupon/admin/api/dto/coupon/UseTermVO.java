package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class UseTermVO implements Serializable {

    private static final long serialVersionUID = 6725314687496087475L;
    /**
     * 使用有效期类型 1 固定有效,2 相对有效期, 3 发券方自定义有效期
     */
    private int useTimeType;
    /**
     * 优惠券生效开始时间
     */
    private Date startUseTime;
    /**
     * 优惠券生效开始时间
     */
    private Long startUseTimeStamp;
    /**
     * 优惠券生效结束时间
     */
    private Date endUseTime;
    /**
     * 优惠券生效结束时间
     */
    private Long endUseTimeStamp;
    /**
     * 有效时长(单位小时)
     */
    private int useDuration;
    /**
     * 相对时间粒度，1-小时，2=天
     */
    private int timeGranularity;
    /**
     * 优惠券生效开始时间（可读）
     */
    private String startUseTimeShow;
    /**
     * 优惠券生效结束时间（可读）
     */
    private String endUseTimeShow;


}
