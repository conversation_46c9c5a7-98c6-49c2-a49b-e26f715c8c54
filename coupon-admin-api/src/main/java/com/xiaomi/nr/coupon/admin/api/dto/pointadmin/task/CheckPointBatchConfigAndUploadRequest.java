package com.xiaomi.nr.coupon.admin.api.dto.pointadmin.task;

import com.xiaomi.nr.coupon.admin.api.dto.BaseRequest;
import lombok.Data;

import java.io.Serializable;

/**
 * @description 校验积分批次信息请求入参
 * <AUTHOR>
 * @date 2024-08-20 14:11
*/
@Data
public class CheckPointBatchConfigAndUploadRequest extends BaseRequest {
    private static final long serialVersionUID = -7828414403243399096L;

    /**
     * 积分批次Id
     */
    private Long pointBatchId;

    /**
     * 计划发放积分总额
     */
    private Long planPointCount;

    /**
     * 是否上传
     */
    private boolean upload = false;

    /**
     * 数据集fds地址
     */
    private String fdsAddress;

}
