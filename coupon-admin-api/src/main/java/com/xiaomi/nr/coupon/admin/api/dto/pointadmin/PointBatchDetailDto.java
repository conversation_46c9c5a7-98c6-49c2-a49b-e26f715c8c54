package com.xiaomi.nr.coupon.admin.api.dto.pointadmin;

import com.xiaomi.nr.coupon.admin.api.dto.BudgetInfoDto;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/12/12 19:40
 */
@Data
public class PointBatchDetailDto implements Serializable {
    private static final long serialVersionUID = -8032692437730000583L;

    /**
     * 积分批次id
     */
    private Long batchId;

    /**
     * 积分批次名称
     */
    private String batchName;

    /**
     * 发放开始时间
     */
    private Long startTime;

    /**
     * 发放结束时间
     */
    private Long endTime;

    /**
     * 发放场景
     */
    private String sceneCode;

    /**
     * 发放场景
     */
    private String sendScene;

    /**
     * 积分总额
     */
    private Long applyCount;

    /**
     * 积分余额
     */
    private Long balanceCount;

    /**
     * 已发总额
     */
    private Long sendCount;

    /**
     * 预计抵扣
     */
    private BigDecimal predictDeduction;

    /**
     * 审批id
     */
    private Long reviewId;

    /**
     * 预算id
     */
    private Long budgetId;

    /**
     * 预算池信息
     */
    private BudgetInfoDto budgetInfoDto;

    /**
     * 批次状态
     */
    private Integer status;

    /**
     * 审批流类型编码 1交付物流、2用户服务
     */
    private Integer processType;
}

