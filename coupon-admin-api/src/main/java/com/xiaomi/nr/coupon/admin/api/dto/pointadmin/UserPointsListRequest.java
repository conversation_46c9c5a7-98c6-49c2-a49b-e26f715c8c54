package com.xiaomi.nr.coupon.admin.api.dto.pointadmin;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 用户积分列表
 *
 * <AUTHOR>
 * @date 2023/12/6
 */
@Data
public class UserPointsListRequest implements Serializable {
    private static final long serialVersionUID = 7323474172678359209L;

    /**
     * 批次id
     */
    @Min(value = 1, message = "批次ID需大于0")
    private Long batchId;

    /**
     * 用户ID
     */
    @Min(value = 1, message = "用户ID需大于0")
    private Long mid;

    /**
     * 最后一条记录ID（作废）
     */
    @Deprecated
    private Long lastId;

    /**
     * 页号
     */
    @NotNull(message = "页号不能为空")
    @Min(value = 1, message = "页号需大于0")
    private Integer pageNo;

    /**
     * 每页记录数
     */
    @NotNull(message = "每页记录数不能为空")
    @Min(value = 1, message = "每页记录数需大于0")
    @Max(value = 1000, message = "每页记录数需小于1000")
    private Integer pageSize;

}
