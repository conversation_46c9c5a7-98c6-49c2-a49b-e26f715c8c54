package com.xiaomi.nr.coupon.admin.api.service.couponadmin;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponTaskListVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.FillCouponDetailVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CouponCodeDownloadRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CouponCodeTaskListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.*;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.response.*;
import com.xiaomi.youpin.infra.rpc.Result;

import javax.validation.Valid;
import java.util.List;

/**
 * 灌券任务服务
 */
public interface DubboCouponTaskService {

    /**
     * 灌券任务列表
     * @param request request
     * @return response
     */
    Result<BasePageResponse<CouponTaskListVO>> couponFillTaskList(CouponFillTaskListRequest request);

    /**
     * 创建灌券任务
     * @param request request
     * @return void
     */
    Result<CreateTaskResponse> couponFillTaskCreate(@Valid CreateFillCouponTaskRequest request);

    /**
     * 查看灌券任务详情
     * @param request request
     * @return response
     */
    Result<CouponFillTaskDetailResponse> taskDetail(CouponFillTaskDetailRequest request);

    /**
     * 重试灌券任务
     * @param request request
     * @return void
     */
    Result<Void> taskRetry(ReStartTaskRequest request);

    /**
     * 下载灌券详情
     * @param request request
     * @return FillCouponDetailVO
     */
    Result<List<FillCouponDetailVO>> detailDownload(DownLoadFillTaskDetailRequest request);

    /**
     * 下载灌券失败名单
     * @param request request
     * @return FillCouponDetailVO
     */
    Result<List<FillCouponDetailVO>> failUidDownload(DownLoadFillTaskDetailRequest request);

    /**
     * 查询人群包信息服务
     * @param request UserTagRequest
     * @return List<String>
     */
    Result<UserTagDescResponse> queryUserTagDesc(UserTagDescRequest request);

    /**
     * 查询券信息
     * @param request request
     * @return String
     */
    Result<CouponConfigDescResponse> queryCouponConfigDesc(CouponConfigDescRequest request);

    /**
     * 下载灌券用户集
     *
     * @param request
     * @return
     */
    Result<DownLoadUserResponse> downloadUserList(DownLoadUserRequest request);

    /**
     * 优惠码列表
     * @param request
     * @return
     */
    Result<BasePageResponse<CouponCodeTaskListVO>> couponCodeTaskList(CouponCodeTaskListRequest request);

    /**
     * 优惠码下载
     * @param request request
     * @return String
     */
    Result<String> downloadCode(CouponCodeDownloadRequest request);


}
