package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

@Data
public class CouponConfigDescVO implements Serializable {

    /**
     * 券配置id
     */
    private long id;

    /**
     * 券描述
     */
    private String nameDesc;

    /**
     * 剩余可发放数量
     */
    private long availableCount;

    /**
     * 优惠券类型  1: 商品券 2: 运费券 3:超级补贴券
     */
    private Integer couponType;


    /**
     * 优惠券类型名
     */
    private String couponTypeName;

    /**
     * 发放场景
     */
    private Map<String, String> sendSceneMap;



}
