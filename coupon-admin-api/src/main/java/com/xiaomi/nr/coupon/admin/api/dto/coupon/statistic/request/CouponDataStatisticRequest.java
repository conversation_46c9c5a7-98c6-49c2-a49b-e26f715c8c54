package com.xiaomi.nr.coupon.admin.api.dto.coupon.statistic.request;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageRequest;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CouponDataStatisticRequest extends BasePageRequest {

    private static final long serialVersionUID = -7354326410522446356L;

    /**
     * 优惠券id列表
     */
    private String configIds;

    /**
     * 灌券任务
     */
    private String activity;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 使用渠道
     */
    private List<String> useChannel;

    /**
     * 发放方式
     */
    private String sendType;

    /**
     * 导出数据类型 1 券数据   2 灌券数据
     */
    private Integer exportData;

    /**
     * 创建人
     */
    private String creator;

}
