package com.xiaomi.nr.coupon.admin.api.service.pointadmin;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.request.UserCouponCancelRequest;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.UserPointCancelRequest;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.UsersPointDto;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.UserPointsListRequest;
import com.xiaomi.youpin.infra.rpc.Result;

import javax.validation.Valid;

/**
 * 用户积分服务
 *
 * <AUTHOR>
 * @date 2023/12/5
 */
public interface DubboUserPointsService {

    /**
     * 获取用户积分列表
     *
     * @param request UserPointListRequest
     * @return Result
     */
    Result<BasePageResponse<UsersPointDto>> getUserPointsList(@Valid UserPointsListRequest request);


    /**
     * 作废用户券
     * @param request
     * @return
     */
    Result<Void> cancelUserPoint(@Valid UserPointCancelRequest request);
}
