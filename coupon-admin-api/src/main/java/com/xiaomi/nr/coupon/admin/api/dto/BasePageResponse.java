package com.xiaomi.nr.coupon.admin.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Collection;

@Data
public class BasePageResponse<T> implements Serializable {

    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = -362450984932162465L;

    /**
     * 页码
     */
    private int pageNo;

    /**
     * 每页数据量
     */
    private int pageSize;

    /**
     * 总数据量
     */
    private long totalCount;
    /**
     * 兼容一下返还给pc工作站的
     */
    private long total;

    /**
     * 总页码
     */
    private long totalPage;

    /**
     * 最后一个记录ID
     */
    private Long lastId;

    /**
     * 分页数据
     */
    private Collection<T> list;

    /**
     * 构造
     */
    public BasePageResponse() {
    }

    public BasePageResponse(int pageNo, int pageSize){
        this.pageNo = pageNo;
        this.pageSize = pageSize;
    }

    public BasePageResponse(int pageNo, int pageSize, long totalCount, Collection<T> list) {
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        this.totalCount = totalCount;
        this.list = list;
    }

    public BasePageResponse(int pageNo, int pageSize, long totalCount, long totalPage, Collection<T> list) {
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        this.totalCount = totalCount;
        this.totalPage = totalPage;
        this.list = list;
    }

    public BasePageResponse(int pageNo, int pageSize, long totalCount, long totalPage, long lastId, Collection<T> list) {
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        this.totalCount = totalCount;
        this.totalPage = totalPage;
        this.lastId = lastId;
        this.list = list;
    }

}
