package com.xiaomi.nr.coupon.admin.api.dto.coupon.sketch.request;

import com.google.common.collect.ImmutableMap;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageRequest;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

@Data
public class CouponSketchListRequest extends BasePageRequest implements Serializable {

    private Map<String,String> orderByMap = ImmutableMap.<String, String>builder()
            .put("startFetchTime", "start_fetch_time")
            .put("createTime", "create_time")
            .build();
    /**
     * 优惠券类型  1: 商品券 2: 运费券
     */
    private Integer couponType;

    /**
     * 草稿id
     */
    private long sketchId;

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     *领取时间起
     */
    private Date startFetchTime;

    /**
     *领取时间止
     */
    private Date endFetchTime;

    /**
     * 提交人
     */
    private String creator;

    /**
     *提交时间起
     */
    private Date startCreateTime;

    /**
     *提交时间止
     */
    private Date endCreateTime;

}
