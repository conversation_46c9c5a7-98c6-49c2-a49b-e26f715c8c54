package com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.request;

import com.google.common.collect.ImmutableMap;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageRequest;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import lombok.Data;

import java.util.Map;

@Data
public class UserCouponListRequest extends BasePageRequest {

    private Map<String,String> orderByMap = ImmutableMap.<String, String>builder()
            .put("configId", "type_id")
            .put("fetchTime", "add_time")
            .put("endUseTime", "end_time")
            .build();

    /**
     * 用户id
     */
    private long uid;

    /**
     * 券配置id
     */
    private long configId;

    /**
     * 优惠券类型  1: 商品券 2: 运费券
     */
    private Integer couponType;

    /**
     * 券配置名称
     */
    private String configName;

    /**
     * 用户券状态
     */
    private String couponStatus;

    /**
     * 用户券id
     */
    private long couponId;

    /**
     * vid
     */
    private String vid;

    /**
     * vin
     */
    private String vin;

}
