package com.xiaomi.nr.coupon.admin.api.service.couponadmin;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.PackageInfoVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.SkuInfoVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.SsuInfoVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.goods.request.*;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.goods.response.*;
import com.xiaomi.youpin.infra.rpc.Result;

/**
 * Created by vision on 2022/3/10.
 */
public interface DubboGoodsService {

    /**
     * skuid查询sku列表
     *
     * @param request
     * @return
     */
    Result<SkuListResponse> queryListBySkuIds(SkuListRequest request);

    /**
     * skuid查询sku列表
     *
     * @param request
     * @return
     */
    Result<SkuListResponse> queryListBySkuIdsGlobal(SkuListRequest request);

    /**
     * 套装id查询套装列表
     *
     * @param request
     * @return
     */
    Result<PackageListResponse> queryListByPackageIds(PackageListRequest request);

    /**
     * 分页查询sku列表
     *
     * @param request
     * @return 商品信息
     */
    Result<BasePageResponse<SkuInfoVO>> querySkuPageList(SkuPageListRequest request);

    /**
     * 分页查询套装列表
     *
     * @param request
     * @return 套装信息
     */
    Result<BasePageResponse<PackageInfoVO>> queryPackagePageList(PackagePageListRequest request);


    /**
     * 查询所有类目
     */
    Result<CategoryListResponse> getCategoryList(CategoryListRequest request);


    /**
     * 根据类目查询sku
     */
    Result<SkuListResponse> getSkuByCategoryId(SkuCategoryIdRequest request);


    /**
     * 查询商品预估折扣力度
     */
    Result<GoodsDiscountLevelResponse> getGoodsDiscountLevel(GoodsDiscountLevelRequest request);


    /**
     * 获取分类新品
     */
    Result<LatestGoodsResponse> getLatestGoods(LatestGoodsRequest request);

    /**
     * 分页查询套装列表
     */
    Result<BasePageResponse<PackageInfoVO>> querySuitPageList(SuitPageListRequest request);

    /**
     * 上传并查询套装
     */
    Result<PackageListResponse> queryListBySuitIds(PackageListRequest request);


    Result<BasePageResponse<SsuInfoVO>> queryGoodsByPageGoodsRequest(PageGoodsRequest pageGoodsRequest);
}
