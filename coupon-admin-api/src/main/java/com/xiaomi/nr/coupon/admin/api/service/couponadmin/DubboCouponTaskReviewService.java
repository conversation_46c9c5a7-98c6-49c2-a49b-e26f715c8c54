package com.xiaomi.nr.coupon.admin.api.service.couponadmin;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.CouponFillReviewDTO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.*;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.response.CouponFillReviewCancelResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.response.CouponFillReviewDetailResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.response.CreateCouponFillReviewResponse;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

public interface DubboCouponTaskReviewService {

    /**
     * 创建灌券任务审核
     * @param request request
     * @return result
     */
    Result<CreateCouponFillReviewResponse> createTaskReview(CreateCouponFillReviewRequest request);

    /**
     * 撤销灌券任务审核
     * @param request request
     * @return result
     */
    Result<CouponFillReviewCancelResponse> cancelTaskReview(CouponFillReviewCancelRequest request);
    /**
     * 撤销灌券任务审核
     * @param request request
     * @return result
     */
    Result<CouponFillReviewCancelResponse> cancelTaskReviewHttp(CouponFillReviewCancelRequest request);

    /**
     * 灌券任务审核列表
     * @param request request
     * @return result
     */
    Result<BasePageResponse<CouponFillReviewDTO>> taskReviewList(CouponFillReviewListRequest request);


    /**
     * 灌券任务审核详情
     * @param request request
     * @return result
     */
    Result<CouponFillReviewDetailResponse> taskReviewDetail(CouponFillReviewDetailRequest request);

}
