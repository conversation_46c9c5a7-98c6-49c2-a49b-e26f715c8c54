package com.xiaomi.nr.coupon.admin.api.dto;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/03/19
 */
@Getter
public class PageInfo<T> implements Serializable {

    private static final long serialVersionUID = 4724410641623127663L;

    /**
     * 当前页码
     */
    private Long pageNum;

    /**
     * 每页数量
     */
    private Long pageSize;

    /**
     * 总数
     */
    private Long total;

    /**
     * 数据列表
     */
    private List<T> list;

    public PageInfo(Long pageNum, Long pageSize, Long total, List<T> list) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.total = total;
        this.list = list;
    }

    public static PageInfo EmptyPageInfo() {
        return new PageInfo<>(0L, 0L, 0L, Lists.newArrayList());
    }
}
