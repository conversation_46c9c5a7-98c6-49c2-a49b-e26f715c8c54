package com.xiaomi.nr.coupon.admin.api.dto.coupon.optlog.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 日志列表
 * @Date: 2022.02.25 18:35
 */
@Data
public class CouponLogListResponse implements Serializable {
    /**
     * 优惠券简要信息
     */
    private CouponBriefVO couponBriefVO;
    /**
     * 操作日志列表
     */
    private List<CouponLogListVO> couponLogListVOList;
    /**
     * 页码
     */
    private int pageNo;
    /**
     * 每页数据量
     */
    private int pageSize;
    /**
     * 总数据量
     */
    private long totalCount;
    /**
     * 总页数
     */
    private int totalPage;

}
