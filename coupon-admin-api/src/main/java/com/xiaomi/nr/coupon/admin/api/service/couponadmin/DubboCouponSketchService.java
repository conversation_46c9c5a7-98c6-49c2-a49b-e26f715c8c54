package com.xiaomi.nr.coupon.admin.api.service.couponadmin;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponSketchListVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.sketch.request.CouponCreateSketchRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.sketch.request.CouponSketchDeleteRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.sketch.request.CouponSketchListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.sketch.request.CouponSketchRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.sketch.response.CouponCreateSketchResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.sketch.response.CouponSketchDetailResponse;
import com.xiaomi.youpin.infra.rpc.Result;

/**
 * @Description: 优惠券草稿
 * @Date: 2022.02.25 15:01
 */
public interface DubboCouponSketchService {
    /**
     * 创建券草稿
     * @param request
     * @return
     */
    Result<CouponCreateSketchResponse> createSketch(CouponCreateSketchRequest request);

    /**
     * 券草稿列表
     * @param request
     * @return
     */
    Result<BasePageResponse<CouponSketchListVO>> querySketchList(CouponSketchListRequest request);

    /**
     * 券草稿详情
     * @param request
     * @return
     */
    Result<CouponSketchDetailResponse> querySketchDetail(CouponSketchRequest request) throws Exception;

    /**
     * 删除草稿
     * @param request
     * @return
     */
    Result<Boolean> deleteSketch(CouponSketchDeleteRequest request);
}
