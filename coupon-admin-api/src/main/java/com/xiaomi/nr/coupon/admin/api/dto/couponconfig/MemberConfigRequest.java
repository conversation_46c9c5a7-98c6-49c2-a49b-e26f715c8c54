package com.xiaomi.nr.coupon.admin.api.dto.couponconfig;

import com.xiaomi.nr.coupon.admin.api.dto.BaseRequest;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: pro会员，权益开关
 * @Date: 2022.05.11 14:22
 */
@Data
public class MemberConfigRequest extends BaseRequest implements Serializable {
    /**
     * 价保
     */
    private MemberConfigPriceProtectDto priceProtect;

    /**
     * 米金
     */
    private MemberConfigPointsDto points;
}
