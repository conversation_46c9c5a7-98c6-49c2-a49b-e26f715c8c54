package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import com.xiaomi.nr.coupon.admin.api.enums.AnnualTypeEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class ExtPropVO implements Serializable {

    /**
     * 是否包邮 1-是 2-否
     */
    private int postFree=2;

    /**
     * 可分享 1-是 2-否
     */
    private int share=2;

    /**
     * 限制地区 1-是 2-否
     */
    private int area=2;
    /**
     * 是否会员 1-是， 2-否, 0-老数据默认反序列化后的值
     */
    private int proMember=2;

    /**
     * 是否专店专用 1-是， 2-否
     */
    private int specialStore=2;

    /**
     * 是否公开推广：1-是; 2-否
     */
    private Integer publicPromotion=2;

    /**
     * 抵扣金额类型（1：定金 2：尾款）
     */
    private Integer checkoutStage=2;

    /**
     * 有效期是否展示(0 不展示 1 展示)
     */
    private Integer displayDate=1;



    /**
     * 年度类型：1-单年度；2-双年度
     * {@link AnnualTypeEnum}
     */
    private Integer annualType=0;

}
