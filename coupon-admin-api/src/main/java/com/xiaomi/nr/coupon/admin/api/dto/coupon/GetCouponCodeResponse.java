package com.xiaomi.nr.coupon.admin.api.dto.coupon;


import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/27 11:13
 */
@Data
public class GetCouponCodeResponse implements Serializable {
    private static final long serialVersionUID = -7682572518304296409L;

    /**
     * 有码优惠券信息
     */
    @ApiDocClassDefine(value = "couponCodeInfoList", description = "有码优惠券信息")
    private List<CouponCodeInfo> couponCodeInfoList;
}
