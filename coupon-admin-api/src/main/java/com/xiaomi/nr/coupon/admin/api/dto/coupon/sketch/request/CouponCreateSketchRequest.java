package com.xiaomi.nr.coupon.admin.api.dto.coupon.sketch.request;

import com.xiaomi.nr.coupon.admin.api.dto.BaseRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.ApplyAttachmentVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponConfigVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CouponCreateSketchRequest extends BaseRequest implements Serializable {
    /**
     * 类型 0 - 新增， 1 - 编辑
     */
    private int type;
    /**
     * 草稿id
     */
    private Long sketchId;
    /**
     * 券信息
     */
    private CouponConfigVO couponConfigVO;

    /**
     * 申请附件地址
     */
    private List<ApplyAttachmentVO> applyAttachment;
}
