package com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 券配置信息
 */
@Getter
@Setter
public class ConfigInfoVO implements Serializable {
    private static final long serialVersionUID = -8990368004871399423L;

    /**
     * 优惠券id
     */
    private long configId;

    /**
     * 优惠券类型(1:商品券｜2:运费券)
     */
    private Integer couponType;

    /**
     * 优惠券名称
     */
    private String name;

    /**
     * 优惠类型, 1:满减, 2:满折, 3:N元券, 4:立减
     */
    private int promotionType;

    /**
     * 优惠值(单位分/折)，如8折，传800
     */
    private long promotionValue;

    /**
     * 门槛值满元（单位分)
     */
    private long bottomPrice;

    /**
     * 门槛值满件（单位个)
     */
    private int bottomCount;

    /**
     * 门槛值满件（单位个)
     */
    private int bottomType;

    /**
     * 投放场景
     */
    private String sendScene;

    /**
     * 投放场景名称
     */
    private String sendSceneName;
}
