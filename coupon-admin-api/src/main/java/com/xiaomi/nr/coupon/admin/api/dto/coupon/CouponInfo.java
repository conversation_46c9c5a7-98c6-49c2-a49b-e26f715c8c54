package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/8/27 15:22
 */
@Data
public class CouponInfo implements Serializable {
    private static final long serialVersionUID = -5624582190617361466L;

    /**
     * 优惠券ID
     */
    @ApiDocClassDefine(value = "id", description = "优惠券id")
    private Long id;

    /**
     * 用户ID
     */
    @ApiDocClassDefine(value = "userId", description = "用户id")
    private Long userId;

    /**
     * 优惠券配置ID
     */
    @ApiDocClassDefine(value = "typeId", description = "优惠券配置id")
    private Long typeId;

    /**
     * 活动编号
     */
    @ApiDocClassDefine(value = "activityId", description = "活动编号")
    private String activityId;

    /**
     * 订单号
     */
    @ApiDocClassDefine(value = "orderId", description = "订单号")
    private Long orderId;

    /**
     * 使用时间
     */
    @ApiDocClassDefine(value = "useTime", description = "使用时间")
    private Long useTime;

    /**
     * 添加时间
     */
    @ApiDocClassDefine(value = "addTime", description = "添加时间")
    private Long addTime;

    /**
     * 优惠券发送类型
     */
    @ApiDocClassDefine(value = "sendType", description = "优惠券发送类型")
    private String sendType;

    /**
     * 发券的订单号
     */
    @ApiDocClassDefine(value = "fromOrderId", description = "发券的订单号")
    private String fromOrderId;

    /**
     * 实际抵用金额
     */
    @ApiDocClassDefine(value = "replaceMoney", description = "实际抵用金额")
    private BigDecimal replaceMoney;

    /**
     * 线下使用
     */
    @ApiDocClassDefine(value = "offline", description = "线下使用")
    private Integer offline;

    /**
     * 减免邮费
     */
    @ApiDocClassDefine(value = "reduceExpress", description = "减免金额")
    private BigDecimal reduceExpress;

    /**
     * 券的父id
     */
    @ApiDocClassDefine(value = "parentId", description = "券的父id")
    private Long parentId;
}
