package com.xiaomi.nr.coupon.admin.api.dto.coupon.user;

import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class UserInfoVo  implements Serializable {

    private static final long serialVersionUID = -6193072663079020646L;
    @ApiDocClassDefine(
            value = "miId",
            description = "米聊号"
    )
    private Long miId;
    @ApiDocClassDefine(
            value = "name",
            description = "姓名"
    )
    private String name;
    @ApiDocClassDefine(
            value = "userState",
            description = "用户状态"
    )
    private Integer userState;
    @ApiDocClassDefine(
            value = "isOfficial",
            description = "是否自有：1-自有员工 0-非自有"
    )
    private Integer isOfficial;
    @ApiDocClassDefine(
            value = "userPrivateInfo",
            description = "用户隐私相关信息"
    )
    private UserPrivateInfoVo userPrivateInfo;
    @ApiDocClassDefine(
            value = "positionList",
            description = "岗位List"
    )
    private List<PositionVo> positionList;

}
