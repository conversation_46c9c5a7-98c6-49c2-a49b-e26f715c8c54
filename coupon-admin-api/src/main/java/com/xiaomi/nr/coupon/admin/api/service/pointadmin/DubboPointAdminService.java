package com.xiaomi.nr.coupon.admin.api.service.pointadmin;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.BudgetInfoDto;
import com.xiaomi.nr.coupon.admin.api.dto.PageInfo;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.*;
import com.xiaomi.youpin.infra.rpc.Result;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/5 11:33
 */
public interface DubboPointAdminService {
    /**
     * 保存积分批次配置信息
     *
     * @param request request
     * @return Result<SavePointBatchResponse>
     */
    Result<SavePointBatchResponse> savePointBatch(@Valid SavePointBatchRequest request);

    /**
     * 更新积分批次配置信息
     *
     * @param request request
     * @return Result<Void>
     */
    Result<SavePointBatchResponse> updatePointBatch(@Valid SavePointBatchRequest request);

    /**
     * 通用黑名单列表
     *
     * @param request request
     * @return Result<BasePageResponse<SsuBlacklistDto>>
     */
    Result<BasePageResponse<SsuBlacklistDto>> ssuBlacklist(SsuBlacklistRequest request);

    /**
     * 黑名单删除ssu
     *
     * @param request request
     * @return Result<Void>
     */
    Result<Void> deleteSsuBlacklist(@Valid DeleteSsuBlacklistRequest request);

    /**
     * 黑名单添加ssu
     *
     * @param request request
     * @return Result<Void>
     */
    Result<Void> addSsuBlacklist(@Valid AddSsuBlacklistRequest request);

    /**
     * 获取积分批次配置列表
     *
     * @param req       request
     * @return          积分批次列表
     */
    Result<List<PointBatchConfigData>> pointBatchList(PointBatchListRequest req);

    /**
     * 批次状态变更
     *
     * @param req   req
     * @return      void
     */
    Result<Void> changePointBatchStatus(@Valid ChangePointBatchStatusRequest req);

    /**
     * 更新积分批次配置信息
     *
     * @return Result<Void>
     */
    Result<Void> renewPointBatchConfig();

    /**
     * 积分批次配置详情
     *
     * @param request request
     * @return Result<PointBatchDetailDto>
     */
    Result<PointBatchDetailDto> pointBatchDetail(@Valid PointBatchDetailRequest request);

    /**
     * 查询预算池列表
     * @param
     * @return
     */
    Result<PageInfo<BudgetInfoDto>> queryBudgetList(PointQueryBudgetListRequest request);
}
