package com.xiaomi.nr.coupon.admin.api.dto.coupon.user;

import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;

@Data
public class PositionVo implements Serializable {

    private static final long serialVersionUID = -3859377587926475688L;
    @ApiDocClassDefine(
            value = "scene",
            required = true,
            description = "租户/场景"
    )
    private String scene;
    @ApiDocClassDefine(
            value = "areaId",
            description = "国家/地区ID"
    )
    private String areaId;
    @ApiDocClassDefine(
            value = "positionId",
            description = "岗位ID"
    )
    private Integer positionId;
    @ApiDocClassDefine(
            value = "positionName",
            description = "岗位名称"
    )
    private String positionName;
    @ApiDocClassDefine(
            value = "privilegeState",
            description = "权限状态：0-无效 1-有效 2-冻结"
    )
    private Integer privilegeState;
    @ApiDocClassDefine(
            value = "organType",
            description = "机构类型"
    )
    private String organType;
    @ApiDocClassDefine(
            value = "organCode",
            description = "机构码"
    )
    private String organCode;
    @ApiDocClassDefine(
            value = "organInfo",
            description = "机构信息"
    )
    private OrganInfoVo organInfo;
    @ApiDocClassDefine(
            value = "manageChannel",
            description = "管理渠道 1-新零售直营、 5-新零售授权"
    )
    private Integer manageChannel;
}
