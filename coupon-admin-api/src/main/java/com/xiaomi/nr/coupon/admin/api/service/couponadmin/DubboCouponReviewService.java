package com.xiaomi.nr.coupon.admin.api.service.couponadmin;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponReviewListVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.WorkFlowConfigVo;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.request.*;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.response.CouponCommonConfigResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.response.CouponReviewDetailResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.response.CouponReviewResponse;
import com.xiaomi.youpin.infra.rpc.Result;

import javax.validation.Valid;
import java.util.List;

/**
 * 券操作审批相关接口
 */
public interface DubboCouponReviewService {

    /**
     * 创建券申请
     * @param request
     * @return
     */
    Result<CouponReviewResponse> createCouponReview(CouponCreateReviewRequest request);


    /**
     * 修改券申请
     * @param request
     * @return
     */
    Result<CouponReviewResponse> updateCouponReview(CouponUpdateReviewRequest request);


    /**
     * 取消券申请
     * @param request
     * @return
     */
    Result<Void> cancelCouponReview(CouponReviewRequest request);

    /**
     * 取消券申请
     * @param request
     * @return
     */
    Result<Void> cancelCouponReviewHttp(CouponReviewRequest request);

    /**
     * 券申请列表
     * @param request
     * @return
     */
    Result<BasePageResponse<CouponReviewListVO>> couponReviewList(CouponReviewListRequest request);

    /**
     * 券申请详情
     * @param request
     * @return
     */
    Result<CouponReviewDetailResponse> couponReviewDetail(CouponReviewRequest request);

    /**
     * 不对外使用，只是为了插入一审组
     * @return
     */
    Result<BasePageResponse<ReviewGroupDTO>> reviewGroupList(ReviewGroupListRequest request);

    /**
     * 不对外使用，只是为了插入一审组
     * @return
     */
    Result<Void> reviewGroupSave(@Valid ReviewGroupSaveRequest request);


    /**
     * 审批流列表
     * @return
     */
    Result<CouponCommonConfigResponse> getCommonConfigInfoList();

    /**
     * 审批流列表
     * @return
     */
    @Deprecated
    Result<List<WorkFlowConfigVo>> getWorkFlowConfigVoList();

}
