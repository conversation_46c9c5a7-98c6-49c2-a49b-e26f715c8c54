package com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.request;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageRequest;
import lombok.Data;

import java.io.Serializable;

@Data
public class UserCouponCodeListRequest extends BasePageRequest implements Serializable {
    private static final long serialVersionUID = -2138763301732728205L;

    /**
     * 查询类型
     * 0：优惠码
     * 1：用户id
     */
    private int type;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 优惠码
     */
    private String couponCode;

    /**
     * 优惠码状态
     */
    private int status = 0;

    /**
     * 使用方式
     */
    private int useMode = 0;

    public UserCouponCodeListRequest(){}
}
