package com.xiaomi.nr.coupon.admin.api.service.pointadmin;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.task.*;
import com.xiaomi.youpin.infra.rpc.Result;

import javax.validation.Valid;


/**
 * <AUTHOR>
 * @description 灌积分任务服务
 * @date 2024-08-14 11:04
 */
public interface DubboPointTaskService {

    /**
     * 灌积分任务列表
     *
     * @param request request
     * @return void
     */
    Result<BasePageResponse<PointTaskListVO>> pointFillTaskList(PointFillTaskListRequest request);

    /**
     * 创建灌积分任务
     *
     * @param request request
     * @return response
     */
    Result<CreatePointTaskResponse> pointFillTaskCreate(@Valid CreateFillPointTaskRequest request);

    /**
     * 灌积分任务详情
     *
     * @param request request
     * @return response
     */
    Result<PointFillTaskDetailResponse> taskDetail(PointFillTaskDetailRequest request);

    /**
     * 重试灌积分任务
     *
     * @param request
     * @return
     */
    Result<Void> taskRetry(ReStartPointTaskRequest request);

    /**
     * 校验积分批次信息和库存 上传数据文件(可选)
     *
     * @param request request
     * @return
     */
    Result<CheckPointBatchConfigAndUploadResponse> checkPointBatchConfigAndUpload(CheckPointBatchConfigAndUploadRequest request);

    /**
     * 下载灌积分任务详情
     *
     * @param request 任务id
     * @return 下载链接
     */
    Result<DownloadPointFillDetailResponse> downloadPointFillDetail(DownloadPointFillDetailRequest request);

    /**
     * 下载灌积分任务数据集
     *
     * @param request
     * @return
     */
    Result<DownloadPointFillDataResponse> downloadPointFillData(DownloadPointFillDataRequest request);

}
