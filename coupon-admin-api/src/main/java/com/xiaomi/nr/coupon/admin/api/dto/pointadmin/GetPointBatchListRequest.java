package com.xiaomi.nr.coupon.admin.api.dto.pointadmin;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/14
 */
@Data
public class GetPointBatchListRequest implements Serializable {

    private static final long serialVersionUID = 1723971257997466946L;

    /**
     * 场景列表
     */
    private List<Long> batchIdList;

    /**
     * 场景编码
     */
    private String sceneCode;

    /**
     * 场景编码列表
     */
    private List<String> sceneCodeList;

    /**
     * 包含剩余积分余额
     */
    private Boolean withBalanceCnt;

    /**
     * 只返回有效批次（上线且进行中）
     */
    private Boolean onlyAvailable;

}
