package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

@Data
public class GoodsRuleDetailVO implements Serializable {

    /**
     * 商品范围类型  1 商品券 2 分类券
     */
    private int scopeType;

    /**
     * 自动更新新品 1 是  2 否
     */
    private Integer autoUpdateGoods;

    /**
     * 商品部门
     */
    private List<Integer> goodsDepartments;

    /**
     * 可用商品信息
     */
    private List<SkuInfoVO> skuIncludeList;

    /**
     * 排除商品信息
     */
    private List<SkuInfoVO> skuExcludeList;

    /**
     * 可用套装信息
     */
    private List<PackageInfoVO> packageIncludeList;

    /**
     * 排除套装信息
     */
    private List<PackageInfoVO> packageExcludeList;

    /**
     * 可用ssu
     */
    private List<SsuInfoVO> ssuIncludeList;

    /**
     * 排除套装信息
     */
    private List<SsuInfoVO> ssuExcludeList;

    /**
     * 三级类目Id列表
     */
    private Set<Long> categoryIds;

    /**
     * 最高金额商品
     */
    private SkuInfoVO maxPriceSku;

    /**
     * 最高金额套装
     */
    private PackageInfoVO maxPricePackage;



}
