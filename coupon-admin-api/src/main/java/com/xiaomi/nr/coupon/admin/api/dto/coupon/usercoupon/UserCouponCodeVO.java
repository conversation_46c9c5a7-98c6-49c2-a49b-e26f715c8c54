package com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
public class UserCouponCodeVO implements Serializable {
    private static final long serialVersionUID = 8493213205372869050L;

    /**
     * 优惠码
     */
    private String couponCode;

    /**
     * 状态
     */
    private String statusDesc;

    /**
     * 券配置id
     */
    private long configId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 使用时间
     */
    private Date useTime;

    /**
     * 使用类型
     */
    private Integer useMode;

    /**
     * 用户券id
     */
    private Long couponId;

    /**
     * 订单id
     */
    private Long orderId;

}
