package com.xiaomi.nr.coupon.admin.api.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 所有接口的请求类需要继承此类，系统会根据传入的appid和token进行鉴权
 */
@Data
public class BaseRequest implements Serializable {
    private static final long serialVersionUID = 8443508856746012666L;
    /**
     * appId权限验证
     */
    private String appId;

    /**
     * 根据参数规则生成的token
     */
    private String token;

    /**
     * 业务类型 0 新零售 3整车销售 4汽车售后 6国际零售通
     */
    private Integer bizType;

}
