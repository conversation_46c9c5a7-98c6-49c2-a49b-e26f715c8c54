package com.xiaomi.nr.coupon.admin.api.service;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.UserCouponVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.request.UserCouponCancelRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.request.UserCouponDetailRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.request.UserCouponListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.response.UserCouponDetailResponse;
import com.xiaomi.youpin.infra.rpc.Result;

/**
 * 用户券
 */
public interface DubboUserCouponService {

    /**
     * 查询用户券列表
     * @param request
     * caller 自动化测试
     * @return
     */
    Result<BasePageResponse<UserCouponVO>> userCouponList(UserCouponListRequest request);


    /**
     * 作废用户券
     * @param request
     * @return
     */
    Result<Void> cancelUserCoupon(UserCouponCancelRequest request);

    /**
     * 用户券详情
     * @param request
     * @return
     */
    Result<UserCouponDetailResponse> queryUserCouponDetail(UserCouponDetailRequest request);


}
