package com.xiaomi.nr.coupon.admin.api.enums;

import com.xiaomi.nr.coupon.admin.api.utils.I18nUtil;
import com.xiaomi.nr.global.dev.neptune.T;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.function.Supplier;

/**
 * 1:满元, 2:满件, 3:每满元, 4:每满件, 5:满元且满件
 */
@Getter
@AllArgsConstructor
public enum TranslationEnum {
    COUPON_REGION_NOT_SUPPORT("券功能当前区域未开放",() -> T.tr(TranslationConstant.COUPON_REGION_NOT_SUPPORT)),
    COUPON_TASK_COMMIT_ERROR_NULL("提交失败,优惠券不存在",() -> T.tr(TranslationConstant.COUPON_TASK_COMMIT_ERROR_NULL)),
    COUPON_TASK_COMMIT_ERROR_STATUS("提交失败,优惠券状态不是已上线",() -> T.tr(TranslationConstant.COUPON_TASK_COMMIT_ERROR_STATUS)),
    COUPON_TASK_COMMIT_ERROR_TIME("提交失败,未到优惠券领取时间",() -> T.tr(TranslationConstant.COUPON_TASK_COMMIT_ERROR_TIME)),
    COUPON_TASK_COMMIT_ERROR_END("提交失败,优惠券领取时间已结束",() -> T.tr(TranslationConstant.COUPON_TASK_COMMIT_ERROR_END)),
    COUPON_TASK_COMMIT_ERROR_STOCK("提交失败,优惠券库存为0",() -> T.tr(TranslationConstant.COUPON_TASK_COMMIT_ERROR_STOCK)),
    COUPON_TASK_COMMIT_NULL_AREA("提交失败,未传入地区ID",() -> T.tr(TranslationConstant.COUPON_TASK_COMMIT_NULL_AREA)),
    COUPON_TASK_COMMIT_ERROR_AREA("提交失败,区域编码非法",() -> T.tr(TranslationConstant.COUPON_TASK_COMMIT_ERROR_AREA)),
    COUPON_TASK_COMMIT_ERROR_NUM("提交失败,优惠券库存需大于计划发放数量",() -> T.tr(TranslationConstant.COUPON_TASK_COMMIT_ERROR_NUM)),
    COUPON_TASK_COMMIT_ERROR_USER("上传人群包数据出错了，请联系管理员",() -> T.tr(TranslationConstant.COUPON_TASK_COMMIT_ERROR_USER)),
    COUPON_TASK_COMMIT_ERROR_BPM("bpm审批异常",() -> T.tr(TranslationConstant.COUPON_TASK_COMMIT_ERROR_BPM)),
    COUPON_TASK_COMMIT_ERROR_WORKFLOW("创建审批dto查询审批流失败",() -> T.tr(TranslationConstant.COUPON_TASK_COMMIT_ERROR_WORKFLOW)),
    COUPON_TASK_COMMIT_ERROR_CREATE("创建审批失败",() -> T.tr(TranslationConstant.COUPON_TASK_COMMIT_ERROR_CREATE)),
    COUPON_TASK_COMMIT_ERROR_CANCEL("取消审批失败",() -> T.tr(TranslationConstant.COUPON_TASK_COMMIT_ERROR_CANCEL)),
    COUPON_TASK_COMMIT_ERROR_AUTH_ID("参数错误：审核ID为空",() -> T.tr(TranslationConstant.COUPON_TASK_COMMIT_ERROR_AUTH_ID)),
    COUPON_TASK_COMMIT_CODE_AREA("提交失败,地区ID不合法",() -> T.tr(TranslationConstant.COUPON_TASK_COMMIT_CODE_AREA)),
    COUPON_TASK_COMMIT_ERROR_SAVE("保存优惠券失败",() -> T.tr(TranslationConstant.COUPON_TASK_COMMIT_ERROR_SAVE)),
    COUPON_TASK_COMMIT_ERROR_UPDATE("更新优惠券失败",() -> T.tr(TranslationConstant.COUPON_TASK_COMMIT_ERROR_UPDATE)),
    COUPON_TASK_COMMIT_ERROR_BIZ_TYPE("业务平台非法",() -> T.tr(TranslationConstant.COUPON_TASK_COMMIT_ERROR_BIZ_TYPE)),
    COUPON_TASK_COMMIT_ERROR_ARGS("入参校验失败",() -> T.tr(TranslationConstant.COUPON_TASK_COMMIT_ERROR_ARGS)),
    COUPON_TASK_COMMIT_ERROR_CONVERT("解析灌券任务数据失败",() -> T.tr(TranslationConstant.COUPON_TASK_COMMIT_ERROR_CONVERT)),
    COUPON_TASK_QUERY_ERROR_AREA("查询失败,结果地区ID和全局地区ID不匹配",() -> T.tr(TranslationConstant.COUPON_TASK_QUERY_ERROR_AREA)),
    COUPON_TASK_QUERY_NULL_AREA("查询失败,未传入地区ID",() -> T.tr(TranslationConstant.COUPON_TASK_QUERY_NULL_AREA)),
    COUPON_TASK_QUERY_NULL_CONFIG("券配置ID为NULL",() -> T.tr(TranslationConstant.COUPON_TASK_QUERY_NULL_CONFIG)),
    COUPON_TASK_QUERY_ERROR_ARGS("入参校验失败",() -> T.tr(TranslationConstant.COUPON_TASK_QUERY_ERROR_ARGS)),
    COUPON_TASK_INSERT_ERROR_AUDIT("插入审核记录失败",() -> T.tr(TranslationConstant.COUPON_TASK_INSERT_ERROR_AUDIT)),
    COUPON_TASK_INSERT_NULL_CONFIG("券id必须为空",() -> T.tr(TranslationConstant.COUPON_TASK_INSERT_NULL_CONFIG)),
    COUPON_TASK_INSERT_NOT_NULL_CONFIG("券id不能为空",() -> T.tr(TranslationConstant.COUPON_TASK_INSERT_NOT_NULL_CONFIG)),
    COUPON_CONFIG_INSERT_NULL_NAME("券名称不能为空",() -> T.tr(TranslationConstant.COUPON_CONFIG_INSERT_NULL_NAME)),
    COUPON_CONFIG_INSERT_NULL_EXPLAIN("使用说明不能为空",() -> T.tr(TranslationConstant.COUPON_CONFIG_INSERT_NULL_EXPLAIN)),
    COUPON_CONFIG_INSERT_NULL_SCENE("投放场景不能为空",() -> T.tr(TranslationConstant.COUPON_CONFIG_INSERT_NULL_SCENE)),
    COUPON_CONFIG_INSERT_ERROR_TIME("领取时间不能为空，且开始领取时间不能大于结束领取时间，结束领取时间不能小于当前时间",() -> T.tr(TranslationConstant.COUPON_CONFIG_INSERT_ERROR_TIME)),
    COUPON_CONFIG_INSERT_NULL_USE("使用有效期类型异常",() -> T.tr(TranslationConstant.COUPON_CONFIG_INSERT_NULL_USE)),
    COUPON_CONFIG_INSERT_NULL_TIME("固定时间有效期，开始使用时间和结束使用时间不能为空",() -> T.tr(TranslationConstant.COUPON_CONFIG_INSERT_NULL_TIME)),
    COUPON_CONFIG_INSERT_NULL_TIME_2("相对时间有效期，有效时长不能为空",() -> T.tr(TranslationConstant.COUPON_CONFIG_INSERT_NULL_TIME_2)),
    COUPON_CONFIG_INSERT_ERROR_TYPE("优惠类型不能为空",() -> T.tr(TranslationConstant.COUPON_CONFIG_INSERT_ERROR_TYPE)),
    COUPON_CONFIG_INSERT_ERROR_SCENE("投放场景信息不存在, sceneCode ： ",() -> T.tr(TranslationConstant.COUPON_CONFIG_INSERT_ERROR_SCENE)),
    COUPON_CONFIG_INSERT_ERROR_APPLY_COUNT("发放总量必须大于等于每人限领",() -> T.tr(TranslationConstant.COUPON_CONFIG_INSERT_ERROR_APPLY_COUNT)),
    COUPON_CONFIG_INSERT_ERROR_LIMIT("不限领情况下每人限领数量异常",() -> T.tr(TranslationConstant.COUPON_CONFIG_INSERT_ERROR_LIMIT)),
    COUPON_CONFIG_INSERT_ERROR_AREA("指定地区码不可为空",() -> T.tr(TranslationConstant.COUPON_CONFIG_INSERT_ERROR_AREA)),
    COUPON_CONFIG_INSERT_ERROR_MATCH("渠道和业务场景不匹配",() -> T.tr(TranslationConstant.COUPON_CONFIG_INSERT_ERROR_MATCH)),
    COUPON_CONFIG_UPDATE_NULL_CONFIG("未找到券信息",() -> T.tr(TranslationConstant.COUPON_CONFIG_UPDATE_NULL_CONFIG)),
    COUPON_CONFIG_UPDATE_ERROR_SCENE("投放场景不可修改",() -> T.tr(TranslationConstant.COUPON_CONFIG_UPDATE_ERROR_SCENE)),
    COUPON_CONFIG_UPDATE_ERROR_TIME("使用时间类型不可修改",() -> T.tr(TranslationConstant.COUPON_CONFIG_UPDATE_ERROR_TIME)),
    COUPON_CONFIG_UPDATE_CHANGE_TYPE("优惠类型不可修改",() -> T.tr(TranslationConstant.COUPON_CONFIG_UPDATE_CHANGE_TYPE)),
    COUPON_CONFIG_UPDATE_CHANGE_BOTTOM("门槛类型不可修改",() -> T.tr(TranslationConstant.COUPON_CONFIG_UPDATE_CHANGE_BOTTOM)),
    COUPON_CONFIG_UPDATE_CHANGE_NUM("发放数量只能增加",() -> T.tr(TranslationConstant.COUPON_CONFIG_UPDATE_CHANGE_NUM)),
    COUPON_CONFIG_CREATE_CHECK_AREA("国际化优惠卷国家地区ID不能为空",() -> T.tr(TranslationConstant.COUPON_CONFIG_CREATE_CHECK_AREA)),
    COUPON_CONFIG_CREATE_CHECK_WORKFLOW("国际化优惠卷审批流ID不能为空",() -> T.tr(TranslationConstant.COUPON_CONFIG_CREATE_CHECK_WORKFLOW)),
    COUPON_CONFIG_CREATE_CHECK_AMOUNT("立减金额必须大于0",() -> T.tr(TranslationConstant.COUPON_CONFIG_CREATE_CHECK_AMOUNT)),
    COUPON_CONFIG_CREATE_CHECK_CHANNEL("使用渠道不能为空",() -> T.tr(TranslationConstant.COUPON_CONFIG_CREATE_CHECK_CHANNEL)),
    COUPON_CONFIG_CREATE_CHECK_STORE("指定门店不能为空",() -> T.tr(TranslationConstant.COUPON_CONFIG_CREATE_CHECK_STORE)),
    COUPON_CONFIG_CREATE_CHECK_DISTINCT("优惠值必须大于0",() -> T.tr(TranslationConstant.COUPON_CONFIG_CREATE_CHECK_DISTINCT)),
    COUPON_CONFIG_CREATE_CHECK_PRODUCT("通过选择商品建券，商品信息不能为空",() -> T.tr(TranslationConstant.COUPON_CONFIG_CREATE_CHECK_PRODUCT)),
    COUPON_CONFIG_CHANGE_CHECK_TYPE("业务类型不可修改",() -> T.tr(TranslationConstant.COUPON_CONFIG_CHANGE_CHECK_TYPE)),
    COUPON_CONFIG_UPDATE_CHECK_BPM("更新bpmKey失败",() -> T.tr(TranslationConstant.COUPON_CONFIG_UPDATE_CHECK_BPM)),
    COUPON_CONFIG_CHANGE_CHECK_AUDIT("提交券配置审核失败",() -> T.tr(TranslationConstant.COUPON_CONFIG_CHANGE_CHECK_AUDIT)),
    COUPON_CONFIG_CHANGE_ERROR_AUDIT("存在进行中的审批",() -> T.tr(TranslationConstant.COUPON_CONFIG_CHANGE_ERROR_AUDIT)),
    COUPON_CONFIG_CHANGE_ERROR_SYC("创建审批dto反序列化失败",() -> T.tr(TranslationConstant.COUPON_CONFIG_CHANGE_ERROR_SYC)),
    COUPON_CONFIG_CHANGE_ERROR_WORKFLOW("创建审批dto查询审批流失败",() -> T.tr(TranslationConstant.COUPON_CONFIG_CHANGE_ERROR_WORKFLOW)),
    COUPON_LOGIN_QUERY_ERROR_USER("未找到登录用户信息",() -> T.tr(TranslationConstant.COUPON_LOGIN_QUERY_ERROR_USER)),
    COUPON_CONFIG_QUERY_ERROR_WORKFLOW("未找到审批记录",() -> T.tr(TranslationConstant.COUPON_CONFIG_QUERY_ERROR_WORKFLOW)),
    COUPON_CONFIG_QUERY_ERROR_SKU("查询sku失败",() -> T.tr(TranslationConstant.COUPON_CONFIG_QUERY_ERROR_SKU)),
    COUPON_CONFIG_QUERY_ERROR_CATEGORY("查询类目失败",() -> T.tr(TranslationConstant.COUPON_CONFIG_QUERY_ERROR_CATEGORY)),
    COUPON_CONFIG_QUERY_ERROR_PRODUCT("请求商品失败",() -> T.tr(TranslationConstant.COUPON_CONFIG_QUERY_ERROR_PRODUCT)),
    COUPON_CONFIG_QUERY_ERROR_CODE("商品编号为空",() -> T.tr(TranslationConstant.COUPON_CONFIG_QUERY_ERROR_CODE)),
    COUPON_UTIL_QUERY_ERROR_AREA("获取地区ID失败",() -> T.tr(TranslationConstant.COUPON_UTIL_QUERY_ERROR_AREA)),
    COUPON_CONFIG_QUERY_ERROR_STORE("查询门店失败",() -> T.tr(TranslationConstant.COUPON_CONFIG_QUERY_ERROR_STORE)),
    COUPON_CONFIG_QUERY_ERROR_STORE_2("请求门店服务，获取门店数据失败",() -> T.tr(TranslationConstant.COUPON_CONFIG_QUERY_ERROR_STORE_2)),
    COUPON_CONFIG_QUERY_ERROR_STORE_3("请求门店服务，获取门店数据失败，结果为空.",() -> T.tr(TranslationConstant.COUPON_CONFIG_QUERY_ERROR_STORE_3)),
    COUPON_CONFIG_QUERY_ERROR_STORE_4("请求门店服务，获取门店数据失败，code:",() -> T.tr(TranslationConstant.COUPON_CONFIG_QUERY_ERROR_STORE_4)),
    COUPON_CONFIG_UPDATE_CHANGE_STATUS("状态变更操作非法",() -> T.tr(TranslationConstant.COUPON_CONFIG_UPDATE_CHANGE_STATUS)),
    COUPON_CONFIG_UPDATE_OPERATION_NULL("操作人不能为空",() -> T.tr(TranslationConstant.COUPON_CONFIG_UPDATE_OPERATION_NULL)),
    COUPON_CONFIG_UPDATE_OPERATION_END("已终止的优惠券不能进行上下线操作",() -> T.tr(TranslationConstant.COUPON_CONFIG_UPDATE_OPERATION_END)),
    COUPON_CONFIG_UPDATE_ERROR_ID("修改券状态失败, configId:",() -> T.tr(TranslationConstant.COUPON_CONFIG_UPDATE_ERROR_ID)),
    COUPON_UTIL_LOGIN_USER_ID("未找到登录人信息：",() -> T.tr(TranslationConstant.COUPON_UTIL_LOGIN_USER_ID)),
    COUPON_CONFIG_QUERY_ERROR_ID("ID不合法",() -> T.tr(TranslationConstant.COUPON_CONFIG_QUERY_ERROR_ID)),
    COUPON_ENUM_QUERY_ERROR_DIRECT("直营店",() -> T.tr(TranslationConstant.COUPON_ENUM_QUERY_ERROR_DIRECT)),
    COUPON_ENUM_QUERY_ERROR_AUTH("授权店",() -> T.tr(TranslationConstant.COUPON_ENUM_QUERY_ERROR_AUTH)),
//    COUPON_ENUM_QUERY_ERROR_ONLINE("已上架",() -> T.tr(TranslationConstant.COUPON_ENUM_QUERY_ERROR_ONLINE)),
//    COUPON_ENUM_QUERY_ERROR_OFFLINE("已下架",() -> T.tr(TranslationConstant.COUPON_ENUM_QUERY_ERROR_OFFLINE)),
//    COUPON_WORKFLOW_ENUM_HK("香港-直营店-优惠券",() -> T.tr(TranslationConstant.COUPON_WORKFLOW_ENUM_HK)),
    COUPON_TASK_CREATE_VALIDATE_NAME("任务名称不能为空",() -> T.tr(TranslationConstant.COUPON_TASK_CREATE_VALIDATE_NAME)),
    COUPON_TASK_CREATE_VALIDATE_ID("优惠券ID不能为空",() -> T.tr(TranslationConstant.COUPON_TASK_CREATE_VALIDATE_ID)),
    COUPON_TASK_CREATE_VALIDATE_NUM("优惠券申请发放数量不合法",() -> T.tr(TranslationConstant.COUPON_TASK_CREATE_VALIDATE_NUM)),
    COUPON_TASK_CREATE_VALIDATE_TXT("TXT文件内容为空",() -> T.tr(TranslationConstant.COUPON_TASK_CREATE_VALIDATE_TXT)),
    COUPON_TASK_CREATE_VALIDATE_UID("用户UID列表不能为空",() -> T.tr(TranslationConstant.COUPON_TASK_CREATE_VALIDATE_UID)),
    COUPON_TASK_CREATE_VALIDATE_USER("请正确输入人群包ID",() -> T.tr(TranslationConstant.COUPON_TASK_CREATE_VALIDATE_USER)),
    COUPON_DISCOUNT_TEMP_UNIT("满{0}件减{1}优惠券",() -> T.tr(TranslationConstant.COUPON_DISCOUNT_TEMP_UNIT)),
    COUPON_DISCOUNT_TEMP_PRICE("满{0}减{1}优惠券",() -> T.tr(TranslationConstant.COUPON_DISCOUNT_TEMP_PRICE)),
    COUPON_INFO_ERROR_ARG("参数错误",() -> T.tr(TranslationConstant.COUPON_INFO_ERROR_ARG)),
    COUPON_INFO_ERROR_EXIST("优惠券不存在",() -> T.tr(TranslationConstant.COUPON_INFO_ERROR_EXIST)),
    COUPON_TASK_ERROR_DOWN("下载地址为空",() -> T.tr(TranslationConstant.COUPON_TASK_ERROR_DOWN)),
    COUPON_TASK_ERROR_EXIST("用户集已被清理或不存在",() -> T.tr(TranslationConstant.COUPON_TASK_ERROR_EXIST)),
    COUPON_TASK_ERROR_BIG("灌券用户集大于1W, 请联系RD同学下载",() -> T.tr(TranslationConstant.COUPON_TASK_ERROR_BIG)),
    COUPON_TASK_ERROR_EXIST_ID("灌券用户集不存在,taskId:",() -> T.tr(TranslationConstant.COUPON_TASK_ERROR_EXIST_ID)),
    COUPON_TASK_ERROR_REDIS_COUNT("获取已发放总量失败",() -> T.tr(TranslationConstant.COUPON_TASK_ERROR_REDIS_COUNT)),
    COUPON_TASK_DETAIL_ERROR_NULL("灌券任务为空",() -> T.tr(TranslationConstant.COUPON_TASK_DETAIL_ERROR_NULL)),
    COUPON_TASK_DETAIL_ERROR_EXIST("灌券任务不存在",() -> T.tr(TranslationConstant.COUPON_TASK_DETAIL_ERROR_EXIST)),
//    COUPON_TASK_COUPON_TYPE_GOODS("商品券",() -> T.tr(TranslationConstant.COUPON_TASK_COUPON_TYPE_GOODS)),
//    COUPON_TASK_COUPON_TYPE_POSTFREE("运费券",() -> T.tr(TranslationConstant.COUPON_TASK_COUPON_TYPE_POSTFREE)),
//    COUPON_TASK_COUPON_TYPE_SUBSIDY("超级补贴券",() -> T.tr(TranslationConstant.COUPON_TASK_COUPON_TYPE_SUBSIDY)),
//    COUPON_TASK_COUPON_TYPE_DEDUCTION("抵扣券",() -> T.tr(TranslationConstant.COUPON_TASK_COUPON_TYPE_DEDUCTION)),
//    COUPON_TASK_COUPON_TYPE_CARD("不限次服务卡",() -> T.tr(TranslationConstant.COUPON_TASK_COUPON_TYPE_CARD)),
//    COUPON_WORKFLOW_ENUM_TW("台湾-直营店-优惠券",() -> T.tr(TranslationConstant.COUPON_WORKFLOW_ENUM_TW)),
    COUPON_SCENE_QUERY_TYPE_ERROR("优惠券类型不能为空",() -> T.tr(TranslationConstant.COUPON_SCENE_QUERY_TYPE_ERROR)),
    COUPON_QUERY_CONFIG_ID_COUNT_ERROR("优惠券配置id数量不能超过50",() -> T.tr(TranslationConstant.COUPON_QUERY_CONFIG_ID_COUNT_ERROR)),
    COUPON_PARAM_AREA_ID_ERROR("无法获取有效的区域ID",() -> T.tr(TranslationConstant.COUPON_PARAM_AREA_ID_ERROR)),
    COUPON_SCENE_SAVE_SAME_ERROR("存在同名场景！",() -> T.tr(TranslationConstant.COUPON_SCENE_SAVE_SAME_ERROR)),
    COUPON_SCENE_SAVE_RELATION_ERROR("关联一级场景非法",() -> T.tr(TranslationConstant.COUPON_SCENE_SAVE_RELATION_ERROR)),
    COUPON_SCENE_SAVE_RELATION_DIFF("关联一级场景与业务平台不符",() -> T.tr(TranslationConstant.COUPON_SCENE_SAVE_RELATION_DIFF)),
    COUPON_SCENE_SAVE_ID_NULL("场景id不能为空",() -> T.tr(TranslationConstant.COUPON_SCENE_SAVE_ID_NULL)),
    COUPON_SCENE_SAVE_ID_ERROR("无效场景id",() -> T.tr(TranslationConstant.COUPON_SCENE_SAVE_ID_ERROR)),
    COUPON_SCENE_UPDATE_TYPE_ERROR("场景编码方式不允许被修改",() -> T.tr(TranslationConstant.COUPON_SCENE_UPDATE_TYPE_ERROR)),
    COUPON_SCENE_UPDATE_SCENE_ERROR("场景编码不允许被修改",() -> T.tr(TranslationConstant.COUPON_SCENE_UPDATE_SCENE_ERROR)),
    COUPON_SCENE_UPDATE_WAY_ERROR("投放方式不允许被修改",() -> T.tr(TranslationConstant.COUPON_SCENE_UPDATE_WAY_ERROR)),
    COUPON_SCENE_OPERATE_STATUS_ERROR("无效id",() -> T.tr(TranslationConstant.COUPON_SCENE_OPERATE_STATUS_ERROR)),
    COUPON_SCENE_CREATE_PERMISSION_SAME("该appId已在该场景",() -> T.tr(TranslationConstant.COUPON_SCENE_CREATE_PERMISSION_SAME)),
    COUPON_SCENE_OPERATE_PERMISSION_ERROR("记录不存在",() -> T.tr(TranslationConstant.COUPON_SCENE_OPERATE_PERMISSION_ERROR)),
    COUPON_CONFIG_INSERT_STATUS_SCENE("投放场景未生效",() -> T.tr(TranslationConstant.COUPON_CONFIG_INSERT_STATUS_SCENE)),
    COUPON_SCENE_INSERT_WAY_NULL("投放方式不能为空",() -> T.tr(TranslationConstant.COUPON_SCENE_INSERT_WAY_NULL)),
    COUPON_SCENE_INSERT_ASSIGN_NULL("投放方式为优惠券时发放方式不能为空", () -> T.tr(TranslationConstant.COUPON_SCENE_INSERT_ASSIGN_NULL)),
    COUPON_CONFIG_ACCESS_DENIED("无权访问优惠券配置",() -> T.tr(TranslationConstant.COUPON_CONFIG_ACCESS_DENIED)),
    COUPON_WORKFLOW_QUERY_ANALYSIS("优惠券审批流解析异常",() -> T.tr(TranslationConstant.COUPON_WORKFLOW_QUERY_ANALYSIS)),
    COUPON_AUTH_ERROR_MERCHANT_NULL("商门店列表为空",() -> T.tr(TranslationConstant.COUPON_AUTH_ERROR_MERCHANT_NULL)),
    COUPON_AUTH_ERROR_LIST("优惠券列表查询失败",() -> T.tr(TranslationConstant.COUPON_AUTH_ERROR_LIST)),
    COUPON_AUTH_ERROR_DETAIL("优惠券详情查询失败",() -> T.tr(TranslationConstant.COUPON_AUTH_ERROR_DETAIL)),
    COUPON_AUTH_ERROR_STATION_CHECK("岗位校验未通过",() -> T.tr(TranslationConstant.COUPON_AUTH_ERROR_STATION_CHECK)),
    COUPON_AUTH_USER_SERVICE_MESSAGE_ERROR("请求用户服务，返回的数据异常",() -> T.tr(TranslationConstant.COUPON_AUTH_USER_SERVICE_MESSAGE_ERROR)),
    COUPON_AUTH_USER_SERVICE_FAIL("请求用户服务，获取用户数据失败",() -> T.tr(TranslationConstant.COUPON_AUTH_USER_SERVICE_FAIL)),
    COUPON_ID_IS_NULL("券id必须为空",() -> T.tr(TranslationConstant.COUPON_ID_IS_NULL)),
    COUPON_OVERYUAN_IS_NULL("门槛值满元不能为空",() -> T.tr(TranslationConstant.COUPON_OVERYUAN_IS_NULL)),
    COUPON_OVERCOUNT_IS_NULL("门槛值满件不能为空",() -> T.tr(TranslationConstant.COUPON_OVERCOUNT_IS_NULL)),
    COUPON_UNSUPPORTED_BOTTOMTYPE("不支持的门槛类型",() -> T.tr(TranslationConstant.COUPON_UNSUPPORTED_BOTTOMTYPE)),
    COUPON_MAXREDUCE_MUST_BE_GREATER_THAN_ZERO("最大减免金额必须大于0",() -> T.tr(TranslationConstant.COUPON_MAXREDUCE_MUST_BE_GREATER_THAN_ZERO)),
    COUPON_CONFIG_CREATE_CHECK_DISCOUNT("折扣值必须大于0",() -> T.tr(TranslationConstant.COUPON_CONFIG_CREATE_CHECK_DISCOUNT)),
    COUPON_DISCOUNT_RULE_DESC("{discount/100}折",() -> T.tr(TranslationConstant.COUPON_DISCOUNT_RULE_DESC)),
    COUPON_DISCOUNT_RULE_DESC_LANGUAGE_NULL("折扣券翻译文案映射语言缺失",() -> T.tr(TranslationConstant.COUPON_DISCOUNT_RULE_DESC_LANGUAGE_NULL)),
    ;

    private final String desc;
    private final Supplier<String> translateSupplier;

    public String getTranslateContent() {
        return I18nUtil.isI18n() ? this.translateSupplier.get() : getDesc();
    }


    static class TranslationConstant {
        public static final String COUPON_REGION_NOT_SUPPORT = "coupon.region.not.support";
        public static final String COUPON_TASK_COMMIT_ERROR_NULL = "coupon.task.commit.error.null";
        public static final String COUPON_TASK_COMMIT_ERROR_STATUS = "coupon.task.commit.error.status";
        public static final String COUPON_TASK_COMMIT_ERROR_TIME = "coupon.task.commit.error.time";
        public static final String COUPON_TASK_COMMIT_ERROR_END = "coupon.task.commit.error.end";
        public static final String COUPON_TASK_COMMIT_ERROR_STOCK = "coupon.task.commit.error.stock";
        public static final String COUPON_TASK_COMMIT_NULL_AREA = "coupon.task.commit.null.area";
        public static final String COUPON_TASK_COMMIT_ERROR_AREA = "coupon.task.commit.error.area";
        public static final String COUPON_TASK_COMMIT_ERROR_NUM = "coupon.task.commit.error.num";
        public static final String COUPON_TASK_COMMIT_ERROR_USER = "coupon.task.commit.error.user";
        public static final String COUPON_TASK_COMMIT_ERROR_BPM = "coupon.task.commit.error.bpm";
        public static final String COUPON_TASK_COMMIT_ERROR_WORKFLOW = "coupon.task.commit.error.workflow";
        public static final String COUPON_TASK_COMMIT_ERROR_CREATE = "coupon.task.commit.error.create";
        public static final String COUPON_TASK_COMMIT_ERROR_CANCEL = "coupon.task.commit.error.cancel";
        public static final String COUPON_TASK_COMMIT_ERROR_AUTH_ID = "coupon.task.commit.error.authId";
        public static final String COUPON_TASK_COMMIT_CODE_AREA = "coupon.task.commit.code.area";
        public static final String COUPON_TASK_COMMIT_ERROR_SAVE = "coupon.task.commit.error.save";
        public static final String COUPON_TASK_COMMIT_ERROR_UPDATE = "coupon.task.commit.error.update";
        public static final String COUPON_TASK_COMMIT_ERROR_BIZ_TYPE = "coupon.task.commit.error.bizType";
        public static final String COUPON_TASK_COMMIT_ERROR_ARGS = "coupon.task.commit.error.args";
        public static final String COUPON_TASK_COMMIT_ERROR_CONVERT = "coupon.task.commit.error.convert";
        public static final String COUPON_TASK_QUERY_ERROR_AREA = "coupon.task.query.error.area";
        public static final String COUPON_TASK_QUERY_NULL_AREA = "coupon.task.query.null.area";
        public static final String COUPON_TASK_QUERY_NULL_CONFIG = "coupon.task.query.null.config";
        public static final String COUPON_TASK_QUERY_ERROR_ARGS = "coupon.task.query.error.args";
        public static final String COUPON_TASK_INSERT_ERROR_AUDIT = "coupon.task.insert.error.audit";
        public static final String COUPON_TASK_INSERT_NULL_CONFIG = "coupon.task.insert.null.config";
        public static final String COUPON_TASK_INSERT_NOT_NULL_CONFIG = "coupon.task.insert.notNull.config";
        public static final String COUPON_CONFIG_INSERT_NULL_NAME = "coupon.config.insert.null.name";
        public static final String COUPON_CONFIG_INSERT_NULL_EXPLAIN = "coupon.config.insert.null.explain";
        public static final String COUPON_CONFIG_INSERT_NULL_SCENE = "coupon.config.insert.null.scene";
        public static final String COUPON_CONFIG_INSERT_ERROR_TIME = "coupon.config.insert.error.time";
        public static final String COUPON_CONFIG_INSERT_NULL_USE = "coupon.config.insert.null.use";
        public static final String COUPON_CONFIG_INSERT_NULL_TIME = "coupon.config.insert.null.time";
        public static final String COUPON_CONFIG_INSERT_NULL_TIME_2 = "coupon.config.insert.null.time2";
        public static final String COUPON_CONFIG_INSERT_ERROR_TYPE = "coupon.config.insert.error.type";
        public static final String COUPON_CONFIG_INSERT_ERROR_SCENE = "coupon.config.insert.error.scene";
        public static final String COUPON_CONFIG_INSERT_ERROR_APPLY_COUNT = "coupon.config.insert.error.applyCount";
        public static final String COUPON_CONFIG_INSERT_ERROR_LIMIT = "coupon.config.insert.error.limit";
        public static final String COUPON_CONFIG_INSERT_ERROR_AREA = "coupon.config.insert.error.area";
        public static final String COUPON_CONFIG_INSERT_ERROR_MATCH = "coupon.config.insert.error.match";
        public static final String COUPON_CONFIG_UPDATE_NULL_CONFIG = "coupon.config.update.null.config";
        public static final String COUPON_CONFIG_UPDATE_ERROR_SCENE = "coupon.config.update.error.scene";
        public static final String COUPON_CONFIG_UPDATE_ERROR_TIME = "coupon.config.update.error.time";
        public static final String COUPON_CONFIG_UPDATE_CHANGE_TYPE = "coupon.config.update.change.type";
        public static final String COUPON_CONFIG_UPDATE_CHANGE_BOTTOM = "coupon.config.update.change.bottom";
        public static final String COUPON_CONFIG_UPDATE_CHANGE_NUM = "coupon.config.update.change.num";
        public static final String COUPON_CONFIG_CREATE_CHECK_AREA = "coupon.config.create.check.area";
        public static final String COUPON_CONFIG_CREATE_CHECK_WORKFLOW = "coupon.config.create.check.workflow";
        public static final String COUPON_CONFIG_CREATE_CHECK_AMOUNT = "coupon.config.create.check.amount";
        public static final String COUPON_CONFIG_CREATE_CHECK_CHANNEL = "coupon.config.create.check.channel";
        public static final String COUPON_CONFIG_CREATE_CHECK_STORE = "coupon.config.create.check.store";
        public static final String COUPON_CONFIG_CREATE_CHECK_DISTINCT = "coupon.config.create.check.distinct";
        public static final String COUPON_CONFIG_CREATE_CHECK_PRODUCT = "coupon.config.create.check.product";
        public static final String COUPON_CONFIG_CHANGE_CHECK_TYPE = "coupon.config.change.check.type";
        public static final String COUPON_CONFIG_UPDATE_CHECK_BPM = "coupon.config.update.check.bpm";
        public static final String COUPON_CONFIG_CHANGE_CHECK_AUDIT = "coupon.config.change.check.audit";
        public static final String COUPON_CONFIG_CHANGE_ERROR_AUDIT = "coupon.config.change.error.audit";
        public static final String COUPON_CONFIG_CHANGE_ERROR_SYC = "coupon.config.change.error.syc";
        public static final String COUPON_CONFIG_CHANGE_ERROR_WORKFLOW = "coupon.config.change.error.workflow";
        public static final String COUPON_LOGIN_QUERY_ERROR_USER = "coupon.login.query.error.user";
        public static final String COUPON_CONFIG_QUERY_ERROR_WORKFLOW = "coupon.config.query.error.workflow";
        public static final String COUPON_CONFIG_QUERY_ERROR_SKU = "coupon.config.query.error.sku";
        public static final String COUPON_CONFIG_QUERY_ERROR_CATEGORY = "coupon.config.query.error.category";
        public static final String COUPON_CONFIG_QUERY_ERROR_PRODUCT = "coupon.config.query.error.product";
        public static final String COUPON_CONFIG_QUERY_ERROR_CODE = "coupon.config.query.error.code";
        public static final String COUPON_UTIL_QUERY_ERROR_AREA = "coupon.util.query.error.area";
        public static final String COUPON_CONFIG_QUERY_ERROR_STORE = "coupon.config.query.error.store";
        public static final String COUPON_CONFIG_QUERY_ERROR_STORE_2 = "coupon.config.query.error.store2";
        public static final String COUPON_CONFIG_QUERY_ERROR_STORE_3 = "coupon.config.query.error.store3";
        public static final String COUPON_CONFIG_QUERY_ERROR_STORE_4 = "coupon.config.query.error.store4";
        public static final String COUPON_CONFIG_UPDATE_CHANGE_STATUS = "coupon.config.update.change.status";
        public static final String COUPON_CONFIG_UPDATE_OPERATION_NULL = "coupon.config.update.operation.null";
        public static final String COUPON_CONFIG_UPDATE_OPERATION_END = "coupon.config.update.operation.end";
        public static final String COUPON_CONFIG_UPDATE_ERROR_ID = "coupon.config.update.error.id";
        public static final String COUPON_UTIL_LOGIN_USER_ID = "coupon.util.login.user.Id";
        public static final String COUPON_CONFIG_QUERY_ERROR_ID = "coupon.config.query.error.id";
        public static final String COUPON_ENUM_QUERY_ERROR_DIRECT = "coupon.enum.query.error.direct";
        public static final String COUPON_ENUM_QUERY_ERROR_AUTH = "coupon.enum.query.error.auth";
//        public static final String COUPON_ENUM_QUERY_ERROR_ONLINE = "coupon.enum.query.error.online";
//        public static final String COUPON_ENUM_QUERY_ERROR_OFFLINE = "coupon.enum.query.error.offline";
//        public static final String COUPON_WORKFLOW_ENUM_HK = "coupon.workflow.enum.hk";

        public static final String COUPON_TASK_CREATE_VALIDATE_NAME = "coupon.task.create.validate.name";
        public static final String COUPON_TASK_CREATE_VALIDATE_ID = "coupon.task.create.validate.id";
        public static final String COUPON_TASK_CREATE_VALIDATE_NUM = "coupon.task.create.validate.num";
        public static final String COUPON_TASK_CREATE_VALIDATE_TXT = "coupon.task.create.validate.txt";
        public static final String COUPON_TASK_CREATE_VALIDATE_UID = "coupon.task.create.validate.uid";
        public static final String COUPON_TASK_CREATE_VALIDATE_USER = "coupon.task.create.validate.user";
        public static final String COUPON_DISCOUNT_TEMP_UNIT = "coupon.discount.temp.unit";
        public static final String COUPON_DISCOUNT_TEMP_PRICE = "coupon.discount.temp.price";
        public static final String COUPON_INFO_ERROR_ARG = "coupon.info.error.arg";
        public static final String COUPON_INFO_ERROR_EXIST = "coupon.info.error.exist";
        public static final String COUPON_TASK_ERROR_DOWN = "coupon.task.error.down";
        public static final String COUPON_TASK_ERROR_EXIST = "coupon.task.error.exist";
        public static final String COUPON_TASK_ERROR_BIG = "coupon.task.error.big";
        public static final String COUPON_TASK_ERROR_EXIST_ID = "coupon.task.error.exist.id";
        public static final String COUPON_TASK_ERROR_REDIS_COUNT = "coupon.task.error.redis.count";
        public static final String COUPON_TASK_DETAIL_ERROR_NULL = "coupon.task.detail.error.null";
        public static final String COUPON_TASK_DETAIL_ERROR_EXIST = "coupon.task.detail.error.exist";
//        public static final String COUPON_TASK_COUPON_TYPE_GOODS = "coupon.task.coupon.type.goods";
//        public static final String COUPON_TASK_COUPON_TYPE_POSTFREE = "coupon.task.coupon.type.postfree";
//        public static final String COUPON_TASK_COUPON_TYPE_SUBSIDY = "coupon.task.coupon.type.subsidy";
//        public static final String COUPON_TASK_COUPON_TYPE_DEDUCTION = "coupon.task.coupon.type.dedection";
//        public static final String COUPON_TASK_COUPON_TYPE_CARD = "coupon.task.coupon.type.card";
//        public static final String COUPON_WORKFLOW_ENUM_TW = "coupon.workflow.enum.tw";
        public static final String COUPON_SCENE_QUERY_TYPE_ERROR = "coupon.scene.query.type.error";
        public static final String COUPON_QUERY_CONFIG_ID_COUNT_ERROR = "coupon.query.config.id.count.error";
        public static final String COUPON_PARAM_AREA_ID_ERROR = "coupon.param.area.id.error";
        public static final String COUPON_SCENE_SAVE_SAME_ERROR = "coupon.scene.save.same.error";
        public static final String COUPON_SCENE_SAVE_RELATION_ERROR = "coupon.scene.save.relation.error";
        public static final String COUPON_SCENE_SAVE_RELATION_DIFF = "coupon.scene.save.relation.diff";
        public static final String COUPON_SCENE_SAVE_ID_NULL = "coupon.scene.save.id.null";
        public static final String COUPON_SCENE_SAVE_ID_ERROR = "coupon.scene.save.id.error";
        public static final String COUPON_SCENE_UPDATE_TYPE_ERROR = "coupon.scene.update.type.error";
        public static final String COUPON_SCENE_UPDATE_SCENE_ERROR = "coupon.scene.update.scene.error";
        public static final String COUPON_SCENE_UPDATE_WAY_ERROR = "coupon.scene.update.way.error";
        public static final String COUPON_SCENE_OPERATE_STATUS_ERROR = "coupon.scene.operate.status.error";
        public static final String COUPON_SCENE_CREATE_PERMISSION_SAME = "coupon.scene.create.permission.same";
        public static final String COUPON_SCENE_OPERATE_PERMISSION_ERROR = "coupon.scene.operate.permission.error";

        public static final String COUPON_CONFIG_INSERT_STATUS_SCENE = "coupon.config.insert.status.scene";
        public static final String COUPON_SCENE_INSERT_WAY_NULL = "coupon.scene.insert.way.null";
        public static final String COUPON_SCENE_INSERT_ASSIGN_NULL = "coupon.scene.insert.assign.null";
        public static final String COUPON_CONFIG_ACCESS_DENIED = "coupon.config.access.denied";
        public static final String COUPON_WORKFLOW_QUERY_ANALYSIS = "coupon.workflow.query.analysis";
        public static final String COUPON_AUTH_ERROR_MERCHANT_NULL = "coupon.auth.error.merchant.null";
        public static final String COUPON_AUTH_ERROR_LIST = "coupon.auth.error.list";
        public static final String COUPON_AUTH_ERROR_DETAIL = "coupon.auth.error.detail";
        public static final String COUPON_AUTH_ERROR_STATION_CHECK = "coupon.auth.error.station.check";
        public static final String COUPON_AUTH_USER_SERVICE_MESSAGE_ERROR = "auth.user.service.message.error";
        public static final String COUPON_AUTH_USER_SERVICE_FAIL = "auth.user.service.fail";
        public static final String COUPON_ID_IS_NULL = "coupon.id.is.null";
        public static final String COUPON_OVERYUAN_IS_NULL = "coupon.overYuan.is.null";
        public static final String COUPON_OVERCOUNT_IS_NULL = "coupon.overCount.is.null";
        public static final String COUPON_UNSUPPORTED_BOTTOMTYPE = "coupon.unsupported.bottomtype";
        public static final String COUPON_MAXREDUCE_MUST_BE_GREATER_THAN_ZERO = "coupon.maxReduce.must.be.greater.than.zero";
        public static final String COUPON_CONFIG_CREATE_CHECK_DISCOUNT = "coupon.config.create.check.discount";
        public static final String COUPON_DISCOUNT_RULE_DESC = "discount.coupon.rule.desc";
        public static final String COUPON_DISCOUNT_RULE_DESC_LANGUAGE_NULL = "coupon.discount.rule.desc.language.null";
    }
}
