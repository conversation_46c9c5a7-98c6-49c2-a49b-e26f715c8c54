package com.xiaomi.nr.coupon.admin.api.dto.pointadmin.task;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description 灌积分任务列表
 * <AUTHOR>
 * @date 2024-08-14 10:42
 * @version 1.0
*/
@Data
public class PointTaskListVO implements Serializable {

    private static final long serialVersionUID = -1647779760615625136L;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 积分批次id
     */
    private Long pointBatchId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 积分批次名称
     */
    private String pointBatchName;

    /**
     * 任务开始时间
     */
    private Date startTime;

    /**
     * 任务结束时间
     */
    private Date endTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 任务进度
     */
    private Long processRate;

    /**
     * 任务状态
     */
    private Integer status;
    
    /**
     * 执行数量
     */
    private Long totalCount;

    /**
     * 完成数量
     */
    private Long successCount;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 数据集地址
     */
    private String address;
}
