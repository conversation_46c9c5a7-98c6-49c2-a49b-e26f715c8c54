package com.xiaomi.nr.coupon.admin.api.dto.pointadmin;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户积分记录
 *
 * <AUTHOR>
 * @date 2023/12/6
 */
@Data
public class UsersPointDto implements Serializable {

    private static final long serialVersionUID = 7463489022390359239L;

    /**
     * 积分记录ID
     */
    private Long pointId;

    /**
     * 批次ID
     */
    private Long batchId;

    /**
     * 批次名称
     */
    private String batchName;

    /**
     * 场景编码
     */
    private String sceneCode;

    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 发放时间
     */
    private Date sendTime;

    /**
     * 激活时间
     */
    private Date activateTime;

    /**
     * 作废时间
     */
    private Date invalidTime;

    /**
     * 总额
     */
    private Long sendCount;

    /**
     * 余额
     */
    private Long balanceCount;

    /**
     * 状态
     */
    private Integer stat;

    /**
     * 状态名称
     */
    private String statName;

    /**
     * mid
     */
    private Long mid;

    /**
     * 操作人
     */
    private String operator;

}
