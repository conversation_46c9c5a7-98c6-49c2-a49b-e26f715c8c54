package com.xiaomi.nr.coupon.admin.api.dto.coupon.user;

import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;

@Data
public class UserPrivateInfoVo implements Serializable {

    private static final long serialVersionUID = 9120948737937798224L;
    @ApiDocClassDefine(
            value = "emplId",
            required = true,
            description = "员工号"
    )
    private String emplId;
    @ApiDocClassDefine(
            value = "email",
            required = true,
            description = "邮箱"
    )
    private String email;
}
