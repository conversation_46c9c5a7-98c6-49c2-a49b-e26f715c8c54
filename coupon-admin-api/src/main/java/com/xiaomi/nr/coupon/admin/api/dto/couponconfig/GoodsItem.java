package com.xiaomi.nr.coupon.admin.api.dto.couponconfig;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: 商品项
 * @author: he<PERSON><PERSON><PERSON>
 * @Date 2023/4/6 3:01 下午
 * @Version: 1.0
 **/
@Data
public class GoodsItem implements Serializable {

    private static final long serialVersionUID = 8230086363805120452L;

    /**
     * 货品ID或套装ID
     */
    private long id;

    /**
     * 商品品级 goods代表货品ID，package代表是套装，目前只能是这两种
     */
    private String level;
}
