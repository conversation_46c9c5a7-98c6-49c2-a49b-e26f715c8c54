package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class SsuInfoVO implements Serializable {

    private static final long serialVersionUID = -3025215957092233278L;

    /**
     * ssu
     */
    private Long ssuId;

    /**
     * ssn名称
     */
    private String ssuName;

    /**
     * 上架状态
     */
    private Integer saleState;

    /**
     * 建议零售价/平台价
     */
    private Long marketPrice;

    /**
     * 商品子类型10：整车，11：必选配置，12：选装配置，13：配件，14：工时
     */
    private Integer ssuType;

    /**
     *预计销量
     */
    private Long expectedSaleAmount;

    /**
     *单台投入
     */
    private BigDecimal rebatesPricePerUnit;

    @ApiDocClassDefine(
            value = "validStatus",
            description = "validStatus,见枚举ProductValidityEnum"
    )
    private Integer validStatus;

    @ApiDocClassDefine(
            value = "itemId",
            description = "itemId"
    )
    private Long itemId;
    @ApiDocClassDefine(
            value = "sku",
            description = "sku"
    )
    private Long sku;

    @ApiDocClassDefine(
            value = "directSsuStatusText",
            description = "直营/授权渠道ssu状态"
    )
    private String ssuStatusText;

    @ApiDocClassDefine(
            value = "SsuStatus",
            description = "商品聚合状态"
    )
    private Integer ssuStatus;

    @ApiDocClassDefine(
            value = "businessCategoryNames",
            description = "三级业务类目名称"
    )
    private String businessCategoryNames;
    @ApiDocClassDefine(
            value = "businessCategoryIds",
            description = "三级业务类目ids"
    )
    private String businessCategoryIds;

    @ApiDocClassDefine(
            value = "channel",
            description = "渠道"
    )
    private Integer channel;

    @ApiDocClassDefine(
            value = "image",
            description = "主图"
    )
    private String image;


    /**
     * 建议零售价/平台价
     */
    private String marketPriceShow;

    /**
     * 活动券后价
     */
    private String promotionPriceShow;


}
