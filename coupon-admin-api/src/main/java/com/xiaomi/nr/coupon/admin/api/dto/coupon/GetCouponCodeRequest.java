package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/27 11:08
 */
@Data
public class GetCouponCodeRequest implements Serializable {
    private static final long serialVersionUID = 2808903960171390182L;

    /**
     * coupon_index列表
     */
    @NotNull(message = "couponIndexList不能为空")
    @Size(max = 50, message = "优惠券索引数量不能超过50")
    @ApiDocClassDefine(value = "couponIndexList", description = "coupon_index列表")
    private List<String> couponIndexList;
}
