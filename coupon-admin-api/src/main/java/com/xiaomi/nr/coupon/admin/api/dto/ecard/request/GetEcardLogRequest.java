package com.xiaomi.nr.coupon.admin.api.dto.ecard.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/8/27 16:33
 */
@Data
public class GetEcardLogRequest implements Serializable {
    private static final long serialVersionUID = 2630370364841593472L;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空")
    private Long userId;

    /**
     * 退款单号
     */
    private Long refundNo;

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 日志类型
     */
    @NotNull(message = "日志类型不能为空")
    private Integer logType;
}
