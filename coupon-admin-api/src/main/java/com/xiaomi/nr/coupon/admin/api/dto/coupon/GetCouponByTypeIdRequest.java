package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/30 10:17
 */
@Data
public class GetCouponByTypeIdRequest implements Serializable {
    private static final long serialVersionUID = 9159427442481096630L;

    /**
     * typeId列表
     */
    @NotNull(message = "typeIdList不能为空")
    @Size(max = 50, message = "优惠券类型id数量不能超过50")
    @ApiDocClassDefine(value = "typeIdList", description = "typeId列表")
    private List<String> typeIdList;

    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空")
    @ApiDocClassDefine(value = "startTime", description = "开始时间")
    private Long startTime;

    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空")
    @ApiDocClassDefine(value = "endTime", description = "结束时间")
    private Long endTime;
}
