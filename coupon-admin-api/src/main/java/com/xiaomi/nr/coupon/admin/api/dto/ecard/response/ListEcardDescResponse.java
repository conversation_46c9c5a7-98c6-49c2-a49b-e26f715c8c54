package com.xiaomi.nr.coupon.admin.api.dto.ecard.response;

import com.xiaomi.nr.coupon.admin.api.dto.ecard.LogDesc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 礼品卡信息查询接口返回值
 *
 * <AUTHOR>
 */


@Data
public class ListEcardDescResponse implements Serializable {

    private static final long serialVersionUID = -8601396369717683588L;

    /**
     * 礼品卡id
     */
    private Long ecardId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 状态描述
     */
    private String statDesc;

    /**
     * 礼品卡金额
     */
    private String money;

    /**
     * 绑定时间
     */
    private Long bindTime;

    /**
     * 作废时间
     */
    private Long invalidTime;

    /**
     * 激活时间
     */
    private Long activeTime;

    /**
     * 到期时间
     */
    private Long endTime;

    /**
     * 首次使用时间（下单消费）
     * */
    private Long firstUseTime;

    /**
     * 最近使用时间
     * */
    private Long tailUseTime;

    /**
     * 使用金额
     */
    private String amountMoney;

    /**
     * 剩余金额
     */
    private String balanceMoney;

    /**
     * 日志信息
     */
    private List<LogDesc> logList;

}
