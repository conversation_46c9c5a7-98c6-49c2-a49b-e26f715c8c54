package com.xiaomi.nr.coupon.admin.api.dto.ecard;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/8/27 16:52
 */
@Data
public class EcardDto implements Serializable {
    private static final long serialVersionUID = -561104172636955241L;

    /**
     * ecard编号
     */
    private Long cardId;

    /**
     * sn
     */
    private String sn;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * ecard类型ID
     */
    private Integer typeId;

    /**
     * e卡原始SKU
     */
    private String sku;

    /**
     * 面值
     */
    private BigDecimal money;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 售出价
     */
    private BigDecimal salePrice;

    /**
     * 生成任务ID
     */
    private String missionId;

    /**
     * 区域id
     */
    private Integer areaId;

    /**
     * 有效期开始时间
     */
    private String startTime;

    /**
     * 有效期结束时间
     */
    private String endTime;

    /**
     * 0预开卡，1开卡，2开卡作废，3激活，4激活作废，5绑定，6绑定作废
     */
    private Integer stat;

    /**
     * 0未锁定，1锁定
     */
    private Integer isLocked;

    /**
     * 1虚拟卡,0实物卡
     */
    private Integer isVirtual;

    /**
     * 购买的用户ID
     */
    private Long fromUserId;

    /**
     * 购买的订单号
     */
    private String fromOrderId;

    /**
     * 生成类型, reissue(后台生成), marketing(任务生成), external(接口生成)
     */
    private String sendType;

    /**
     * 添加时间
     */
    private Integer addTime;

    /**
     * 作废时间
     */
    private Integer invalidTime;

    /**
     * 激活时间
     */
    private Integer activeTime;

    /**
     * 绑定时间
     */
    private Integer bindTime;

    /**
     * 延期次数
     */
    private Integer delayTimes;

    /**
     * 最后更新时间
     */
    private String lastUpdateTime;

    /**
     * 是否是临时卡
     */
    private Integer isCasual;
}
