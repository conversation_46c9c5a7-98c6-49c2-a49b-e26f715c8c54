package com.xiaomi.nr.coupon.admin.api.dto.mission;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

@Data
public class CouponMissionListRequest implements Serializable {

    private static final long serialVersionUID = -6109595507414785204L;

    /**
     * 优惠券发放渠道,如果传store_manager则取店长券的，如果为空则取send_channel为空的券发放任务列表
     * */
    @SerializedName("sendChannel")
    private String sendChannel;

    /**
     * 发放任务id
     */
    @SerializedName("missionId")
    private Long missionId;

    /**
     * 上页数据的最后一个发券任务id
     */
    private Long lastMissionId;

    /**
     * 分页-每页返回N条
     */
    private Integer pageSize;

    /**
     * appId权限验证
     */
    private String appId;

    /**
     * 根据参数规则生成的token
     */
    private String token;
}
