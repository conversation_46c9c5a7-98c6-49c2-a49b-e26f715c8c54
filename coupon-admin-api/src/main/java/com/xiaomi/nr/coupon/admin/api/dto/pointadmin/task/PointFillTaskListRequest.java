package com.xiaomi.nr.coupon.admin.api.dto.pointadmin.task;

import com.google.common.collect.ImmutableMap;
import com.xiaomi.nr.coupon.admin.api.dto.BaseRequest;
import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * @description 灌积分任务列表查询
 * <AUTHOR>
 * @date 2024-08-14 11:08
 * @version 1.0
*/
@Data
public class PointFillTaskListRequest extends BaseRequest {

    private static final long serialVersionUID = 5114565557628718705L;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 积分批次id
     */
    private Long pointBatchId;

    /**
     * 积分批次名称
     */
    private String pointBatchName;

    /**
     * 灌积分任务状态
     */
    private Integer status;

    /**
     * 任务创建人
     */
    private String creator;

    /**
     * 灌积分开始时间
     */
    private Date startTime;

    /**
     * 灌积分结束时间
     */
    private Date endTime;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 排序顺序 (desc倒序, asc顺序)
     */
    private String orderDirection;

    /**
     * 当前页码
     */
    private Integer pageNo;

    /**
     * 页面大小
     */
    private Integer pageSize;
}
