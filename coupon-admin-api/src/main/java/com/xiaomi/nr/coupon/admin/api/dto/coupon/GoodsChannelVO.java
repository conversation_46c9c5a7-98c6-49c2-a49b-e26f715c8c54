package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 商品渠道价格
 */
@Data
public class GoodsChannelVO {
    /**
     * 使用渠道
     */
    private Integer useChannel;
    /**
     * 使用渠道名称
     */
    private String useChannelName;
    /**
     * 售价 元
     */
    private BigDecimal price;
    /**
     * 预估折扣力度 8.5折
     */
    private BigDecimal discount;

}
