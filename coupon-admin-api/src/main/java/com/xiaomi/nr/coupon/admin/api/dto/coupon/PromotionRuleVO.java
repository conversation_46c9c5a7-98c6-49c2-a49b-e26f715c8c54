package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
public class PromotionRuleVO extends GlobalBaseVO implements Serializable {

    /**
     * 优惠类型, 1:满减, 2:满折, 3:N元券, 4:立减
     */
    private int promotionType;
    /**
     * 优惠值(单位分/折)，如8折，传800
     */
    private Long promotionValue;
    /**
     * 门槛值满元（单位分)
     */
    private Long bottomPrice;
    /**
     * 门槛值满件（单位个)
     */
    private int bottomCount;
    /**
     * 门槛类型, 1:满元, 2:满件, 3:每满元, 4:每满件, 5:满元且满件
     */
    private int bottomType;
    /**
     * 最大减免金额 单位分
     */
    private Long maxReduce;

    /**
     * 折扣规则国际化翻译文案
     */
    private String discountDesc;

    public PromotionRuleVO(){}
    public PromotionRuleVO(long promotionValue, long bottomPrice, int bottomCount, int bottomType){
        this.promotionValue = promotionValue;
        this.bottomPrice = bottomPrice;
        this.bottomCount = bottomCount;
        this.bottomType = bottomType;
    }

}
