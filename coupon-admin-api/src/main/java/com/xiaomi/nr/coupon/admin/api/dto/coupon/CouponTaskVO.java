package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import lombok.Data;

import java.io.Serializable;

@Data
public class CouponTaskVO implements Serializable {
    private static final long serialVersionUID = -6172347512937785870L;

    /**
     * 灌券任务名称
     */
    private String name;

    /**
     * 优惠券名称
     */
    private String couponName;
    /**
     * 优惠券id
     */
    private String couponId;

    /**
     * 发放数量
     */
    private Long planCount;

    /**
     * 人群包类型
     */
    private Integer userGroupType;

    /**
     * 自定义人群包类型
     */
    private Integer customizeType;

    /**
     * HDFS地址
     */
    private String hdfsAddr;

    /**
     * 人群包id
     */
    private Long batchId;

    /**
     * 人群包名称
     */
    private String batchName;

    /**
     * 任务进度
     */
    private Long processRate;

    /**
     * 任务状态
     */
    private Integer status;

    /**
     * 人群包是否去重
     * 0 ： 去重
     * 1 ： 不去重
     */
    private int distinct;

    /**
     * 地区ID
     */
    private String areaId;
    /**
     * 审批流id
     */
    private Long workflowId;


    /**
     * 优惠卷类型id
     *
     */
    private Integer couponType;

    /**
     * 优惠卷类型名
     */
    private String couponTypeName;
}
