package com.xiaomi.nr.coupon.admin.api.dto.pointadmin;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/3/19
 */
@Data
public class PointQueryBudgetListRequest implements Serializable {
    
    
    private static final long serialVersionUID = 1591189707350617200L;

    /**
     * pageNum
     */
    private Integer pageNum;

    /**
     * pageSize
     */
    private Integer pageSize;

    /**
     * 积分发放场景编码
     */
    private String sendScene;

    /**
     * 促销类型
     */
    private Integer promotionType;

    /**
     * 模糊搜索词
     */
    private String keyword;


    
    public Pair<Boolean, String> checkParam() {
        if (StringUtils.isEmpty(sendScene)) {
            return Pair.of(Boolean.FALSE, "sendScene不能为空");
        }
        return Pair.of(Boolean.TRUE, "");
    }
}
