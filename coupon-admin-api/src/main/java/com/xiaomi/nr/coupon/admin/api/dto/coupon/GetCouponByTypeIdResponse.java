package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/30 10:17
 */
@Data
public class GetCouponByTypeIdResponse implements Serializable {
    private static final long serialVersionUID = 977058258237116431L;

    /**
     * 券信息列表
     */
    @ApiDocClassDefine(value = "couponInfoList", description = "券信息列表")
    private List<CouponInfo> couponInfoList;
}
