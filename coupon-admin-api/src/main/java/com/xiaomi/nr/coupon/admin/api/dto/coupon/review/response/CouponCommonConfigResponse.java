package com.xiaomi.nr.coupon.admin.api.dto.coupon.review.response;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.WorkFlowConfigVo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class CouponCommonConfigResponse implements Serializable {

    /**
     * By国家的审批流列表
     */
    private List<WorkFlowConfigVo> workFlowConfigVoList;



    private Map<Integer, List<Integer>> couponPermissionConfigMap;
}

