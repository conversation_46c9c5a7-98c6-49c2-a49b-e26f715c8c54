package com.xiaomi.nr.coupon.admin.api.service.couponadmin;

import com.xiaomi.newretail.bpm.api.model.callback.OnStatusChangedRequest;
import com.xiaomi.newretail.bpm.api.model.callback.OnStatusChangedResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.response.CouponReviewInfoResponse;
import com.xiaomi.youpin.infra.rpc.Result;

public interface DubboCouponBpmCallBackService {

    /**
     * 券新增修改回调
     * @param request
     */
    Result<OnStatusChangedResponse> onStatusChanged(OnStatusChangedRequest request);

    /**
     * 券新增修改回调 + 灌卷回调
     * @param request
     */
    OnStatusChangedResponse onStatusChangedHttp(OnStatusChangedRequest request);


    /**
     * 小程序回调业务详情接口
     */
    Result<CouponReviewInfoResponse>  queryReviewInfo(String bpmKey);


    /**
     * 灌券券新增修改回调
     * @param request
     */
    Result<OnStatusChangedResponse> onTaskStatusChanged(OnStatusChangedRequest request);

}
