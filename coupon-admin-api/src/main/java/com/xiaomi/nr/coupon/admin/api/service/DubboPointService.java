package com.xiaomi.nr.coupon.admin.api.service;

import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.GetPointBatchListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.GetPointBatchListResponse;
import com.xiaomi.youpin.infra.rpc.Result;

/**
 * 优惠券服务
 *
 * <AUTHOR>
 */
public interface DubboPointService {

    /**
     * 有效积分列表查询
     *
     * @param req       request
     * @return          积分批次列表
     */
    Result<GetPointBatchListResponse> getPointBatchList(GetPointBatchListRequest req);
}
