package com.xiaomi.nr.coupon.admin.api.dto.pointadmin;

import lombok.Data;

import java.io.Serializable;

/**
 * 积分批次
 *
 * <AUTHOR>
 * @date 2023/12/7
 */
@Data
public class PointBatchConfigData implements Serializable {

    private static final long serialVersionUID = 6832150402774382502L;
    /**
     * 积分批次id
     */
    private Long batchId;

    /**
     * 积分批次名称
     */
    private String batchName;

    /**
     * 预算池Id
     */
    private Long budgetId;

    /**
     * 发放开始时间
     */
    private Long startTime;

    /**
     * 发放结束时间
     */
    private Long endTime;

    /**
     * 发放场景
     */
    private String sendScene;

    /**
     * 可用状态
     */
    private Integer status;

    /**
     * 积分总额
     */
    private Long applyCount;

    /**
     * 积分余额
     */
    private Long balanceCount;
}
