package com.xiaomi.nr.coupon.admin.api.dto.couponconfig;

import com.xiaomi.nr.coupon.admin.api.dto.BaseRequest;
import lombok.Data;

import java.io.Serializable;

/**
 * @description: 获取券详情入参
 * @author: he<PERSON><PERSON><PERSON>
 * @Date 2022/3/3 2:12 下午
 * @Version: 1.0
 **/
@Data
public class GetConfigInfoRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = -3871104047111138445L;

    /**
     * 券配置Id
     */
    private Long configId;
}
