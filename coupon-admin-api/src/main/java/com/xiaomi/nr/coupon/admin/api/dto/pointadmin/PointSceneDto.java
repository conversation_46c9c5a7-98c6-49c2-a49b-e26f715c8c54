package com.xiaomi.nr.coupon.admin.api.dto.pointadmin;

import lombok.Data;

import java.io.Serializable;


@Data
public class PointSceneDto implements Serializable {

    /**
     * 主键
     */
    private Long id;
    /**
     * 场景名称
     */
    private String name;
    /**
     * 场景编号
     */
    private String sceneCode;

    /**
     * 父类场景Id
     */
    private Integer parentId;

    /**
     * 使用状态 1 启用 2 禁用
     */
    private Integer status;

    /**
     * 场景描述
     */
    private String sceneDesc;

}
