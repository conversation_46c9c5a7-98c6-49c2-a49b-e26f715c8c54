package com.xiaomi.nr.coupon.admin.api.dto.pointadmin;

import lombok.Data;

import java.io.Serializable;

@Data
public class PointSceneRequest implements Serializable {
    /**
     * 场景ID
     */
    private Long sceneId;

    /**
     * 操作类型 0-新增 1-修改 2 修改状态
     */
    private Integer type;

    /**
     * 使用状态  1 启用 2 禁用
     */
    private Long status;

    /**
     * 二级场景名称
     */
    private String sceneName;

    /**
     * 场景描述
     */
    private String sceneDesc;

    /**
     * 场景编码
     */
    private String sceneCode;

    /**
     * 父类场景Id
     */
    private Integer parentId;

    /**
     * 红包渠道信息
     */
    private PointSceneDto pointSceneDTO;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 发放场景枚举代码
     */
    private String enumCode;

    /**
     * 发放方式 1:接口发放 2:灌发
     */
    private String assignMode;
}
