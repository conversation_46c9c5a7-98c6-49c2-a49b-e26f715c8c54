package com.xiaomi.nr.coupon.admin.api.dto.autotest;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class UserEcardVO implements Serializable {

    /**
     * 礼品卡类型
     */
    private Long typeId;

    /**
     * 礼品卡id
     */
    private Long cardId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 状态
     */
    private Integer stat;

    /**
     * 礼品卡面值
     */
    private String money;

    /**
     * 礼品卡余额
     */
    private String balance;

    /**
     * 有效期开始时间
     */
    private Long startTime;

    /**
     * 有效期结束时间
     */
    private Long endTime;

    /**
     * 绑定时间
     */
    private Long bindTime;

    /**
     * 作废时间
     */
    private Long invalidTime;

    /**
     * 激活时间
     */
    private Long activeTime;




}
