package com.xiaomi.nr.coupon.admin.api.service;

import com.xiaomi.nr.coupon.admin.api.dto.mission.CouponMissionListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.mission.MissionDto;
import com.xiaomi.nr.coupon.admin.api.dto.mission.PageResponse;
import com.xiaomi.youpin.infra.rpc.Result;


/**
 * 发放任务服务
 * <AUTHOR>
 */
public interface DubboMissionService {

    /**
     * 根据发放渠道查询当前有效的发放任务信息列表接口
     * @param request CouponMissionListRequest
     * @return List<MissionDto>
     */
    Result<PageResponse<MissionDto>> getCouponMissionList(CouponMissionListRequest request);
}
