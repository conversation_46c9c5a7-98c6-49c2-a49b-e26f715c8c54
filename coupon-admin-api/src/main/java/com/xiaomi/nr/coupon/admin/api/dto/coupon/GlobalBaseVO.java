package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xiaomi.com.i18n.area.Area;
import com.xiaomi.nr.coupon.admin.api.utils.I18nUtil;
import lombok.Data;

import java.util.Date;
import java.util.Objects;

/**
 * @author: zhangliwei6
 * @date: 2025/5/22 10:31
 * @description: 国际通用字段
 */
@Data
public abstract class GlobalBaseVO {

    protected abstract Long getPromotionValue();

    /**
     * 优惠值描述
     */
    private String promotionValueStr;

    protected abstract Long getBottomPrice();

    /**
     * 门槛值描述
     */
    private String bottomPriceStr;

    public Long getMaxReduce() {
        return null;
    }

    private String maxReduceStr;

    @JsonIgnore
    protected Date getStartFetchTimeDate() {
        return null;
    }

    /**
     * 领取有效期开始时间
     */
    private Long startFetchTimeStamp;

    @JsonIgnore
    protected Date getEndFetchTimeDate() {
        return null;
    }

    /**
     * 领取有效期结束时间
     */
    private Long endFetchTimeStamp;

    @JsonIgnore
    protected Date getStartUseTimeDate() {
        return null;
    };

    private Long startUseTimeStamp;

    @JsonIgnore
    protected Date getEndUseTimeDate() {
        return null;
    };

    private Long endUseTimeStamp;

    /**
     * 地区ID
     */
    private String areaId;

    /**
     * 币种
     */
    private String currency;

    public void fillGlobalField() {
        fillGlobalField(null, false, false, false);
    }

    public void fillGlobalField(Area area) {
        fillGlobalField(area, true, false, false);
    }

    public void fillGlobalField(Area inputArea, boolean isInputArea, boolean fetchTime, boolean useTime) {
        Area area = isInputArea ? inputArea : Area.of(I18nUtil.getGlobalAreaId());
        // question 有的地方不校验area非空
        if (Objects.nonNull(area) && ! I18nUtil.CN.equals(area.getAreaId())) {
            if (this.getPromotionValue() != null) {
                this.setPromotionValueStr(area.moneyFormat(this.getPromotionValue()));
            }
            if (this.getBottomPrice() != null) {
                this.setBottomPriceStr(area.moneyFormat(this.getBottomPrice()));
            }
            if (this.getMaxReduce() != null) {
                this.setMaxReduceStr(area.moneyFormat(this.getMaxReduce()));
            }
            this.setCurrency(String.format(area.getCurrency(), ""));
        }
        if (fetchTime) {
            if (this.getStartFetchTimeDate() != null) {
                this.setStartFetchTimeStamp(this.getStartFetchTimeDate().getTime() / 1000);
            }
            if (this.getEndFetchTimeDate() != null) {
                this.setEndFetchTimeStamp(this.getEndFetchTimeDate().getTime() / 1000);
            }
        }
        if (useTime) {
            if (this.getStartUseTimeDate() != null) {
                this.setStartUseTimeStamp(this.getStartUseTimeDate().getTime() / 1000);
            }
            if (this.getEndUseTimeDate() != null) {
                this.setEndUseTimeStamp(this.getEndUseTimeDate().getTime() / 1000);
            }
        }
    }
}
