package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import com.xiaomi.com.i18n.area.Area;
import com.xiaomi.nr.coupon.admin.api.utils.I18nUtil;
import lombok.Data;

import java.util.Date;
import java.util.Objects;

/**
 * @author: zhang<PERSON>wei6
 * @date: 2025/5/22 10:31
 * @description: 国际通用字段
 */
@Data
public abstract class GlobalBaseVO {

    protected abstract Long getPromotionValue();

    /**
     * 优惠值描述
     */
    private String promotionValueStr;

    protected abstract Long getBottomPrice();

    /**
     * 门槛值描述
     */
    private String bottomPriceStr;

    Long getMaxReduce() {
        return null;
    }

    private String maxReduceStr;

    Date getStartFetchTime() {
        return null;
    }

    /**
     * 领取有效期开始时间
     */
    private Long startFetchTimeStamp;

    Date getEndFetchTime() {
        return null;
    }

    /**
     * 领取有效期结束时间
     */
    private Long endFetchTimeStamp;

    Date getStartUseTime() {
        return null;
    };

    private Long startUseTimeStamp;

    Date getEndUseTime() {
        return null;
    };

    private Long endUseTimeStamp;

    /**
     * 地区ID
     */
    private String areaId;

    /**
     * 币种
     */
    private String currency;

    public void fillGlobalField() {
        fillGlobalField(null, false, false, false);
    }

    public void fillGlobalField(Area area) {
        fillGlobalField(area, true, false, false);
    }

    public void fillGlobalField(Area inputArea, boolean isInputArea, boolean fetchTime, boolean useTime) {
        Area area = isInputArea ? inputArea : Area.of(I18nUtil.getGlobalAreaId());
        // question 有的地方不校验area非空
        if (Objects.nonNull(area) && ! I18nUtil.CN.equals(area.getAreaId())) {
            if (this.getPromotionValue() != null) {
                this.setPromotionValueStr(area.moneyFormat(this.getPromotionValue()));
            }
            if (this.getBottomPrice() != null) {
                this.setBottomPriceStr(area.moneyFormat(this.getBottomPrice()));
            }
            if (this.getMaxReduce() != null) {
                this.setMaxReduceStr(area.moneyFormat(this.getMaxReduce()));
            }
            this.setCurrency(String.format(area.getCurrency(), ""));
        }
        if (fetchTime) {
            if (this.getStartFetchTime() != null) {
                this.setStartFetchTimeStamp(this.getStartFetchTime().getTime() / 1000);
            }
            if (this.getEndFetchTime() != null) {
                this.setEndFetchTimeStamp(this.getEndFetchTime().getTime() / 1000);
            }
        }
        if (useTime) {
            if (this.getStartUseTime() != null) {
                this.setStartUseTimeStamp(this.getStartUseTime().getTime() / 1000);
            }
            if (this.getEndUseTime() != null) {
                this.setEndUseTimeStamp(this.getEndUseTime().getTime() / 1000);
            }
        }
    }
}
