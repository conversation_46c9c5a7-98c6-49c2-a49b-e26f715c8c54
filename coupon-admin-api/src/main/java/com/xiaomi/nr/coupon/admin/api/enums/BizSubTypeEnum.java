package com.xiaomi.nr.coupon.admin.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;

/**
 * 商品业务类型枚举
 *
 * <AUTHOR>
 * @date 2023/10/26 09:52
 */
@Getter
@AllArgsConstructor
public enum BizSubTypeEnum {
    /**
     * 1: 普通3c商品
     */
    ORDINARY_3C(1, "普通3c商品"),

    /**
     * 2: 运营商专属商品
     */
    CARRIER_EXCLUSIVE(2, "运营商专属商品"),

    /**
     * 3: 运营商泛全商品
     */
    CARRIER_PAN_COMPLETE(3, "运营商泛全商品"),

    ;
    /**
     * 枚举code
     */
    private final Integer code;

    /**
     * 枚举描述
     */
    private final String desc;

    private static final HashMap<Integer, BizSubTypeEnum> MAPPING = new HashMap<>();

    static {
        for (BizSubTypeEnum e : BizSubTypeEnum.values()) {
            MAPPING.put(e.getCode(), e);
        }
    }

    public static BizSubTypeEnum valueOf(Integer code) {
        return MAPPING.get(code);
    }



}
