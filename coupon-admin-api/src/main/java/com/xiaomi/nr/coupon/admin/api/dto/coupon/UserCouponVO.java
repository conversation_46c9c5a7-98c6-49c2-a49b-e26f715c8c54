package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class UserCouponVO implements Serializable {

    /**
     * 用户id
     */
    private long uid;
    /**
     * 券配置id
     */
    private long configId;
    /**
     * 优惠券类型  1: 商品券 2: 运费券
     */
    private Integer couponType;

    /**
     * 用户券id
     */
    private long couponId;
    /**
     * 券名称
     */
    private String name;
    /**
     * 优惠类型
     */
    private long promotionType;
    /**
     * 优惠值
     */
    private long promotionValue;
    /**
     * 门槛类型
     */
    private long bottomType;
    /**
     * 门槛值
     */
    private long bottomPrice;
    /**
     * 领取时间
     */
    private long bottomCount;
    /**
     *
     */
    private Date fetchTime;
    /**
     * 使用场景
     */
    private String sendScene;
    /**
     * 使用场景
     */
    private String sendSceneName;
    /**
     *使用开始时间
     */
    private Date startUseTime;
    /**
     * 使用结束时间
     */
    private Date endUseTime;
    /**
     * 使用渠道
     */
    private List<Integer> useChannel;
    /**
     * 用户券状态
     */
    private String couponStatus;
    /**
     * 使用时间
     */
    private Date useTime;
    /**
     * 转增人
     */
    private long recipientUid;
    /**
     * 转增时间
     */
    private Date givingTime;

    /**
     * vid
     */
    private String vid;

    /**
     * 业务领域 @BizPlatformEnum
     */
    private Integer bizPlatform;



}
