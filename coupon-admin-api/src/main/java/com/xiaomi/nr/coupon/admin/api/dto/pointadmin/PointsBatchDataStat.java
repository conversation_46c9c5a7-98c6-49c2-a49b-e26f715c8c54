package com.xiaomi.nr.coupon.admin.api.dto.pointadmin;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 积分批次数据统计
 *
 * <AUTHOR>
 * @date 2023/12/6
 */
@Data
@Builder
public class PointsBatchDataStat implements Serializable {
    private static final long serialVersionUID = -8449633185011534499L;

    /**
     * 已发放总量
     */
    private Long sendCnt;

    /**
     * 未发放总量
     */
    private Long unUseCnt;

    /**
     * 已使用总量
     */
    private Long usedCnt;

    /**
     * 已过期总量
     */
    private Long expiredCnt;

    /**
     * 已作废总量
     */
    private Long invalidCnt;

}
