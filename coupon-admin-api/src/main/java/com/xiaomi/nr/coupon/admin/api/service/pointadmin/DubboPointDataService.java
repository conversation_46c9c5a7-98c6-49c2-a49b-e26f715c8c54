package com.xiaomi.nr.coupon.admin.api.service.pointadmin;

import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.PointsBatchDataStatRequest;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.PointsBatchDataStatResponse;
import com.xiaomi.youpin.infra.rpc.Result;

import javax.validation.Valid;

/**
 * 积分数据服务
 *
 * <AUTHOR>
 * @date 2023/12/5
 */
public interface DubboPointDataService {

    /**
     * 积分批次数据统计
     *
     * @param request PointBatchDataStatRequest
     * @return Result
     */
    Result<PointsBatchDataStatResponse> pointBatchStat(@Valid PointsBatchDataStatRequest request);

}
