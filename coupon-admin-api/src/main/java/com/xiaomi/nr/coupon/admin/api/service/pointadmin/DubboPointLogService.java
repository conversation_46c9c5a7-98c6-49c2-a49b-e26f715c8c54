package com.xiaomi.nr.coupon.admin.api.service.pointadmin;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponConfigListVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.optlog.request.CouponLogListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.optlog.request.LogDetailRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.optlog.response.CouponLogListResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.optlog.response.LogDetailResponse;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.PointBatchLogDto;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.PointLogRequest;
import com.xiaomi.youpin.infra.rpc.Result;

/**
 * @Description: 优惠券日志
 * @Date: 2022.02.25 15:06
 */
public interface DubboPointLogService {
    /**
     * 查询修改列表
     * @param request
     * @return
     */
    Result<BasePageResponse<PointBatchLogDto>> queryLogList(PointLogRequest request);

}
