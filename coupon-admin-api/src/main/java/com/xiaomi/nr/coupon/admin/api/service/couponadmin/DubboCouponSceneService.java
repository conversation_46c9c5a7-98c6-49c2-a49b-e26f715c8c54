package com.xiaomi.nr.coupon.admin.api.service.couponadmin;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.scene.request.*;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.scene.response.*;
import com.xiaomi.youpin.infra.rpc.Result;

/**
 * @Description: 优惠券场景
 * @Date: 2022.03.03 16:53
 */
public interface DubboCouponSceneService {
    /**
     * 场景列表
     * @param request
     * @return
     */
    Result<BasePageResponse<SceneListVO>> querySceneList(QuerySceneListRequest request);

    /**
     * 新增或编辑场景
     * @param request
     * @return
     */
    Result<CreateOrUpdateSendSceneResponse> createOrUpdateSendScene(CreateOrUpdateSendSceneRequest request);

    /**
     * 查询场景详情
     * @param request
     * @return
     */
    Result<SceneDetailResponse> sceneDetail(SceneDetailRequest request);

    /**
     * 上下线场景
     * @param request
     * @return
     */
    Result<Boolean> operateScene(OperateSceneRequest request);

    /**
     * 授权列表
     * @param request
     * @return
     */
    Result<BasePageResponse<PermissionListVO>> queryPermissionList(PermissionListRequest request);

    /**
     * 场景授权
     * @param request
     * @return
     */
    Result<Boolean> createPermission(CreatePermissionRequest request);

    /**
     * 停用或启用授权
     * @param request
     * @return
     */
    Result<Boolean> operatePermission(OperatePermissionRequest request);

    /**
     * 查询所有场景并且分类
     * @return
     */
    Result<SearchSceneWithCatResponse> searchSceneWithCat();

    /**
     * 查询所有场景并且分类
     * @return
     */
    Result<SearchSceneWithCatResponse> searchSceneWithCatV2(SearchSceneWithCatRequest request);
}
