package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


@Data
public class SkuInfoVO implements Serializable{
    /**
     * gid
     */
    private long goodsId;
    /**
     * pid
     */
    private long productId;
    /**
     * sku
     */
    private long sku;
    /**
     * sku
     */
    private long ssu;
    /**
     * 平台价 分
     */
    private long marketPrice;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 线上上下架状态
     */
    private int onSaleOnline;
    /**
     * 米家上下架状态
     */
    private int onSaleOffline;
    /**
     * 授权店上下架状态
     */
    private int sqOnSale;
    /**
     * 车商城上下架状态
     */
    private int onSaleCarShop;
    /**
     * 一级类目id
     */
    private long firstCategoryId;
    /**
     * 一级类目名称
     */
    private String firstCategoryName;
    /**
     * 二级类目id
     */
    private long secondCategoryId;
    /**
     * 二级类目名称
     */
    private String secondCategoryName;
    /**
     * 三级类目id
     */
    private long thirdCategoryId;
    /**
     * 三级类目名称
     */
    private String thirdCategoryName;

    /**
     * 新品标志 0-非新品  1-新品
     */
    private int newFlag;

    /**
     * 线上在售状态
     */
    private int onShelfOnline;
    /**
     * 线下在售状态
     */
    private int onShelfOffline;

    /**
     * 授权店货品状态
     * 0、下架   1、上架
     */
    private int sqOnShelf;

    /**
     *预计销量
     */
    private Long expectedSaleAmount;

    /**
     *单台投入
     */
    private BigDecimal rebatesPricePerUnit;
}
