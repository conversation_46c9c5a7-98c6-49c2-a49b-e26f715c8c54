package com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户券详情
 */
@Getter
@Setter
public class CouponInfoVO implements Serializable {
    private static final long serialVersionUID = -4368651499883089326L;

    /**
     * 用户id
     */
    private long uid;

    /**
     * 用户券id
     */
    private long couponId;

    /**
     * 发放方式
     */
    private String sendMode;

    /**
     * 领取时间
     */
    private Date fetchTime;

    /**
     * 领取门店
     */
    private String fetchOrg;

    /**
     *使用开始时间
     */
    private Date startUseTime;

    /**
     * 使用结束时间
     */
    private Date endUseTime;

    /**
     * 用户券状态
     */
    private String couponStatus;

    /**
     * 使用时间
     */
    private Date useTime;

    /**
     * 发放活动 / 任务id
     */
    private String activityId;
}
