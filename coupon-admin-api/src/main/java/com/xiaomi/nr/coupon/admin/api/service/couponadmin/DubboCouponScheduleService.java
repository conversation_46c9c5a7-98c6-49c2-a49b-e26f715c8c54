package com.xiaomi.nr.coupon.admin.api.service.couponadmin;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.schedule.RefreshCouponUserWhiteListReq;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.schedule.RefreshCouponUserWhiteListResp;
import com.xiaomi.youpin.infra.rpc.Result;

/**
 * coupon定时任务
 *
 *
 * <AUTHOR>
 * @date 2024/03/25
 */
public interface DubboCouponScheduleService {

    /**
     * 积分批次定时任务
     * 涉及：预算释放
     *
     * @return      void
     */
    Result<Void> budgetRelease();

    /**
     * 刷新有券用户白名单
     */
    Result<RefreshCouponUserWhiteListResp> refreshCouponUserWhiteList(RefreshCouponUserWhiteListReq req);
}
