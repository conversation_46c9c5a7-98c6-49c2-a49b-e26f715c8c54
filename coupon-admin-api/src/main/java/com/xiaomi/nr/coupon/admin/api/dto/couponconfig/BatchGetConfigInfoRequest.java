package com.xiaomi.nr.coupon.admin.api.dto.couponconfig;

import com.xiaomi.nr.coupon.admin.api.dto.BaseRequest;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 获取券详情入参
 * @author: he<PERSON><PERSON><PERSON>
 * @Date 2022/3/3 2:12 下午
 * @Version: 1.0
 **/
@Data
public class BatchGetConfigInfoRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = -3871104047111138445L;

    /**
     * 券配置
     */
    private List<Long> configIds;

    /**
     * 领券场景
     */
    private String sceneCode;

    /**
     * 开始创建时间
     */
    private Integer beginTime;

    /**
     * 结束创建时间
     */
    private Integer endTime;

    /**
     * 返回剩余券数量
     */
    private Boolean withSurplusCnt;

    /**
     * 只返回有效可领券配置
     */
    private Boolean onlyAvailable;

    /**
     * 地区：所属区域ID
     */
    private String areaId;
}
