package com.xiaomi.nr.coupon.admin.api.dto.coupon.statistic.response;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

@Data
public class CouponDataStatisticVO implements Serializable {

    private static final long serialVersionUID = 5641968368227347652L;

    /**
     * 优惠券id
     */
    private	Integer configId;

    /**
     * 活动编号
     */
    private	String activityId;

    /**
     * 优惠券名称
     */
    private	String name;

    /**
     * 优惠类型 (1:满减, 2:满折, 3:N元券, 4:立减)
     */
    private Integer promotionType;

    /**
     * 优惠类型名称
     */
    private String promotionTypeName;

    /**
     * 优惠信息描述
     */
    private String promotionDesc;

    /**
     * 投放场景
     */
    private String sendScene;

    /**
     * 投放方式
     */
    private String sendType;

    /**
     * 申请数量
     */
    private Integer applyCount;

    /**
     * 已发数量
     */
    private Integer sendCount;

    /**
     * 累计已发数量
     */
    private Integer totalSendCount;

    /**
     * 已发数量
     */
    private Integer useCount;

    /**
     * 累计算已用数量
     */
    private Integer totalUseCount;

    /**
     * 使用率
     */
    private BigDecimal useRatio;

    /**
     * 累计使用率
     */
    private BigDecimal totalUseRatio;

    /**
     * 使用渠道
     */
    private	String useChannel;

    /**
     * 抵扣金额
     */
    private BigDecimal reduceAmount;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * ROI
     */
    private BigDecimal roi;


    public void addSendCount(Integer incrSendCnt) {
        if(sendCount == null) {
            sendCount = 0;
        }
        sendCount += incrSendCnt;
    }

    public void addUseCount(Integer incrUseCnt) {
        if(useCount == null) {
            useCount = 0;
        }
        useCount += incrUseCnt;
    }

    public void addTotalSendCount(Integer incrSendCnt) {
        if(totalSendCount == null) {
            totalSendCount = 0;
        }
        totalSendCount += incrSendCnt;
    }

    public void addTotalUseCount(Integer incrUseCnt) {
        if(totalUseCount == null) {
            totalUseCount = 0;
        }
        totalUseCount += incrUseCnt;
    }

    public void addReduceAmount(BigDecimal incrAmount) {
        if(reduceAmount == null) {
            reduceAmount = BigDecimal.valueOf(0);
        }
        reduceAmount = reduceAmount.add(incrAmount);
    }

    public void addOrderAmount(BigDecimal incrAmount) {
        if(orderAmount == null) {
            orderAmount = BigDecimal.valueOf(0);
        }
        orderAmount = orderAmount.add(incrAmount);
    }

    public void calculateUseRatio() {
        if (sendCount != null && sendCount > 0L && useCount != null && useCount > 0L) {
            useRatio = BigDecimal.valueOf(useCount).multiply(BigDecimal.valueOf(100))
                    .divide(BigDecimal.valueOf(sendCount), 1, RoundingMode.HALF_UP);
        } else {
            useRatio = BigDecimal.ZERO;
        }
    }

    public void calculateTotalUseRatio() {
        if (totalSendCount != null && totalSendCount > 0L && totalUseCount != null && totalUseCount > 0L) {
            totalUseRatio = BigDecimal.valueOf(totalUseCount).multiply(BigDecimal.valueOf(100))
                    .divide(BigDecimal.valueOf(totalSendCount), 1, RoundingMode.HALF_UP);
        } else {
            totalUseRatio = BigDecimal.ZERO;
        }
    }


    public void calculateRoi() {
        if (orderAmount != null && reduceAmount != null && reduceAmount.compareTo(BigDecimal.valueOf(0)) > 0) {
            roi = orderAmount.divide(reduceAmount, 1, RoundingMode.HALF_UP);
        } else {
            roi = BigDecimal.ZERO;
        }
    }

}
