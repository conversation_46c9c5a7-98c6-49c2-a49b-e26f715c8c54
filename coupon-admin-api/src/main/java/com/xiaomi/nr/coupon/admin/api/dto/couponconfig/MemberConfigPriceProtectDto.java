package com.xiaomi.nr.coupon.admin.api.dto.couponconfig;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description:
 * @Date: 2022.05.11 14:24
 */
@Data
public class MemberConfigPriceProtectDto implements Serializable {
    /**
     * 是否打开了会员价保N天权益
     */
    private Boolean open;

    /**
     * 打开会员价保N天权益的开始时间
     */
    private Long startTime;

    /**
     * 打开会员价保N天权益的结束时间
     */
    private Long endTime;

    /**
     * 价保SKU黑名单，如果没有则传空列表
     */
    @SerializedName("sku_blacks")
    private List<Long> skuBlacks;

    /**
     * 价保套装ID黑名单，如果没有则传空列表
     */
    @SerializedName("package_blacks")
    private List<Long> packageBlacks;
}
