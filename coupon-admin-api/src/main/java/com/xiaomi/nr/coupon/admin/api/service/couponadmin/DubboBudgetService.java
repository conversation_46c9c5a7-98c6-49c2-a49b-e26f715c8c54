package com.xiaomi.nr.coupon.admin.api.service.couponadmin;

import com.xiaomi.nr.coupon.admin.api.dto.BudgetInfoDto;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.budget.request.QueryByUploadUrlFileRequest;
import com.xiaomi.youpin.infra.rpc.Result;

/**
 * 预算信息
 *
 * <AUTHOR>
 * @date 2024/9/19
 */
public interface DubboBudgetService {

    /**
     * 通过上传文件查询预算信息
     *
     * @param request 文件url和预算类型
     * @return 预算信息
     */
    Result<BudgetInfoDto> queryByUploadBudgetFileUrl(QueryByUploadUrlFileRequest request);
}
