package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import lombok.Data;

import java.io.Serializable;

@Deprecated
@Data
public class AssignCouponTmpDto implements Serializable {
    private static final long serialVersionUID = 4522745980807503321L;

    /**
     * 券发放任务id
     */
    private Long missionId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 是否幂等 true：是，false: 否
     */
    private Boolean idempotent;

    /**
     * 实发优惠券id
     */
    private Long couponId;

    /**
     * 是否发放成功
     */
    private Boolean success;

    /**
     * 发放失败原因
     */
    private String reason;

    public AssignCouponTmpDto(){}
    public AssignCouponTmpDto(long missionId, long userId, long couponId , boolean success, boolean idempotent, String reason){
        this.missionId = missionId;
        this.userId = userId;
        this.couponId = couponId;
        this.success = success;
        this.idempotent = idempotent;
        this.reason = reason;

    }

}
