package com.xiaomi.nr.coupon.admin.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @description 按需保养券年度类型
 * <AUTHOR>
 * @date 2024-12-26 13:36
*/
@Getter
@AllArgsConstructor
public enum AnnualTypeEnum {
    SINGLE(1, "单年度"),
    DOUBLE(2, "双年度"),
    ;
    private final Integer value;
    private final String desc;

    public static AnnualTypeEnum getByValue(Integer value) {
        for (AnnualTypeEnum annualTypeEnum : AnnualTypeEnum.values()) {
            if (Objects.equals(annualTypeEnum.getValue(), value)) {
                return annualTypeEnum;
            }
        }
        return null;
    }
}
