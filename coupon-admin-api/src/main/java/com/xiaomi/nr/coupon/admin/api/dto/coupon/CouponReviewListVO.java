package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class CouponReviewListVO implements Serializable {

    /**
     * 审批id
     */
    private long reviewId;
    /**
     * 券id
     */
    private Long configId;
    /**
     * 优惠券名称
     */
    private	String	couponName;
    /**
     * 优惠类型, 1:满减, 2:满折, 3:N元券, 4:立减
     */
    private Integer promotionType;
    /**
     * 预估总成本 元
     */
    private BigDecimal cost;

    private String costStr;

    private String currency;
    /**
     * 领取有效期开始时间
     */
    private Date startFetchTime;
    /**
     * 领取有效期开始时间
     */
    private Long startFetchTimeStamp;
    /**
     * 领取有效期结束时间
     */
    private	Date endFetchTime;
    /**
     * 领取有效期结束时间
     */
    private	Long endFetchTimeStamp;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 提交时间
     */
    private Date createTime;
    /**
     * 提交时间
     */
    private Long createTimeStamp;
    /**
     * 审核状态
     */
    private Integer status;

    /**
     * 审批原因
     */
    private String bpmReason;

    /**
     * 区域areaId
     */
    private String areaId;

    /**
     * 使用渠道
     */
    private String useChannel;
}
