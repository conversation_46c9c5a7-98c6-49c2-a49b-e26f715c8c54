package com.xiaomi.nr.coupon.admin.api.dto.pointadmin;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/12/12 19:36
 */
@Data
public class PointBatchDetailRequest implements Serializable {
    private static final long serialVersionUID = -708185194817158886L;

    /**
     * 积分批次id
     */
    @NotNull(message = "积分批次id不能为空")
    private Long batchId;
}
