package com.xiaomi.nr.coupon.admin.api.service.couponadmin;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponConfigListVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponInfoRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponUpdateStatusRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.response.CouponInfoResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.statistic.request.CouponDataStatisticRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.statistic.response.CouponDataStatisticVO;
import com.xiaomi.youpin.infra.rpc.Result;

/**
 * @description: 优惠券管理后券数据分析接口
 * @author: hejiapeng
 * @Version: 1.0
 **/

public interface DubboCouponDataService {

    /**
     * 查询优惠券数据分析详情
     *
     * @param request request
     * @return response
     */
    Result<BasePageResponse<CouponDataStatisticVO>> getCouponDataStatistic(CouponDataStatisticRequest request);

    /**
     * 查询灌券数据分析详情
     *
     * @param request request
     * @return response
     */
    Result<BasePageResponse<CouponDataStatisticVO>> getFillCouponDataStatistic(CouponDataStatisticRequest request);


    /**
     * 导出券数据分析详情
     *
     * @param request request
     * @return void
     */
    Result<String> exportCouponDataStatistic(CouponDataStatisticRequest request);

}
