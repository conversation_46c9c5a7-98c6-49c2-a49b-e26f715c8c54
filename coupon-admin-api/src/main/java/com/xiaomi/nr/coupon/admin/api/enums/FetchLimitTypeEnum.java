package com.xiaomi.nr.coupon.admin.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2023/12/27 11:06
 */
@Getter
@AllArgsConstructor
public enum FetchLimitTypeEnum {
    /**
     * 1: 限领
     */
    LIMIT(1, "限领"),

    /**
     * 2: 不限领
     */
    NO_LIMIT(2, "不限领"),
    ;

    /**
     * 枚举code
     */
    private final Integer code;

    /**
     * 枚举描述
     */
    private final String desc;

    private static final HashMap<Integer, FetchLimitTypeEnum> MAPPING = new HashMap<>();

    static {
        for (FetchLimitTypeEnum e : FetchLimitTypeEnum.values()) {
            MAPPING.put(e.getCode(), e);
        }
    }

    public static FetchLimitTypeEnum valueOf(Integer code) {
        return MAPPING.get(code);
    }
}
