package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CategoryNewGoodsVO implements Serializable{
    /**
     * 一级类目id
     */
    private long firstCategoryId;
    /**
     * 一级类目名称
     */
    private String firstCategoryName;
    /**
     * 二级类目id
     */
    private long secondCategoryId;
    /**
     * 二级类目名称
     */
    private String secondCategoryName;
    /**
     * 三级类目id
     */
    private long thirdCategoryId;
    /**
     * 三级类目名称
     */
    private String thirdCategoryName;

    /**
     * 新增商品列表
     */
    private List<SkuInfoVO> children;

}
