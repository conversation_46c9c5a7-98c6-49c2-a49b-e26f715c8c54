package com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.response;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.ConfigInfoVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.CouponInfoVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.OrderInfoVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.ShareInfoVO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class UserCouponDetailResponse implements Serializable {
    private static final long serialVersionUID = -5989049531244593486L;

    /**
     * 用户券信息
     */
    private CouponInfoVO couponInfo;

    /**
     * 券配置信息
     */
    private ConfigInfoVO configInfo;

    /**
     * 订单信息
     */
    private OrderInfoVO orderInfo;

    /**
     * 券分享信息
     */
    private List<ShareInfoVO> shareInfoList;

    public UserCouponDetailResponse(){}
    public UserCouponDetailResponse(CouponInfoVO couponInfo, ConfigInfoVO configInfo, OrderInfoVO orderInfo, List<ShareInfoVO> shareInfoList){
        this.couponInfo = couponInfo;
        this.configInfo = configInfo;
        this.orderInfo = orderInfo;
        this.shareInfoList = shareInfoList;
    }

}
