package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 灌券任务信息
 */
@Data
public class CouponTaskListVO implements Serializable {
    private static final long serialVersionUID = -8996695490430909652L;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 优惠券配置Id
     */
    private Long configId;

    /**
     * 优惠券类型  1: 商品券 2: 运费券
     */
    private Integer couponType;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     * 任务开始时间
     */
    private Date startTime;
    /**
     * 任务开始时间
     */
    private Long startTimeStamp;

    /**
     * 任务结束时间
     */
    private Date endTime;

    /**
     * 任务结束时间
     */
    private Long endTimeStamp;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建时间
     */
    private Long createTimeStamp;

    /**
     * 任务进度
     */
    private Long processRate;

    /**
     * 任务状态
     */
    private Integer status;

    /**
     * 排队位置
     */
    private Integer waitPosition;

    /**
     * 执行数量
     */
    private Long totalCount;

    /**
     * 完成数量
     */
    private Long successCount;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 数据集地址
     */
    private String address;
}
