package com.xiaomi.nr.coupon.admin.api.dto.coupon.user;

import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class OrganInfoVo implements Serializable {

    private static final long serialVersionUID = -4798821700340116529L;
    @ApiDocClassDefine(
            value = "scene",
            description = "场景/租户"
    )
    private String scene;
    @ApiDocClassDefine(
            value = "areaId",
            description = "国家/地区ID"
    )
    private String areaId;
    @ApiDocClassDefine(
            value = "departmentId",
            description = "组织ID"
    )
    private Integer departmentId;
    @ApiDocClassDefine(
            value = "organCode",
            description = "机构码"
    )
    private String organCode;
    @ApiDocClassDefine(
            value = "organName",
            description = "机构名称"
    )
    private String organName;
    @ApiDocClassDefine(
            value = "organType",
            description = "机构类型"
    )
    private String organType;
    @ApiDocClassDefine(
            value = "storeManagerList",
            description = "店长miIdList"
    )
    private List<Long> storeManagerList;
    @ApiDocClassDefine(
            value = "type",
            description = "站点/管理类型 1-米家 5-授权 20-米家+授权"
    )
    private String type;
    @ApiDocClassDefine(
            value = "thirdCode",
            description = "三方code"
    )
    private String thirdCode;
    @ApiDocClassDefine(
            value = "pioneerType",
            description = "先锋小区划分1-先锋小区 0-非先锋小区"
    )
    private Integer pioneerType;
}
