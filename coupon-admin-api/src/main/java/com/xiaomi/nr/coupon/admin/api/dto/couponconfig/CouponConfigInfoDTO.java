package com.xiaomi.nr.coupon.admin.api.dto.couponconfig;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.GlobalBaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @description: 券详情DTO
 * @author: he<PERSON><PERSON><PERSON>
 * @Date 2022/3/3 2:15 下午
 * @Version: 1.0
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class CouponConfigInfoDTO extends GlobalBaseVO implements Serializable {

    /**
     * 券配置ID
     */
    private Long configId;
    /**
     * 优惠券状态
     */
    private Integer status;
    /**
     * 优惠类型 (1:满减, 2:满折, 3:N元券, 4:立减)
     */
    private Integer promotionType;
    /**
     * 优惠金额
     */
    private Long promotionValue;
    /**
     * 券配置名称
     */
    private String couponName;
    /**
     * 发券场景
     */
    private String sendScene;
    /**
     * 使用渠道
     */
    private List<Long> useChannelList;
    /**
     * 券配置描述
     */
    private String couponDesc;
    /**
     * 券开始领取时间
     */
    private Long startFetchTime;
    /**
     * 券结束领取时间
     */
    private Long endFetchTime;
    /**
     * 券使用有效期类型  1 固定时间 2 相对时间
     */
    private Integer useTimeType;

    /**
     * 券开始使用时间
     */
    private Long startUseTime;

    /**
     * 券结束使用时间
     */
    private Long endUseTime;

    /**
     * 券发放总量
     */
    private Integer sendCount;

    /**
     * 券当前剩余总量
     */
    private Integer nowCount;

    /**
     * 每人限领
     */
    private Integer fetchLimit;

    /**
     * 限领类型: 1-限领; 2-不限领
     */
    private Integer fetchLimitType;

    /**
     * 券有效时长 单位小时
     */
    private Integer useDuration;

    /**
     * 新平台券 1 是 2 否
     */
    private Integer newType;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Integer createTime;

    /**
     * 修改时间
     */
    private Integer updateTime;

    /**
     * 优惠券分摊信息
     */
    private List<CostShareDTO> costShareDTOS;

    /**
     * 优惠券类型
     */
    private Integer couponType;

    /**
     * 是否包邮 1是 2否，海外暂时不用。不过下游兼容了
     */
    private Integer postFree;

    /**
     * 门槛值
     */
    private Long bottomPrice;

    /**
     * 门槛类型, 1:满元, 2:满件, 3:每满元, 4:每满件, 5:满元且满件
     */
    private Integer bottomType;

    /**
     * 门槛值 件
     */
    private Integer bottomCount;

    /**
     * 最大减免金额
     */
    private Long maxReduce;

    /***
     * 减免规则描述
     */
    private String couponRuleDesc;

    /***
     * 简化版减免金额
     */
    private String benefit;

    /**
     * 是否全部门店
     */
    private Boolean isAllStore;

    /**
     * 门店code列表
     */
    private List<String> storeCodes;

    /**
     * 折扣规则国际化翻译文案
     */
    private String discountDesc;

}
