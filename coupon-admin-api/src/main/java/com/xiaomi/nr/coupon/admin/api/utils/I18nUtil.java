package com.xiaomi.nr.coupon.admin.api.utils;

import com.xiaomi.newretail.common.tools.base.exception.BaseException;
import com.xiaomi.nr.coupon.admin.api.enums.TranslationEnum;
import com.xiaomi.nr.global.dev.base.RequestContextInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class I18nUtil {

    private static String group;
    public static final String GLOBAL = "GLOBAL";
    public static final String CN = "CN";

    // 使用setter方法注入配置值到静态变量
    @Value("${i18n.group}")
    public void setGroup(String grp) {
        I18nUtil.group = grp;
    }

    // 将isI18n方法改为静态方法
    public static boolean isI18n() {
        return GLOBAL.equals(group);
    }

    public static boolean isNotI18n() {
        return !GLOBAL.equals(group);
    }

    /**
     * 获取全局区域ID
     *
     * @return 全局区域ID
     */
    public static String getGlobalAreaId() {
        String areaId = RequestContextInfo.getAreaId();
//        String areaId = "HK";
        if (areaId != null && !areaId.isEmpty() && !"null".equals(areaId)) {
            return areaId;
        }
        if (isI18n()) {
            throw new BaseException(-1, TranslationEnum.COUPON_UTIL_QUERY_ERROR_AREA.getTranslateContent());
        }
        return CN;
    }

    /**
     * 检查areaId是否为国际区域ID
     *
     * @param areaId 区域ID
     * @return true/false
     */
    public static boolean checkI18n(String areaId) {
        return StringUtils.isNotBlank(areaId) && !areaId.equals("CN");
    }

    /**
     * 结合参数areaId与上下文区域ID进行比较，取出最终的区域ID，或不一致刚报错
     *
     * @param areaId 参数区域ID
     * @return string
     */
    public static String getGlobalAreaId(String areaId) {
        areaId = StringUtils.isNotBlank(areaId) && !"null".equals(areaId) ? areaId : null;
        String ctxAreaId = RequestContextInfo.getAreaId();
        ctxAreaId = StringUtils.isNotBlank(ctxAreaId) && !"null".equals(ctxAreaId) ? ctxAreaId : null;
        if (!isI18n()) {
            return ctxAreaId != null ? ctxAreaId : areaId;
        }
        if (ctxAreaId == null && areaId == null) {
            throw new BaseException(-1, TranslationEnum.COUPON_UTIL_QUERY_ERROR_AREA.getTranslateContent());
        }
        if (ctxAreaId != null && areaId != null && !StringUtils.equals(ctxAreaId, areaId)) {
            throw new BaseException(-1, TranslationEnum.COUPON_PARAM_AREA_ID_ERROR.getTranslateContent());
        }
        return ctxAreaId != null ? ctxAreaId : areaId;
    }
}

