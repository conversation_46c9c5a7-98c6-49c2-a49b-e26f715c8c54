package com.xiaomi.nr.coupon.admin.api.service;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.sync.request.SyncXiguaMarketCouponReceiveRecordRequest;
import com.xiaomi.youpin.infra.rpc.Result;

import javax.validation.Valid;

/**
 * 优惠券
 *
 * <AUTHOR>
 * @date 2024/6/26 15:28
 */
public interface DubboCouponSyncService {
    /**
     * 券码信息同步
     */
    Result<Void> syncXiguaMarketCouponMessage();

    /**
     * 西瓜侧券码领取记录同步
     *
     * @param request request
     */
    Result<Void> syncXiguaMarketCouponReceiveRecord(@Valid SyncXiguaMarketCouponReceiveRecordRequest request);
}
