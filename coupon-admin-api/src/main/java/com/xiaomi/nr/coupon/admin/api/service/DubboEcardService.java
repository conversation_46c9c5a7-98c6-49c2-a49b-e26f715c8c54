package com.xiaomi.nr.coupon.admin.api.service;

import com.xiaomi.nr.coupon.admin.api.dto.ecard.request.GetEcardInfoRequest;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.request.GetEcardLogRequest;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.request.ListEcardIdRequest;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.response.GetEcardInfoResponse;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.response.GetEcardLogResponse;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.response.ListEcardDescResponse;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.response.ListEcardStatResponse;
import com.xiaomi.youpin.infra.rpc.Result;

import javax.validation.Valid;
import java.util.List;

/**
 * 礼品卡服务
 *
 * <AUTHOR>
 */
public interface DubboEcardService {

    /**
     * 礼品卡状态查询接口
     *
     * @param request ListEcardIdRequest
     * @return List<ListEcardResponse>
     */
    Result<List<ListEcardStatResponse>> listEcardStat(ListEcardIdRequest request);

    /**
     * 礼品卡信息查询接口
     *
     * @param request ListEcardIdRequest
     * @return List<ListEcardResponse>
     */
    Result<List<ListEcardDescResponse>> listEcardDesc(ListEcardIdRequest request);

    /**
     * 查询ecard日志（财务系统使用）
     *
     * @param request 获取电子卡日志的请求对象
     * @return 包含电子卡日志的响应结果
     */
    Result<GetEcardLogResponse> getEcardLog(@Valid GetEcardLogRequest request);

    /**
     * 查询ecard信息（财务系统使用）
     *
     * @param request 获取电子卡信息的请求对象
     * @return 包含电子卡信息的响应结果
     */
    Result<GetEcardInfoResponse> getEcardInfo(@Valid GetEcardInfoRequest request);


}
