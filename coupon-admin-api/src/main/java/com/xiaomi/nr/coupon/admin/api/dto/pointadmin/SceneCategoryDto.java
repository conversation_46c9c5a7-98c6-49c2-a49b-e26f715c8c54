package com.xiaomi.nr.coupon.admin.api.dto.pointadmin;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description:
 * @Date: 2022.03.16 二级场景VO
 */
@Data
public class SceneCategoryDto implements Serializable {

    /**
     * 场景code
     */
    private String sceneCode;

    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * App展示名称
     */
    private String appName;

    /**
     * 发放记录文案
     */
    private String assignRecordDesc;

    /**
     * 过期记录文案
     */
    private String expireRecordDesc;

    /**
     * 延期记录文案
     */
    private String delayRecordDesc;

}
