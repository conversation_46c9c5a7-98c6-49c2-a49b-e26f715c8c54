package com.xiaomi.nr.coupon.admin.api.dto.pointadmin;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/8 09:28
 */
@Data
public class AddSsuBlacklistRequest implements Serializable {
    private static final long serialVersionUID = 5963330014080594847L;

    /**
     * ssuIdList
     */
    @Size(min = 1, message = "黑名单SSU不能为空")
    @NotNull(message = "黑名单SSU不能为空")
    private List<Long> ssuIdList;

}
