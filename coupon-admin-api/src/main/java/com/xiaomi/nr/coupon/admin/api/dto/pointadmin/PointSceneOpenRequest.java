package com.xiaomi.nr.coupon.admin.api.dto.pointadmin;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @date 2025-06-11
 */
@Data
public class PointSceneOpenRequest extends PointSceneRequest {
    /**
     * 是否创建一级场景  false-不新建 true-新建
     */
    private Boolean createParentScene;

    /**
     * 一级场景名称
     */
    private String parentSceneName;

    /**
     * 一级场景id
     */
    private Integer parentSceneId;

    /**
     * 一级场景描述
     */
    private String parentSceneDesc;

    /**
     * APP展示名称
     */
    private String appName;

    /**
     * 发放记录文案
     */
    private String assignRecordDesc;

    /**
     * 过期记录文案
     */
    private String expireRecordDesc;

    /**
     * 延期记录文案
     */
    private String delayRecordDesc;

}
