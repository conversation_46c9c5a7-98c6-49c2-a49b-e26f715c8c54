package com.xiaomi.nr.coupon.admin.api.service.couponadmin;

import com.xiaomi.mone.docs.annotations.dubbo.ApiDoc;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.store.StoreInfoVo;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.store.request.PageStoreRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.store.request.StoreListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.store.response.StoreListResponse;
import com.xiaomi.nr.coupon.admin.api.dto.mission.PageResponse;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;

/**
 * 门店相关接口
 */
public interface DubboStoreService {

    /**
     * 门店id查询门店
     * @return
     */
    Result<StoreListResponse> queryListByStoreIds(StoreListRequest request);


    /**
     * 查询门店信息
     * @param storeRequest
     * @return
     */
    public Result<BasePageResponse<StoreInfoVo>> queryStoreList(PageStoreRequest storeRequest);



}
