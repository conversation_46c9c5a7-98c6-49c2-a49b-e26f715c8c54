package com.xiaomi.nr.coupon.admin.api.dto.coupon.review.response;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.*;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CouponReviewDetailResponse implements Serializable {

    /**
     * 审核id
     */
    private long reviewId;

    /**
     * 审核状态 1 待审核, 2 审核中, 3 审核通过, 4 已驳回 5 已撤回 6 已过期
     */
    private Integer status;

    /**
     * 券信息
     */
    private CouponConfigVO couponConfigVO;

    /**
     * 商品配置信息
     */
    private GoodsRuleDetailVO goodsRuleDetailVO;


    /**
     * 申请附件地址
     */
    private	List<ApplyAttachmentVO> applyAttachment;

    /**
     * 审批原因
     */
    private String bpmReason;



}

