package com.xiaomi.nr.coupon.admin.api.dto.coupon.optlog.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 券码下载
 * @Date: 2022.02.25 18:29
 */
@Data
public class CouponCodeDownloadResponse implements Serializable {

    /**
     * 优惠码fds下载地址
     */
    private String downLoadUrl;

    public CouponCodeDownloadResponse(){}
    public CouponCodeDownloadResponse(String url){
        this.downLoadUrl = url;
    }
}
