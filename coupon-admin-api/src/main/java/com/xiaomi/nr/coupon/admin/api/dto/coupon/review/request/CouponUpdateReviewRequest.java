package com.xiaomi.nr.coupon.admin.api.dto.coupon.review.request;

import com.xiaomi.nr.coupon.admin.api.dto.BaseRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.ApplyAttachmentVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponConfigVO;
import lombok.Data;

import java.util.List;

@Data
public class CouponUpdateReviewRequest extends BaseRequest {

    /**
     * 券信息
     */
    private CouponConfigVO couponConfigVO;

    /**
     * 申请附件地址
     */
    private	List<ApplyAttachmentVO> applyAttachment;

    /**
     * 1：校验 2：创建
     */
    private Integer checkOrSubmit;
}

