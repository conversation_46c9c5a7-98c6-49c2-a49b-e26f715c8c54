package com.xiaomi.nr.coupon.admin.api.dto.couponconfig;

import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * @description:
 * @author: heji<PERSON><PERSON>
 * @Date 2023/3/29 2:25 下午
 * @Version: 1.0
 **/
@Data
public class GoodsConfigRelRequest implements Serializable {

    private static final long serialVersionUID = 5181355864764146637L;

    /**
     * 货品或套装列表
     */
    @NotNull(message = "货品或套装列表不能为空")
    @Size(min = 1, max = 30, message = "商品或套装列表个数范围为1-30")
    private List<GoodsItem> goodsItems;

    /**
     * 业务领域 @BizPlatformEnum
     */
    private Integer bizPlatform = BizPlatformEnum.RETAIL.getCode();

    /**
     * 使用渠道
     */
    private List<Integer> useChannel;

}
