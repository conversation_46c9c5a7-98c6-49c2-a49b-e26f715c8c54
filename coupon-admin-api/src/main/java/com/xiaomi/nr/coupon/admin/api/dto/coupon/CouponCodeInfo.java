package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/8/27 11:18
 */
@Data
public class CouponCodeInfo implements Serializable {
    private static final long serialVersionUID = -6188905027964927283L;

    /**
     * 优惠券编号
     */
    @ApiDocClassDefine(value = "id", description = "优惠券编号")
    private Long id;

    /**
     * 加密code
     */
    @ApiDocClassDefine(value = "couponCode", description = "加密code")
    private String couponCode;

    /**
     * 索引，code的md5
     */
    @ApiDocClassDefine(value = "couponIndex", description = "索引，code的md5")
    private String couponIndex;

    /**
     * 优惠券类型编号
     */
    @ApiDocClassDefine(value = "typeId", description = "优惠券类型编号")
    private Long typeId;

    /**
     * 活动编号
     */
    @ApiDocClassDefine(value = "batchId", description = "活动编号")
    private String batchId;

    /**
     * 状态
     */
    @ApiDocClassDefine(value = "stat", description = "状态")
    private String stat;

    /**
     * 用户ID
     */
    @ApiDocClassDefine(value = "userId", description = "用户id")
    private Long userId;

    /**
     * 兑换的优惠ID
     */
    @ApiDocClassDefine(value = "couponId", description = "兑换的优惠ID")
    private Long couponId;

    /**
     * 使用方式（1结算/2兑换）
     */
    @ApiDocClassDefine(value = "useMode", description = "使用方式（1结算/2兑换）")
    private Integer useMode;

    /**
     * 使用时间(或兑换时间)
     */
    @ApiDocClassDefine(value = "useTime", description = "使用时间(或兑换时间)")
    private Long useTime;

    /**
     * 订单号
     */
    @ApiDocClassDefine(value = "orderId", description = "订单号")
    private Long orderId;

    /**
     * 实际抵用金额
     */
    @ApiDocClassDefine(value = "replaceMoney", description = "实际抵用金额")
    private BigDecimal replaceMoney;

    /**
     * 实际减免邮费
     */
    @ApiDocClassDefine(value = "reduceExpress", description = "实际减免邮费")
    private BigDecimal reduceExpress;

    /**
     * 发送类型
     */
    @ApiDocClassDefine(value = "sendType", description = "发送类型")
    private String sendType;

    /**
     * 线下使用
     */
    @ApiDocClassDefine(value = "offline", description = "线下使用")
    private Integer offline;

    /**
     * 添加时间
     */
    @ApiDocClassDefine(value = "addTime", description = "添加时间")
    private Long addTime;

    /**
     * 发券的订单编号
     */
    @ApiDocClassDefine(value = "fromOrderId", description = "发券的订单编号")
    private String fromOrderId;
}
