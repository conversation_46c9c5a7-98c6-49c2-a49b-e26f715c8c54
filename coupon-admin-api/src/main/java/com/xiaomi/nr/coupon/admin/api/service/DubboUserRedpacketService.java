package com.xiaomi.nr.coupon.admin.api.service;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.redpacket.UserRedpacketVO;
import com.xiaomi.nr.coupon.admin.api.dto.redpacket.request.UserRedpacketListRequest;
import com.xiaomi.youpin.infra.rpc.Result;

/**
 * 用户红包查询
 */
public interface DubboUserRedpacketService {


    /**
     * 查询用户红包列表
     * caller 自动化测试
     * @param request
     * @return
     */
    Result<BasePageResponse<UserRedpacketVO>> userRedpacketList(UserRedpacketListRequest request);



}
