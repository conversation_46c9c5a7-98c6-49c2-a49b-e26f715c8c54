package com.xiaomi.nr.coupon.admin.api.service.ecardadmin;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.EcardBaseRequest;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.UserEcardVO;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.request.UpdateUserEcardRequest;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.request.UserEcardListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.response.UserEcardUseInfoResponse;
import com.xiaomi.youpin.infra.rpc.Result;

public interface DubboEcardAdminService {

    /**
     * 查询用户礼品卡信息列表
     *
     * @param request requset
     * @return 礼品卡分页数据
     */
    Result<BasePageResponse<UserEcardVO>> userEcardList(UserEcardListRequest request);

    /**
     * 查询用户礼品卡使用详情
     *
     * @param request requset
     * @return 礼品卡使用详情
     */
    Result<UserEcardUseInfoResponse> userEcardUseInfo(EcardBaseRequest request);

    /**
     * 更新用户礼品卡(延期)
     *
     * @param request requset
     * @return void
     */
    Result<Void> updateUserEcard(UpdateUserEcardRequest request);

}
