package com.xiaomi.nr.coupon.admin.api.dto.pointadmin;

import com.xiaomi.dubbo.validation.annotation.Mid;
import com.xiaomi.nr.coupon.admin.api.dto.BaseRequest;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
public class UserPointCancelRequest extends BaseRequest {

    /**
     * 用户id
     */
    @NotNull
    @Mid
    private long mid;

    /**
     * 用户积分id
     */
    @NotNull(message = "用户积分ID不能为空")
    @Min(value = 1, message = "用户积分ID不合法")
    private long pointId;

    /**
     * 作废原因
     */
    private String invalidReason;


}
