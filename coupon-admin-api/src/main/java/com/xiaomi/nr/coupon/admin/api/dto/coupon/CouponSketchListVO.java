package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CouponSketchListVO implements Serializable {

    /**
     * 草稿id
     */
    private long sketchId;
    /**
     * 券名称
     */
    private	String couponName;
    /**
     * 优惠类型, 1:满减, 2:满折, 3:N元券, 4:立减
     */
    private Integer promotionType;
    /**
     * 优惠值(单位分/折)，如8折，传800
     */
    private long promotionValue;
    /**
     * 门槛类型, 1:满元, 2:满件, 3:每满元, 4:每满件, 5:满元且满件
     */
    private Integer bottomType;
    /**
     * 门槛值满元（单位分)
     */
    private long bottomPrice;
    /**
     * 门槛值满件（单位个)
     */
    private Integer bottomCount;
    /**
     * 领取有效期开始时间
     */
    private Date startFetchTime;
    /**
     * 领取有效期结束时间
     */
    private	Date endFetchTime;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createTime;

    @Override
    public String toString() {
        return "CouponSketchListVO{" +
                "sketchId=" + sketchId +
                ", couponName='" + couponName + '\'' +
                ", promotionType=" + promotionType +
                ", promotionValue=" + promotionValue +
                ", bottomType=" + bottomType +
                ", bottomPrice=" + bottomPrice +
                ", bottomCount=" + bottomCount +
                ", startFetchTime=" + startFetchTime +
                ", endFetchTime=" + endFetchTime +
                ", creator='" + creator + '\'' +
                ", createTime=" + createTime +
                '}';
    }
}
