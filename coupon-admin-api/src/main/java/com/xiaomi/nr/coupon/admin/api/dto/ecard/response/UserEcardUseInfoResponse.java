package com.xiaomi.nr.coupon.admin.api.dto.ecard.response;


import com.xiaomi.nr.coupon.admin.api.dto.ecard.UserEcardLogVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class UserEcardUseInfoResponse implements Serializable {
    private static final long serialVersionUID = -284876899052447255L;

    /**
     * 礼品卡使用详情
     */
    private List<UserEcardLogVO> userEcardLogVOList;

    public UserEcardUseInfoResponse(){}
    public UserEcardUseInfoResponse(List<UserEcardLogVO> userEcardLogVOList){
        this.userEcardLogVOList = userEcardLogVOList;
    }

}
