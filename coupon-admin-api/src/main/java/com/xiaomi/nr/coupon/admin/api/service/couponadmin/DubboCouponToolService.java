package com.xiaomi.nr.coupon.admin.api.service.couponadmin;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponConfigListVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.GoodConfigIdsRequest;
import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.GenAllConfigRedisCacheRequest;
import com.xiaomi.youpin.infra.rpc.Result;

/**
 * @description: 优惠券管理后台服务接口
 * @author: hejiapeng
 * @Date 2022/3/3 12:44 下午
 * @Version: 1.0
 **/

public interface DubboCouponToolService {

    /**
     * 查询商品可用券
     *
     * @param request
     * @return
     */
    Result<BasePageResponse<CouponConfigListVO>> searchGoodConfigId(GoodConfigIdsRequest request);

    /**
     * 券配置刷缓存
     *
     * @return
     */
    Result<Boolean> loadAllCouponConfig(GenAllConfigRedisCacheRequest request);

    /**
     * 商品可用券刷缓存
     *
     * @return
     */
    Result<Boolean> loadAllGoodsCoupon(boolean isFullLoad);

    /**
     * 迁移老券补偿工具
     *
     * @return
     */
    @Deprecated
    Result<Boolean> moveOldCouponByIds(GenAllConfigRedisCacheRequest request);


    /**
     * 优惠券配置预警
     *
     * @return
     */
    Result<Boolean> couponEarlyWarning();


    /**
     * 商品可用券数据导出接口
     *
     * @param request
     * @return
     */
    Result<Integer> exportGoodsCoupon(GoodConfigIdsRequest request);


    /**
     * 索引forceMerge
     *
     * @return
     */
    Result<Boolean> forceMergeIndex();
}