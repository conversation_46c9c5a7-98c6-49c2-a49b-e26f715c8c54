package com.xiaomi.nr.coupon.admin.api.dto.redpacket;

import lombok.Data;

import java.io.Serializable;

@Data
public class UserRedpacketVO implements Serializable {

    /**
     * 用户id
     */
    private long userId;
    /**
     * 红包类型id
     */
    private long typeId;
    /**
     * 红包生效时间
     */
    private long startTime;
    /**
     * 红包的过期时间
     */
    private long endTime;
    /**
     * 用户红包id
     */
    private long redpacketId;
    /**
     * 红包面额，单位分
     */
    private long amount;
    /**
     * 红包余额，单位分
     */
    private long balance;
    /**
     * 领取时间
     */
    private long addTime;

}
