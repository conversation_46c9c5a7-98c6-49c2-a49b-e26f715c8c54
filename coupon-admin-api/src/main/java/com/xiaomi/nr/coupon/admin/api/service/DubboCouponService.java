package com.xiaomi.nr.coupon.admin.api.service;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.GetCouponByIdResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.GetCouponByTypeIdRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.GetCouponByTypeIdResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.GetCouponCodeResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.GetCouponCodeRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.GetCouponByIdRequest;
import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.BatchGetConfigInfoReponse;
import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.BatchGetConfigInfoRequest;
import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.GetConfigInfoReponse;
import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.GetConfigInfoRequest;
import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.GoodsConfigRelRequest;
import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.GoodsConfigRelResponse;
import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.MemberConfigRequest;
import com.xiaomi.youpin.infra.rpc.Result;

import javax.validation.Valid;

/**
 * 优惠券服务
 *
 * <AUTHOR>
 */
public interface DubboCouponService {

    /**
     * 查询优惠券信息
     *
     * @param request
     * @return
     */
    Result<GetConfigInfoReponse> getCouponConfigInfo(GetConfigInfoRequest request);

    /**
     * 查询优惠券信息
     *
     * @param request
     * @return
     */
    Result<BatchGetConfigInfoReponse> batchGetCouponConfigInfo(BatchGetConfigInfoRequest request);


    /**
     * 查询商品可用券配置
     * caller: 凑单页
     *
     * @param request
     * @return
     */
    Result<GoodsConfigRelResponse> getGoodsCouponConfigRel(GoodsConfigRelRequest request);


    /**
     * 会员pro权益开关、黑名单信息同步接口
     *
     * @param request
     * @return
     */
    Result<Boolean> pushProMemberConfig(MemberConfigRequest request);

    /**
     * 获取优惠券码信息（财务系统使用）
     *
     * @param request 包含获取优惠券码信息所需参数的请求对象
     * @return 包含优惠券码信息的响应结果
     */
    Result<GetCouponCodeResponse> getCouponCodeInfo(@Valid GetCouponCodeRequest request);

    /**
     * 根据优惠券ID获取优惠券信息（财务系统使用）
     *
     * @param request 包含优惠券ID的请求对象
     * @return 包含优惠券信息的响应结果
     */
    Result<GetCouponByIdResponse> getCouponById(@Valid GetCouponByIdRequest request);


    /**
     * 根据类型ID获取优惠券（财务系统使用）
     *
     * @param request 包含类型ID的请求对象
     * @return 包含优惠券信息的响应结果
     */
    Result<GetCouponByTypeIdResponse> getCouponByTypeId(@Valid GetCouponByTypeIdRequest request);

}
