package com.xiaomi.nr.coupon.admin.api.dto.pointadmin;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 积分批次数据统计
 *
 * <AUTHOR>
 * @date 2023/12/6
 */
@Data
public class PointsBatchDataStatRequest implements Serializable {
    private static final long serialVersionUID = 7653474172590359209L;

    /**
     * 批次id
     */
    @NotNull(message = "批次id不能为空")
    @Min(value = 1, message = "批次id需大于0")
    private Long batchId;

}
