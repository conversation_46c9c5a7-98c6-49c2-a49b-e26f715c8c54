package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/27 15:06
 */
@Data
public class GetCouponByIdRequest implements Serializable {
    private static final long serialVersionUID = -1846524844491587463L;

    /**
     * id列表
     */
    @NotNull(message = "idList不能为空")
    @Size(max = 50, message = "id数量不能超过50")
    @ApiDocClassDefine(value = "idList", description = "id列表")
    private List<String> idList;
}
