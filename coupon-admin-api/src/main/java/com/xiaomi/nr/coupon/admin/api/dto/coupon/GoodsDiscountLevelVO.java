package com.xiaomi.nr.coupon.admin.api.dto.coupon;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class GoodsDiscountLevelVO implements Serializable {

    /**
     * 折扣力度警戒值 8.5折
     */
    private BigDecimal discountLevel;

    /**
     * 是否是低于折扣力度
     */
    private boolean hasLowLevelGoods;

    /**
     * 当前折扣力度 8.5折
     */
    private BigDecimal nowDiscountLevel;

    /**
     * 低于折扣的sku商品
     */
    private List<GoodsInfoVO> skuInfoVOList;

    /**
     * 低于折扣的套装商品
     */
    private List<GoodsInfoVO> packageInfoVOList;
}
