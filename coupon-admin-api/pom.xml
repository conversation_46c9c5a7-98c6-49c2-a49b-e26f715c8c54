<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <artifactId>coupon-admin</artifactId>
        <groupId>com.xiaomi.nr</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>coupon-admin-api</artifactId>
    <version>${coupon.admin.api.version}</version>

      <distributionManagement>
           <repository>
               <id>central</id>
               <name>maven-release-virtual</name>
               <url>https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual</url>
           </repository>
           <snapshotRepository>
               <id>snapshots</id>
               <name>maven-snapshot-virtual</name>
               <url>https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual</url>
           </snapshotRepository>
       </distributionManagement>

    <dependencies>
        <dependency>
            <groupId>com.xiaomi.youpin</groupId>
            <artifactId>youpin-infra-rpc</artifactId>
            <version>2.0.0-CNZONE-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.youpin</groupId>
            <artifactId>coloregg</artifactId>
            <version>1.4-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.youpin</groupId>
            <artifactId>aop</artifactId>
            <version>1.4-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.youpin</groupId>
            <artifactId>common</artifactId>
            <version>1.7-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
            <version>2.7.0-mone-v14-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>xiaomi-dubbo-validator</artifactId>
            <version>2.0.1-SNAPSHOT</version>
            <scope>provided</scope>
        </dependency>

        <!-- 翻译平台sdk(热更新版本)-->
        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>global-nr-dev-common</artifactId>
            <version>1.1-SNAPSHOT</version>
        </dependency>

        <!-- 国际化标准sdk-区域主数据 -->
        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>i18n-area-java-sdk</artifactId>
            <version>${i18n-area.version}</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.newretail</groupId>
            <artifactId>basic-tools</artifactId>
            <version>1.2.0</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.mone</groupId>
            <artifactId>miapi-doc-annos</artifactId>
            <version>2.7.12-mone-v8-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.mone</groupId>
            <artifactId>dubbo-docs-annotations</artifactId>
            <version>2.7.12-mone-v8-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.mone</groupId>
            <artifactId>dubbo-docs-core</artifactId>
            <version>2.7.12-mone-v8-SNAPSHOT</version>
        </dependency>
    </dependencies>
</project>