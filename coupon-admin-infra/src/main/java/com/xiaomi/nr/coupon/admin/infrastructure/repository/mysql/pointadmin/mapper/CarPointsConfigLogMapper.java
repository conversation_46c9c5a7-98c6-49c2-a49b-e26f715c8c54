package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.pointadmin.mapper;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.pointadmin.po.CarPointsConfigLogPo;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/6 17:18
 */
@Mapper
@Component
public interface CarPointsConfigLogMapper {

    /**
     * 插入日志
     *
     * @return
     */
    @Insert("insert into car_points_config_log (" +
            "   batch_id, opt_type, operator, content" +
            ")" +
            " values (" +
            "   #{batchId}, #{optType}, #{operator}, #{content}" +
            ")")
    Integer insert(CarPointsConfigLogPo po);

    /**
     * 查询最近一次修改人
     *
     * @return
     */
    @Select("select operator " +
            "from car_points_config_log " +
            "where batch_id = #{batchId} " +
            "order by id desc " +
            "limit 1")
    String selectUpdater(Long batchId);


    /**
     * 根据批次Id查询日志总数
     * @param batchId
     * @return
     */
    @Select("select count(*) from car_points_config_log where batch_id = #{batchId}")
    Integer getCountByBatchId(Long batchId);

    /**
     * 根据批次ID分页查询日志信息
     * @param batchId
     * @param offset
     * @param limit
     * @return
     */
    @Select("select * from car_points_config_log where batch_id = #{batchId} order by id desc limit #{offset},#{limit}")
    List<CarPointsConfigLogPo> getLogByPage(@Param("batchId") Long batchId, @Param("offset") int offset, @Param("limit") int limit);


}
