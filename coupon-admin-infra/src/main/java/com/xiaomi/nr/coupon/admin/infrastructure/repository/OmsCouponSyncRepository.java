package com.xiaomi.nr.coupon.admin.infrastructure.repository;

import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.EventContext;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponConfigStatusEnum;
import com.xiaomi.nr.coupon.admin.enums.scene.SceneCodeEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.xmstore.XmStoreCouponInfoMapper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.xmstore.XmStoreCouponMapper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.xmstore.po.XmStoreCouponConvert;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.xmstore.po.XmStoreCouponInfoPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.xmstore.po.XmStoreCouponPO;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * @description: 优惠券配置资源库
 * @author: hejiapeng
 * @Date 2022/3/1 10:42 上午
 * @Version: 1.0
 **/
@Component
@Slf4j
public class OmsCouponSyncRepository {

    @Autowired
    private XmStoreCouponMapper xmStoreCouponMapper;

    @Autowired
    private XmStoreCouponInfoMapper xmStoreCouponInfoMapper;

    @Autowired
    private XmStoreCouponConvert xmStoreCouponConvert;


    /**
     * 更新OMS数据库数据
     *
     * @param couponConfigPO
     * @throws Exception
     */
    @Transactional(transactionManager = "xmStoreTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public void insertXmStoreCoupon(CouponConfigPO couponConfigPO, EventContext eventContext) {

        List<XmStoreCouponPO> xmStoreCouponPOs = xmStoreCouponConvert.serializeXmStoreCouponPO(couponConfigPO, eventContext.getSkuInfoDtos(), eventContext.getBatchedInfoDtos());


        for (XmStoreCouponPO xmStoreCouponPO : xmStoreCouponPOs) {
            xmStoreCouponMapper.insertXmStoreCoupon(xmStoreCouponPO);
        }
        if (SceneCodeEnum.COMPENSATE_SCENE.getCode().equals(couponConfigPO.getSendScene())) {
            List<XmStoreCouponInfoPO> xmStoreCouponInfoPOList = xmStoreCouponConvert.serializeXmStoreCouponInfoPO(couponConfigPO);
            log.info("insertXmStoreCoupon.xmStoreCouponInfoPOList:{}", xmStoreCouponInfoPOList);
            //此处有特殊逻辑，超过1个渠道被认为不合业务场景要求
            if (CollectionUtils.isNotEmpty(xmStoreCouponInfoPOList) && xmStoreCouponInfoPOList.size() == 1) {
                xmStoreCouponInfoMapper.batchInsert(xmStoreCouponInfoPOList);
            }
        }
    }


    /**
     * 更新OMS数据库数据
     *
     * @param couponConfigPO
     * @throws Exception
     */
    @Transactional(transactionManager = "xmStoreTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public void updateXmStoreCoupon(CouponConfigPO couponConfigPO, EventContext eventContext) {

        List<XmStoreCouponPO> xmStoreCouponPOs = xmStoreCouponConvert.serializeXmStoreCouponPO(couponConfigPO, eventContext.getSkuInfoDtos(), eventContext.getBatchedInfoDtos());
        for (XmStoreCouponPO xmStoreCouponPO : xmStoreCouponPOs) {
            xmStoreCouponMapper.updateXmStoreCoupon(xmStoreCouponPO);
        }

        if (SceneCodeEnum.COMPENSATE_SCENE.getCode().equals(couponConfigPO.getSendScene())) {
            List<XmStoreCouponInfoPO> xmStoreCouponInfoPOList = xmStoreCouponConvert.serializeXmStoreCouponInfoPO(couponConfigPO);
            log.info("updateXmStoreCoupon.xmStoreCouponInfoPOList:{}", xmStoreCouponInfoPOList);
            if (CollectionUtils.isNotEmpty(xmStoreCouponInfoPOList) && xmStoreCouponInfoPOList.size() == 1) {
                for (XmStoreCouponInfoPO xmStoreCouponInfoPO : xmStoreCouponInfoPOList) {
                    if (checkXmStoreCouponInfo(xmStoreCouponInfoPO.getClassId())) {
                        xmStoreCouponInfoMapper.update(xmStoreCouponInfoPO);
                    } else {
                        xmStoreCouponInfoMapper.batchInsert(xmStoreCouponInfoPOList);
                    }
                }
            }
        }
    }

    /**
     * 更新OMS数据库数据
     *
     * @param couponConfigPO
     * @throws Exception
     */
    @Transactional(transactionManager = "xmStoreTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public void updateXmStoreCouponStatus(CouponConfigPO couponConfigPO) {
        log.info("updateXmStoreCouponStatus begin id:{} status:{}", couponConfigPO.getId(), couponConfigPO.getStatus());
        int status = 5;
        if (couponConfigPO.getStatus() == CouponConfigStatusEnum.ONLINE.code) {
            status = 1;
        }
        xmStoreCouponMapper.updateXmStoreCouponStatus(status, couponConfigPO.getId());
        if (SceneCodeEnum.COMPENSATE_SCENE.getCode().equals(couponConfigPO.getSendScene())) {
            List<XmStoreCouponInfoPO> xmStoreCouponInfoPOList = xmStoreCouponConvert.serializeXmStoreCouponInfoPO(couponConfigPO);
            log.info("updateXmStoreCoupon.xmStoreCouponInfoPOList:{}", xmStoreCouponInfoPOList);
            if (CollectionUtils.isNotEmpty(xmStoreCouponInfoPOList) && xmStoreCouponInfoPOList.size() == 1) {
                for (XmStoreCouponInfoPO xmStoreCouponInfoPO : xmStoreCouponInfoPOList) {
                    if (checkXmStoreCouponInfo(xmStoreCouponInfoPO.getClassId())) {
                        xmStoreCouponInfoMapper.update(xmStoreCouponInfoPO);
                    } else {
                        xmStoreCouponInfoMapper.batchInsert(xmStoreCouponInfoPOList);
                    }
                }
            }
        }
    }

    /**
     * 判断OMS中是否已经存在改券
     * @param configId
     * @return
     */
    public boolean checkXmStoreMarketCoupon(Long configId){
        Long marketCouponId = xmStoreCouponMapper.getXmStoreCouponIdByConfigId(configId);
        return Optional.ofNullable(marketCouponId).orElse(0L) > 0;
    }

    public boolean checkXmStoreCouponInfo(Long configId) {
        Long classId = xmStoreCouponInfoMapper.getXmStoreCouponInfoIdByConfigId(configId);
        return Optional.ofNullable(classId).orElse(0L) > 0;
    }

}
