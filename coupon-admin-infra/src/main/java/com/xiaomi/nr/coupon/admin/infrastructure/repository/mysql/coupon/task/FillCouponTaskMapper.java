package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.task;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.task.po.CodeTaskSearchParam;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.task.po.FillCouponTaskPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.task.po.SearchTaskParam;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.task.po.TaskIdParentIdParamPO;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 灌券任务操作mapper
 */
@Mapper
@Component
public interface FillCouponTaskMapper {


    String TASK_TABLE_NAME = "nr_coupon_task";

    /**
     * 创建灌券任务
     * @param fillCouponTaskPO 灌券任务实体
     * @return 写入条数
     */
    @Insert("insert into "+ TASK_TABLE_NAME +" (status,type,priority,parent_id,params,source,offset,process_rate,process_desc," +
            "create_time,start_time,config_id,update_time,department_id,creator)" +
            " values " +
            "(#{status},#{type},#{priority},#{parentId},#{params},#{source},#{offset},#{processRate},#{processDesc}," +
            "#{createTime},#{startTime},#{configId},#{updateTime},#{departmentId},#{creator})")
    Integer insertTask(FillCouponTaskPO fillCouponTaskPO);




    /**
     * 根据父任务id获取子任务信息
     * @param parentId 父任务id
     * @return 任务信息
     */
    @Select("select * from "+ TASK_TABLE_NAME +" where parent_id=#{parentId} order by task_id desc")
    List<FillCouponTaskPO> getTaskByParentId(@Param("parentId") long parentId);


    /**
     * 根据参数查询灌券任务
     *
     * @param searchTaskParam
     * @return
     */
    @Select("<script>" +
            "select * from "+ TASK_TABLE_NAME +" where parent_id=0 and type=#{type} and biz_platform in (${bizType}) "+
            "<if test='taskId != null'> and task_id=#{taskId} </if>"+
            "<if test='configIds != null'> and config_id in "+
            "<foreach collection='configIds' item='configId' index='index' open='(' close=')' separator=','>#{configId}</foreach></if>"+
            "<if test='statusList != null'> and status in "+
            "<foreach collection='statusList' item='status' index='index' open='(' close=')' separator=','>#{status}</foreach></if>"+
            "<if test='startTime!=null'> and create_time &gt;= #{startTime}</if>"+
            "<if test='endTime!=null'> and create_time &lt;= #{endTime}</if>"+
            "<if test='creator!=null'> and creator=#{creator} </if>"+
            "<if test='areaId!=null'> and area_id=#{areaId} </if>"+
            " order by ${orderBy}  ${orderDirection}"+
            "</script>")
    List<FillCouponTaskPO> getFillCouponTaskByParam(SearchTaskParam searchTaskParam);

    /**
     * 根据参数查询任务
     *
     * @param codeTaskSearchParam
     * @return
     */
    @Select("<script>" +
            "select * from "+ TASK_TABLE_NAME +" where type=#{codeTaskSearchParam.type}" +
            "<if test='codeTaskSearchParam.configId != null'> and config_id=#{codeTaskSearchParam.configId} </if>"+
            "<if test='codeTaskSearchParam.couponName != null'> and task_name like concat(concat('%',#{codeTaskSearchParam.couponName}),'%') </if>"+
            "<if test='codeTaskSearchParam.status != null'> and status in " +
            "<foreach collection='codeTaskSearchParam.status' item='status' index='index' open='(' close=')' separator=','>#{status}</foreach> </if>"+
            "order by ${codeTaskSearchParam.orderBy} ${codeTaskSearchParam.orderDirection} limit #{offset}, #{limit}" +
            "</script>")
    List<FillCouponTaskPO> getTaskPageByParam(@Param("codeTaskSearchParam") CodeTaskSearchParam codeTaskSearchParam, @Param("offset")int offset, @Param("limit") int limit);


    @Select("<script>" +
            "select count(*) from "+ TASK_TABLE_NAME +" where type=#{type}" +
            "<if test='configId != null'> and config_id=#{configId} </if>" +
            "<if test='couponName != null'> and task_name like concat(concat('%',#{couponName}),'%') </if>"+
            "<if test='status != null'> and status in" +
            "<foreach collection='status' item='statu' index='index' open='(' close=')' separator=','>#{statu}</foreach> </if>" +
            "</script>")
    Integer getCouponTaskCountByParam(CodeTaskSearchParam codeTaskSearchParam);




    /**
     * 根据父任务id批量获取子任务
     *
     * @param parentIds
     * @return
     */
    @Select("<script>select * from "+ TASK_TABLE_NAME +" where parent_id in "
            + "<foreach collection='parentIds' item='parentId' index='index' open='(' close=')' separator=','>#{parentId}</foreach> order by parent_id asc, task_id desc"
            + "</script>")
    List<FillCouponTaskPO> getFillCouponTaskByParentIds(@Param("parentIds") List<Long> parentIds);


    /**
     * 获取等待任务id和父id
     * @return
     */
    @Select("select task_id, parent_id,params from "+ TASK_TABLE_NAME +" where type=11 and status in (0,1) " +
            "union select task_id, parent_id, params from "+ TASK_TABLE_NAME +" where type=11 and status = 3 and retry_times < 3 " +
            "union select task_id, parent_id, params from "+ TASK_TABLE_NAME +" where type=11 and status = 2 and update_time < unix_timestamp(NOW()) - #{thresholdTime}")
    List<TaskIdParentIdParamPO> getReadyTask(@Param("thresholdTime") long thresholdTime);

    /**
     * 获取运行中的任务数
     */
    @Select("select count(*) from "+ TASK_TABLE_NAME +" where type=11 and status = 2 and update_time >= unix_timestamp(NOW()) - #{thresholdTime}")
    Integer getRunningCount(@Param("thresholdTime") long thresholdTime);

    /**
     * 批量任务id查询灌券任务信息
     * @param taskIds 灌券任务id
     * @return    灌券任务列表
     */
    @Select("<script>" +
            "    select " +
            "        *" +
            "    from " +
            TASK_TABLE_NAME +
            "    where " +
            "        task_id in" +
            "        <foreach item='task_id' index='index' collection='taskIds' open='(' separator=',' close=')'>" +
            "            #{task_id}" +
            "        </foreach> " +
            "</script>")
    List<FillCouponTaskPO> getTaskByIds(@Param("taskIds")List<Long> taskIds);


    /**
     * 批量任务id查询灌券任务信息(排序)
     * @param taskIds   灌券任务id
     * @param sortField 排序字段
     * @param sortValue 排序方式(asc/desc)
     * @return 灌券任务列表
     */
    @Select("<script>" +
            "    select " +
            "        *" +
            "    from " +
            TASK_TABLE_NAME +
            "    where " +
            "        task_id in" +
            "        <foreach item='task_id' index='index' collection='taskIds' open='(' separator=',' close=')'>" +
            "            #{task_id}" +
            "        </foreach> " +
            " order by ${sortField}  ${sortValue}" +
            "</script>")
    List<FillCouponTaskPO> getTaskByIdsSort(@Param("taskIds")List<Long> taskIds, @Param("sortField") String sortField, @Param("sortValue") String sortValue);




    /**
     * 创建任务落库
     *
     * @param fillCouponTaskPO
     * @return
     */
    @Options(useGeneratedKeys = true, keyProperty = "taskId", keyColumn = "task_id")
    @Insert("insert into "+ TASK_TABLE_NAME +" (task_name,status,type,priority,parent_id,params,source,create_time,start_time," +
            "config_id,department_id,creator,biz_platform,area_id,workflow_id)" +
            " values " +
            "(#{taskName},#{status},#{type},#{priority},#{parentId},#{params},#{source},#{createTime},#{startTime}," +
            "#{configId},#{departmentId},#{creator},#{bizPlatform},#{areaId},#{workflowId})")
    Integer insert(FillCouponTaskPO fillCouponTaskPO);


    /**
     * 根据任务id查询灌券任务信息
     *
     * @param taskId 灌券任务id
     * @return FillCouponTaskPO
     */
    @Select("select * from "+ TASK_TABLE_NAME +" where task_id=#{taskId}")
    FillCouponTaskPO getDetailTaskById(@Param("taskId") long taskId);


    /**
     * 更改灌券任务状态
     *
     * @param taskId 灌券任务id
     * @return int
     */
    @Update("update "+ TASK_TABLE_NAME +" set status=#{status} where task_id=#{taskId}")
    Integer updateTaskStatusById(@Param("status") int status, @Param("taskId") long taskId);


    /**
     * 根据任务id查询灌券任务信息
     *
     * @param configId 券配置id
     * @return FillCouponTaskPO
     */
    @Select("select task_id from "+ TASK_TABLE_NAME +" where config_id=#{configId} and status in(0,1,2)")
    List<Long> getTaskByConfigId(@Param("configId") long configId);

    /**
     * 根据任务id查询灌券参数信息
     *
     * @param taskId 灌券任务id
     * @return String
     */
    @Select("select params from "+ TASK_TABLE_NAME +" where task_id=#{taskId}")
    String getTaskParamById(@Param("taskId") long taskId);

    @Select("select count(1) from "+ TASK_TABLE_NAME +" where config_id=#{configId} and type=21 and status=-2")
    int getPreTask(@Param("configId") long configId);

    @Update("update "+ TASK_TABLE_NAME +" set status=0 where config_id=#{configId} and type=21 and status=-2")
    Integer updateTaskStatusByCoupon(@Param("configId") long configId);

    @Select("<script>" +
            "select config_id, result from "+ TASK_TABLE_NAME +
            " where type=21 " +
            "<if test='configIds != null'> and config_id in " +
            "        <foreach item='id' index='index' collection='configIds' open='(' separator=',' close=')'>" +
            "            #{id}" +
            "        </foreach> " +
            "</if>" +
            " limit 1000" +
            "</script>")
    List<FillCouponTaskPO> getCodeTaskResult(@Param("configIds") List<Long> configIds);


}
