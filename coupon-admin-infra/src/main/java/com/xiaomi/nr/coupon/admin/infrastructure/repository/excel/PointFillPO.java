package com.xiaomi.nr.coupon.admin.infrastructure.repository.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * @description 灌积分上传文件
 * <AUTHOR>
 * @date 2024-08-15 20:02
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PointFillPO {

    public static final int HEADROWNUMBER = 1;

    /**
     * 用户id
     */
    @ExcelProperty("mid")
    private Long mid;

    /**
     * 积分发放数量
     */
    @ExcelProperty("积分发放数量")
    private Long pointCount;

    @Override
    public String toString() {
        return this.mid + "," + this.pointCount;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PointFillPO that = (PointFillPO) o;
        return Objects.equals(mid, that.mid) &&
                Objects.equals(pointCount, that.pointCount);
    }

    @Override
    public int hashCode() {
        return Objects.hash(mid, pointCount);
    }
}
