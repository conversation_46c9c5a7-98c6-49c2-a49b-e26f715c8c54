package com.xiaomi.nr.coupon.admin.infrastructure.repository;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.ecard.EcardLogMapper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.ecard.EcardMapper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.ecardconfig.po.EcardLogPo;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.ecardconfig.po.EcardPo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
public class EcardRepository {

    @Autowired
    private EcardMapper ecardMapper;

    @Autowired
    private EcardLogMapper ecardLogMapper;

    public List<EcardPo> getEcardById(Long userId, Long cardId) {
        return ecardMapper.queryByCardUserId(userId, cardId);
    }

    public EcardPo getEcardByCardId(Long userId, Long cardId) {
        return ecardMapper.queryByCardId(userId, cardId);
    }

    public List<EcardLogPo> getEcardLogById(Long userId, Long cardId) {
        return ecardLogMapper.queryByUserCardId(userId, cardId);
    }

    @Transactional(transactionManager = "xmPulseWriteTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public void updateTime(Long cardId, Long delayTime, EcardLogPo logPo) throws Exception {

        ecardMapper.updateEcardEndTime(cardId, String.valueOf(delayTime));

        ecardLogMapper.insertEcardLog(logPo);
    }

}
