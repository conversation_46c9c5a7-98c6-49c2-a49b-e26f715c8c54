package com.xiaomi.nr.coupon.admin.infrastructure.repository;

import com.xiaomi.nr.coupon.admin.domain.pointadmin.event.entity.PointBatchConfigCreateEvent;
import com.xiaomi.nr.coupon.admin.domain.pointadmin.event.entity.PointBatchConfigUpdateEvent;
import com.xiaomi.nr.coupon.admin.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.pointadmin.mapper.CarPointsBatchConfigMapper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.pointadmin.po.CarPointsBatchConfigPo;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.pointadmin.PointBatchConfigRedisDao;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.pointadmin.po.PointBatchConfigCachePo;
import com.xiaomi.nr.coupon.admin.infrastructure.rpc.PointBudgetService;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/6 11:00
 */
@Component
@Slf4j
public class PointConfigRepository {

    @Autowired
    private CarPointsBatchConfigMapper carPointsBatchConfigMapper;

    @Autowired
    private PointBatchConfigRedisDao pointBatchConfigRedisDao;

    @Autowired
    private PointBudgetService pointBudgetService;

    /**
     * 插入批次配置数据
     *
     * @param batchConfigPo batchConfigPo
     * @param event         event
     * @return batchId
     */
    @Transactional(transactionManager = "nrPointAdminTransactionManager", rollbackFor = {Exception.class})
    public long insert(CarPointsBatchConfigPo batchConfigPo, PointBatchConfigCreateEvent event) throws BizError {
        int effectRows = carPointsBatchConfigMapper.insert(batchConfigPo);

        if (effectRows <= 0) {
            log.error("PointConfigRepository.insert 积分批次配置数据插入失败, batchConfigPo = {}, event = {}", batchConfigPo, event);
            throw ExceptionHelper.create(ErrCode.POINT, "积分批次配置数据插入失败");
        }

        // 更新批次配置缓存
        updatePointBatchConfigCache(batchConfigPo);

        // 更新批次积分已发放的额度
        updatePointBatchDistributeCache(batchConfigPo, 0);

        event.setData(batchConfigPo);
        //占用br预算金额
        if (StringUtils.isNotEmpty(batchConfigPo.getBrApplyNo())) {
            pointBudgetService.preOccupyBudget(batchConfigPo);
        }
        return batchConfigPo.getId();
    }

    /**
     * 更新积分批次配置缓存数据
     *
     * @param batchConfigPo batchConfigPo
     */
    public void updatePointBatchConfigCache(CarPointsBatchConfigPo batchConfigPo) throws BizError {
        if (Objects.isNull(batchConfigPo)) {
            return;
        }

        PointBatchConfigCachePo cachePo = new PointBatchConfigCachePo();
        BeanUtils.copyProperties(batchConfigPo, cachePo);
        cachePo.setTotalPoints(batchConfigPo.getApplyCount());

        pointBatchConfigRedisDao.setPointBatchConfigCache(cachePo);
    }

    public void updatePointBatchConfigCache(List<CarPointsBatchConfigPo> batchConfigPoList) throws BizError {
        if (CollectionUtils.isEmpty(batchConfigPoList)) {
            return;
        }

        List<PointBatchConfigCachePo> configCachePoList = Lists.newArrayList();

        for (CarPointsBatchConfigPo batchConfigPo : batchConfigPoList) {
            PointBatchConfigCachePo cachePo = new PointBatchConfigCachePo();
            BeanUtils.copyProperties(batchConfigPo, cachePo);
            cachePo.setTotalPoints(batchConfigPo.getApplyCount());

            configCachePoList.add(cachePo);
        }

        pointBatchConfigRedisDao.setPointBatchConfigCache(configCachePoList);
    }

    public void updatePointBatchDistributeCache(CarPointsBatchConfigPo batchConfigPo, int value) throws BizError {
        if ((Objects.isNull(batchConfigPo) || value < 0)) {
            return;
        }

        pointBatchConfigRedisDao.updatePointBatchDistributeCache(batchConfigPo.getId(), value);
    }

    /**
     * 根据批次配置id查询
     *
     * @param batchId batchId
     * @return 积分批次配置
     */
    public CarPointsBatchConfigPo findById(Long batchId) {
        return carPointsBatchConfigMapper.findById(batchId);
    }

    /**
     * 批量查询批次配置信息
     *
     * @param batchIdList List
     * @return Map
     */
    public Map<Long, CarPointsBatchConfigPo> batchGetConfigInfo(List<Long> batchIdList) {
        List<CarPointsBatchConfigPo> r = carPointsBatchConfigMapper.batchGetConfigInfo(batchIdList);
        return r.stream().collect(Collectors.toMap(CarPointsBatchConfigPo::getId, e -> e, (k1, k2) -> k2));
    }

    @Transactional(transactionManager = "nrPointAdminTransactionManager", rollbackFor = {Exception.class})
    public long update(CarPointsBatchConfigPo oldPo, CarPointsBatchConfigPo batchConfigPo, PointBatchConfigUpdateEvent event) throws Exception {

        update(batchConfigPo);

        event.setData(batchConfigPo);

        // 更新批次配置缓存
        updatePointBatchConfigCache(batchConfigPo);

        // 进行br预算金额的追加、返还
        handleBr(oldPo, batchConfigPo);
        return batchConfigPo.getId();
    }

    /**
     * 处理br增加、返还
     *
     * @param
     * @return
     */
    public void handleBr(CarPointsBatchConfigPo oldPo, CarPointsBatchConfigPo batchConfigPo) throws Exception {
        if (StringUtils.isEmpty(batchConfigPo.getBrApplyNo())) {
            return;
        }
        // 进行br预算金额的追加、返还
        long gap = batchConfigPo.getApplyCount() - oldPo.getApplyCount();
        if (gap > 0) {
            //追加预算
            pointBudgetService.addBudget(batchConfigPo, gap);
        } else if (gap < 0) {
            //返还预算
            pointBudgetService.reduceBudget(batchConfigPo, -gap);
        }
    }

    /**
     * 更新po
     *
     * @param batchConfigPo po
     * @throws BizError bizError
     */
    public void update(CarPointsBatchConfigPo batchConfigPo) throws BizError {

        int effectRows = carPointsBatchConfigMapper.update(batchConfigPo);

        if (effectRows <= 0) {
            log.error("PointConfigRepository.update 积分批次配置数据更新失败, batchConfigPo = {}", batchConfigPo);
            throw ExceptionHelper.create(ErrCode.POINT, "积分批次配置数据更新失败");
        }
    }

    /**
     * 根据周期状态类型查询数据
     *
     * @param periodStatus 周期状态类型  1-进行中 2-未开始 3-已结束
     * @param queryTime    查询时间
     * @param id           id
     * @param name         name模糊查询
     * @return 批次列表
     */
    public List<CarPointsBatchConfigPo> selectByPeriodStatus(Integer periodStatus, Long queryTime, Long id, String name) {
        return carPointsBatchConfigMapper.selectByPeriodStatus(periodStatus, queryTime, id, name);
    }

    /**
     * 获取当前时间所有可用的积分批次配置
     *
     * @return 积分批次配置
     */
    public List<CarPointsBatchConfigPo> findAllValidPointConfig() {
        long currentTimestamp = System.currentTimeMillis() / 1000;
        return carPointsBatchConfigMapper.findAllValidPointConfig(currentTimestamp);
    }

    /**
     * 获取积分批次配置缓存
     *
     * @param batchId batchId
     * @return 积分批次配置缓存
     */
    public PointBatchConfigCachePo getPointBatchConfigCache(Long batchId) {
        return pointBatchConfigRedisDao.getPointBatchConfigCache(batchId);
    }

    /**
     * 获取积分批次已发数量缓存
     *
     * @param batchId batchId
     * @return 已发数量
     */
    public Long getPointBatchDistributeCache(Long batchId) {
        return pointBatchConfigRedisDao.getPointBatchDistributeCache(batchId);
    }

    /**
     * 获取积分批次已发数量缓存
     *
     * @param batchIdList batchIdList
     * @return 已发数量 key:batchId,value:已发数量
     */
    public Map<Long, Long> getPointBatchDistributeCache(List<Long> batchIdList) {
        return pointBatchConfigRedisDao.getPointBatchDistributeCache(batchIdList);
    }

    /**
     * 扫描上线并且进行中的记录列表方法
     *
     * @param startId   起始id，不包含当前id
     * @param queryTime 查询时间
     * @param batchSize 批大小
     * @return 记录列表
     */
    public List<CarPointsBatchConfigPo> selectOnlineInProgressBatch(long startId, long queryTime, int batchSize) {
        return carPointsBatchConfigMapper.selectOnlineInProgressBatch(startId, queryTime, batchSize);
    }

    /**
     * 扫描已过期的记录列表方法
     *
     * @param startId   起始id，不包含当前id
     * @param queryTime 查询时间
     * @param batchSize 批大小
     * @return 记录列表
     */
    public List<CarPointsBatchConfigPo> selectCompletedBatch(long startId, long queryTime, int batchSize) {
        return carPointsBatchConfigMapper.selectCompletedBatch(startId, queryTime, batchSize);
    }

    /**
     * 有效积分列表查询
     *
     * @param batchIdList   批次id列表
     * @param sceneCodeList     场景编码
     * @param onlyAvailable 只返回有效批次
     * @param queryTime     查询时间
     * @return 有效积分批次列表
     */
    public List<CarPointsBatchConfigPo> selectAvailableBatch(List<Long> batchIdList,
                                                             List<String> sceneCodeList,
                                                             Boolean onlyAvailable,
                                                             Long queryTime) {
        return carPointsBatchConfigMapper.selectAvailableBatch(batchIdList, sceneCodeList, onlyAvailable, queryTime);
    }

    @Transactional(transactionManager = "nrPointAdminTransactionManager", rollbackFor = {Exception.class})
    public void budgetRelease(CarPointsBatchConfigPo batchConfigPo) throws BizError {

        long releaseCount = batchConfigPo.getApplyCount() - batchConfigPo.getSendCount();

        // 先更新DB后调用接口释放预算，释放预算失败后进行回滚
        // 1、更新释放总额字段
        CarPointsBatchConfigPo updatePo = new CarPointsBatchConfigPo();
        updatePo.setId(batchConfigPo.getId());
        updatePo.setReleaseCount(releaseCount);
        update(updatePo);

        // 2、调用总额释放
        log.info("call EMS Open API release budget, batchId {}, budgetId {}, releaseCount {}", batchConfigPo.getId(), batchConfigPo.getBudgetId(), releaseCount);
        pointBudgetService.reduceBudget(batchConfigPo, releaseCount);
    }

    /**
     * 获取存在时间交集，处于上线状态的积分批次配置
     *
     * @param startTime startTime
     * @param endTime   endTime
     * @param sendScene sendScene
     * @param batchId   batchId
     * @return 积分批次配置列表
     */
    public List<CarPointsBatchConfigPo> findTimeIntersectionPointConfig(Long startTime, Long endTime, String sendScene, Long batchId) {
        return carPointsBatchConfigMapper.findTimeIntersectionPointConfig(startTime, endTime, sendScene, batchId);
    }

    /**
     * 根据积分批次名称获取积分批次id
     *
     * @param batchName batchName
     * @return 积分批次id列表
     */
    public List<Long> getBatchConfigIdByName(String batchName) {
        return carPointsBatchConfigMapper.getBatchConfigIdByName(batchName);
    }

    public List<CarPointsBatchConfigPo> getBathcConfigByIds(List<Long> batchIdList) {
        return carPointsBatchConfigMapper.getBatchConfigByIds(batchIdList);
    }
}
