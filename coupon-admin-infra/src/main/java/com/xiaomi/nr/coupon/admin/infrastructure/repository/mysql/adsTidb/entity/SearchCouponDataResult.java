package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.adsTidb.entity;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.adsTidb.po.CouponStatisticPo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SearchCouponDataResult implements Serializable {

    /**
     * 总数据
     */
    private Integer totalCnt = 0;

    /**
     * 总页数
     */
    private Integer totalPage = 0;

    /**
     * 券分析数据
     */
    private List<CouponStatisticPo> couponStatisticPos;


}
