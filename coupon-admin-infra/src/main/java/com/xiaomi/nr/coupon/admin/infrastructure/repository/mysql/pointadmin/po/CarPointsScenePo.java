package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.pointadmin.po;

import lombok.Data;
import java.util.Date;

/**
 * 汽车积分场景
 *
 * <AUTHOR>
 * @date 2023/12/6
 */
@Data
public class CarPointsScenePo {

    /**
     * 场景ID
     */
    private Long id;

    /**
     * 发放场景名称
     */
    private String name;

    /**
     * 父类场景Id
     */
    private Integer parentId;

    /**
     * 发放场景编码
     */
    private String sceneCode;

    /**
     * 场景描述
     */
    private String sceneDesc;

    /**
     * 可用状态 1:上线 2:下线
     */
    private Integer status;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 发放场景枚举代码
     */
    private String enumCode;

    /**
     * 发放方式 1:接口发放 2:灌发
     */
    private String assignMode;

    /**
     * 记录文案
     */
    private String content;

}