package com.xiaomi.nr.coupon.admin.domain.pointadmin.event.handler;

import com.xiaomi.nr.coupon.admin.domain.pointadmin.event.entity.PointBatchConfigCreateEvent;
import com.xiaomi.nr.coupon.admin.domain.pointadmin.event.entity.PointBatchConfigUpdateEvent;
import com.xiaomi.nr.coupon.admin.domain.pointadmin.event.entity.PointBatchConfigUpdateStatusEvent;

/**
 * <AUTHOR>
 * @date 2023/12/6 15:47
 */
public abstract class BasePointBatchPostHandler {
    public abstract void createPost(PointBatchConfigCreateEvent event) throws Exception;


    public abstract void updatePost(PointBatchConfigUpdateEvent event) throws Exception;


    public abstract void updateStatus(PointBatchConfigUpdateStatusEvent event) throws Exception;
}
