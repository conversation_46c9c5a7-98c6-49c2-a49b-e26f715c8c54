package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.ecard;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.ecardconfig.po.EcardLogPo;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Component
public interface EcardLogMapper {

    String ECARD_LOG_SQL = "card_id,user_id,order_id,refund_no,log_type,income,old_balance,new_balance,add_time,description,operator_id,hash_code";

    @Select("<script>" +
            "select * from tb_ecard_log " +
            "where log_type = 1 " +
            "<if test=\"cardIds != null and cardIds.size >0\"> and card_id in " +
            "        <foreach item='card_id' index='index' collection='cardIds' open='(' separator=',' close=')'>" +
            "            #{card_id}" +
            "        </foreach> " +
            "</if>" +
            "order by add_time asc" +
            "</script>")
    ArrayList<EcardLogPo> queryByCardId(@Param("cardIds") List<Long> cardIds);


    @Select("<script>" +
            "select * from tb_ecard_log " +
            " where card_id=#{cardId} " +
            "<if test=\"userId != null and userId>0\"> and user_id=#{userId} </if>" +
            " order by add_time asc limit 500" +
            "</script>")
    List<EcardLogPo> queryByUserCardId(@Param("userId") Long userId, @Param("cardId") Long cardId);

    /**
     * 写入礼品卡日志
     *
     * @param ecardLogPo ecardLogPo
     * @return
     */
    @Insert("insert into tb_ecard_log (" + ECARD_LOG_SQL + ") " +
            " values " +
            "(#{ecardLogPo.cardId},#{ecardLogPo.userId},#{ecardLogPo.orderId},#{ecardLogPo.refundNo}," +
            "#{ecardLogPo.logType},#{ecardLogPo.income},#{ecardLogPo.oldBalance},#{ecardLogPo.newBalance}," +
            "#{ecardLogPo.addTime},#{ecardLogPo.description},#{ecardLogPo.operatorId},#{ecardLogPo.hashCode})")
    int insertEcardLog(@Param("ecardLogPo") EcardLogPo ecardLogPo);


    /**
     * 根据搜索参数查询Ecard日志
     *
     * @param searchParam 搜索参数
     * @return 符合搜索参数的Ecard日志列表
     */
    @Select("<script>" +
            "select * from tb_ecard_log " +
            "<where>" +
            "<if test = \"searchParam.userId != null\"> and user_id = #{searchParam.userId}</if>" +
            "<if test = \"searchParam.logType != null\"> and log_type = #{searchParam.logType}</if>" +
            "<if test = \"searchParam.orderId != null\"> and order_id = #{searchParam.orderId}</if>" +
            "<if test = \"searchParam.refundNo != null\"> and refund_no = #{searchParam.refundNo}</if>" +
            "</where>" +
            "</script>")
    List<EcardLogPo> searchByParam(@Param("searchParam") EcardLogSearchParam searchParam);


    /**
     * 删除用户礼品卡，自动化测试
     * @return
     */
    @Delete("<script>delete from tb_ecard_log where user_id=#{uid} and card_id =#{cardId}</script>")
    Long deleteEcardLog(@Param("uid") long uid, @Param("cardId") long cardId);


}
