package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.userpoint;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.userpoint.po.UserPointsLogPo;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/12/6 17:17
 */
@Mapper
@Component
public interface UserPointsLogMapper {

    /**
     * 添加用户领取积分变动日志
     *
     * @param userPointsLogPo
     * @return
     */
    @Options(useGeneratedKeys = true, keyProperty = "id")
    @Insert("insert into user_points_log(id,point_id,mid,type,order_id,income_points,old_points,new_points,add_time) " +
            "values(#{id},#{pointId},#{mid},#{type},#{orderId},#{incomePoints},#{oldPoints},#{newPoints},#{addTime})")
    Integer insert(UserPointsLogPo userPointsLogPo);

}
