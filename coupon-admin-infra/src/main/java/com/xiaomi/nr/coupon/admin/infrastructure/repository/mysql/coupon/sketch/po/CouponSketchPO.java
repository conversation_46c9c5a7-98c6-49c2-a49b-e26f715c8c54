package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.sketch.po;

import lombok.Data;

import java.util.Date;

/**
 * @Description: 优惠券草稿
 * @Date: 2022.03.01 16:42
 */
@Data
public class CouponSketchPO {
    /**
     * 草稿id
     */
    private Long id;
    /**
     * 优惠券类型  1: 商品券 2: 运费券
     */
    private Integer couponType;
    /**
     * 优惠券名称
     */
    private String couponName;
    /**
     * 业务平台  @BizPlatformEnum
     */
    private Integer bizPlatform;
    /**
     * 开始领取时间
     */
    private Long startFetchTime;
    /**
     * 结束领取时间
     */
    private Long endFetchTime;
    /**
     * 优惠金额
     */
    private Long promotionValue;
    /**
     * 优惠类型
     */
    private Integer bottomType;
    /**
     * 门槛
     */
    private Long bottomPrice;
    /**
     * 申请数量
     */
    private Long applyCount;
    /**
     * 优惠券压缩信息
     */
    private byte[] configCompress;
    /**
     * 附件地址
     */
    private String applyAttachment;
    /**
     * 删除状态
     */
    private Integer deleteStatus;
    /**
     * 部门id
     */
    private Long departmentId;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建人
     */
    private String creator;
}
