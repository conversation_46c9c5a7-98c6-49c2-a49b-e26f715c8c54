package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.couponconfig.po;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 规则
 *
 * <AUTHOR>
 */
@Data
public class Rule implements Serializable {

    private static final long serialVersionUID = 8422295560912592807L;

    /**
     * 满减券的满减金额（单位元）
     */
    @SerializedName("reduce_money")
    private String reduceMoney;

    /**
     * 折扣券的折扣（9折就是0.9）
     */
    @SerializedName("reduce_discount")
    private String reduceDiscount;

    /**
     * 可抵扣商品
     */
    @SerializedName("target_goods")
    private List<TargetGoodsItem> targetGoods;
}