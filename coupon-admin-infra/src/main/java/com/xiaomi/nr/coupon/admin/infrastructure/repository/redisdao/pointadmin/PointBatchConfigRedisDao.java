package com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.pointadmin;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.pointadmin.po.PointBatchConfigCachePo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/6 16:41
 */
public interface PointBatchConfigRedisDao {
    /**
     * 更新积分批次配置缓存数据
     *
     * @param cachePo cachePo
     */
    void setPointBatchConfigCache(PointBatchConfigCachePo cachePo) throws BizError;

    /**
     * 批量更新积分批次配置缓存数据
     *
     * @param cachePoList cachePoList
     */
    void setPointBatchConfigCache(List<PointBatchConfigCachePo> cachePoList) throws BizError;

    /**
     * 批量更新积分批次发放数量缓存数据
     *
     * @param batchId batchId
     * @param value value
     */
    void updatePointBatchDistributeCache(Long batchId, int value) throws BizError;

    /**
     * 获取积分批次配置缓存数据
     *
     * @param batchId batchId
     * @return PointBatchConfigCachePo
     */
    PointBatchConfigCachePo getPointBatchConfigCache(Long batchId);

    /**
     * 获取积分批次配置已发数量缓存数据
     *
     * @param batchId batchId
     * @return 已发数量
     */
    Long getPointBatchDistributeCache(Long batchId);

    /**
     * 批量获取积分批次配置已发数量缓存数据
     *
     * @param batchIdList batchIdList
     * @return Map<String, Long> key:batchId,value:已发数量
     */
    Map<Long, Long> getPointBatchDistributeCache(List<Long> batchIdList);
}
