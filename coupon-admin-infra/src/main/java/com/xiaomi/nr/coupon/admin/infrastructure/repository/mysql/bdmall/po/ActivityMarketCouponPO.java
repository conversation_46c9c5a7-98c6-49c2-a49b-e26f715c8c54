package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.bdmall.po;

import lombok.Data;

import java.util.Date;


/**
 * @description:
 * @author: hejiapeng
 * @Date 2022/3/26 8:12 下午
 * @Version: 1.0
 **/
@Data
public class ActivityMarketCouponPO {

    private int companyId;

    private long couponId;

    private int couponType;

    private String couponName;

    private String couponDesc;

    private Date startTime;

    private Date endTime;

    private int sendLimit;

    private int isCode;

    private String policyText;

    private int deductType;

    private String selectType;

    private String allGoods;

    private String goodsInclude;

    private String getGoodsIncludeIds;

    private String client;

    private int postFree;

    private int isShare;

    private int checkPrice;

    private int checkPackage;

    private Date addTime;

    private String addUser;

    private Date updateTime;

    private String updateUser;

    private String sendChannel;

    private String useChannel;

    private String auditStatus;

    private int createChannel;
}
