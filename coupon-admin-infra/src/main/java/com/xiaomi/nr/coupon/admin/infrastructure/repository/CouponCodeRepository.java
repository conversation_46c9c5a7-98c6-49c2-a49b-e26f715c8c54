package com.xiaomi.nr.coupon.admin.infrastructure.repository;

import com.xiaomi.nr.coupon.admin.infrastructure.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.usercoupon.CouponCodeMapper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.usercoupon.CodeCouponReceiveRecordMapper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.usercoupon.po.CodeCouponReceiveRecordPo;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.coupon.po.CouponCodePO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.coupon.UserCouponCodeMapper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.coupon.po.SearchCodeListParam;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import groovy.util.logging.Slf4j;
import org.assertj.core.util.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 优惠码
 * @Date: 2022.03.11 0:27
 */
@Component
@Slf4j
public class CouponCodeRepository {

    private static final Logger log = LoggerFactory.getLogger(CouponCodeRepository.class);

    @Autowired
    private UserCouponCodeMapper userCouponCodeMapper;

    @Autowired
    private CouponCodeMapper couponCodeMapper;

    @Autowired
    private CodeCouponReceiveRecordMapper codeCouponReceiveRecordMapper;

    // 分页查询限制条数
    private static final int SELECT_LIMIT = 5000;

    /**
     * 根据券配置id获取优惠码信息
     *
     * @param configId 券配置id
     * @return List<>
     */
    public List<CouponCodePO> getCouponCodeByConfigId(long configId) {
        int start = CommonConstant.ZERO_INT;
        List<CouponCodePO> result = Lists.newArrayList();
        List<CouponCodePO> tmpList;
        while (true) {
            tmpList = userCouponCodeMapper.getCouponCodeByConfigId(configId, start, SELECT_LIMIT);
            if (tmpList == null || tmpList.isEmpty()) {
                break;
            }

            result.addAll(tmpList);

            if (tmpList.size() < SELECT_LIMIT) {
                break;
            }

            start += SELECT_LIMIT;

        }

        return result;
    }


    /**
     * 格局参数查询优惠码信息
     *
     * @param searchParam
     * @return
     */
    public List<CouponCodePO> getUserCouponCode(SearchCodeListParam searchParam) {
        return userCouponCodeMapper.getCodeListByParam(searchParam);
    }


    /**
     * 获取未同步的优惠券代码列表
     *
     * @param sendType      发送类型
     * @param nowUnixSecond 当前的Unix时间戳（秒）
     * @return 未同步的优惠券代码列表
     */
    public List<CouponCodePO> getNotSyncCouponCodes(String sendType, long nowUnixSecond) {
        return userCouponCodeMapper.getNotSyncCouponCodes(sendType, nowUnixSecond);
    }

    /**
     * 修改券码同步状态
     *
     * @param couponIndex couponIndex
     * @param couponCode  couponCode
     */
    public void updateSyncSuccessStatus(String couponIndex, String couponCode) throws BizError {
        int effectRows = couponCodeMapper.updateSyncStatus(couponIndex, couponCode);
        if (effectRows <= 0) {
            log.error("CouponCodeRepository.updateSyncSuccessStatus failed, couponIndex = {}, couponCode = {}", couponIndex, couponCode);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "修改券码同步状态失败");
        }
    }

    /**
     * 优惠码领取记录
     *
     * @param receiveRecordPo receiveRecordPo
     */
    public void insertReceiveRecord(CodeCouponReceiveRecordPo receiveRecordPo) throws BizError {
        int effectRows = codeCouponReceiveRecordMapper.insert(receiveRecordPo);
        if (effectRows <= 0) {
            log.error("CouponCodeRepository.insertReceiveRecord 优惠码领取记录插入失败, receiveRecordPo = {}", receiveRecordPo);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "优惠码领取记录插入失败");
        }
    }

    public CouponCodePO getByCouponCode(String couponCode, String couponIndex) {
        return userCouponCodeMapper.getByCouponCode(couponCode, couponIndex);
    }

    public CodeCouponReceiveRecordPo getReceiveRecordByCouponCode(String couponCode, String couponIndex) {
        return codeCouponReceiveRecordMapper.getReceiveRecordByCouponCode(couponCode, couponIndex);
    }

    /**
     * 获取优惠券代码信息
     *
     * @param couponIndexList 优惠券索引列表
     * @return 优惠券代码信息列表
     */
    public List<CouponCodePO> getCouponCodeInfo(List<String> couponIndexList) {
        // 创建一个空的优惠券代码列表
        List<CouponCodePO> couponCodePOList = Lists.newArrayList();
        // 初始化页码和每页大小
        int pageNo = 1;
        int pageSize = 100;
        List<CouponCodePO> poList;
        do {
            // 计算当前页的偏移量
            int offset = (pageNo - 1) * pageSize;
            // 从数据库中获取当前页的优惠券代码信息
            poList = userCouponCodeMapper.getByCouponIndex(couponIndexList, pageSize, offset);
            // 将当前页的优惠券代码信息添加到总列表中
            couponCodePOList.addAll(poList);
            // 页码加1，准备获取下一页的数据
            pageNo++;
        } while (poList.size() == pageSize); // 如果当前页的数据量等于每页大小，继续循环

        // 返回所有获取到的优惠券代码信息
        return couponCodePOList;
    }

}
