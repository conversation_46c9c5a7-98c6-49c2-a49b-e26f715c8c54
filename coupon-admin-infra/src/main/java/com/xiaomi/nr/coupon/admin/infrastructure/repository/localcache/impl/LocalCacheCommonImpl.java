package com.xiaomi.nr.coupon.admin.infrastructure.repository.localcache.impl;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.localcache.LocalCacheCommon;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.CouponConfigMapper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.auth.AppAuthRedisDao;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.auth.po.AppAuthInfo;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.CouponConfigRedisDao;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.po.ConfigInfoCachePo;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.po.CouponConfigPoConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 本地缓存通用操作类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class LocalCacheCommonImpl implements LocalCacheCommon {

    @Autowired
    private CouponConfigMapper couponConfigMapper;


    @Autowired
    private CouponConfigPoConvert couponConfigPoConvert;

    @Autowired
    private AppAuthRedisDao appAuthRedisDao;


    @Autowired
    private CouponConfigRedisDao couponConfigRedisDao;


    public static Cache<String, AppAuthInfo> appAuthCache = Caffeine.newBuilder()
            .expireAfterWrite(1, TimeUnit.HOURS)
            .initialCapacity(100)
            .maximumSize(300)
            .build();


    /**
     * 本地缓存-券基本信息缓存, 缓存过期时间为1个小时
     * key: configId
     */
    private static Cache<Long, ConfigInfoCachePo>  couponInfoLocalCache = Caffeine.newBuilder()
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .initialCapacity(100)
            .maximumSize(300)
            .build();
    @Override
    public ConfigInfoCachePo getConfigInfoCachePo(Long configId){
        ConfigInfoCachePo configInfoCachePo = couponInfoLocalCache.getIfPresent(configId);
        if(configInfoCachePo == null) {
            configInfoCachePo = couponConfigRedisDao.getConfigInfoCache(configId);
            if(configInfoCachePo == null){
                CouponConfigPO couponConfigPO = couponConfigMapper.getCouponConfigById(configId);
                if(couponConfigPO != null) {
                    configInfoCachePo = couponConfigPoConvert.serializeConfigInfoCachePo(couponConfigPO);
                }
            }
            couponInfoLocalCache.put(configId, configInfoCachePo);
        }
        return configInfoCachePo;

    }

    /**
     * 取单个PP配置信息
     *
     * @param appId  appId
     * @return AppAuthInfo
     */
    @Override
    public AppAuthInfo getSingleAppAuth(String appId){
        AppAuthInfo info = null;
        try {
            if(StringUtils.isEmpty(appId)){
                return null;
            }

            info = appAuthCache.getIfPresent(appId);
            if(Objects.isNull(info)){
                info = appAuthRedisDao.getNewAppAuth(appId);
                log.info("getSingleAppAuth appId:{},info:{}",appId,info);
                appAuthCache.put(appId,info);
            }
        } catch (Exception e) {
            log.error("getSingleAppAuth Exception appId:{}",appId,e);
            throw e;
        }

        return info;
    }
}