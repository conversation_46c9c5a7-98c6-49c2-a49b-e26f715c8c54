package com.xiaomi.nr.coupon.admin.infrastructure.rpc;


import com.xiaomi.nr.coupon.admin.infrastructure.rpc.sdk.Result;
import com.xiaomi.nr.coupon.admin.infrastructure.rpc.sdk.SidWrapper;
import com.xiaomi.nr.coupon.admin.infrastructure.rpc.sdk.idinfo;
import com.xiaomi.nr.coupon.admin.api.utils.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class SidProxy {

    @Resource
    private SidWrapper sidClient;

    /**
     * 获取批量Sid列表
     *
     * @param userId 用户ID
     * @param count 数量
     * @return sid 列表
     */
    public List<Long> get(Long userId, Integer count) throws Exception {
        List<Long> result;
        try {
            result = getSids(userId, count);
        } catch (Exception e) {
            log.error("frist get sid error, err:{}", e.getMessage());
            result = getSids(userId, count);
        }
        return result;
    }

    public Long getNextId(Long userId) throws BizError {
        //调sid服务批量生成优惠ID
        List<Long> couponIds;
        try {
            couponIds = get(userId, 1);
        } catch (Exception e) {
            log.error("assign.coupon, 获取红包ID失败, userId={}, num={}, err={}", userId, 1, e.getMessage());
            throw ExceptionHelper.create(GeneralCodes.ServerIsBuzy, "无法连接SID服务");
        }
        return couponIds.get(0);
    }

    private List<Long> getSids(Long userId, Integer count) throws Exception {
        long runStartTime = TimeUtil.getNowUnixMillis();
        sidClient.setTimeout(1000);
        Result res = sidClient.Get(count, "xiaomi_pulse","couponid", "xiaomi_pulse");
        log.info("sidserver.response，userId={}, count={}, res={}, runTime={}毫秒", userId, count, res, TimeUtil.sinceMillis(runStartTime));

        if(res == null){
            throw new Exception("请求sid服务返回失败");
        }

        if(res.getErrorNo() != 200) {
            throw new Exception(String.format("sid服务返回请求失败，err=%s", res.getErrorMsg()));
        }

        if(res.getResponse().size() != count){
            throw new Exception(String.format("sid服务返回请求失败，count=%s, res.size=%s", count, res.getResponse().size()));
        }

        List<Long> result = new ArrayList<>();
        for(idinfo item : res.getResponse()){
            result.add(item.getId());
        }
        return result;
    }
}
