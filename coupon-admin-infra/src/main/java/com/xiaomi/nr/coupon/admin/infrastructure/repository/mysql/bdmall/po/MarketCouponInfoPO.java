package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.bdmall.po;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * @description: 优惠券基本信息
 * @author: he<PERSON><PERSON><PERSON>
 * @Date 2022/4/11 1:21 下午
 * @Version: 1.0
 **/
@Data
public class MarketCouponInfoPO {

    @SerializedName("time_type")
    private String timeType;

    @SerializedName("day_count")
    private String dayCount;

    @SerializedName("range_time")
    private String rangeTime;
}
