package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.userredpacket;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.userredpacket.po.SearchUserRedpacketListParam;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.userredpacket.po.UserRedpacketPO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 用户红包mapper
 */
@Mapper
@Component
public interface UserRedPacketMapper {

    /**
     * 查用户券总数
     */
    @Select("<script>select count(1) from tb_rp_redpacket where 1=1 " +
            "<if test='userId!=null and userId>0'> and user_id=#{userId}</if>" +
            "<if test='typeId!=null and typeId>0'> and type_id=#{typeId}</if>" +
            "<if test='redpacketId!=null and redpacketId>0'> and redpacket_id = #{redpacketId}</if>"+
            "<if test='status ==\"available\"'>and balance &gt; 0 and end_time &gt; unix_timestamp(now())</if>" +
            "<if test='status == \"invalid\"'>and (balance &lt;= 0 or end_time &lt;= unix_timestamp(now()))</if>" +
            "</script>")
    Long selectCount(SearchUserRedpacketListParam param);


    @Select("<script>select * from tb_rp_redpacket where 1=1"  +
            "<if test='userId!=null and userId>0'> and user_id=#{userId}</if>" +
            "<if test='typeId!=null and typeId>0'> and type_id=#{typeId}</if>" +
            "<if test='redpacketId!=null and redpacketId>0'> and redpacket_id = #{redpacketId}</if>"+
            "<if test='status ==\"available\"'>and balance &gt; 0 and end_time &gt; unix_timestamp(now())</if>" +
            "<if test='status == \"invalid\"'>and (balance &lt;= 0 or end_time &lt;= unix_timestamp(now()))</if>" +
            " order by  ${orderBy}  ${orderDirection} "+
            " limit #{offset}, #{limit}"+
            "</script>")
    List<UserRedpacketPO> selectList(SearchUserRedpacketListParam param);




    @Delete("<script>delete from tb_rp_redpacket where user_id=#{uid} </script>")
    Long deleteRedPacket(@Param("uid") long uid);

}
