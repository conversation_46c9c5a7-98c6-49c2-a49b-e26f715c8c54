package com.xiaomi.nr.coupon.admin.infrastructure.rpc;

import com.google.common.collect.Lists;
import com.xiaomi.cnzone.maindataapi.api.StoreProvider;
import com.xiaomi.cnzone.maindataapi.model.OrgDataDto;
import com.xiaomi.cnzone.maindataapi.model.OrgDto;
import com.xiaomi.cnzone.maindataapi.model.OrgResp;
import com.xiaomi.cnzone.maindataapi.model.OrgResponse;
import com.xiaomi.cnzone.maindataapi.model.enums.DomainEnum;
import com.xiaomi.cnzone.maindataapi.model.req.org.OrgBase;
import com.xiaomi.cnzone.maindataapi.model.req.org.OrgBaseEntity;
import com.xiaomi.cnzone.maindataapi.model.req.org.OrgCategory;
import com.xiaomi.cnzone.maindataapi.model.req.store.StoreListAllRequest;
import com.xiaomi.cnzone.maindataapi.model.req.store.StoreListByOrgIdsRequest;
import com.xiaomi.cnzone.maindataapi.model.req.store.StoreListRequest;
import com.xiaomi.cnzone.maindatacommon.enums.BusinessTypeEnums;
import com.xiaomi.newretail.common.tools.base.exception.BaseException;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.store.StoreInfoVo;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.store.request.PageStoreRequest;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.StoreStatusEnum;
import com.xiaomi.nr.coupon.admin.api.enums.TranslationEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.UseChannelsEnum;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.ResultValidator;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * 门店相关
 * https://xiaomi.f.mioffice.cn/docs/dock4Mt8IAZMyXL8Tatp3ujdnVb
 */
@Slf4j
@Component
public class StoreProxyService {

    @Reference(check = false, interfaceClass = StoreProvider.class, timeout = 1000, version ="1.0" ,group = "${store.group}")
    private StoreProvider storeProvider;

    @Autowired
    private ThreadPoolTaskExecutor queryAsyncExecutor;

    @SuppressWarnings("unchecked")
    public List<OrgDto> queryStoreList(List<String> storeIds) throws Exception {
        List<Future<List<OrgDto>>> futureList = new ArrayList<>();
        List<List<String>> storeIdsList = Lists.partition(storeIds, 500);
        for (List<String> storeIdsPart : storeIdsList) {
            Future<List<OrgDto>> future = queryAsyncExecutor.submit(() -> {
                try {
                    StoreListRequest request = new StoreListRequest();
                    String[] filter = new String[2];
                    filter[0] = DomainEnum.BASE.getName();
                    filter[1] = DomainEnum.CATEGORY.getName();
                    request.setFilter(filter);
                    request.setOrgId(StringUtils.join(storeIdsPart, ","));
                    Result<OrgResp> result = storeProvider.selectStoreList(request);
                    log.info("StoreProxyService queryStoreList storeIds:{},result:{}", storeIds, result);
                    ResultValidator.validate(result, TranslationEnum.COUPON_CONFIG_QUERY_ERROR_STORE.getTranslateContent());
                    if (result.getData() != null) {
                        return result.getData().getOrgList();
                    }
                    return Collections.EMPTY_LIST;
                } catch (Exception e) {
                    log.error("StoreProxyService queryStoreList Exception storeIds:{}", storeIds, e);
                    throw ExceptionHelper.create(GeneralCodes.InternalError, TranslationEnum.COUPON_CONFIG_QUERY_ERROR_STORE.getTranslateContent());
                }
            });
            futureList.add(future);
        }

        List<OrgDto> returnList = new ArrayList<>();
        for (Future<List<OrgDto>> future : futureList) {
            if (future != null) {
                List<OrgDto> storeInfoList = future.get();
                if(CollectionUtils.isNotEmpty(storeInfoList)){
                    returnList.addAll(storeInfoList);
                }
            }
        }

        return returnList;
    }

    /**
     * 判断门店的UseChannel，从上游沉下来的
     * @param orgDto
     * @return
     */
    public Integer getChannel(OrgDto orgDto){
        OrgBase orgBase =orgDto.getOrgBase();
        OrgCategory orgCategory = orgDto.getOrgCategory();
        if(orgBase!=null){
            // manage_channel = 1 米家
            if(orgBase.getManageChannel() == 1){
                //manage_type = 1 直营
                if(orgCategory.getManageType() == 1){
                    return UseChannelsEnum.DIRECTSALE_STORE.getValue();
                }
                //manage_type = 2，3，4，5, 35 专卖
                if (orgCategory.getManageType() == 2 || orgCategory.getManageType() == 3 || orgCategory.getManageType() == 4 ||
                        orgCategory.getManageType() == 5 || orgCategory.getManageType() == 35) {
                    return UseChannelsEnum.EXCLUSIVE_SHOP.getValue();
                }
            }
            // manage_channel = 5 授权
            if(orgBase.getManageChannel() == 5){
                return UseChannelsEnum.AUTHORIZED_STORE.getValue();
            }
            // manage_channel = 27 一商一议
            if(orgBase.getManageChannel() == 27 && orgCategory.getManageType() == 61){
                return UseChannelsEnum.FORTRESS_STORE.getValue();
            }
        }
        return null;
    }

    private static final List<String> BUSINESS_TYPE = Lists.newArrayList(BusinessTypeEnums.PHONE.getCode());

    public BasePageResponse<StoreInfoVo> selectPageStoreList(PageStoreRequest storeRequest) {
        StoreListAllRequest storeListRequest = new StoreListAllRequest();
        if (Objects.nonNull(storeRequest.getAreaId())) {
            storeListRequest.setInternationalAreaId(Lists.newArrayList(storeRequest.getAreaId()));
        }
        if (CollectionUtils.isNotEmpty(storeRequest.getPartnerIdList())) {
            storeListRequest.setPartnerIdList(storeRequest.getPartnerIdList());
        }
        if (StringUtils.isNotBlank(storeRequest.getOrgCode())) {
            storeListRequest.setOrgId(Lists.newArrayList(storeRequest.getOrgCode()));
        }else if(CollectionUtils.isNotEmpty(storeRequest.getOrgCodeList())){
            storeListRequest.setOrgId(storeRequest.getOrgCodeList());
        }
        if (StringUtils.isNotBlank(storeRequest.getOrgName())) {
            storeListRequest.setFuzzy(storeRequest.getOrgName());
        }
        if (CollectionUtils.isNotEmpty(storeRequest.getOrgStatusList())) {
            storeListRequest.setStatus(storeRequest.getOrgStatusList());
        }
        if (CollectionUtils.isNotEmpty(storeRequest.getChannelList())) {
            storeListRequest.setManageChannel(
                    storeRequest.getChannelList().stream().map(this::convertPrimitiveType).collect(Collectors.toList()));
        }else{
            //不传默认只差直营店和授权店
            storeListRequest.setManageChannel(Lists.newArrayList(auth,direct));
        }
        if(Objects.nonNull(storeRequest.getPageNo())){
            storeListRequest.setPageIndex(storeRequest.getPageNo());
        }else{
            storeListRequest.setPageIndex(1);
        }
        if(Objects.nonNull(storeRequest.getPageSize())){
            storeListRequest.setPageSize(storeRequest.getPageSize());
        }else{
            storeListRequest.setPageSize(200);
        }
        String[] filter = new String[]{"base"};
        storeListRequest.setFilter(filter);
        storeListRequest.setBusinessType(BUSINESS_TYPE);
        Result<OrgResponse> result;
        try {
            result = storeProvider.selectStoreListBeta(storeListRequest);
        } catch (Exception e) {
            log.error("selectStoreList rpc err. ", e);
            throw new BaseException(-1, TranslationEnum.COUPON_CONFIG_QUERY_ERROR_STORE_2.getTranslateContent());
        }
        if (Objects.isNull(result)) {
            log.error("selectStoreList rpc err. result is null. req:{}", GsonUtil.toJson(storeListRequest));
            throw new BaseException(-1, TranslationEnum.COUPON_CONFIG_QUERY_ERROR_STORE_3.getTranslateContent());
        }
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            log.error("selectStoreList rpc err. req:{}, result:{}", GsonUtil.toJson(storeListRequest), GsonUtil.toJson(result));
            throw new BaseException(-1, TranslationEnum.COUPON_CONFIG_QUERY_ERROR_STORE_4.getTranslateContent() + result.getCode() + "," + result.getMessage());
        }
        if (Objects.isNull(result.getData())) {
            log.error("selectStoreList rpc err. req:{}, result:{}", GsonUtil.toJson(storeListRequest), GsonUtil.toJson(result));
            throw new BaseException(-1, TranslationEnum.COUPON_CONFIG_QUERY_ERROR_STORE_2.getTranslateContent());
        }
        List<OrgDataDto> orgDataDtoList = result.getData().getOrgList();
        if (CollectionUtils.isEmpty(orgDataDtoList)) {
            log.info("selectStoreList rpc err. orgList is null. req:{}", GsonUtil.toJson(storeListRequest));
            return new BasePageResponse<>(storeListRequest.getPageIndex(), storeListRequest.getPageSize(), 0, new ArrayList<>());
        }
        log.info("selectStoreList rpc success. req:{}. result:{}", GsonUtil.toJson(storeListRequest), GsonUtil.toJson(result));
        BasePageResponse<StoreInfoVo> pageResponse = new BasePageResponse<>();
        pageResponse.setList(orgDataDtoList.stream().map(this::convertOrgDataDto2Vo).collect(Collectors.toList()));
        pageResponse.setTotalCount(Long.valueOf(result.getData().getTotal()));
        pageResponse.setPageSize(result.getData().getPageSize());
        pageResponse.setPageNo(result.getData().getPageNum());
        return pageResponse;
    }

    /**
     * 返回的门店字段转化为系统需要的vo
     *
     * @param orgDataDto 门店返回的dto
     * @return storeInfoVo 系统需要的vo
     */
    private StoreInfoVo convertOrgDataDto2Vo(OrgDataDto orgDataDto) {
        OrgBaseEntity orgBase = orgDataDto.getOrgBase();
        StoreInfoVo storeInfoVo = new StoreInfoVo();
        storeInfoVo.setOrgCode(orgBase.getOrgId());
        storeInfoVo.setOrgName(orgBase.getShopName());
        storeInfoVo.setOrgStatus(Integer.valueOf(orgBase.getStatus()));
        storeInfoVo.setOrgStatusText(StoreStatusEnum.getEnumByCode(storeInfoVo.getOrgStatus()).getCnName());
        storeInfoVo.setOrgType(convertOrgType(orgBase.getManageChannel()));
        storeInfoVo.setOrgTypeText(UseChannelsEnum.getNameByValue(storeInfoVo.getOrgType()));
        return storeInfoVo;
    }

    private static final int direct = 1;
    private static final int auth = 5;

    /**
     * 转为门店那边使用的直营授权code
     * @param channelCode
     * @return
     */
    private Integer convertPrimitiveType(Integer channelCode) {
        if (channelCode == null) {
            //不传返空
            return null;
        }
        //默认直营
        int type = direct;
        //为4修改为授权
        if (channelCode == UseChannelsEnum.AUTHORIZED_STORE.getValue()) {
            type = auth;
        }
        return type;
    }

    /**
     * 门店类型转换
     *
     * @param primitiveType store服务的门店类型: 5授权 1直营
     * @return 本系统支持的类型
     */
    private int convertOrgType(Integer primitiveType) {
        int type = 0;
        if (primitiveType == null) {
            return type;
        }
        if (primitiveType == auth) {
            type = UseChannelsEnum.AUTHORIZED_STORE.getValue();
        } else if (primitiveType == direct) {
            type = UseChannelsEnum.DIRECTSALE_STORE.getValue();
        }
        return type;
    }

    public List<StoreInfoVo> selectStoreListByOrgCode(List<String> orgCodeList) {
        StoreListByOrgIdsRequest storeListRequest = new StoreListByOrgIdsRequest();
        storeListRequest.setOrgId(orgCodeList);
        String[] filter = new String[]{"base"};
        storeListRequest.setFilter(filter);
        Result<OrgResponse> result;
        try {
            result = storeProvider.selectStoreByOrgIds(storeListRequest);
        } catch (Exception e) {
            log.error("selectStoreListByOrgCode rpc err. ", e);
            throw new BaseException(-1, "请求门店服务，获取门店数据失败");
        }
        if (Objects.isNull(result)) {
            log.error("selectStoreListByOrgCode rpc err. result is null. req:{}", GsonUtil.toJson(storeListRequest));
            throw new BaseException(-1, "请求门店服务，获取门店数据失败，结果为空.");
        }
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            log.error("selectStoreListByOrgCode rpc err. req:{}, result:{}", GsonUtil.toJson(storeListRequest), GsonUtil.toJson(result));
            throw new BaseException(-1, "请求门店服务，获取门店数据失败，code:" + result.getCode() + "," + result.getMessage());
        }
        if (Objects.isNull(result.getData())) {
            log.error("selectStoreListByOrgCode rpc err. req:{}, result:{}", GsonUtil.toJson(storeListRequest), GsonUtil.toJson(result));
            throw new BaseException(-1, "请求门店服务，获取门店数据失败");
        }
        List<OrgDataDto> orgDataDtoList = result.getData().getOrgList();
        if (CollectionUtils.isEmpty(orgDataDtoList)) {
            log.info("selectStoreListByOrgCode rpc err. orgList is null. req:{}", GsonUtil.toJson(storeListRequest));
            return new ArrayList<>();
        }
        log.info("selectStoreListByOrgCode rpc success. req:{}. result:{}", GsonUtil.toJson(storeListRequest), GsonUtil.toJson(result));
        return orgDataDtoList.stream().map(this::convertOrgDataDto2Vo).collect(Collectors.toList());
    }
}
