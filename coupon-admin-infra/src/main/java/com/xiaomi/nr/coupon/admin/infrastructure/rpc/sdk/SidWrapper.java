/**
 * Autogenerated by Thrift Compiler (0.9.2)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.xiaomi.nr.coupon.admin.infrastructure.rpc.sdk;

import org.mi.thrift.protocol.*;
import org.mi.thrift.protocol.TProtocolException;
import org.mi.thrift.rpc.*;
import org.mi.thrift.transport.*;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;


@Component
public class SidWrapper { 
 
    private String service_ = null; 
    private static int MaxConnRetry = 3; 
    private static int DefReadTimeout = 1000; 
 
    private XContext context_ = null; 
    private ConcurrentHashMap<String, Integer> timeoutMap_ = null; 
    private int timeout_ = 0; 
 
    private XRpc rpc_ = null; 
    private String balanceKey;

    public SidWrapper() {
    }

    public SidWrapper(String service, long traceId) {
        service_ = service; 
        timeoutMap_ = new ConcurrentHashMap<String, Integer>(); 
        rpc_ = XRpc.getInstance(); 
        context_ = new XContext(rpc_.getServiceName(), traceId, 0); 
    } 
 
    public SidWrapper(String service, XContext context) { 
 
        context_ = new XContext(context); 
        service_ = service; 
        timeoutMap_ = new ConcurrentHashMap<String, Integer>(); 
        rpc_ = XRpc.getInstance(); 
        context.setAppId(rpc_.getServiceName()); 
    } 
 
    public void setBalanceKey(String balanceKey) { 
        this.balanceKey = balanceKey; 
    } 
    public void setTimeout(int timeout)  { 
        if (timeout <= 0) { 
            return; 
        } 
 
        timeout_ = timeout; 
    } 
 
    public void setTimeout(String method, int timeout) { 
        if (timeout <= 0) { 
            return ; 
        } 
 
        timeoutMap_.put(method, new Integer(timeout)); 
    } 
 
    public int getTimeout(String method, XService service) { 
 
        int timeout = service.getReadTimeout(); 
        Integer timeoutInt = timeoutMap_.get(method); 
 
        if (timeoutInt != null) { 
            timeout = timeoutInt.intValue(); 
        } else { 
            if (timeout_ > 0) { 
                timeout = timeout_; 
            } 
        } 
 
        if (timeout <= 0) { 
            timeout = DefReadTimeout; 
        } 
 
        return timeout; 
    } 
 
    public TSocket getConn(String method, XService service) throws Exception  { 
 
        TSocket sock = null; 
        Exception save = null; 
        for (int i = 0; i < MaxConnRetry; i++) { 
            try { 
                sock = rpc_.getConn(service_, balanceKey); 
                int timeout = getTimeout(method, service); 
                sock.setTimeout(timeout); 
                return sock; 
            } catch (Exception e) { 
                save = e; 
                rpc_.putConn(sock, true); 
                continue; 
            } 
        } 
 
        rpc_.putConn(sock, true); 
        throw save; 
    } 
 
    public TTransport getTransport(TSocket sock, XService service) throws Exception { 
 
        String transName = service.getTransport(); 
        TTransport transport = null; 
 
        if (transName.equals("xm_header")) { 
            return new TXmHeaderTransport(sock); 
        } else if (transName.equals("frame")) { 
            return new TFramedTransport(sock); 
        } else if (transName.equals("tcp")) { 
            return sock; 
        } 
 
        throw new Exception("not found transport " + transName + ", service:" + service_); 
    } 
 
    public TProtocol getProtocol(TTransport transport, XService service) throws Exception { 
 
        String protocolName = service.getProtocol(); 
        TProtocol protocol = null; 
        if (protocolName.equals("binary")) { 
            return new TBinaryProtocol(transport); 
        } else if (protocolName.equals("compact")) { 
            return new TCompactProtocol(transport); 
        } 
 
        throw new Exception("not found protocol " + protocolName + ", service:" + service_); 
    } 
    public Result Get(int number, String appId, String xnamespace, String password) throws Exception
    {
        XService service = rpc_.getService(service_);
        Exception save = new Exception("Unknown exception");
        int maxRetry = service.getMaxRetry();
        for (int i = 0; i < maxRetry + 1; i++)        {
            TSocket sock = null;
            boolean exception = false;
            try            {
                sock = getConn("Get", service);
                TTransport trans = getTransport(sock, service);
                TProtocol proto = getProtocol(trans, service);
                Sid.Client client = new Sid.Client(proto);
                client.setContext(context_);
                return client.Get(number, appId, xnamespace, password);
            }
            catch(TTransportException e)            {
                exception = true;
                save = e;
                continue;
                } catch (TProtocolException e) {
                exception = true;
                save = e;
                continue;
            }
            finally            {
                rpc_.putConn(sock, exception);
            }
        }
        throw save;
    }


 
} 

