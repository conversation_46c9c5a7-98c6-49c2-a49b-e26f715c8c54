package com.xiaomi.nr.coupon.admin.application.dubbo.convert;


import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponInfo;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.UserCouponVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.ConfigInfoVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.CouponInfoVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.OrderInfoVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.ShareInfoVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.usercoupon.response.UserCouponDetailResponse;
import com.xiaomi.nr.coupon.admin.enums.usercoupon.UserCouponStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.CouponSceneRepository;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.UserCouponRepository;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.usercoupon.po.ExtendInfo;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.usercoupon.po.UserCouponPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.usercoupon.po.UserCouponSharePO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.tidbdao.po.CouponPo;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.api.utils.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UserCouponConvert {

    @Autowired
    private UserCouponRepository userCouponRepository;

    @Autowired
    private CouponSceneRepository couponSceneRepository;


    public UserCouponVO convertToUserCouponVO(UserCouponPO userCouponPO, CouponConfigPO couponConfigPO) {
        UserCouponVO couponVO = new UserCouponVO();
        couponVO.setUid(userCouponPO.getUserId());
        couponVO.setConfigId(userCouponPO.getTypeId());
        couponVO.setCouponId(userCouponPO.getId());
        if (couponConfigPO != null) {
            couponVO.setCouponType(couponConfigPO.getCouponType());
            couponVO.setName(couponConfigPO.getName());
            couponVO.setPromotionType(couponConfigPO.getPromotionType());
            couponVO.setPromotionValue(couponConfigPO.getPromotionValue());
            couponVO.setBottomType(couponConfigPO.getBottomType());
            couponVO.setBottomPrice(couponConfigPO.getBottomPrice());
            couponVO.setBottomCount(couponConfigPO.getBottomCount());
            couponVO.setSendScene(couponConfigPO.getSendScene());

            List<String> sceneCode = new ArrayList<>(Arrays.asList(couponConfigPO.getSendScene().split(",")));
            if (CollectionUtils.isNotEmpty(sceneCode)) {
                Map<String, String> codeMap = couponSceneRepository.selectBySceneCodes(sceneCode);
                couponVO.setSendSceneName(StringUtils.join(codeMap.values(), ","));
            }
            if (StringUtils.isNotBlank(couponConfigPO.getUseChannel())) {
                couponVO.setUseChannel(Arrays.asList(couponConfigPO.getUseChannel().split(",")).stream().map(s -> {
                    return Integer.parseInt(s);
                }).collect(Collectors.toList()));
            }
        }
        if (userCouponPO.getAddTime() > 0L) {
            couponVO.setFetchTime(new Date(userCouponPO.getAddTime() * 1000L));
        }
        if (userCouponPO.getStartTime() > 0L) {
            couponVO.setStartUseTime(new Date(userCouponPO.getStartTime() * 1000L));
        }
        if (userCouponPO.getEndTime() > 0L) {
            couponVO.setEndUseTime(new Date(userCouponPO.getEndTime() * 1000L));
        }
        couponVO.setCouponStatus(userCouponPO.getStat());
        if (UserCouponStatusEnum.RECEIVED.getValue().equals(userCouponPO.getStat())) {
            UserCouponSharePO sharePO = userCouponRepository.selectShareByUidAndStatus(userCouponPO.getUserId(), userCouponPO.getId(), 2);
            if (sharePO != null) {
                couponVO.setRecipientUid(sharePO.getRecipientUid());
                if (sharePO.getGivingTime() > 0L) {
                    couponVO.setGivingTime(new Date(sharePO.getGivingTime() * 1000L));
                }

            }
        }
        if (UserCouponStatusEnum.UNUSED.getValue().equals(userCouponPO.getStat()) && userCouponPO.getEndTime() < System.currentTimeMillis() / 1000) {
            couponVO.setCouponStatus(UserCouponStatusEnum.EXPIRED.getValue());
        }
        if (userCouponPO.getUseTime() > 0L) {
            couponVO.setUseTime(new Date(userCouponPO.getUseTime() * 1000L));
        }

        couponVO.setVid(userCouponPO.getVid());
        couponVO.setBizPlatform(userCouponPO.getBizPlatform());

        return couponVO;
    }


    /**
     * 用户券详情信息转换
     *
     * @param userCouponPO   用户券实体
     * @param couponConfigPO 券配置实体
     * @param sharePOList    券分享信息实体
     * @param orderInfoVO    订单信息
     * @return response
     */
    public UserCouponDetailResponse convertToUserCouponDetail(UserCouponPO userCouponPO, CouponConfigPO couponConfigPO, List<UserCouponSharePO> sharePOList, OrderInfoVO orderInfoVO) {

        // 用户券信息
        CouponInfoVO couponInfo = new CouponInfoVO();
        couponInfo.setUid(userCouponPO.getUserId());
        couponInfo.setCouponId(userCouponPO.getId());
        couponInfo.setSendMode(userCouponPO.getSendType());
        couponInfo.setActivityId(userCouponPO.getActivityId());

        if (userCouponPO.getAddTime() > CommonConstant.ZERO_LONG) {
            couponInfo.setFetchTime(TimeUtil.convertLongToDate(userCouponPO.getAddTime()));
        }
        if (userCouponPO.getStartTime() > CommonConstant.ZERO_LONG) {
            couponInfo.setStartUseTime(TimeUtil.convertLongToDate(userCouponPO.getStartTime()));
        }
        if (userCouponPO.getEndTime() > CommonConstant.ZERO_LONG) {
            couponInfo.setEndUseTime(TimeUtil.convertLongToDate(userCouponPO.getEndTime()));
        }
        couponInfo.setCouponStatus(userCouponPO.getStat());
        if (UserCouponStatusEnum.UNUSED.getValue().equals(userCouponPO.getStat()) && userCouponPO.getEndTime() < System.currentTimeMillis() / 1000) {
            couponInfo.setCouponStatus(UserCouponStatusEnum.EXPIRED.getValue());
        }
        if (userCouponPO.getUseTime() > CommonConstant.ZERO_LONG) {
            couponInfo.setUseTime(TimeUtil.convertLongToDate(userCouponPO.getUseTime()));
        }
        if (!Objects.isNull(orderInfoVO) && StringUtils.isNotEmpty(orderInfoVO.getUseOrg())) {
            couponInfo.setFetchOrg(orderInfoVO.getUseOrg());
        }
        if (StringUtils.isNotEmpty(userCouponPO.getExtendInfo())) {
            ExtendInfo extendInfo = GsonUtil.fromJson(userCouponPO.getExtendInfo(), ExtendInfo.class);
            couponInfo.setFetchOrg(extendInfo.getOrgCode());
        }

        // 券配置信息
        ConfigInfoVO configInfo = new ConfigInfoVO();
        configInfo.setConfigId(couponConfigPO.getId());
        configInfo.setCouponType(couponConfigPO.getCouponType());
        configInfo.setName(couponConfigPO.getName());
        configInfo.setPromotionType(couponConfigPO.getPromotionType());
        configInfo.setPromotionValue(couponConfigPO.getPromotionValue());
        configInfo.setBottomPrice(couponConfigPO.getBottomPrice());
        configInfo.setBottomCount(couponConfigPO.getBottomCount());
        configInfo.setBottomType(couponConfigPO.getBottomType());
        configInfo.setSendScene(couponConfigPO.getSendScene());
        String sceneName = couponSceneRepository.selectNameBySceneCode(couponConfigPO.getSendScene());
        configInfo.setSendSceneName(sceneName);

        // 券分享信息
        List<ShareInfoVO> shareInfoList = new LinkedList<>();
        ShareInfoVO shareInfo = new ShareInfoVO();
        if (CollectionUtils.isNotEmpty(sharePOList)) {
            for (UserCouponSharePO sharePo : sharePOList) {
                shareInfo.setShareTime(TimeUtil.convertLongToDate(sharePo.getGivingTime()));
                shareInfo.setShareUid(sharePo.getUserId());
                shareInfo.setReceiveTime(sharePo.getRecipientTime() > CommonConstant.ZERO_LONG ? TimeUtil.convertLongToDate(sharePo.getRecipientTime()) : null);
                shareInfo.setReceiveUid(sharePo.getRecipientUid());
                shareInfo.setNewCouponId(sharePo.getNewCouponId());
                shareInfoList.add(shareInfo);
            }
        }

        return new UserCouponDetailResponse(couponInfo, configInfo, orderInfoVO, shareInfoList);
    }


    /**
     * 构建优惠券信息列表
     *
     * @param couponPOList 用户优惠券PO列表
     * @return 优惠券信息列表
     */
    public List<CouponInfo> buildCouponInfo(List<UserCouponPO> couponPOList) {
        List<CouponInfo> couponInfoList = Lists.newArrayList();
        for (UserCouponPO userCouponPO : couponPOList) {
            CouponInfo couponInfo = new CouponInfo();
            couponInfo.setUserId(userCouponPO.getUserId());
            couponInfo.setId(userCouponPO.getId());
            couponInfo.setTypeId(userCouponPO.getTypeId());
            couponInfo.setActivityId(userCouponPO.getActivityId());
            couponInfo.setOrderId(userCouponPO.getOrderId());
            couponInfo.setUseTime(userCouponPO.getUseTime());
            couponInfo.setAddTime(userCouponPO.getAddTime());
            couponInfo.setSendType(userCouponPO.getSendType());
            couponInfo.setFromOrderId(userCouponPO.getFromOrderId());
            couponInfo.setReplaceMoney(userCouponPO.getReplaceMoney());
            couponInfo.setOffline(userCouponPO.getOffline());
            couponInfo.setReduceExpress(userCouponPO.getReduceExpress());
            couponInfo.setParentId(userCouponPO.getParentId());

            couponInfoList.add(couponInfo);
        }

        return couponInfoList;
    }

    /**
     * 构建用户优惠券信息列表
     *
     * @param couponPoList 用户优惠券数据列表
     * @return 用户优惠券信息列表
     */
    public List<CouponInfo> buildUserCouponInfo(List<CouponPo> couponPoList) {
        List<CouponInfo> couponInfoList = Lists.newArrayList();
        for (CouponPo userCouponPO : couponPoList) {
            CouponInfo couponInfo = new CouponInfo();
            couponInfo.setUserId(userCouponPO.getUserId());
            couponInfo.setId(userCouponPO.getId());
            couponInfo.setTypeId(userCouponPO.getTypeId());
            couponInfo.setActivityId(userCouponPO.getActivityId());
            couponInfo.setOrderId(userCouponPO.getOrderId());
            couponInfo.setUseTime(userCouponPO.getUseTime());
            couponInfo.setAddTime(userCouponPO.getAddTime());
            couponInfo.setSendType(userCouponPO.getSendType());
            couponInfo.setFromOrderId(userCouponPO.getFromOrderId());
            couponInfo.setReplaceMoney(userCouponPO.getReplaceMoney());
            couponInfo.setOffline(userCouponPO.getOffline());
            couponInfo.setReduceExpress(userCouponPO.getReduceExpress());
            couponInfo.setParentId(userCouponPO.getParentId());

            couponInfoList.add(couponInfo);
        }

        return couponInfoList;
    }
}
