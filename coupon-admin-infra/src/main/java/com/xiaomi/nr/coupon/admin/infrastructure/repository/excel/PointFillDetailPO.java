package com.xiaomi.nr.coupon.admin.infrastructure.repository.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @description 灌积分任务失败详情
 * <AUTHOR>
 * @date 2024-08-21 19:56
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PointFillDetailPO implements Serializable {
    private static final long serialVersionUID = 1384952693732338556L;

    /**
     * mid
     */
    @ExcelProperty("mid")
    private Long mid;

    /**
     * 积分发放数量
     */
    @ExcelProperty("积分发放数量")
    private Long pointCount;

    /**
     * 失败原因
     */
    @ExcelProperty("失败原因")
    private String failReason;
}
