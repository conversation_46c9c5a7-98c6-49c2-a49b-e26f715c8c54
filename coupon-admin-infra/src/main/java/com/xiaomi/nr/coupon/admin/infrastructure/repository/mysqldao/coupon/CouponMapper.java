package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.coupon;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.coupon.po.CouponPo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Component
public interface CouponMapper {

    /**
     * 获取即将过期的运费券列表
     * @param startTime long 过滤的开始时间
     * @param endTime long 过滤的结束时间
     * @param configIds List<Long> 运费券配置ID列表
     * @param dbName String 库名
     * @param limitStart int 单次读取的开始位置
     * @param limit int 单次读取的限制数
     * @return List<CouponPo>
     */
    @Select("<script>" +
            "select id as id, type_id as typeId, end_time as endTime, user_id as userId, extend_info as extendInfo " +
            "from tb_coupon " +
            "where stat='unused' and end_time <![CDATA[>=]]> #{startTime} and end_time <![CDATA[<]]> #{endTime} and database() = #{dbName}" +
            "<if test=\"configIds != null and configIds.size > 0\"> and type_id in " +
            "        <foreach item='configId' index='index' collection='configIds' open='(' separator=',' close=')'>" +
            "            #{configId}" +
            "        </foreach>" +
            "</if> " +
            "order by id asc " +
            "limit #{limitStart}, #{limit}" +
            "</script>")
    List<CouponPo> getExpiringSoonData(@Param("startTime") long startTime, @Param("endTime") long endTime, @Param("configIds") List<Long> configIds, @Param("dbName") String dbName, @Param("function") String function, @Param("limitStart") int limitStart, @Param("limit") int limit);

    @Select("select max(id) as id from tb_coupon where add_time < #{addTime} and stat = 'expired' and database() = #{dbName}")
    Long getMaxCouponIdByAddtime(@Param("addTime") Integer addTime, @Param("dbName") String dbName);

    @Select("select min(id) as id from tb_coupon where add_time < #{addTime} and stat = 'expired' and database() = #{dbName}")
    Long getMinCouponIdByAddtime(@Param("addTime") Integer addTime, @Param("dbName") String dbName);

    @Select("select max(id) as id from tb_coupon where stat in ('cancel', 'invalid') and database() = #{dbName}")
    Long getMaxCouponIdByStat(@Param("dbName") String dbName);

    @Select("select min(id) as id from tb_coupon where stat in ('cancel', 'invalid') and database() = #{dbName}")
    Long getMinCouponIdByStat(@Param("dbName") String dbName);

    @Delete("delete from tb_coupon where id >= #{idStart} and id <#{idEnd} and add_time < #{addTime} and stat = 'expired' and database() = #{dbName}")
    void deleteCouponByIdAddtimeStat(@Param("idStart") Long idStart, @Param("idEnd") Long idEnd, @Param("addTime") Integer addTime, @Param("dbName") String dbName);

    @Delete("delete from tb_coupon where id >= #{idStart} and id <#{idEnd} and stat in ('cancel', 'invalid') and database() = #{dbName}")
    void deleteCouponByIdStat(@Param("idStart") Long idStart, @Param("idEnd") Long idEnd, @Param("dbName") String dbName);
    
}
