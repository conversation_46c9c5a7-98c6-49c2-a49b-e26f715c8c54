package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.scene;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.scene.request.PermissionListRequest;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.scene.po.ScenePermissionPO;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 优惠券场景授权
 * @Date: 2022.03.03 14:21
 */
@Mapper
@Component
public interface ScenePermissionMapper {

    /**
     * 根据场景id查询授权列表
     *
     * @param sceneId
     * @return
     */
    @Select("select * from nr_coupon_scene_permission where scene_id=#{sceneId} and app_id=#{appId} and area_id =#{areaId}")
    List<ScenePermissionPO> searchPermissionListBySceneIdAppId(@Param("sceneId") Long sceneId, @Param("appId") String appId, @Param("areaId") String areaId);

    /**
     * 根据后台前端列表参数查询
     *
     * @param request
     * @param offset
     * @param limit
     * @return
     */
    @Select("select * from nr_coupon_scene_permission where scene_id=#{request.sceneId}" +
            " order by id desc limit #{offset},#{limit}")
    List<ScenePermissionPO> searchPermissionByListReq(@Param("request") PermissionListRequest request, @Param("offset") int offset, @Param("limit") int limit);

    @Select("select count(*) from nr_coupon_scene_permission where scene_id=#{sceneId}")
    Integer searchPermissionCountBySceneId(Long sceneId);

    @Insert("insert into nr_coupon_scene_permission (scene_id,app_id,app_name,app_contact,creator,status,area_id) values " +
            "(#{sceneId},#{appId},#{appName},#{appContact},#{creator},#{status},#{areaId})")
    Integer insert(ScenePermissionPO scenePermissionPO);

    @Select("select * from nr_coupon_scene_permission where id=#{id} and scene_id=#{sceneId} and app_id=#{appId}")
    ScenePermissionPO searchPermissionByIdSceneIdAppId(@Param("id") Long id, @Param("sceneId") Long sceneId, @Param("appId") String appId);


    @Update("update nr_coupon_scene_permission set status=#{status},modifier=#{modifier},update_time=#{updateTime} where id=#{id}")
    Integer updateStatusById(@Param("id") Long id, @Param("status") Integer status, @Param("modifier") String modifier, @Param("updateTime") Long updateTime);
}
