package com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponpush.impl;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponpush.CouponExpirePushRedisDao;
import com.xiaomi.nr.coupon.admin.util.StringUtil;
import com.xiaomi.nr.coupon.admin.api.utils.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

/**
 * 优惠券过期push
 * <AUTHOR>
 */
@Slf4j
@Component
public class CouponExpirePushRedisDaoImpl implements CouponExpirePushRedisDao {

    @Autowired
    @Qualifier("numberNewCouponRedisTemplate")
    private RedisTemplate redisNumberTemplate;

    private static final String KEY_COUPON_EXPIRE_PUSH_ENDTIME_CACHE = "nr:coupon:expire:push:lastEndTime:{timeMark}";


    /**
     * 设置数据区间结束时间节点
     *
     * @param timeMark String
     * @param endTime long
     */
    @Override
    public void setLastEndTime(String timeMark, long endTime) {
        ValueOperations<String, Long> operations = redisNumberTemplate.opsForValue();
        String key = getEndTimeKey(timeMark);
        try {
            operations.set(key, endTime);
        } catch (Exception e) {
            log.warn("postFeeExpirePushScheduleTask, 初次写入redis的lastCouponId缓存失败, key:{}, err:", key, e);
            operations.set(key, endTime);
        }
    }

    /**
     * 获取数据区间结束时间节点
     *
     * @param timeMark    String
     * @return long
     */
    @Override
    public long getLastEndTime(String timeMark){
        ValueOperations<String, Number> operations = redisNumberTemplate.opsForValue();
        String key = getEndTimeKey(timeMark);
        Number endTime = operations.get(key);
        if(endTime == null || endTime.longValue() <= 0) {
            log.info("postFeeExpirePushScheduleTask, 初次获取redis的lastCouponId值为空, key:{}, endTime={}", key, endTime);
            //第一次跑：取1小时以后～timeMark小时之内的数据
            return TimeUtil.getNowUnixSecond()+3600;
        }
        return endTime.longValue();
    }


    private String getEndTimeKey(String timeMark){
        return StringUtil.formatContent(KEY_COUPON_EXPIRE_PUSH_ENDTIME_CACHE, timeMark);
    }
}
