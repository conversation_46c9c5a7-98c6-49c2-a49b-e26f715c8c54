package com.xiaomi.nr.coupon.admin.infrastructure.repository.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class SkuInfoPO {

    /**
     * 一级类目名称
     */
    @ExcelProperty("一级类目")
    private String firstCategoryName;

    /**
     * 二级类目名称
     */
    @ExcelProperty("二级类目")
    private String secondCategoryName;

    /**
     * 三级类目名称
     */
    @ExcelProperty("三级类目")
    private String thirdCategoryName;

    /**
     * pid
     */
    @ExcelProperty("pid")
    private long productId;

    /**
     * gid
     */
    @ExcelProperty("gid")
    private long goodsId;

    /**
     * sku或套装id
     */
    @ExcelProperty("sku")
    private long sku;

    /**
     * 商品名称
     */
    @ExcelProperty("商品名称")
    private String goodsName;






}
