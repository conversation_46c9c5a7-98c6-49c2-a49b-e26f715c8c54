package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.scene.po;

import lombok.Data;

import java.util.Date;

/**
 * @Description: 场景授权
 * @Date: 2022.03.03 14:23
 */
@Data
public class ScenePermissionPO {
    /**
     * id
     */
    private Long id;
    /**
     * 投放场景编码
     */
    private Long sceneId;
    /**
     * appId
     */
    private String appId;
    /**
     * 服务名称
     */
    private String appName;
    /**
     * 联系人邮箱
     */
    private String appContact;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 添加时间
     */
    private Date addTime;
    /**
     * 1 生效中 2 已停用
     */
    private Integer status;
    /**
     * 修改人邮箱
     */
    private String modifier;
    /**
     * 修改时间
     */
    private Long updateTime;

    /**
     * 地区ID
     */
    private String areaId;
}
