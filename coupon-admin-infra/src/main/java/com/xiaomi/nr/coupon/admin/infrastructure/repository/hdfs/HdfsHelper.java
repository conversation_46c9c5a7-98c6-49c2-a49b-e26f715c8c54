package com.xiaomi.nr.coupon.admin.infrastructure.repository.hdfs;

import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FSDataInputStream;
import org.apache.hadoop.fs.FileStatus;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.URI;
import java.net.URISyntaxException;

@Component
@Slf4j
public class HdfsHelper {


    @Autowired
    private ApplicationContext context;

    @Value("${server.type}")
    private String activeProfile;

    @Value("${hdfs.url}")
    private String HDFS_URL;

    /**
     * 根据文件路径查找文件在哪个hdfs 集群
     *
     * @param filePath 文件地址
     * @return URL
     */
    public URI getFileUri(String filePath) {
        try {
            //String activeProfile = getActiveProfile();
            log.info("[getFileUri] active profile is ={}，filePath is={}", activeProfile, filePath);

            Configuration conf = new Configuration();
            URI uri = new URI(HDFS_URL);
            Path sourcePath = new Path(filePath);

            //设置zjyprc-hadoop为默认访问集群，去除c3prc-hadoop访问
            FileSystem fs = FileSystem.get(uri, conf);
            if (fs.exists(sourcePath)) {
                return uri;
            }
            //如果没有获取到重试
            uri = new URI(HDFS_URL);
            fs = FileSystem.get(uri, conf);
            if (fs.exists(sourcePath)) {
                return uri;
            }

        } catch (Exception e) {
            log.error("【HdfsHelper】getFileUri", e);
        }
        return null;
    }


    /**
     * 遍历目录文件
     *
     * @param uri      需要访问的hdfs的集群
     * @param filePath 需要访问的文件路径
     * @return
     * @throws IOException
     * @throws URISyntaxException
     */
    public FileStatus[] listStatus(URI uri, String filePath) throws IOException {
        FileSystem fs = FileSystem.get(uri, new Configuration());
        FileStatus[] status = fs.listStatus(new Path(filePath));
        return status;
    }


    /**
     * 判断文件是否存在
     * @param uri
     * @param filePath
     * @return
     * @throws IOException
     */
    public boolean fileExists(URI uri,String filePath)throws IOException{
        FileSystem fs = FileSystem.get(uri, new Configuration());
        return fs.exists(new Path(filePath));
    }


    /**
     * 读取文件的内容
     *
     * @param filePath
     * @throws IOException
     */
    public BufferedReader readFile(URI uri, Path filePath) throws IOException {
        Configuration config = new Configuration();
        FileSystem fs = FileSystem.get(uri, config);
        //读取文件
        FSDataInputStream hdfsInStream = fs.open(filePath);
        InputStreamReader isr = new InputStreamReader(hdfsInStream, "utf-8");
        BufferedReader reader = new BufferedReader(isr);
        return reader;
    }

    /**
     * 本地文件上传至中经云HDFS
     *
     * @param srcFile  源文件 路径
     * @param destPath hdfs路径
     */
    public void copyFileToHDFS(String srcFile, String destPath) throws Exception {
        FileInputStream fis = new FileInputStream(new File(srcFile));
        String filename = new Path(srcFile).getName();
        copyFileToHDFS(filename, fis, destPath);
    }

    public void copyFileToHDFS(String filename, InputStream fis, String destPath) throws Exception {
        Configuration config = new Configuration();
        //String activeProfile = this.getActiveProfile();
        log.info("[copyFileToHDFS] active profile is ={}", activeProfile);

        FileSystem fs  = FileSystem.get(new URI(HDFS_URL), config);

        String dir = new File(destPath).getPath();
        if (!fs.exists(new Path(dir))) {
            fs.mkdirs(new Path(dir));
        }

        log.info("[copyFileToHDFS] destionation is {}", destPath + "/" + filename);
        OutputStream os = fs.create(new Path(destPath + "/" + filename));
        //copy
        IOUtils.copyBytes(fis, os, 4096, true);
    }

    /**
     * 删除HDFS文件
     * @param uri
     * @param filePath
     * @throws IOException
     */
    public void removeHDFSFile(URI uri, String filePath) throws IOException{
        Configuration config = new Configuration();
        FileSystem fs = FileSystem.get(uri, config);
        Path path = new Path(filePath);
        if (fs.exists(path)) {
            fs.delete(path,true);
            log.info("delete file " + filePath + " success!");
        }
    }

    /**
     * 获取到当前的环境
     *
     * @return
     */
    public String getActiveProfile() {
        return context.getEnvironment().getActiveProfiles()[0];
    }

}
