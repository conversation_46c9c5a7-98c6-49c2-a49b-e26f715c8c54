package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.pointadmin.mapper;


import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.pointadmin.po.CarPointsParentScenePo;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @date 2025-06-11
 */
@Mapper
@Component
public interface CarPointsParentScenePoMapper {

    /**
     * 新建一级场景
     * @param carPointsParentScenePo
     * @return
     */
    @Insert("insert into car_points_parent_scene (name,creator) values " +
            "(#{name},#{creator})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    Integer insert(CarPointsParentScenePo carPointsParentScenePo);

    /**
     * 查询有效的一级场景列表
     * @param
     * @return
     */
    @Select("select id,name from car_points_parent_scene")
    List<CarPointsParentScenePo> searchAllParentScene();

    /**
     * 根据名称查询一级场景
     * @param name
     * @return
     */
    @Select("select * from car_points_parent_scene where name=#{name}")
    CarPointsParentScenePo searchParentSceneByName(String name);
}
