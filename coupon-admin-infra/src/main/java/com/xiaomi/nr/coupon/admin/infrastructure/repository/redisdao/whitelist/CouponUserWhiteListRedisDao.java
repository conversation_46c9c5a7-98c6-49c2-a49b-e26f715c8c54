package com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.whitelist;

import com.xiaomi.youpin.infra.rpc.errors.BizError;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-31 19:34
*/
public interface CouponUserWhiteListRedisDao {

    /**
     * 设置白名单redis缓存
     * @param userIds 用户id列表
     */
    void addUserToWhiteListCache(List<Long> userIds, int shardId) throws BizError;

    /**
     * 清空白名单缓存
     */
    void clearUserWhiteListCache() throws BizError;

    /**
     * 设置上次刷新的最大addTime
     * @param maxAddTime 最大addTime
     */
    void setLastMaxAddTime(long maxAddTime);

    /**
     * 获取上次刷新的最大addTime
     * @return 最大addTime
     */
    long getLastMaxAddTime();
}
