package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig;

import com.google.common.collect.Lists;
import com.xiaomi.goods.gms.dto.sku.SkuInfoDto;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponUpdateStatusRequest;
import com.xiaomi.nr.coupon.admin.api.enums.BizSubTypeEnum;
import com.xiaomi.nr.coupon.admin.api.enums.TranslationEnum;
import com.xiaomi.nr.coupon.admin.api.utils.I18nUtil;
import com.xiaomi.nr.coupon.admin.api.utils.TimeUtil;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponCreateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateStatusEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.goods.GoodsService;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponConfigStatusEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponScopeTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponTimeStatusEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.mail.PostOfficeMsgChannelEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.infrastructure.constant.coupon.CouponConstant;
import com.xiaomi.nr.coupon.admin.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastructure.notify.rocketmq.PostMessageService;
import com.xiaomi.nr.coupon.admin.infrastructure.notify.rocketmq.model.PostOfficeMsg;
import com.xiaomi.nr.coupon.admin.infrastructure.notify.rocketmq.model.PostOfficeMsgContent;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.CouponLogRepository;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.po.GoodItemPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.po.SearchConfigParam;
import com.xiaomi.nr.coupon.admin.util.CouponCollectionUtil;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.StringUtil;
import com.xiaomi.nr.coupon.admin.util.beancopy.BeanMapper;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 优惠券配置服务
 */
@Service
@Slf4j
public class CouponConfigAdminService {

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    @Autowired
    private CouponLogRepository couponLogRepository;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private PostMessageService postMessageService;

    @Value("${postoffice.newGoodsNotifySceneId}")
    private int newGoodsNotifySceneId;

    @Value("${coupon.update.url}")
    private String couponUpdateUrl;

    @Value("${postCoupon.update.url}")
    private String postCouponUpdateUrl;


    /**
     * 查询优惠券列表
     *
     * @param param request
     * @return BasePageResponse
     */
    public List<CouponConfigPO> searchCouponConfigList(SearchConfigParam param) throws IOException {
        log.info("coupon config search request: [{}]", param);
        // 判断时间有无交集
        if (!checkTimeIntersection(param)) {
            return Collections.emptyList();
        }

        // 使用渠道要通过es查询
        if (CollectionUtils.isNotEmpty(param.getUseChannel()) || param.getSsu() != null || param.getStoreId() != null|| CollectionUtils.isNotEmpty(param.getStoreIds())) {
            if (I18nUtil.isNotI18n()) {
                param.setAreaId(null);
            }
            log.info("CouponConfigAdminService searchCouponConfigList ES");
            Set<Long> configIds = couponConfigRepository.getESCouponIdByParam(param, TimeUtil.getNowUnixSecond());
            if (CollectionUtils.isEmpty(configIds)) {
                return Collections.emptyList();
            }
            // 查到活动过多会出现问题
            return couponConfigRepository.searchCouponListByIdSort(configIds, param.getCouponType(), param.getName(), param.getOrderBy(), param.getOrderDirection());
        }
        log.info("CouponConfigAdminService searchCouponConfigList DB");
        return couponConfigRepository.searchCouponList(param);
    }

    /**
     * 修改券状态(上下线操作)
     *
     * @param request 券配置id,状态
     */
    public void updateStatus(CouponUpdateStatusRequest request) throws Exception {
        CouponConfigPO oldPo = couponConfigRepository.searchCouponById(request.getId());
        int oldStat = oldPo.getStatus();
        if (oldStat == request.getOperateType()) {
            throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_CONFIG_UPDATE_CHANGE_STATUS.getTranslateContent());
        }

        if (CouponConfigStatusEnum.STOP_FETCHING.getCode() == oldStat) {
            throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_CONFIG_UPDATE_OPERATION_END.getTranslateContent());
        }

        CouponUpdateStatusEvent couponUpdateEvent = new CouponUpdateStatusEvent();
        couponUpdateEvent.setBizPlatform(oldPo.getBizPlatform());
        couponUpdateEvent.setOldPo(oldPo);
        CouponConfigPO newPo = new CouponConfigPO();
        BeanMapper.copy(oldPo, newPo);
        newPo.setStatus(request.getOperateType());
        couponUpdateEvent.setData(newPo);
        couponUpdateEvent.setOperator(request.getOperator());

        int count = couponConfigRepository.updateStatus(newPo, couponUpdateEvent);
        if (count < CommonConstant.ONE_INT) {
            throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_CONFIG_UPDATE_ERROR_ID.getTranslateContent() + request.getId());
        }

        applicationEventPublisher.publishEvent(couponUpdateEvent);
    }


    /**
     * 新增券
     */
    public long insertCoupon(CouponConfigPO couponConfigPO) throws Exception {

        CouponCreateEvent event = new CouponCreateEvent();
        event.setBizPlatform(couponConfigPO.getBizPlatform());

        long configId = couponConfigRepository.insert(couponConfigPO, event);

        applicationEventPublisher.publishEvent(event);

        return configId;
    }


    /**
     * 修改券
     *
     * @param couponConfigPO CouponConfigPO
     * @throws Exception 业务异常
     */
    public void updateCoupon(CouponConfigPO couponConfigPO, String modifier) throws Exception {
        CouponConfigPO oldPo = couponConfigRepository.searchCouponById(couponConfigPO.getId());
        if (oldPo == null || oldPo.getId() <= 0) {
            throw ExceptionHelper.create(ErrCode.COUPON, "未找到券配置");
        }
        couponConfigPO.setBrApplyNo(oldPo.getBrApplyNo());
        couponConfigPO.setStatus(oldPo.getStatus());
        CouponUpdateEvent couponUpdateEvent = new CouponUpdateEvent();
        couponUpdateEvent.setBizPlatform(couponConfigPO.getBizPlatform());
        couponUpdateEvent.setOldPo(oldPo);
        couponUpdateEvent.setData(couponConfigPO);
        couponUpdateEvent.setOperator(modifier);

        couponConfigRepository.update(oldPo, couponConfigPO, couponUpdateEvent);

        applicationEventPublisher.publishEvent(couponUpdateEvent);

    }

    /**
     * 检查时间状态与领取时间交集
     *
     * @param request SearchConfigParam
     * @return
     */
    private boolean checkTimeIntersection(SearchConfigParam request) {
        Integer timeStatus = request.getTimeStatus();
        Long startFetchTime = request.getStartFetchTime();
        Long endFetchTime = request.getEndFetchTime();

        if (!(timeStatus != null && startFetchTime != null && endFetchTime != null)) {
            return true;
        }

        long timeNow = TimeUtil.getNowUnixMillis();
        if (timeStatus == CouponTimeStatusEnum.NOT_START.getCode()) {
            // 未开始
            if (startFetchTime < timeNow && endFetchTime > timeNow) {
                request.setStartFetchTime(timeNow);
            } else {
                return false;
            }
        } else if (timeStatus == CouponTimeStatusEnum.IN_PROGRESS.getCode()) {
            // 进行中
            if (startFetchTime > timeNow || endFetchTime < timeNow) {
                return false;
            }
        } else if (timeStatus == CouponTimeStatusEnum.ENDED.getCode()) {
            // 已结束
            if (startFetchTime < timeNow && endFetchTime > timeNow) {
                request.setEndFetchTime(timeNow);
            } else {
                return false;
            }
        } else if (timeStatus == CouponTimeStatusEnum.STOP_FETCHING.getCode()) {
            // 已终止
            request.setTimeStatus(CouponTimeStatusEnum.STOP_FETCHING.getCode());
        }
        return true;
    }


    /**
     * 新品通知创建人和最后修改人
     *
     * @throws Exception
     */
    public void notifyNewGoods() {
        Date date = new Date();
        long now = date.getTime() / 1000L;

        Date createDate = Date.from(LocalDate.now().minusDays(6).atStartOfDay(ZoneId.systemDefault()).toInstant());

        List<Long> configIds = couponConfigRepository.selectValidConfigId(CouponScopeTypeEnum.Categories.getValue(),
                now, createDate.getTime() / 1000);
        if (CollectionUtils.isEmpty(configIds)) {
            return;
        }
        for (Long configId : configIds) {
            try {
                CouponConfigPO couponConfigPO = couponConfigRepository.searchCouponById(configId);
                if (couponConfigPO == null) {
                    continue;
                }
                // 创建日期到今日之间相隔天数是否为7的倍数
                if (!sendEmailFlag(new Date(couponConfigPO.getCreateTime() * 1000L), date)) {
                    continue;
                }

                List<Integer> categoryIds = StringUtil.convertToIntegerList(couponConfigPO.getCategoryIds());
                boolean miSupportShipment = couponConfigPO.getCouponType().equals(CouponTypeEnum.POSTFREE.getValue());
                List<SkuInfoDto> skuInfoDtosTemp = goodsService.getSkuByCategoryId(new HashSet<>(categoryIds), Lists.newArrayList(CouponConstant.BUSINESS_TYPE_SELF), miSupportShipment, Lists.newArrayList(BizSubTypeEnum.ORDINARY_3C.getCode()));
                if (CollectionUtils.isEmpty(skuInfoDtosTemp)) {
                    continue;
                }
                List<SkuInfoDto> skuInfoDtos = skuInfoDtosTemp.stream().filter(x -> {
                    return goodsService.filterSku(couponConfigPO.getCouponType(), x, null);
                }).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(skuInfoDtos)) {
                    continue;
                }
                List<Long> skuIds = skuInfoDtos.stream().map(SkuInfoDto::getSku).distinct().collect(Collectors.toList());
                GoodItemPO goodItemPO = GsonUtil.fromJson(couponConfigPO.getGoodsInclude(), GoodItemPO.class);
                List<Long> skuIdList = goodItemPO.getSku();

                List<Long> newSkuIds = CouponCollectionUtil.removeAll(skuIds, skuIdList);
                if (CollectionUtils.isEmpty(newSkuIds)) {
                    continue;
                }

                String emailReceivers = "";
                String lastModifier = couponLogRepository.getLatestLogByConfigId(couponConfigPO.getId());
                if (couponConfigPO.getCreator().equals(lastModifier)) {
                    emailReceivers = lastModifier + "@xiaomi.com";
                } else {
                    emailReceivers = couponConfigPO.getCreator() + "@xiaomi.com" + ";" + lastModifier + "@xiaomi.com";
                }
                if (StringUtils.isNotBlank(emailReceivers)) {
                    PostOfficeMsg postOfficeMsg = getMsg(emailReceivers, couponConfigPO, newSkuIds.size());
                    postMessageService.sendMesaage(postOfficeMsg, "new_good_notify_" + couponConfigPO.getId());
                }
            } catch (Exception e) {
                log.error("CouponConfigAdminService notifyNewGoods error configId:{}", configId, e);
            }

        }
    }

    private boolean sendEmailFlag(Date createTime, Date now) {
        long days = TimeUtil.daysBetweenDate(createTime, now);
        if (days % 7 == 0) {
            return true;
        }
        return false;
    }


    private PostOfficeMsg getMsg(String emailReceivers, CouponConfigPO couponConfigPO, long count) {
        PostOfficeMsg postOfficeMsg = new PostOfficeMsg();
        postOfficeMsg.setUnikey("new_good_notify_" + couponConfigPO.getId() + "_" + System.currentTimeMillis());
        postOfficeMsg.setSceneId(newGoodsNotifySceneId);
        PostOfficeMsgContent postOfficeMsgContent = new PostOfficeMsgContent();
        postOfficeMsgContent.setChannel(PostOfficeMsgChannelEnum.EMAIL.getCode());
        postOfficeMsgContent.setEmailReceivers(emailReceivers);
        Map<String, Object> params = new HashMap<>();
        params.put("configId", couponConfigPO.getId());
        params.put("configName", couponConfigPO.getName());
        params.put("count", count);
        String url = CouponTypeEnum.POSTFREE.getValue().equals(couponConfigPO.getCouponType()) ? postCouponUpdateUrl : couponUpdateUrl;
        params.put("url", "<a href=" + url + couponConfigPO.getId() + ">更新</a>");
        postOfficeMsgContent.setParams(params);
        postOfficeMsg.setContent(Lists.newArrayList(postOfficeMsgContent));
        return postOfficeMsg;
    }


}
