package com.xiaomi.nr.coupon.admin.infrastructure.annotation.aspect;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * */
@Slf4j
@Aspect
@Component
public class MethodDescAop {
    /**
     * 切点定义为注解@annotation(注解类路径)
     */
    @Pointcut("@annotation(com.xiaomi.nr.coupon.admin.infrastructure.annotation.MethodDesc)")
    public void consume(){
    }

    @Around(value = "consume()")
    public  Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        //方法开始执行时间
        Long startTime = System.currentTimeMillis();

        Object[] args = joinPoint.getArgs();
        Object result;
        Method methodClass;

        try {
            result = joinPoint.proceed(args);//执行方法
        }finally {
            //方法执行结束时间
            long endTime = System.currentTimeMillis();
            String methodName = joinPoint.getSignature().getName();
            String value  =  "方法";
            log.info(value+"[{}] 执行耗时：{}ms",methodName,endTime-startTime);
        }
        return result;
    }

}
