package com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.impl;

import com.xiaomi.nr.coupon.admin.infrastructure.constant.coupon.CouponConstant;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.GoodsConfigRedisDao;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.po.GoodsConfigRelationPo;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.StringUtil;
import com.xiaomi.nr.coupon.admin.api.utils.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 商品对应券配置的redis缓存操作对象
 * <AUTHOR>
 */
@Component
@Slf4j
public class GoodsConfigRedisDaoImpl implements GoodsConfigRedisDao {
    /**
     * 缓存操作对象
     */
    @Autowired
    @Qualifier("stringNewCouponRedisTemplate")
    private StringRedisTemplate redisTemplate;

    /**
     * key的默认过期时间48h 60 * 60 * 48
     */
    private static final long DEFAULT_EXPIRE_TIME = 172800L;

    private static Random random = new Random();

    /**
     * key随机偏移时间
     */
    private static final int DEFAULT_RANDOM_TIME = 60;


    private static final String KEY_GOODS_COUPON_CONFIG_CACHE = "nr_coupon_goods_relation_config_cache_{level}_{id}";

    private static final String GOODS_COUPON_CONFIG_KEY = "nr:coupon:reverse:{level}_{id}";

    /**
     * 每次写入100个缓存
     */
    private static final int LIMIT_REDIS_SET_COUNT = 100;

    /**
     * 每次读取100个缓存
     */
    private static final int LIMIT_REDIS_GET_COUNT = 100;

    /**
     * 将SKU/套装对应优惠券配置列表写入redis（支持批量）
     *
     * @param list List<GoodsConfigRelationPo>
     */
    @Override
    public void set(List<GoodsConfigRelationPo> list) {
        if (list == null || list.size() <= 0) {
            return;
        }

        long runStartTime = TimeUtil.getNowUnixMillis();

        ValueOperations<String, String> operations = redisTemplate.opsForValue();

        Map<String, String> data = new HashMap<>(LIMIT_REDIS_SET_COUNT);
        for (GoodsConfigRelationPo item : list) {
            if (item.getId() == null || item.getId() <= 0) {
                continue;
            }

            String key = StringUtil.formatContent(KEY_GOODS_COUPON_CONFIG_CACHE, item.getLevel(), String.valueOf(item.getId()));
            data.put(key, GsonUtil.toJson(item));

            if (data.size() >= LIMIT_REDIS_SET_COUNT) {
                operations.multiSet(data);
                data = new HashMap<>(LIMIT_REDIS_SET_COUNT);
            }
        }

        if (data.size() > 0) {
            operations.multiSet(data);
        }

        long runCostTime = TimeUtil.sinceMillis(runStartTime);
        if(runCostTime > CouponConstant.REDIS_TIMEOUT_LIMIT) {
            log.info("couponConfig.goods.relation, 设置SKU/套装对应优惠券配置列表时间比较长, runTime={}ms, keys={}", runCostTime, data.keySet());
        }
    }

    @Override
    public void set(String level, Map<Long, Set<Long>> goodCouponRelMap) {
        if (MapUtils.isEmpty(goodCouponRelMap)) {
            return;
        }

        RedisSerializer<String> serializer = redisTemplate.getStringSerializer();
        long expire = DEFAULT_EXPIRE_TIME + random.nextInt(DEFAULT_RANDOM_TIME);
        try {
            pipelinedSaveGoodsCouponInvertedIndex(level, goodCouponRelMap, serializer, expire);
        } catch (Exception e) {
            log.error("write inverted to redis first_time_fail, start to retry, level:{}, goodCouponRelMap:{}", level, goodCouponRelMap);
            pipelinedSaveGoodsCouponInvertedIndex(level, goodCouponRelMap, serializer, expire);
        }
    }

    private void pipelinedSaveGoodsCouponInvertedIndex(String level, Map<Long, Set<Long>> goodCouponRelMap, RedisSerializer<String> serializer, long expire) {
        redisTemplate.executePipelined((RedisCallback<String>) connection -> {
            goodCouponRelMap.keySet().forEach((item) -> {
                connection.setEx(serializer.serialize(getKey(level, item)), expire, serializer.serialize(StringUtils.join(goodCouponRelMap.get(item), ",")));
            });
            return null;
        }, serializer);
    }

    /**
     * 从redis读取单个SKU/套装对应优惠券配置列表
     * @param id    String
     * @param level String
     * @return GoodsConfigRelationPo
     */
    @Override
    public GoodsConfigRelationPo get(String id, String level) {
        long runStartTime = TimeUtil.getNowUnixMillis();

        ValueOperations<String, String> operations = redisTemplate.opsForValue();
        String key = StringUtil.formatContent(KEY_GOODS_COUPON_CONFIG_CACHE, level, id);
        String resJson = operations.get(key);
        if (resJson == null || resJson.isEmpty()) {
            log.warn("couponConfig.goods.relation, 从redis里取到的数据为空, id={}, level={}, key={}, resJson={}", id, level, key, resJson);
            return null;
        }
        GoodsConfigRelationPo resInfo = GsonUtil.fromJson(resJson, GoodsConfigRelationPo.class);
        if (resInfo == null || resInfo.getId() == 0) {
            log.warn("couponConfig.goods.relation, 从redis里取到的数据解析后发现不符合要求, id={}, level={}, key={}, resJson={}", id, level, key, resJson);
            return null;
        }

        long runCostTime = TimeUtil.sinceMillis(runStartTime);
        if(runCostTime > CouponConstant.REDIS_TIMEOUT_LIMIT) {
            log.info("couponConfig.goods.relation, 获取单个SKU/套装对应优惠券配置列表时间比较长, runTime={}ms, key={}", runCostTime, key);
        }

        return resInfo;
    }

    @Override
    public String getGoodsCouponRel(String level, Long id) {
        ValueOperations<String, String> operations = redisTemplate.opsForValue();
        String key = getKey(level, id);
        return operations.get(key);
    }


    public String getKey(String level, Long item) {
        return StringUtil.formatContent(GOODS_COUPON_CONFIG_KEY, level, String.valueOf(item));

    }
}