package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.pointadmin.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/12/6 17:10
 */
@Data
public class CarPointsConfigLogPo implements Serializable {
    private static final long serialVersionUID = 5764320785676138482L;

    /**
     * id
     */
    private Long id;

    /**
     * 积分批次id
     */
    private Long batchId;

    /**
     * 操作类型 1 创建, 2 修改, 3 上线, 4 下线
     */
    private Integer optType;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 修改信息
     */
    private String content;

    /**
     * 创建时间
     */
    private Date createTime;
}
