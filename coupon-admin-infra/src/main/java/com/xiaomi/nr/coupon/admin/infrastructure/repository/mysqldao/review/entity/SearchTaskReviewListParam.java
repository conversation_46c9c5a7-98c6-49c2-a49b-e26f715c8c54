package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SearchTaskReviewListParam implements Serializable {
    private static final long serialVersionUID = 3420614067825419493L;

    /**
     * 审核id
     */
    private Long id;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 券配置id
     */
    private Long configId;

    /**
     * 券配置id
     */
    private List<Long> configIds;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 当前页码
     */
    private int pageNo = 1;

    /**
     * 页面条数
     */
    private int pageSize = 10;

    /**
     * 排序字段
     */
    private String orderBy = "id";

    /**
     * 排序顺序  desc倒序   asc顺序
     */
    private String orderDirection = "desc";

    /**
     * 区域ID
     */
    private String areaId;

    /**
     * 审核状态
     */
    private Integer status;
}
