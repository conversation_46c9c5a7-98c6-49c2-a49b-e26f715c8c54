package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.usercoupon.po;

import lombok.Data;

import java.io.Serializable;

/**
 * 优惠码领取记录表po
 *
 * <AUTHOR>
 * @date 2024/6/26 15:59
 */
@Data
public class CodeCouponReceiveRecordPo implements Serializable {
    private static final long serialVersionUID = 1349561700348016374L;

    /**
     * 加密code
     */
    private String couponCode;

    /**
     * 索引
     */
    private String couponIndex;

    /**
     * 优惠券类型编号
     */
    private Integer typeId;

    /**
     * 用户手机号
     */
    private String mobileNo;

    /**
     * 外部唯一标识
     */
    private String externalId;

    /**
     * 外部用户
     */
    private String externalUserId;

    /**
     * 外投方式 1 小米投放 2 西瓜投放
     */
    private Integer assignType;

    /**
     * 券领取时间
     */
    private Integer assignTime;

    /**
     * 领取品牌
     */
    private String assignMarket;
}
