package com.xiaomi.nr.coupon.admin.infrastructure.constant.coupon;

/**
 * 优惠券配置常量
 *
 * <AUTHOR>
 */
public class CouponConstant {
    /**
     * 表中字段代表所有
     */
    public static final String DB_FIELD_ALL = "[\"*\"]";

    /**
     * redis查询超时时间限制
     */
    public static final long REDIS_TIMEOUT_LIMIT = 50L;

    /**
     * 灌券任务重试次数限制
     */
    public final static int FILL_COUPON_RETRY_TIMES = 3;


    /**
     * 商品展示限制
     */
    public final static int COUPON_GOODS_LIMIT = 500;



    /**
     * 商品业务类型
     * 1自营, 2POP，3采销，4有品，5有品VMI，6有品VMI自营
     */
    public final static int BUSINESS_TYPE_SELF = 1;
    public final static int BUSINESS_TYPE_CAIXIAO = 3;

    public static final String CN_PROFILE = "staging,preview,c3,c4";

    /**
     * 有效岗位
     */
    public static final Integer POSITION_VALID_STATUS = 1;

    /**
     * 门店的授权店编码
     */
    public static final Integer manageAuthChannel = 5;
    /**
     * 门店的直营店编码
     */
    public static final Integer manageDirectChannel = 1;
}
