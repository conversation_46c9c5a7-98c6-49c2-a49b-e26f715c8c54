package com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.po;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Map;
import java.util.Set;

@Data
public class NewConfigGoodsCachePo {

    /**
     * 券配置id
     */
    @SerializedName("id")
    private Long configId;

    /**
     * SKU
     */
    @SerializedName("s")
    private Set<Long> skus;

    /**
     * 货品ID
     */
    @SerializedName("gi")
    private Set<Long> goodsIds;

    /**
     * 套装ID
     */
    @SerializedName("pi")
    private Set<Long> packageIds;

    /**
     * 套装ID
     */
    @SerializedName("ssu")
    private Set<Long> ssuIds;

    /**
     * 扩展信息（ssu => 扩展信息）
     */
    @SerializedName("ext")
    private Map<Long, GoodExt> goodsExt;

    /**
     * 最后更新时间
     */
    @SerializedName("ut")
    private Timestamp updateTime;
}
