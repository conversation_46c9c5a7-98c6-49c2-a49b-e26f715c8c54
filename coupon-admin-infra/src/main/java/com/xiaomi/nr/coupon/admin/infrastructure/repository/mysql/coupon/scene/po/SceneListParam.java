package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.scene.po;

import lombok.Data;

/**
 * @Description: 场景列表查询参数
 * @Date: 2022.03.28 14:10
 */
@Data
public class SceneListParam {
    /**
     * 场景id
     */
    private String sceneCode;
    /**
     * 场景类型
     */
    private Integer sceneType;
    /**
     * 优惠券类型, 此查询参数查非此类型的券的场景
     */
    private String couponTypeInclude;
    /**
     * 场景名称
     */
    private String name;
    /**
     * 业务平台
     */
    private Integer bizPlatform;
    /**
     * 当前页码
     */
    private int pageNo;

    /**
     * 页面条数
     */
    private int pageSize;

    /**
     * 场景名称
     */
    private String areaId;
}
