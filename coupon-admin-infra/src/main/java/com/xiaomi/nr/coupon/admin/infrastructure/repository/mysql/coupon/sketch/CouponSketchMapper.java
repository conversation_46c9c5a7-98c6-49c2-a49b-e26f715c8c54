package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.sketch;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.sketch.po.CouponSketchListParam;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.sketch.po.CouponSketchPO;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 优惠券草稿
 * @Date: 2022.03.01 16:39
 */
@Mapper
@Component
public interface CouponSketchMapper {
    /**
     * 插入优惠券草稿
     * @param couponSketchPO
     * @return
     */
    @Options(useGeneratedKeys = true, keyProperty = "id")
    @Insert("insert into nr_coupon_sketch (coupon_name, start_fetch_time, end_fetch_time, promotion_value, bottom_type, bottom_price," +
            "apply_count, config_compress, apply_attachment, department_id, creator, coupon_type, biz_platform) values" +
            "(#{couponName},#{startFetchTime},#{endFetchTime},#{promotionValue},#{bottomType},#{bottomPrice}," +
            "#{applyCount},#{configCompress},#{applyAttachment},#{departmentId},#{creator},#{couponType},#{bizPlatform})")
    Integer insert(CouponSketchPO couponSketchPO);

    /**
     * 更新草稿
     * @param couponSketchPO
     * @return
     */
    @Update("update nr_coupon_sketch set coupon_name =#{couponName},start_fetch_time=#{startFetchTime},end_fetch_time=#{endFetchTime}," +
            "promotion_value=#{promotionValue},bottom_type=#{bottomType},bottom_price=#{bottomPrice},apply_count=#{applyCount}," +
            "config_compress=#{configCompress},apply_attachment=#{applyAttachment},department_id=#{departmentId},creator=#{creator},coupon_type=#{couponType},biz_platform=#{bizPlatform} where id=#{id}")
    Integer update(CouponSketchPO couponSketchPO);

    @Select("<script>select * from nr_coupon_sketch where delete_status!=2 and coupon_type=#{couponType} " +
            "<if test='sketchId != null and sketchId != 0'> and id = #{sketchId} </if>" +
            "<if test='couponName != null'> and coupon_name like concat(concat('%',#{couponName}),'%') </if>" +
            "<if test='startFetchTime != null and endFetchTime != null'> and start_fetch_time <![CDATA[<= ]]> #{endFetchTime} and end_fetch_time<![CDATA[>= ]]>#{startFetchTime}</if>" +
            "<if test='creator != null'> and creator like concat(concat('%',#{creator}),'%') </if>" +
            "<if test='startCreateTime != null and endCreateTime != null'> and create_time <![CDATA[>= ]]> #{startCreateTime} and create_time<![CDATA[<= ]]>#{endCreateTime}</if>" +
            " order by ${orderBy} ${orderDirection} limit #{offset},#{limit}</script>")
    List<CouponSketchPO> searchSketchListByListReq(CouponSketchListParam param);

    @Select("<script>select count(*) from nr_coupon_sketch where delete_status!=2 and coupon_type=#{couponType} " +
            "<if test='sketchId != null and sketchId != 0'> and id = #{sketchId}</if>" +
            "<if test='couponName != null'> and coupon_name like concat(concat('%',#{couponName}),'%') </if>" +
            "<if test='startFetchTime != null and endFetchTime != null'> and start_fetch_time <![CDATA[<= ]]> #{endFetchTime} and end_fetch_time<![CDATA[>= ]]>#{startFetchTime}</if>" +
            "<if test='creator != null'> and creator like concat(concat('%',#{creator}),'%') </if>" +
            "<if test='startCreateTime != null and endCreateTime != null'> and create_time <![CDATA[>= ]]> #{startCreateTime} and create_time<![CDATA[<= ]]>#{endCreateTime}</if>" +
            "</script>")
    Integer searchSketchCountByParam(CouponSketchListParam param);

    /**
     * 根据id查询草稿
     * @param id
     * @return
     */
    @Select("select * from nr_coupon_sketch where id = #{id}")
    CouponSketchPO getSketchById(@Param("id") long id);

    /**
     * 删除草稿
     * @param id
     * @return
     */
    @Update("update nr_coupon_sketch set delete_status=2 where id = #{id}")
    Integer deleteById(@Param("id") long id);
}
