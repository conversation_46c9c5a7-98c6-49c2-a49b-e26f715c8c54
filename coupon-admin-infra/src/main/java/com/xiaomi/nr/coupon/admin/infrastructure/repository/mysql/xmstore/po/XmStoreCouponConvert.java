package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.xmstore.po;

import com.xiaomi.goods.gms.dto.batch.BatchedInfoDto;
import com.xiaomi.goods.gms.dto.sku.SkuInfoDto;
import com.xiaomi.newretail.common.tools.base.exception.BaseException;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.UseChannelClientRelationDo;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.*;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.po.ExtPropPO;
import com.xiaomi.nr.coupon.admin.infrastructure.staticdata.UseChannelClientRel;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.StringUtil;
import com.xiaomi.nr.coupon.admin.api.utils.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 构建券配置缓存
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class XmStoreCouponConvert {

    @Autowired
    private UseChannelClientRel useChannelClientRel;

    /**
     * 一天24小时
     */
    private static final float DAY_HOUR = 24f;

    public List<XmStoreCouponPO> serializeXmStoreCouponPO(CouponConfigPO couponConfigPO, List<SkuInfoDto> skuInfoDtos, List<BatchedInfoDto> batchedInfoDtos) {
        List<XmStoreCouponPO> xmStoreCouponPOS = Lists.newArrayList();
        Set<String> oldUseChannel = convertUseChannel(StringUtil.convertToIntegerList(couponConfigPO.getUseChannel()));

        for (String channel : oldUseChannel) {
            if(UseChannelEnum.MiHome.getValue().equals(channel)){
                XmStoreCouponPO xmStoreCouponPO = convertXmStoreCouponPO(couponConfigPO, skuInfoDtos, batchedInfoDtos, oldUseChannel);
                xmStoreCouponPO.setSiteId(1);
                xmStoreCouponPOS.add(xmStoreCouponPO);
            }
            if(UseChannelEnum.MiAuthorized.getValue().equals(channel)){
                XmStoreCouponPO xmStoreCouponPO = convertXmStoreCouponPO(couponConfigPO, skuInfoDtos, batchedInfoDtos, oldUseChannel);
                xmStoreCouponPO.setSiteId(5);
                xmStoreCouponPOS.add(xmStoreCouponPO);
            }
        }


        return xmStoreCouponPOS;
    }


    public List<XmStoreCouponInfoPO> serializeXmStoreCouponInfoPO(CouponConfigPO couponConfigPO) {
        List<XmStoreCouponInfoPO> xmStoreCouponPOList = new ArrayList<>();
        Set<String> oldUseChannel = convertUseChannel(StringUtil.convertToIntegerList(couponConfigPO.getUseChannel()));

        for (String channel : oldUseChannel) {
            //补差券不支持小米商城
            if (UseChannelEnum.MiShop.getValue().equals(channel)) continue;

            XmStoreCouponInfoPO xmStoreCouponInfoPO = convertToXmStoreCouponInfoPO(couponConfigPO);
            if (UseChannelEnum.MiHome.getValue().equals(channel)) {
                xmStoreCouponInfoPO.setSiteId(1);
            }
            if (UseChannelEnum.MiAuthorized.getValue().equals(channel)) {
                xmStoreCouponInfoPO.setSiteId(5);
            }
            xmStoreCouponPOList.add(xmStoreCouponInfoPO);
        }
        return xmStoreCouponPOList;
    }

    /**
     * 将CouponConfigPO 转为 XmStoreCouponInfoPO
     * @param couponConfigPO
     * @return
     */
    private XmStoreCouponInfoPO convertToXmStoreCouponInfoPO(CouponConfigPO couponConfigPO) {
        XmStoreCouponInfoPO xmStoreCouponInfoPO = new XmStoreCouponInfoPO();
        xmStoreCouponInfoPO.setClassId(couponConfigPO.getId());
        xmStoreCouponInfoPO.setMissionId(couponConfigPO.getId());
        xmStoreCouponInfoPO.setName(couponConfigPO.getName());
        if (couponConfigPO.getUseTimeType() == UseTimeTypeEnum.ABSOLUTE.getValue()) {
            xmStoreCouponInfoPO.setExpireDay(1);
        } else {
            //向上取整， 防止为0
            xmStoreCouponInfoPO.setExpireDay(Math.round(couponConfigPO.getUseDuration() / DAY_HOUR));
        }
        xmStoreCouponInfoPO.setRemark(StringUtils.EMPTY);
        xmStoreCouponInfoPO.setStatus(convertAuditStatus(couponConfigPO.getStatus()));
        log.info("convertToXmStoreCouponInfoPO id:{} status:{}", xmStoreCouponInfoPO.getClassId(), xmStoreCouponInfoPO.getStatus());
        BigDecimal value = new BigDecimal(couponConfigPO.getPromotionValue()).divide(new BigDecimal(100)).setScale(2, RoundingMode.UP).stripTrailingZeros();
        xmStoreCouponInfoPO.setReduceMoney(value.intValue());
        xmStoreCouponInfoPO.setUpdatedBy(1000L);
        xmStoreCouponInfoPO.setUpdatedAt(TimeUtil.getNowUnixSecond());
        xmStoreCouponInfoPO.setCreatedBy(1000L);
        xmStoreCouponInfoPO.setCreatedAt(couponConfigPO.getCreateTime());
        return xmStoreCouponInfoPO;
    }

    private XmStoreCouponPO convertXmStoreCouponPO(CouponConfigPO couponConfigPO, List<SkuInfoDto> skuInfoDtos, List<BatchedInfoDto> batchedInfoDtos, Set<String> oldUseChannel) {
        XmStoreCouponPO xmStoreCouponPO = new XmStoreCouponPO();

        xmStoreCouponPO.setClassId(Math.toIntExact(couponConfigPO.getId()));
         xmStoreCouponPO.setOnlyCheck(2);
        xmStoreCouponPO.setName(couponConfigPO.getName());
        xmStoreCouponPO.setRangeDesc(couponConfigPO.getCouponDesc());
        xmStoreCouponPO.setCouponType(convertUseType(couponConfigPO.getPromotionType()).getValue());
        xmStoreCouponPO.setSendLimit(couponConfigPO.getApplyCount());
        xmStoreCouponPO.setIsCode(couponConfigPO.getCode());

        xmStoreCouponPO.setStartDate(couponConfigPO.getStartFetchTime().intValue());
        xmStoreCouponPO.setEndDate(couponConfigPO.getEndFetchTime().intValue());

        ExtPropPO extPropPO = GsonUtil.fromJson(couponConfigPO.getExtProp(), ExtPropPO.class);

        xmStoreCouponPO.setCheckPackage(extPropPO.getCheckPackage() == 1 ? 1 : 0);
        xmStoreCouponPO.setCheckPrice(extPropPO.getCheckPrice() == 1 ? 0 : 1);

        xmStoreCouponPO.setClient(StringUtils.join(convertClients(oldUseChannel),","));

        List<XmStoreGoodsItemPO> list = convertGoodsIncludes(skuInfoDtos, batchedInfoDtos);
        xmStoreCouponPO.setGoodsInclude(GsonUtil.toJson(list));

        StoreCouponPolicyPO storeCouponPolicyPO = convertPolicyText(couponConfigPO);
        if(PromotionTypeEnum.NyuanBuy.getValue() == couponConfigPO.getPromotionType()) {
            storeCouponPolicyPO.setTargetGoods(list);
        }

        xmStoreCouponPO.setPolicy(GsonUtil.toJson(storeCouponPolicyPO));

        xmStoreCouponPO.setStatus(convertAuditStatus(couponConfigPO.getStatus()));

        xmStoreCouponPO.setSelectType(convertSelectType(couponConfigPO));

        xmStoreCouponPO.setCreatedAt(couponConfigPO.getCreateTime().intValue());
        // 和oms 田一青约定使用1000
        xmStoreCouponPO.setCreatedBy(1000);
        xmStoreCouponPO.setUpdatedAt(Math.toIntExact(TimeUtil.getNowUnixSecond()));
        xmStoreCouponPO.setUpdatedBy(1000);

        return xmStoreCouponPO;
    }


    private List<XmStoreGoodsItemPO> convertGoodsIncludes(List<SkuInfoDto> skuInfoDtos, List<BatchedInfoDto> batchedInfoDtos) {

        List<XmStoreGoodsItemPO> xmStoreGoodsItemPOS = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(skuInfoDtos)){
            for (SkuInfoDto skuInfoDto : skuInfoDtos) {
                XmStoreGoodsItemPO xmStoreGoodsItemPO = new XmStoreGoodsItemPO();
                xmStoreGoodsItemPO.setId(skuInfoDto.getSku());
                xmStoreGoodsItemPO.setLevel("sku");
                xmStoreGoodsItemPO.setName(skuInfoDto.getGoodsName());
                xmStoreGoodsItemPOS.add(xmStoreGoodsItemPO);
            }
        }

        if(CollectionUtils.isNotEmpty(batchedInfoDtos)){
            for (BatchedInfoDto batchedInfoDto : batchedInfoDtos) {
                XmStoreGoodsItemPO xmStoreGoodsItemPO = new XmStoreGoodsItemPO();
                xmStoreGoodsItemPO.setId(batchedInfoDto.getBatchedId());
                xmStoreGoodsItemPO.setLevel("package");
                xmStoreGoodsItemPO.setName(batchedInfoDto.getBatchedName());
                xmStoreGoodsItemPOS.add(xmStoreGoodsItemPO);
            }
        }

        return xmStoreGoodsItemPOS;
    }

    private StoreCouponPolicyPO convertPolicyText(CouponConfigPO couponConfigPO) {
        StoreCouponPolicyPO policyPO = new StoreCouponPolicyPO();
        policyPO.setCode(QuotaTypeEnum.findByCode(couponConfigPO.getBottomType()).getMysqlValue());

        if(BottomTypeEnum.OverYuan.getValue() == couponConfigPO.getBottomType() || BottomTypeEnum.PerOverYuan.getValue() == couponConfigPO.getBottomType()){
            policyPO.setValue(getQuatoMoney(couponConfigPO));
        } else {
            policyPO.setValue(couponConfigPO.getBottomCount());
        }
        BigDecimal maxReduce = new BigDecimal(couponConfigPO.getMaxReduce()).divide(new BigDecimal(100)).setScale(2, RoundingMode.UP).stripTrailingZeros();
        policyPO.setMaxPrice(maxReduce.longValue());
        BigDecimal value = new BigDecimal(couponConfigPO.getPromotionValue()).divide(new BigDecimal(100)).setScale(2, RoundingMode.UP).stripTrailingZeros();
        if(PromotionTypeEnum.ConditionDiscount.getValue() == couponConfigPO.getPromotionType()){
            policyPO.setReduceDiscount(value.longValue());
        } else {
            policyPO.setReduceMoney(value.longValue());
        }
        return policyPO;
    }

    private Integer convertSelectType(CouponConfigPO couponConfigPO) {
        return 2;
    }


    public Integer convertAuditStatus(int status) {
        if (status == CouponConfigStatusEnum.ONLINE.code) {
            return 1;
        }
        return 5;
    }


    private Integer getQuatoMoney(CouponConfigPO couponConfigPO) {
        BigDecimal money = new BigDecimal(couponConfigPO.getBottomPrice());
        BigDecimal moneyYuan = money.divide(new BigDecimal(100)).setScale(2, RoundingMode.UP).stripTrailingZeros();
        return moneyYuan.intValue();
    }


    /**
     * convert
     *
     * @return List<String>
     */
    private List<Long> convertClients(Set<String> oldUseChannel) {
        if (CollectionUtils.isEmpty(oldUseChannel)) {
            throw new BaseException(-1, String.format("非法的券配置，可用券的useChannel列表不能为空，oldUseChannel=%s", oldUseChannel));
        }

        Map<String, UseChannelClientRelationDo> clientsMap = useChannelClientRel.getUseChannelClientRelation();

        List<Long> clientList = Lists.newArrayList();
        for (String channel : oldUseChannel) {
            UseChannelClientRelationDo useChannelClientRelationDo = clientsMap.get(channel);
            if (useChannelClientRelationDo != null) {
                clientList.addAll(useChannelClientRelationDo.getClientIds());
            }
        }

        if (CollectionUtils.isEmpty(clientList)) {
            throw new BaseException(-1, String.format("非法的券配置，可用券的client列表无法正常解析，oldUseChannel=%s", oldUseChannel));
        }
        return clientList;
    }


    private Set<String> convertUseChannel(List<Integer> useChannels) {

        Set<String> useChannelList = new HashSet<>();

        for (Integer useChannel : useChannels) {
            UseChannelEnum useChannelEnum = UseChannelEnum.findByCode(useChannel);
            if (UseChannelsEnum.EXCLUSIVE_SHOP.getValue() == useChannel) {
                useChannelEnum = UseChannelEnum.MiHome;
            }
            if (useChannelEnum == null) {
                continue;
            }
            useChannelList.add(useChannelEnum.getValue());
        }

        if (CollectionUtils.isEmpty(useChannelList)) {
            throw new BaseException(-1, String.format("非法的券配置，无使用渠道配置，useChannel=%s", useChannels));
        }
        return useChannelList;
    }

    /**
     * convert
     *
     * @param type Integer
     * @return String
     */
    private PromotionTypeEnum convertUseType(Integer type) {
        if (type == null || type <= 0) {
            throw new BaseException(-1, String.format("非法的券配置，使用类型不能为空，type=%d", type));
        }
        PromotionTypeEnum val = PromotionTypeEnum.getByValue(type);
        if (val == null) {
            throw new BaseException(-1, String.format("非法的券配置，使用类型超出已知范围，type=%d", type));
        }
        if (val == PromotionTypeEnum.DirectReduce) {
            val = PromotionTypeEnum.ConditionReduce;
        }
        return val;
    }


}
