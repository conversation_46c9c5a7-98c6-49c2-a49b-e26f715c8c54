package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.bdmall.po;

import com.xiaomi.goods.gms.dto.batch.BatchedInfoDto;
import com.xiaomi.goods.gms.dto.sku.SkuInfoDto;
import com.xiaomi.newretail.common.tools.base.exception.BaseException;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.UseChannelClientRelationDo;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.*;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.po.ExtPropPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.mission.po.CouponMissionPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.xmstore.po.XmStoreGoodsItemPO;
import com.xiaomi.nr.coupon.admin.infrastructure.staticdata.UseChannelClientRel;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.api.utils.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 构建券配置缓存
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class BdMallCouponConvert {

    @Autowired
    private UseChannelClientRel useChannelClientRel;

    public ActivityMarketCouponPO serializeActivityMarketCouponPO(CouponConfigPO couponConfigPO, List<SkuInfoDto> skuInfoDtos, List<BatchedInfoDto> batchedInfoDtos) {
        ActivityMarketCouponPO activityMarketCouponPO = new ActivityMarketCouponPO();

        activityMarketCouponPO.setCompanyId(10000);
        activityMarketCouponPO.setCouponId(couponConfigPO.getId());
        activityMarketCouponPO.setCouponName(couponConfigPO.getName());
        activityMarketCouponPO.setCouponDesc(couponConfigPO.getCouponDesc());
        activityMarketCouponPO.setCouponType(convertUseType(couponConfigPO.getPromotionType()).getValue());
        activityMarketCouponPO.setAllGoods("0");
        activityMarketCouponPO.setIsCode(couponConfigPO.getCode());

        activityMarketCouponPO.setStartTime(new Date(couponConfigPO.getStartFetchTime() * 1000));
        activityMarketCouponPO.setEndTime(new Date(couponConfigPO.getEndFetchTime() *1000));

        PolicyPO policyPO = convertPolicyText(couponConfigPO);
        if(PromotionTypeEnum.NyuanBuy.getValue() == couponConfigPO.getPromotionType()) {
            policyPO.setTargetGoods(convertTargetGoods(skuInfoDtos, batchedInfoDtos));
        }

        activityMarketCouponPO.setPolicyText(GsonUtil.toJson(policyPO));

        activityMarketCouponPO.setGoodsInclude(GsonUtil.toJson(convertGoodsIncludes(skuInfoDtos, batchedInfoDtos)));

        activityMarketCouponPO.setGetGoodsIncludeIds(StringUtils.join(convertGoodsIncludeIds(skuInfoDtos, batchedInfoDtos),","));

        activityMarketCouponPO.setSelectType(convertSelectType(couponConfigPO));
        activityMarketCouponPO.setSendChannel(couponConfigPO.getSendChannel());
        activityMarketCouponPO.setSendLimit(couponConfigPO.getApplyCount());

        List<Integer> useChannel = Arrays.stream(StringUtils.split(couponConfigPO.getUseChannel(), ",")).map(Integer::parseInt).collect(Collectors.toList());
        Set<String> oldUseChannel = convertUseChannel(useChannel);

        activityMarketCouponPO.setUseChannel(StringUtils.join(oldUseChannel, ","));
        activityMarketCouponPO.setUpdateTime(new Date());
        activityMarketCouponPO.setUpdateUser("");

        activityMarketCouponPO.setAddTime(new Date(couponConfigPO.getCreateTime() * 1000));
        activityMarketCouponPO.setAddUser(couponConfigPO.getCreator());
        activityMarketCouponPO.setClient(StringUtils.join(convertClients(oldUseChannel),","));
        activityMarketCouponPO.setDeductType(convertDeductType(couponConfigPO));

        ExtPropPO extPropPO = GsonUtil.fromJson(couponConfigPO.getExtProp(), ExtPropPO.class);
        activityMarketCouponPO.setCheckPackage(extPropPO.getCheckPackage() == 1 ? 1 : 2);
        activityMarketCouponPO.setCheckPrice(extPropPO.getCheckPrice() == 1 ? 1 : 2);
        activityMarketCouponPO.setIsShare(extPropPO.getShare());
        activityMarketCouponPO.setPostFree(extPropPO.getPostFree());
        activityMarketCouponPO.setIsCode(couponConfigPO.getCode());
        activityMarketCouponPO.setAuditStatus(convertAuditStatus(couponConfigPO.getStatus()));
        activityMarketCouponPO.setCreateChannel(1);
        return activityMarketCouponPO;
    }

    public MarketCouponApplyPO serializeMarketCouponApplyPO(CouponMissionPO couponMissionPO, CouponConfigPO couponConfigPO){
        MarketCouponApplyPO marketCouponApplyPO = new MarketCouponApplyPO();
        marketCouponApplyPO.setMissionId(couponMissionPO.getId());
        marketCouponApplyPO.setCouponId(couponMissionPO.getTypeId());
        marketCouponApplyPO.setMissionType(couponMissionPO.getMissionType());
        marketCouponApplyPO.setSendNum(couponMissionPO.getSendNum());
        marketCouponApplyPO.setAddTime(new Date(couponMissionPO.getAddTime() * 1000));
        marketCouponApplyPO.setAddUser(couponMissionPO.getAdminName());
        marketCouponApplyPO.setAuditStatus("approved");
        marketCouponApplyPO.setCouponInfo(GsonUtil.toJson(convertMarketCouponInfoPO(couponConfigPO)));
        marketCouponApplyPO.setUpdateTime(new Date());
        marketCouponApplyPO.setUpdateUser("");
        return marketCouponApplyPO;
    }

    private List<Long> convertGoodsIncludeIds(List<SkuInfoDto> skuInfoDtos, List<BatchedInfoDto> batchedInfoDtos) {
        List<Long> goodsList = Lists.newArrayList();

        if(CollectionUtils.isNotEmpty(skuInfoDtos)){
            goodsList.addAll(skuInfoDtos.stream().map(SkuInfoDto::getGoodsId).collect(Collectors.toList()));
        }

        if(CollectionUtils.isNotEmpty(batchedInfoDtos)){
            goodsList.addAll(batchedInfoDtos.stream().map(BatchedInfoDto::getBatchedId).collect(Collectors.toList()));
        }
        return goodsList;
    }

    private MallCouponGoodsInfoPO convertGoodsIncludes(List<SkuInfoDto> skuInfoDtos, List<BatchedInfoDto> batchedInfoDtos) {
        MallCouponGoodsInfoPO mallCouponGoodsInfoPO = new MallCouponGoodsInfoPO();

        if(CollectionUtils.isNotEmpty(skuInfoDtos)){
            Map<Long, MallGoodsItemPO> selectGoods = new HashMap<>();
            for (SkuInfoDto skuInfoDto : skuInfoDtos) {
                MallGoodsItemPO mallGoodsItemPO = new MallGoodsItemPO();
                mallGoodsItemPO.setCommodityId(skuInfoDto.getCommodityId());
                mallGoodsItemPO.setGoodsId(skuInfoDto.getGoodsId());
                mallGoodsItemPO.setProductId(skuInfoDto.getProductId());
                mallGoodsItemPO.setType("goods");
                selectGoods.put(skuInfoDto.getGoodsId(), mallGoodsItemPO);
            }
            mallCouponGoodsInfoPO.setSelectGoods(selectGoods);
        }

        if(CollectionUtils.isNotEmpty(batchedInfoDtos)){
            Map<Long, MallGoodsItemPO> selectBatchs = new HashMap<>();
            for (BatchedInfoDto batchedInfoDto : batchedInfoDtos) {
                MallGoodsItemPO mallGoodsItemPO = new MallGoodsItemPO();
                mallGoodsItemPO.setGoodsId(batchedInfoDto.getBatchedId());
                mallGoodsItemPO.setCommodityId(batchedInfoDto.getBatchedId());
                mallGoodsItemPO.setProductId(batchedInfoDto.getProductId());
                mallGoodsItemPO.setType("commodity");
                selectBatchs.put(batchedInfoDto.getBatchedId(), mallGoodsItemPO);
            }
            mallCouponGoodsInfoPO.setSelectBatchs(selectBatchs);
        }

        return mallCouponGoodsInfoPO;
    }

    private PolicyPO convertPolicyText(CouponConfigPO couponConfigPO) {
        PolicyPO policyPO = new PolicyPO();
        policyPO.setCode(QuotaTypeEnum.findByCode(couponConfigPO.getBottomType()).getMysqlValue());

        if(BottomTypeEnum.OverYuan.getValue() == couponConfigPO.getBottomType() || BottomTypeEnum.PerOverYuan.getValue() == couponConfigPO.getBottomType()){
            policyPO.setValue(getQuatoMoney(couponConfigPO));
        } else {
            policyPO.setValue(couponConfigPO.getBottomCount());
        }

        BigDecimal maxReduce = new BigDecimal(couponConfigPO.getMaxReduce()).divide(new BigDecimal(100)).setScale(2, RoundingMode.UP).stripTrailingZeros();
        policyPO.setMaxPrice(maxReduce.toPlainString());
        BigDecimal value = new BigDecimal(couponConfigPO.getPromotionValue()).divide(new BigDecimal(100)).setScale(2, RoundingMode.UP).stripTrailingZeros();
        if(PromotionTypeEnum.ConditionDiscount.getValue() == couponConfigPO.getPromotionType()){
            policyPO.setReduceDiscount(value.toPlainString());
        } else {
            policyPO.setReduceMoney(value.toPlainString());
        }
        return policyPO;
    }

    private List<XmStoreGoodsItemPO> convertTargetGoods(List<SkuInfoDto> skuInfoDtos, List<BatchedInfoDto> batchedInfoDtos) {

        List<XmStoreGoodsItemPO> xmStoreGoodsItemPOS = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(skuInfoDtos)){
            for (SkuInfoDto skuInfoDto : skuInfoDtos) {
                XmStoreGoodsItemPO xmStoreGoodsItemPO = new XmStoreGoodsItemPO();
                xmStoreGoodsItemPO.setId(skuInfoDto.getGoodsId());
                xmStoreGoodsItemPO.setLevel("goods");
                xmStoreGoodsItemPO.setName(skuInfoDto.getGoodsName());
                xmStoreGoodsItemPOS.add(xmStoreGoodsItemPO);
            }
        }

        // todo package还是 goods
        if(CollectionUtils.isNotEmpty(batchedInfoDtos)){
            for (BatchedInfoDto batchedInfoDto : batchedInfoDtos) {
                XmStoreGoodsItemPO xmStoreGoodsItemPO = new XmStoreGoodsItemPO();
                xmStoreGoodsItemPO.setId(batchedInfoDto.getBatchedId());
                xmStoreGoodsItemPO.setLevel("commodity");
                xmStoreGoodsItemPO.setName(batchedInfoDto.getBatchedName());
                xmStoreGoodsItemPOS.add(xmStoreGoodsItemPO);
            }
        }

        return xmStoreGoodsItemPOS;
    }

    private String convertSelectType(CouponConfigPO couponConfigPO) {
        if(couponConfigPO.getPromotionType() == 3) {
            return "deduction";
        } else {
            return "add";
        }
    }


    private Integer convertDeductType(CouponConfigPO couponConfigPO) {
        Integer deductType = DeductTypeEnum.OneCent.getMysqlValue();
        if(couponConfigPO.getPromotionType() == PromotionTypeEnum.NyuanBuy.getValue()) {
            if(couponConfigPO.getPromotionValue() <= 0){
                deductType = DeductTypeEnum.Zero.getMysqlValue();
            }
        }
        return deductType;
    }

    public String convertAuditStatus(int status) {
        if (status == CouponConfigStatusEnum.ONLINE.code) {
            return StatusEnum.Approved.getMysqlValue();
        }
        return StatusEnum.Cancel.getMysqlValue();
    }


    private Integer getQuatoMoney(CouponConfigPO couponConfigPO) {
        BigDecimal money = new BigDecimal(couponConfigPO.getBottomPrice());
        BigDecimal moneyYuan = money.divide(new BigDecimal(100)).setScale(2, RoundingMode.UP).stripTrailingZeros();
        return moneyYuan.intValue();
    }


    /**
     * convert
     *
     * @return List<String>
     */
    private List<Long> convertClients(Set<String> oldUseChannel) {
        if (CollectionUtils.isEmpty(oldUseChannel)) {
            throw new BaseException(-1, String.format("非法的券配置，可用券的useChannel列表不能为空，oldUseChannel=%s", oldUseChannel));
        }

        Map<String, UseChannelClientRelationDo> clientsMap = useChannelClientRel.getUseChannelClientRelation();

        List<Long> clientList = Lists.newArrayList();
        for (String channel : oldUseChannel) {
            UseChannelClientRelationDo useChannelClientRelationDo = clientsMap.get(channel);
            if (useChannelClientRelationDo != null) {
                clientList.addAll(useChannelClientRelationDo.getClientIds());
            }
        }

        if (CollectionUtils.isEmpty(clientList)) {
            throw new BaseException(-1, String.format("非法的券配置，可用券的client列表无法正常解析，oldUseChannel=%s", oldUseChannel));
        }
        return clientList;
    }


    private Set<String> convertUseChannel(List<Integer> useChannels) {

        Set<String> useChannelList = new HashSet<>();

        for (Integer useChannel : useChannels) {
            UseChannelEnum useChannelEnum = UseChannelEnum.findByCode(useChannel);
            if (UseChannelsEnum.EXCLUSIVE_SHOP.getValue() == useChannel) {
                useChannelEnum = UseChannelEnum.MiHome;
            }
            if (useChannelEnum == null) {
                continue;
            }
            useChannelList.add(useChannelEnum.getValue());
        }

        if (CollectionUtils.isEmpty(useChannelList)) {
            throw new BaseException(-1, String.format("非法的券配置，无使用渠道配置，useChannel=%s", useChannels));
        }
        return useChannelList;
    }

    /**
     * convert
     *
     * @param type Integer
     * @return String
     */
    private PromotionTypeEnum convertUseType(Integer type) {
        if (type == null || type <= 0) {
            throw new BaseException(-1, String.format("非法的券配置，使用类型不能为空，type=%d", type));
        }
        PromotionTypeEnum val = PromotionTypeEnum.getByValue(type);
        if (val == null) {
            throw new BaseException(-1, String.format("非法的券配置，使用类型超出已知范围，type=%d", type));
        }
        if (val == PromotionTypeEnum.DirectReduce) {
            val = PromotionTypeEnum.ConditionReduce;
        }
        return val;
    }

    private MarketCouponInfoPO convertMarketCouponInfoPO(CouponConfigPO couponConfigPO){

        MarketCouponInfoPO marketCouponInfoPO = new MarketCouponInfoPO();

        if(couponConfigPO.getUseTimeType() == UseTimeTypeEnum.RELATIVE.getValue()) {
            marketCouponInfoPO.setTimeType("day");
            double dayCount = ((double)couponConfigPO.getUseDuration() / 24);
            marketCouponInfoPO.setDayCount(String.format("%.2f",dayCount));
        }else {
            marketCouponInfoPO.setTimeType("range");

            marketCouponInfoPO.setRangeTime(TimeUtil.formatSecond(couponConfigPO.getStartUseTime()) +" - "+ TimeUtil.formatSecond(couponConfigPO.getEndUseTime()));
        }
        return marketCouponInfoPO;
    }

}
