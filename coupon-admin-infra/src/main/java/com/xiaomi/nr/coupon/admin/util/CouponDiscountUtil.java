package com.xiaomi.nr.coupon.admin.util;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.text.MessageFormat;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
@Slf4j
public class CouponDiscountUtil {
    private static final Pattern RULE_PATTERN = Pattern.compile(".*?\\{(.*?)}.*");
    private static final Pattern REPLACE_PATTERN = Pattern.compile("\\{.*?\\}");
    private static final ExpressionParser expressionParser = new SpelExpressionParser();

    /**
     * 格式化优惠券文案
     * spel表达式计算，如果被除数和除数都是整数，计算结果会导致0.xx折的小数被丢失
     * 所以需要保证，被除数或者除数必须至少有一个是浮点数
     * @param discountDescRule
     * @param promotionValue 默认存储的是分 例如8折    800分
     * @return
     */
    public static String formatDiscountCouponDesc(String discountDescRule, Long promotionValue) {
        StandardEvaluationContext context = new StandardEvaluationContext();
        context.setVariables(Collections.singletonMap("discount", promotionValue.doubleValue()));

        String expression = RULE_PATTERN.matcher(discountDescRule).replaceFirst("$1");
        Object value = expressionParser.parseExpression(expression).getValue(context);

        return MessageFormat.format(REPLACE_PATTERN.matcher(discountDescRule).replaceAll("{0}"), value);
    }

    public static Map<Integer, List<Integer>> convertCouponPermissionConfigMap(String permissionConfig,String areaId){
        // 获取nacos配置
        if(StrUtil.isBlank(permissionConfig)){
            return MapUtil.newHashMap();
        }
        Map<String, Map<Integer, List<Integer>>> nacosConfigMap =GsonUtil.fromJson(permissionConfig, new TypeToken<Map<String, Map<Integer, List<Integer>>>>(){}.getType());
        return nacosConfigMap.get(areaId);
    }
}
