package com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponactivity.impl;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponactivity.ProMemberRedisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

/**
 * @Description:
 * @Date: 2022.05.11 15:33
 */
@Component
@Slf4j
public class ProMemberRedisDaoImpl implements ProMemberRedisDao {

    /**
     * 券配置商品cache key
     */
    private static final String POINT_MEMBER = "points_member_conf";

    /**
     * 价保 key
     */
    private static final String PRICE_PROTECT = "shopapi:priceprotect:proMemberConfig";

    /**
     * 缓存操作对象
     */
    @Autowired
    @Qualifier("stringKarosRedisTemplate")
    private StringRedisTemplate redisStringTemplate;

    @Override
    public void setPriceProtect(String data) {
        ValueOperations<String, String> operations = redisStringTemplate.opsForValue();
        operations.set(PRICE_PROTECT, data);
    }

    @Override
    public void setPoints(String data) {
        ValueOperations<String, String> operations = redisStringTemplate.opsForValue();
        operations.set(POINT_MEMBER, data);
    }
}
