package com.xiaomi.nr.coupon.admin.infrastructure.repository;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.sketch.CouponSketchMapper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.sketch.po.CouponSketchListParam;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.sketch.po.CouponSketchPO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 优惠券草稿
 * @Date: 2022.03.01 17:11
 */
@Component
public class CouponSketchRepository {

    @Autowired
    private CouponSketchMapper couponSketchMapper;

    /**
     * 插入草稿
     * @param couponSketchPO
     * @return
     */
    public Integer insert(CouponSketchPO couponSketchPO) {
        return couponSketchMapper.insert(couponSketchPO);
    }

    /**
     * 更新草稿
     * @param couponSketchPO
     * @return
     */
    public Integer update(CouponSketchPO couponSketchPO) {
        return couponSketchMapper.update(couponSketchPO);
    }

    /**
     * 根据页面查询条件查询草稿列表
     * @param param
     * @return
     */
    public List<CouponSketchPO> searchSketchList(CouponSketchListParam param) {
        return couponSketchMapper.searchSketchListByListReq(param);
    }

    /**
     * 根据页面查询条件查询草稿数
     * @param param
     * @return
     */
    public int searchCount(CouponSketchListParam param) {
        return couponSketchMapper.searchSketchCountByParam(param);
    }

    /**
     * 根据id查询草稿
     * @param id
     * @return
     */
    public CouponSketchPO getSketchById(long id) {
        return couponSketchMapper.getSketchById(id);
    }

    /**
     * 删除草稿
     * @param id
     * @return
     */
    public int delete(long id) {
        return couponSketchMapper.deleteById(id);
    }
}
