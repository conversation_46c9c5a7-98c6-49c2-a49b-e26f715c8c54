package com.xiaomi.nr.coupon.admin.infrastructure.rpc.goods;

import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.xiaomi.goods.gms.api.batch.BatchedInfoService;
import com.xiaomi.goods.gms.api.category.DubboGmsCategoryService;
import com.xiaomi.goods.gms.api.sku.SkuInfoService;
import com.xiaomi.goods.gms.dto.batch.*;
import com.xiaomi.goods.gms.dto.category.GetCategoryProductByCateReq;
import com.xiaomi.goods.gms.dto.common.PageInfoDto;
import com.xiaomi.goods.gms.dto.sku.*;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsOnlineStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.goods.po.GoodsPo;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.goods.po.PackagePo;
import com.xiaomi.nr.coupon.admin.util.ResultValidator;
import com.xiaomi.nr.goods.tob.api.SsuQueryService;
import com.xiaomi.nr.goods.tob.dto.common.PageDTO;
import com.xiaomi.nr.goods.tob.dto.common.PageResponse;
import com.xiaomi.nr.goods.tob.dto.request.ssu.SsuFilterDTO;
import com.xiaomi.nr.goods.tob.dto.request.ssu.SsuQueryRequest;
import com.xiaomi.nr.goods.tob.dto.response.ssu.SsuDTO;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 调用gms接口获取goods信息
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class GmsProxyService {

    /**
     * 分页查询GMS商品信息服务
     */
    @Reference(check = false, interfaceClass = SkuInfoService.class, group = "${gms.dubbo.group}", version = "1.0", timeout = 5000)
    private SkuInfoService skuInfoService;

    /**
     * 分页查询GMS套装信息服务
     */
    @Reference(check = false, interfaceClass = BatchedInfoService.class, group = "${gms.dubbo.group}", version = "1.0", timeout = 5000)
    private BatchedInfoService batchedInfoService;

    @Reference(check = false, interfaceClass = DubboGmsCategoryService.class, group = "${gms.dubbo.group}", version = "1.0", timeout = 5000)
    private DubboGmsCategoryService dubboGmsCategoryService;

    @Reference(check = false, interfaceClass = SsuQueryService.class, group = "${gms.dubbo.group}", version = "1.0", timeout = 5000)
    private SsuQueryService ssuQueryService;

    @Autowired
    private ThreadPoolTaskExecutor queryGoodsAsyncExecutor;


    /**
     * 根据三级分类id查询商品信息
     */
    @SuppressWarnings("unchecked")
    public List<SkuInfoDto> getProductDetailListByCate(int cateId, List<Integer> businessType, boolean needMiSupportShipment, List<Integer> bizSubTypeList) throws Exception {
        try {
            GetCategoryProductByCateReq request = new GetCategoryProductByCateReq();
            request.setCateId(cateId);
            request.setIsNeedMiSupportShipment(needMiSupportShipment);
            Result<List<SkuInfoDto>> result = dubboGmsCategoryService.getProductDetailListByCate(request);
            ResultValidator.validate(result, "根据三级分类id查询商品失败");
            if (Objects.isNull(result.getData())) {
                return Collections.EMPTY_LIST;
            }

            List<SkuInfoDto> res = result.getData();
            if (Objects.nonNull(bizSubTypeList)) {
                res = res.stream().filter(x -> bizSubTypeList.contains(x.getBizSubType())).collect(Collectors.toList());
            }
            return CollectionUtils.isEmpty(businessType) ? res : res.stream().filter(x -> businessType.contains(x.getBusinessType())).collect(Collectors.toList());

        } catch (Exception e) {
            log.error("getProductDetailListByCate Exception cateId:{}", cateId, e);
            throw e;
        }
    }


    /**
     * 根据skuid查询sku信息
     *
     * @param needMiSupportShipment 为true时，SkuInfoDto的参数miSupportShipment才有意义，
     *                              1. miSupportShipment = True, 代表米家大仓发货商品
     *                              2. miSupportShipment = False, 代表米家非大仓发货商品
     *                              3. miSupportShipment = null, 代表没有配置是否米家大仓发货属性
     * @param bizSubTypeList        商品业务类型枚举 参见 BizSubTypeEnum 定义：普通3c商品、运营商专属商品、运营商泛全商品
     */
    @SuppressWarnings("unchecked")
    public List<SkuInfoDto> queryListBySkuIds(List<Long> skuIds, boolean isNeedStock, boolean isNeedCategory, boolean needMiSupportShipment, List<Integer> bizSubTypeList) throws Exception {
        List<List<Long>> skuPartList = Lists.partition(skuIds, 100);
        List<Future<List<SkuInfoDto>>> futureList = new ArrayList<>();

        for (List<Long> skuIdsPart : skuPartList) {
            Future<List<SkuInfoDto>> future = queryGoodsAsyncExecutor.submit(() -> {
                try {
                    SkuRequest skuRequest = new SkuRequest();
                    skuRequest.setSkuList(skuIdsPart);
                    skuRequest.setIsNeedStock(isNeedStock);
                    skuRequest.setIsNeedCategory(isNeedCategory);
                    skuRequest.setIsNeedMiSupportShipment(needMiSupportShipment);
                    Result<SkuInfoResponse> result = skuInfoService.listSku(skuRequest);
                    ResultValidator.validate(result, "查询商品失败");
                    if (Objects.isNull(result.getData())) {
                        return Collections.EMPTY_LIST;
                    }

                    Map<Long, List<SkuInfoDto>> skuMap = result.getData().getSkuMap();
                    if (MapUtils.isEmpty(skuMap)) {
                        return Collections.EMPTY_LIST;
                    }

                    List<SkuInfoDto> skuInfoDtoList = new LinkedList<>();
                    for (Map.Entry<Long, List<SkuInfoDto>> entry : skuMap.entrySet()) {
                        List<SkuInfoDto> skuInfos = entry.getValue();
                        if (Objects.nonNull(bizSubTypeList)) {
                            skuInfos = skuInfos.stream().filter(x -> bizSubTypeList.contains(x.getBizSubType())).collect(Collectors.toList());
                        }
                        skuInfoDtoList.addAll(skuInfos);
                    }
                    return skuInfoDtoList;
                } catch (Exception e) {
                    log.error("GmsProxyService queryListBySkuIds Exception skuIdsPart:{}", skuIdsPart, e);
                    throw ExceptionHelper.create(GeneralCodes.InternalError, "查询sku失败");
                }
            });
            futureList.add(future);
        }

        List<SkuInfoDto> returnList = new ArrayList<>();
        for (Future<List<SkuInfoDto>> future : futureList) {
            if (Objects.nonNull(future)) {
                List<SkuInfoDto> infoDtoList = future.get();
                returnList.addAll(infoDtoList);
            }
        }
        return returnList;
    }

    /**
     * 根据skuid查询sku信息
     */
    @SuppressWarnings("unchecked")
    public List<BatchedInfoDto> queryListByPackageIds(List<Long> packageIds, Boolean isNeedStock) throws Exception {
        List<List<Long>> packagePartList = Lists.partition(packageIds, 30);

        List<Future<List<BatchedInfoDto>>> futureList = new ArrayList<>();

        for (List<Long> packageIdsPart : packagePartList) {
            Future<List<BatchedInfoDto>> future = queryGoodsAsyncExecutor.submit(() -> {
                try {
                    BatchedRequest batchedRequest = new BatchedRequest();
                    batchedRequest.setBatchedIds(packageIdsPart);
                    batchedRequest.setIsNeedStock(isNeedStock);
                    Result<BatchedMapResponse> result = batchedInfoService.listBatched(batchedRequest);
                    ResultValidator.validate(result, "查询商品失败");
                    if (result.getData() != null) {
                        List<BatchedInfoDto> infoDtos = new LinkedList<>();
                        infoDtos.addAll(result.getData().getBatchedMap().values());
                        return infoDtos;
                    }
                    return Collections.EMPTY_LIST;
                } catch (Exception e) {
                    log.error("GmsProxyService queryListBySkuIds Exception packageIdsPart:{}", packageIdsPart, e);
                    throw ExceptionHelper.create(GeneralCodes.InternalError, "查询套装失败");
                }
            });
            futureList.add(future);
        }

        List<BatchedInfoDto> returnList = new ArrayList<>();
        for (Future<List<BatchedInfoDto>> future : futureList) {
            if (future != null) {
                List<BatchedInfoDto> infoDtoList = future.get();
                returnList.addAll(infoDtoList);
            }
        }

        return returnList;

    }


    public List<SsuDTO> queryListBySsuIds(List<Long> ssuIds) throws Exception {
        List<List<Long>> ssuIdPartList = Lists.partition(ssuIds, 30);

        List<Future<List<SsuDTO>>> futureList = new ArrayList<>();

        for (List<Long> ssuIdsPart : ssuIdPartList) {
            Future<List<SsuDTO>> future = queryGoodsAsyncExecutor.submit(() -> {
                try {
                    SsuQueryRequest request = new SsuQueryRequest();

                    // 渠道为 car
                    request.setChannelPath("car");

                    SsuFilterDTO filter = new SsuFilterDTO();
                    filter.setSsuIdList(ssuIdsPart);
                    request.setFilter(filter);

                    // 分页信息
                    PageDTO pageDTO = new PageDTO();
                    pageDTO.setPageNum(1);
                    pageDTO.setPageSize(ssuIdsPart.size());
                    request.setPage(pageDTO);

                    Result<PageResponse<SsuDTO>> result = ssuQueryService.selectSsu(request);
                    ResultValidator.validate(result, "查询ssu失败");

                    PageResponse<SsuDTO> pageResp = result.getData();
                    if (pageResp != null && !CollectionUtils.isEmpty(pageResp.getList())) {
                        return pageResp.getList();
                    }

                    return Collections.emptyList();
                } catch (Exception e) {
                    log.error("GmsProxyService queryListBySkuIds Exception packageIdsPart:{}", ssuIdsPart, e);
                    throw ExceptionHelper.create(GeneralCodes.InternalError, "查询ssu失败");
                }
            });
            futureList.add(future);
        }

        List<SsuDTO> returnList = new ArrayList<>();
        for (Future<List<SsuDTO>> future : futureList) {
            if (future != null) {
                List<SsuDTO> infoDtoList = future.get();
                returnList.addAll(infoDtoList);
            }
        }

        return returnList;

    }

    /**
     * 分页调用gms接口返回Sku信息
     *
     * @param pageNum  分页查询页码
     * @param pageSize 页面大小
     * @return List<GoodsPo> Sku基本信息返列表
     * @throws BizError 业务异常
     */
    public List<GoodsPo> getGoodsList(int pageNum, int pageSize, GoodsOnlineStatusEnum goodsOnlineStatusEnum) throws BizError {

        Stopwatch stopwatch = Stopwatch.createStarted();
        SkuPageRequest skuPageRequest = new SkuPageRequest();

        PageInfoDto pageInfoDto = new PageInfoDto();
        pageInfoDto.setPageNum(pageNum);
        pageInfoDto.setPageSize(pageSize);
        skuPageRequest.setPage(pageInfoDto);
        if (Objects.isNull(goodsOnlineStatusEnum)) {
            goodsOnlineStatusEnum = GoodsOnlineStatusEnum.ON_SHELF_ONLINE;
        }

        // 取线上线下上下架状态的商品信息(0下架，1上架)
        switch (goodsOnlineStatusEnum) {
            case ON_SHELF_OFFLINE:
                // 线下上架
                skuPageRequest.setOnShelfOffline(GoodsOnlineStatusEnum.ON_SHELF_OFFLINE.getStat());
                break;
            case DOWN_SHELF_ONLINE:
                // 线上下架
                skuPageRequest.setOnShelfOnline(GoodsOnlineStatusEnum.DOWN_SHELF_ONLINE.getStat());
                break;
            case DOWN_SHELF_OFFLINE:
                // 线下下架
                skuPageRequest.setOnShelfOffline(GoodsOnlineStatusEnum.DOWN_SHELF_OFFLINE.getStat());
                break;
            default: // 默认取线上上架的商品
                skuPageRequest.setOnShelfOnline(GoodsOnlineStatusEnum.ON_SHELF_ONLINE.getStat());
                break;
        }

        // 是否需要库存 false不需要
        skuPageRequest.setIsNeedStock(false);
        Result<SkuPageResponse> response = null;

        // 请求1次＋重试1次(共2次请求)
        int retry = 1;
        while (true) {
            try {
                response = skuInfoService.querySkuList(skuPageRequest);
                break;
            } catch (Exception e) {
                log.error("querySkuList, 请求GMS出错, retry={}, pageNum={}, pageSize={}, runTime={}", retry, pageNum, pageSize, stopwatch.elapsed(TimeUnit.MILLISECONDS), e);
                retry++;
                if (retry > 2) {
                    throw ExceptionHelper.create(ErrCode.COUPON, "请求GMS失败");
                }
            }
        }

        ResultValidator.validate(response, "请求GMS失败, 未能正常响应, code不为0");

        SkuPageResponse data = response.getData();
        if (Objects.isNull(data)) {
            log.error("querySkuList, 请求GMS结果data为空, retry={}, pageNum={}, pageSize={}, runTime={}, response={}", retry, pageNum, pageSize, stopwatch.elapsed(TimeUnit.MILLISECONDS), response);
            return Collections.emptyList();
        }

        List<SkuInfoDto> skuInfoDtoList = data.getSkuList();
        if (CollectionUtils.isEmpty(skuInfoDtoList)) {
            log.info("querySkuList, 请求GMS结果skuList为空, retry={}, pageNum={}, pageSize={}, runTime={}, response={}", retry, pageNum, pageSize, stopwatch.elapsed(TimeUnit.MILLISECONDS), response);
            return Collections.emptyList();
        }

        log.info("querySkuList, 请求GMS成功, retry={}, pageNum={}, pageSize={}, runTime={}, listSize={}", retry, pageNum, pageSize, stopwatch.elapsed(TimeUnit.MILLISECONDS), skuInfoDtoList.size());
        stopwatch.stop();

        List<GoodsPo> result = new ArrayList<>();
        for (SkuInfoDto skuInfoDto : skuInfoDtoList) {
            GoodsPo goodsPo = convertSkuInfo2GoodsPo(skuInfoDto);
            if (Objects.isNull(goodsPo)) {
                continue;
            }
            result.add(goodsPo);
        }

        return result;
    }

    /**
     * 分页调用gms接口返回Sku信息
     *
     * @param pageNum  分页查询页码
     * @param pageSize 页面大小
     * @return List<GoodsPo> Sku基本信息返列表
     * @throws BizError 业务异常
     */
    public List<GoodsPo> getGoodsList(int pageNum, int pageSize) throws BizError {

        Stopwatch stopwatch = Stopwatch.createStarted();
        SkuPageRequest skuPageRequest = new SkuPageRequest();

        PageInfoDto pageInfoDto = new PageInfoDto();
        pageInfoDto.setPageNum(pageNum);
        pageInfoDto.setPageSize(pageSize);
        skuPageRequest.setPage(pageInfoDto);

        // 是否需要库存 false不需要
        skuPageRequest.setIsNeedStock(false);
        Result<SkuPageResponse> response = null;

        // 请求1次＋重试1次(共2次请求)
        int retry = 1;
        while (true) {
            try {
                response = skuInfoService.querySkuList(skuPageRequest);
                break;
            } catch (Exception e) {
                log.error("querySkuList, 请求GMS出错, retry={}, pageNum={}, pageSize={}, runTime={}", retry, pageNum, pageSize, stopwatch.elapsed(TimeUnit.MILLISECONDS), e);
                retry++;
                if (retry > 2) {
                    throw ExceptionHelper.create(ErrCode.COUPON, "请求GMS失败");
                }
            }
        }

        ResultValidator.validate(response, "请求GMS失败, 未能正常响应, code不为0");

        SkuPageResponse data = response.getData();
        if (Objects.isNull(data)) {
            log.error("querySkuList, 请求GMS结果data为空, retry={}, pageNum={}, pageSize={}, runTime={}, response={}", retry, pageNum, pageSize, stopwatch.elapsed(TimeUnit.MILLISECONDS), response);
            return Collections.emptyList();
        }

        List<SkuInfoDto> skuInfoDtoList = data.getSkuList();
        if (CollectionUtils.isEmpty(skuInfoDtoList)) {
            log.info("querySkuList, 请求GMS结果skuList为空, retry={}, pageNum={}, pageSize={}, runTime={}, response={}", retry, pageNum, pageSize, stopwatch.elapsed(TimeUnit.MILLISECONDS), response);
            return Collections.emptyList();
        }

        log.info("querySkuList, 请求GMS成功, retry={}, pageNum={}, pageSize={}, runTime={}, listSize={}", retry, pageNum, pageSize, stopwatch.elapsed(TimeUnit.MILLISECONDS), skuInfoDtoList.size());
        stopwatch.stop();

        List<GoodsPo> result = new ArrayList<>();
        for (SkuInfoDto skuInfoDto : skuInfoDtoList) {
            GoodsPo goodsPo = convertSkuInfo2GoodsPo(skuInfoDto);
            if (Objects.isNull(goodsPo)) {
                continue;
            }
            result.add(goodsPo);
        }

        return result;
    }

    /**
     * 分页调用gms接口返回套装基本信息
     *
     * @param pageNum  分页查询页码
     * @param pageSize 页面大小
     * @return List<PackagePo> 套装信息列表
     * @throws BizError 业务异常
     */
    public List<PackagePo> getPackageList(int pageNum, int pageSize) throws BizError {
        Stopwatch stopwatch = Stopwatch.createStarted();
        Result<BatchedBasePageResponse> response = null;
        PageInfoDto pageInfoDto = new PageInfoDto();
        pageInfoDto.setPageNum(pageNum);
        pageInfoDto.setPageSize(pageSize);

        // 请求1次＋重试1次(共2次请求)
        int retry = 1;
        while (true) {
            try {
                response = batchedInfoService.listBatchedBase(pageInfoDto);
                break;
            } catch (Exception e) {
                log.error("listBatchedBase, 请求GMS出错, retry={}, pageNum={}, pageSize={}, runTime={}", retry, pageNum, pageSize, stopwatch.elapsed(TimeUnit.MILLISECONDS), e);
                retry++;
                if (retry > 2) {
                    throw ExceptionHelper.create(ErrCode.COUPON, "请求GMS失败");
                }
            }
        }

        ResultValidator.validate(response, "请求GMS失败, 未能正常响应, code不为0");

        BatchedBasePageResponse batchedBase = response.getData();
        if (Objects.isNull(batchedBase)) {
            log.error("listBatchedBase, 请求GMS结果data为空, retry={}, pageNum={}, pageSize={}, runTime={}, response={}", retry, pageNum, pageSize, stopwatch.elapsed(TimeUnit.MILLISECONDS), response);
            return Collections.emptyList();
        }

        List<BatchedBaseDto> batchedBaseDtoList = batchedBase.getBatchedList();
        if (CollectionUtils.isEmpty(batchedBaseDtoList)) {
            log.info("listBatchedBase, 请求GMS结果batchedList为空, retry={}, pageNum={}, pageSize={}, runTime={}, response={}", retry, pageNum, pageSize, stopwatch.elapsed(TimeUnit.MILLISECONDS), response);
            return Collections.emptyList();
        }

        log.info("listBatchedBase, 请求GMS成功, retry={}, pageNum={}, pageSize={}, runTime={}, listSize={}", retry, pageNum, pageSize, stopwatch.elapsed(TimeUnit.MILLISECONDS), batchedBaseDtoList.size());
        stopwatch.stop();

        List<PackagePo> result = new ArrayList<>();
        for (BatchedBaseDto batchedBaseDto : batchedBaseDtoList) {
            PackagePo packagePo = convertBatchedBase2PackagePo(batchedBaseDto);
            if (Objects.isNull(packagePo)) {
                continue;
            }
            result.add(packagePo);
        }

        return result;
    }

    /**
     * SkuInfoDto to GoodsPo
     *
     * @param skuInfoDto gms查询接口返回SkuInfo信息
     * @return GoodsPo   生成券配置缓存Sku基本信息
     */
    private GoodsPo convertSkuInfo2GoodsPo(SkuInfoDto skuInfoDto) {
        if (Objects.isNull(skuInfoDto)) {
            return null;
        }

        GoodsPo goodsPo = new GoodsPo();
        goodsPo.setGoodsId(skuInfoDto.getGoodsId());
        goodsPo.setSku(skuInfoDto.getSku());
        goodsPo.setCommodityId(skuInfoDto.getCommodityId());
        goodsPo.setProductId(skuInfoDto.getProductId());

        return goodsPo;
    }


    /**
     * BatchedBaseDto to PackagePo
     *
     * @param batchedBaseDto gms接口返回的套装信息
     * @return PackagePo     生成券配置缓存的套装基本信息
     */
    private PackagePo convertBatchedBase2PackagePo(BatchedBaseDto batchedBaseDto) {
        if (Objects.isNull(batchedBaseDto)) {
            return null;
        }

        PackagePo packagePo = new PackagePo();
        packagePo.setPackageId(batchedBaseDto.getBatchedId());
        packagePo.setProductId(batchedBaseDto.getProductId());

        return packagePo;
    }


    /**
     * 分页查询商品信息
     *
     * @param request GmsGoodsListRequest
     * @return GmsGoodsListDto
     * @throws BizError 业务异常
     */
    public SkuPageResponse querySkuList(SkuPageRequest request) throws BizError {

        Result<SkuPageResponse> result = null;
        try {

            result = skuInfoService.querySkuList(request);
            ResultValidator.validate(result, "查询商品失败");

            return result.getData();
        } catch (Exception e) {
            log.error("GmsProxyService querySkuList Exception packageIdsPart:{}", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "分页查询SKU信息失败");
        }
    }


    /**
     * 分页查询套装信息
     *
     * @param request BatchedListRequest
     * @return BatchedPageResponse
     * @throws BizError 业务异常
     */
    public BatchedPageResponse queryPackageList(BatchedListRequest request) throws BizError {

        Result<BatchedPageResponse> result = null;
        try {

            result = batchedInfoService.queryBatched(request);
            ResultValidator.validate(result, "查询商品失败");

            return result.getData();
        } catch (Exception e) {
            log.error("GmsProxyService querySkuList Exception packageIdsPart:{}", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "分页查询信息失败");
        }
    }
}
