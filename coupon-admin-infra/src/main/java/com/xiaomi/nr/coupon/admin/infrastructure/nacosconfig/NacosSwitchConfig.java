package com.xiaomi.nr.coupon.admin.infrastructure.nacosconfig;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: nacos开关配置
 * @author: hejiapeng
 * @Date 2022/3/23 7:14 下午
 * @Version: 1.0
 **/
@Component
@Data
@NacosConfigurationProperties(dataId = NacosSwitchConfig.dataId, autoRefreshed = true, ignoreNestedProperties = true, type = ConfigType.JSON)
public class NacosSwitchConfig {

    public static final String dataId = "coupon_admin_switch";

    private boolean stopWriteBdData;

    private boolean stopWriteOmsData;

    private boolean stopWriteCouponData;

    private boolean runWriteMissionData;

    private boolean refreshOldGoodsData = true;
    /**
     * 车商城礼品兑换券SSU白名单
     */
    private List<String> carShopGiftSsuWhitelist = new ArrayList<>();

    /**
     * 整车券配置id列表
     * 用于刷新用户白名单
     */
    private List<Long> couponConfigIdList = new ArrayList<>();
}
