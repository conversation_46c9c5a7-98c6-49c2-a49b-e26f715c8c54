package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.pointadmin.mapper;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.pointadmin.po.CarPointsBaseConfigPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @date 2023/12/6
 */
@Mapper
public interface CarPointsBaseConfigMapper {
    /**
     * 获取最后一个有效的通用配置
     *
     * @return CarPointsBaseConfigPo
     */
    @Select("select * from car_points_base_config where status = 1 order by update_time desc limit 1")
    CarPointsBaseConfigPo findLastValid();
}
