package com.xiaomi.nr.coupon.admin.infrastructure.repository;

import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.es.CouponConfigESHelper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.CouponConfigMapper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.GoodsConfigRedisDao;
import com.xiaomi.nr.coupon.admin.api.utils.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;

/**
 * @description: 商品可用优惠券配置资源库
 * @author: hejiapeng
 * @Date 2022/2/28 4:25 下午
 * @Version: 1.0
 **/
@Slf4j
@Component
public class GoodsCouponRepository {

    @Autowired
    private CouponConfigMapper couponConfigMapper;

    @Autowired
    @Qualifier("couponInvertedTOCHelper")
    private CouponConfigESHelper couponConfigESHelper;

    @Autowired
    private GoodsConfigRedisDao goodsConfigRedisDao;


    /**
     * 获取发生变化的券配置
     *
     * @param lastUpdateTime
     * @return
     */
    public List<CouponConfigPO> getChangedCouponConfig(String lastUpdateTime) {
        List<CouponConfigPO> couponConfigs = Lists.newArrayList();
        long validFinalTime = TimeUtil.getNowUnixSecond();
        long lastId = 0;
        while (lastId >= 0) {
            List<CouponConfigPO> tmpCouponConfigs = couponConfigMapper.getValidConfigByOffset(validFinalTime, lastUpdateTime, lastId, 100);
            if (CollectionUtils.isEmpty(tmpCouponConfigs)) {
                break;
            }
            if (tmpCouponConfigs.size() < 100) {
                lastId = -1;
            } else {
                lastId = tmpCouponConfigs.get(tmpCouponConfigs.size() - 1).getId();
            }
            couponConfigs.addAll(tmpCouponConfigs);
        }
        return couponConfigs;
    }


    /**
     * 更新商品和券关系redis
     *
     * @param goods
     * @param goodType
     * @throws IOException
     */
    public void updateGoodCouponRelRedis(Set<Long> goods, String goodType) {
        // 更新倒排
        long timeNow = TimeUtil.getNowUnixSecond();
        List<List<Long>> skuPartition = Lists.partition(new ArrayList<>(goods), 3);
        for (List<Long> skuList : skuPartition) {
            try {
                Map<Long, Set<Long>> skuCouponRelMap = couponConfigESHelper.batchGetInverted(goodType, skuList, timeNow, null);
                goodsConfigRedisDao.set(goodType, skuCouponRelMap);
            } catch (Exception e) {
                log.error("GoodsCouponRepository.full_updateGoodCouponRelRedis failed goods:{}, level:{}, error info:{}", skuList, goodType, e.getMessage(), e);
            }
        }
    }

    /**
     * 更新商品和券关系redis
     *
     * @param goods
     * @param skuConfigIdMap
     * @param goodType
     * @throws IOException
     */
    public void updateGoodCouponRelRedis(Set<Long> goods, Map<Long, List<Long>> skuConfigIdMap, String goodType) {
        // 更新倒排
        long timeNow = TimeUtil.getNowUnixSecond();
        List<List<Long>> skuPartition = com.google.common.collect.Lists.partition(new ArrayList<>(goods), 5);
        for (List<Long> skuList : skuPartition) {
            try {
                Map<Long, Set<Long>> skuCouponRelMap = couponConfigESHelper.batchGetInverted(goodType, skuList, timeNow, null);
                for (Long sku : skuList) {
                    if (skuCouponRelMap.containsKey(sku)) {
                        skuCouponRelMap.get(sku).addAll(skuConfigIdMap.get(sku));
                    } else {
                        skuCouponRelMap.put(sku, new HashSet<>(skuConfigIdMap.get(sku)));
                    }
                }
                goodsConfigRedisDao.set(goodType, skuCouponRelMap);;
            } catch (Exception e) {
                log.error("GoodsCouponRepository.incr_updateGoodCouponRelRedis faild good:{}, level:{}", skuList, goodType, e);
            }
        }
    }
}
