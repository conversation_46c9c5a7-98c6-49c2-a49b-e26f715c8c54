package com.xiaomi.nr.coupon.admin.infrastructure.repository;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.userredpacket.UserRedPacketMapper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.userredpacket.po.SeachUserRedpacketListResult;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.userredpacket.po.SearchUserRedpacketListParam;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.userredpacket.po.UserRedpacketPO;
import com.xiaomi.nr.coupon.admin.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Component
public class UserRedPacketRepository {

    @Autowired
    private UserRedPacketMapper userRedPacketMapper;

    @Autowired
    private StringRedisTemplate stringPulseTypeRedisTemplate;


    /**
     * 根据id查询
     * @return
     */
    public SeachUserRedpacketListResult selectList(SearchUserRedpacketListParam param){
        SeachUserRedpacketListResult result = new SeachUserRedpacketListResult();
        long count = userRedPacketMapper.selectCount(param);
        if(count > 0){
            List<UserRedpacketPO> list = userRedPacketMapper.selectList(param);
            if (CollectionUtils.isNotEmpty(list)) {
                result.setUserRedpacketPOList(list);
            }
        }else {
            result.setUserRedpacketPOList(Collections.emptyList());
        }
        result.setTotalCount(count);
        result.setTotalPage(count%param.getLimit()==0? count/param.getLimit() : count/param.getLimit()+1);
        return result;
    }


    /**
     * 删除用户红包，自动化测试使用
     * @return
     */
    public Long deleteRedPacket(long uid){
        Long count = userRedPacketMapper.deleteRedPacket(uid);
        String key = StringUtil.formatContent("shopapi_redpacket_user_{userId}", String.valueOf(uid));
        stringPulseTypeRedisTemplate.delete(key);
        return count;
    }

}
