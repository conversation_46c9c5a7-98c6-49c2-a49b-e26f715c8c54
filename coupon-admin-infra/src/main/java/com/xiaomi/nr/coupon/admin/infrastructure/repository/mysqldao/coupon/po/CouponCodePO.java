package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.coupon.po;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 优惠码信息实体
 */
@Data
public class CouponCodePO implements Serializable {
    private static final long serialVersionUID = -2773799903619912491L;

    /**
     * 优惠券编号
     */
    private Long id;

    /**
     * 加密code
     */
    private String couponCode;

    /**
     * 索引，code的md5
     */
    private String couponIndex;

    /**
     * 优惠券类型编号
     */
    private Long typeId;

    /**
     * 活动编号
     */
    private String batchId;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 过期时间
     */
    private Long endTime;

    /**
     * 状态
     */
    private String stat;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 兑换的优惠ID
     */
    private Long couponId;

    /**
     * 使用方式（1结算/2兑换）
     */
    private Integer useMode;

    /**
     * 使用时间(或兑换时间)
     */
    private Long useTime;

    /**
     * 兑换或使用的门店ID
     */
    private String orgCode;

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 实际抵用金额
     */
    private BigDecimal replaceMoney;

    /**
     * 实际减免邮费
     */
    private BigDecimal reduceExpress;

    /**
     * 发送类型
     */
    private String sendType;

    /**
     * 线下使用
     */
    private Integer offline;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 发券的订单编号
     */
    private String fromOrderId;
}
