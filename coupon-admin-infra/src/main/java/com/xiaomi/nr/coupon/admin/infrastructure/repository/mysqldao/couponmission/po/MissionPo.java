package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.couponmission.po;

import lombok.Data;

import java.io.Serializable;

/**
 * 优惠券发放任务实体 - PO
 */

@Data
public class MissionPo implements Serializable {


    private static final long serialVersionUID = -2343242386014363628L;
    /**
     * 发放任务id
     */
    private Long id;

    /**
     * 发放任务状态(add, history, approved, reject, cancel, send, failed)
     */
    private String stat;

    /**
     * 发放任务名称
     */
    private String name;

    /**
     * 对应人群id
     */
    private Long groupId;

    /**
     * 生成数量
     */
    private Long sendNum;

    /**
     * 优惠券配置id
     */
    private Long typeId;

    /**
     * 优惠券有效开始时间
     */
    private Long couponStartTime;

    /**
     * 优惠券有效结束时间
     */
    private Long couponEndTime;

    /**
     * 发券开始时间
     */
    private Long sendTime;

    /**
     * 发券结束时间
     */
    private Long sendEndTime;

    /**
     * 后台人员id
     */
    private Long adminId;

    /**
     * 后台人员姓名
     */
    private String adminName;

    /**
     * 发券任务添加时间
     */
    private Long addTime;

    /**
     * 审核人员id
     */
    private Long approvedAdminId;

    /**
     * 审核人员姓名
     */
    private String approvedAdminName;

    /**
     * 审核时间
     */
    private Long approvedTime;

    /**
     * 区域id
     */
    private Integer areaId;

    /**
     * 下载地址
     */
    private String download;

    /**
     * 发放任务类型
     */
    private Integer missionType;

    /**
     * 发送人邮箱
     */
    private String email;

    /**
     * 发送人电话
     */
    private String tel;

    /**
     * 申请部门
     */
    private String department;

    /**
     * 申请理由
     */
    private String applyDesc;

    /**
     * 优惠券有效天数
     */
    private Integer couponDays;

    /**
     * 最大发放数量
     */
    private Long maxNum;

    /**
     * 已经发送数量
     */
    private Long nowNum;

    /**
     * 幂等所有审核任务id
     */
    private Long approvedId;

    /**
     * 对应人群id列表
     */
    private String groupIds;

}
