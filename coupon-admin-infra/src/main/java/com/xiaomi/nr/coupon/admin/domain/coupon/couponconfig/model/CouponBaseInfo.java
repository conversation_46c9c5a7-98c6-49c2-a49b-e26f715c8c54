package com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.ExtPropVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.UseChannelVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class CouponBaseInfo {

    /**
     * 优惠券id
     */
    private long id;
    /**
     * 优惠券名称
     */
    private String name;
    /**
     * 使用说明
     */
    private String couponDesc;
    /**
     * 投放场景
     */
    private String sendScene;
    /**
     * 领取有效期开始时间
     */
    private Date startFetchTime;
    /**
     * 领取有效期结束时间
     */
    private Date endFetchTime;

    /**
     * 使用有效期类型 1 固定有效,2 相对有效期
     */
    private int useTimeType;
    /**
     * 优惠券生效开始时间
     */
    private Date startUseTime;
    /**
     * 优惠券生效结束时间
     */
    private Date endUseTime;
    /**
     * 相对时间粒度，1-小时，2=天
     */
    private int timeGranularity;
    /**
     * 有效时长(单位小时)
     */
    private int useDuration;

    /**
     * 使用渠道
     */
    private Map<Integer, UseChannelVO> useChannel;

    /**
     * 优惠类型, 1:满减, 2:满折, 3:N元券, 4:立减
     */
    private int promotionType;
    /**
     * 优惠值(单位分/折)，如8折，传800
     */
    private long promotionValue;
    /**
     * 门槛值满元（单位分)
     */
    private long bottomPrice;
    /**
     * 门槛值满件（单位个)
     */
    private int bottomCount;
    /**
     * 门槛类型, 1:满元, 2:满件, 3:每满元, 4:每满件, 5:满元且满件
     */
    private int bottomType;
    /**
     * 最大减免金额 单位分
     */
    private long maxReduce;

    /**
     * 发放总量
     */
    private long applyCount;

    /**
     * 每人限领
     */
    private long fetchLimit;


    /**
     * 特殊规则  1可包邮  2可转增  3指定地区
     */
    private ExtPropVO extProp;

    /**
     * 指定地区id
     */
    private List<Long> areaIds;

    /**
     * 投放目的
     */
    private int sendPurpose;

    /**
     * 成本分摊 json
     */
    private Map<Integer, Integer> costShare;

    /**
     * 预估总成本 分
     */
    private BigDecimal cost;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建人部门
     */
    private long departmentId;

    /**
     * 券状态
     */
    private int status;
    /**
     * 优惠券类型(1:商品券｜2:运费券｜2:超级补贴券)
     */
    private int couponType;

    /**
     * 履约方式 -1: 所有方式 139: 门店闪送 (这个跟交易中台统一定义)
     */
    private Integer shipmentId;

    /**
     * 0:3c, 3:汽车, 5:汽车服务
     */
    private Integer bizPlatform;

    /**
     * 服务券类型
     */
    private Integer serviceType;

    /**
     * 限领类型 1限领 2不限领
     */
    private Integer fetchLimitType;

    /**
     * 使用次数限制，1-限制，2-不限制
     */
    private Integer timesLimit;

    /**
     * 财务BR单号
     */
    private String budgetApplyNo;

    /**
     * 财务BR行号
     */
    private Long lineNum;

    /**
     * BR创建时间
     */
    private String budgetCreateTime;

    /**
     * 地区：所属区域ID
     */
    private String areaId;

    /**
     * 审批流ID
     */
    private Long workflowId;
}
