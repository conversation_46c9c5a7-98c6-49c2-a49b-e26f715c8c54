package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.goods.po;


import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * 套装
 *
 * <AUTHOR>
 */
@Data
public class PackagePo implements Serializable {

    private static final long serialVersionUID = -7760722797940012716L;

    /**
     * 套装ID
     */
    @SerializedName("package_id")
    private Long packageId;

    /**
     * 产品ID
     */
    @SerializedName("product_id")
    private Long productId;

    /**
     * 创建时间
     */
    @SerializedName("add_time")
    private Long addTime;
}

