package com.xiaomi.nr.coupon.admin.infrastructure.rpc;

import com.google.common.collect.Lists;
import com.mi.oa.infra.oaucf.core.dto.PageVO;
import com.mi.oa.infra.oaucf.ems.enums.BudgetApplyTypeEnum;
import com.mi.oa.infra.oaucf.ems.enums.SystemSourceEnum;
import com.mi.oa.infra.oaucf.ems.req.BudgetReleaseReq;
import com.mi.oa.infra.oaucf.ems.req.BudgetVerifyReq;
import com.mi.oa.infra.oaucf.ems.req.br.BudgetApplyDetailReq;
import com.mi.oa.infra.oaucf.ems.req.br.BudgetApplyReq;
import com.mi.oa.infra.oaucf.ems.resp.BudgetVerifyResp;
import com.mi.oa.infra.oaucf.ems.resp.EmsResp;
import com.mi.oa.infra.oaucf.ems.resp.br.BrResp;
import com.mi.oa.infra.oaucf.ems.resp.br.BudgetApplyDetailResp;
import com.mi.oa.infra.oaucf.ems.resp.br.BudgetApplyResp;
import com.mi.oa.infra.oaucf.ems.service.BrBudgetApplyService;
import com.mi.oa.infra.oaucf.ems.service.EmsBudgetService;
import com.xiaomi.nr.coupon.admin.api.dto.BudgetInfoDto;
import com.xiaomi.nr.coupon.admin.api.dto.PageInfo;
import com.xiaomi.nr.coupon.admin.api.enums.BudgetFeeTypeEnum;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/03/19
 */

@Slf4j
@Component
public class BrProxy {

    @Autowired
    private BrBudgetApplyService brBudgetApplyService;

    @Autowired
    private EmsBudgetService emsBudgetService;

    /**
     * 查询预算池列表
     *
     * @param
     * @return
     */
    public PageInfo<BudgetInfoDto> queryBudgetList(BudgetApplyReq req) throws BizError {
        try {
            PageVO<BudgetApplyDetailResp> resp = brBudgetApplyService.queryPage(req).getBody();
            log.info("BrProxy.queryBudgetList. req is {}, resp is {}", GsonUtil.toJson(req), GsonUtil.toJson(resp));
            return new PageInfo(resp.getPageNum(), resp.getPageSize(), resp.getTotal(), convertPageResp2Dto(resp.getList()));
        } catch (Exception e) {
            log.error("BrProxy.queryBudgetList is error, req is {}", GsonUtil.toJson(req), e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, e.getMessage());
        }
    }

    /**
     * 占用br预算金额
     *
     * @param
     * @return
     */
    public EmsResp<BudgetVerifyResp> preOccupyBudget(List<BudgetVerifyReq> list) throws BizError {
        try {
            EmsResp<BudgetVerifyResp> resp = emsBudgetService.verifyAndPreOccupy(list);
            log.info("BrProxy.preOccupyBudget. req is {}, resp is {}", GsonUtil.toJson(list), GsonUtil.toJson(resp));
            if (!resp.isSuccess() || !resp.getBody().getOccupyRes().getOccupyStatus()) {
                throw ExceptionHelper.create(GeneralCodes.InternalError, "调用BR占用预算失败");
            }
            return resp;
        } catch (Exception e) {
            log.error("BrProxy.preOccupyBudget is error, req is {}", GsonUtil.toJson(list), e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, e.getMessage());
        }
    }

    /**
     * 追加br预算金额
     *
     * @param
     * @return
     */
    public EmsResp<BudgetVerifyResp> addBudget(List<BudgetVerifyReq> list) throws BizError {
        try {
            EmsResp<BudgetVerifyResp> resp = emsBudgetService.verifyAndAppendPreOccupy(list);
            log.info("BrProxy.addBudget. req is {}, resp is {}", GsonUtil.toJson(list), GsonUtil.toJson(resp));
            if (!resp.isSuccess() || !resp.getBody().getOccupyRes().getOccupyStatus()) {
                throw ExceptionHelper.create(GeneralCodes.InternalError, "调用BR追加预算失败");
            }
            return resp;
        } catch (Exception e) {
            log.error("BrProxy.addBudget is error, req is {}", GsonUtil.toJson(list), e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, e.getMessage());
        }
    }

    /**
     * 返还br预算金额
     *
     * @param
     * @return
     */
    public EmsResp<Boolean> reduceBudget(List<BudgetReleaseReq> list) throws BizError {
        try {
            EmsResp<Boolean> resp = emsBudgetService.releaseBudgetByAmount(list);
            log.info("BrProxy.reduceBudget. req is {}, resp is {}", GsonUtil.toJson(list), GsonUtil.toJson(resp));
            if (!resp.isSuccess() || resp.getBody().equals(false)) {
                throw ExceptionHelper.create(GeneralCodes.InternalError, "调用BR释放预算失败");
            }
            return resp;
        } catch (Exception e) {
            log.error("BrProxy.reduceBudget is error, req is {}", GsonUtil.toJson(list), e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, e.getMessage());
        }
    }


    /**
     * 查询预算池详情
     *
     * @param
     * @return
     */
    public BudgetInfoDto queryBudgetDetail(String budgetApplyNo, Long lineNum) throws BizError {
        return StringUtils.isBlank(budgetApplyNo) || Objects.isNull(lineNum) ? new BudgetInfoDto() : queryBrInfo(budgetApplyNo, lineNum, null);
    }

    /**
     * 查询预算信息
     *
     * @param budgetApplyNo 预算申请单号
     * @param lineNum 行号
     * @param feeType 费用类型
     * @return 预算信息
     */
    public BudgetInfoDto queryBrInfo(String budgetApplyNo, Long lineNum, String feeType) throws BizError {
        BudgetFeeTypeEnum feeTypeEnum = BudgetFeeTypeEnum.getByCode(feeType);
        if(StringUtils.isNotBlank(feeType) && Objects.isNull(feeTypeEnum)){
            throw ExceptionHelper.create(GeneralCodes.ParamError, "预算类型错误");
        }
        BudgetApplyDetailReq req = new BudgetApplyDetailReq();
        req.setBudgetApplyNo(budgetApplyNo);
        req.setBudgetApplyType(BudgetApplyTypeEnum.EQUITY);
        req.setSystemSource(SystemSourceEnum.EQUITY_CENTER);
        req.setBudgetApplyDetailIdList(Lists.newArrayList(lineNum));
        BrResp<BudgetApplyResp> budgetApplyRespBrResp;
        try {
            budgetApplyRespBrResp = brBudgetApplyService.queryDetailAndMin(req);
        } catch (Exception e) {
            log.error("BrProxy.queryBrInfo brBudgetApplyService.queryDetailAndMin error. budgetApplyNo: {}, lineNum: {}", budgetApplyNo, lineNum, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "查询BR接口错误");
        }
        BudgetApplyResp resp = budgetApplyRespBrResp.getBody();
        log.info("BrProxy.queryBrInfo brBudgetApplyService.queryDetailAndMin success. budgetApplyNo: {}, lineNum: {}, feeType: {}, response is {}", budgetApplyNo, lineNum, feeType, GsonUtil.toJson(resp));
        if (Objects.isNull(resp) || resp.getDetailList().isEmpty() || (Objects.nonNull(feeTypeEnum) && !resp.getDetailList().get(0).getFeeType().equals(feeTypeEnum.getCode()))) {
            log.error("BrProxy.queryBrInfo brBudgetApplyService.queryDetailAndMin resp data error. budgetApplyNo: {}, lineNum: {}, feeType: {}", budgetApplyNo, lineNum, feeType);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "上传的信息有误，请确认后重新上传");
        }
        return convertDetailResp2Dto(resp);
    }

    private List<BudgetInfoDto> convertPageResp2Dto(List<BudgetApplyDetailResp> respList) {
        List<BudgetInfoDto> budgetInfoDtoList = Lists.newArrayList();
        for (BudgetApplyDetailResp resp : respList) {
            BudgetInfoDto dto = new BudgetInfoDto();
            dto.setBudgetApplyNo(resp.getBudgetApplyMain().getBudgetApplyNo());
            dto.setLineNum(resp.getLineNum());
            dto.setLineTitle(resp.getLineTitle());
            dto.setBudgetBearDeptName(resp.getBudgetBearDeptName());
            dto.setBudgetIncludeTaxAmountY(resp.getBudgetIncludeTaxAmountY());
            dto.setBudgetFreeExcludeTaxAmountY(resp.getBudgetFreeExcludeTaxAmountY());
            dto.setBudgetCreateTime(resp.getCreateTime());
            budgetInfoDtoList.add(dto);
        }
        return budgetInfoDtoList;
    }

    private BudgetInfoDto convertDetailResp2Dto(BudgetApplyResp resp) {
        BudgetInfoDto dto = new BudgetInfoDto();
        if (Objects.nonNull(resp) && CollectionUtils.isNotEmpty(resp.getDetailList())) {
            BudgetApplyDetailResp detailResp = resp.getDetailList().get(0);
            dto.setBudgetApplyNo(resp.getBudgetApplyNo());
            dto.setLineNum(detailResp.getLineNum());
            dto.setLineTitle(detailResp.getLineTitle());
            dto.setBudgetBearDeptName(detailResp.getBudgetBearDeptName());
            dto.setBudgetIncludeTaxAmountY(detailResp.getBudgetIncludeTaxAmountY());
            dto.setBudgetFreeExcludeTaxAmountY(detailResp.getBudgetFreeExcludeTaxAmountY());
            dto.setBudgetCreateTime(detailResp.getCreateTime());
        }
        return dto;
    }
}
