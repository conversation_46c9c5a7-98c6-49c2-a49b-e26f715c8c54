package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.couponconfig.po;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * suffix item
 *
 * <AUTHOR>
 */
@Data
public class TargetGoodsItem implements Serializable {

    private static final long serialVersionUID = -7787182909535941352L;

    /**
     * id
     */
    @SerializedName("id")
    private Long id;

    /**
     * level  sku/goods/commodity/package
     */
    @SerializedName("level")
    private String level;

    /**
     * name
     */
    @SerializedName("name")
    private String name;
}