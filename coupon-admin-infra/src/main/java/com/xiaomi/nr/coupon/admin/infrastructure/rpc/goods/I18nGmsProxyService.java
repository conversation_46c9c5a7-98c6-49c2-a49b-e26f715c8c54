package com.xiaomi.nr.coupon.admin.infrastructure.rpc.goods;

import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.xiaomi.goods.gis.dto.category.CategoryResponse;
import com.xiaomi.newretail.common.tools.base.exception.BaseException;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.SsuInfoVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.goods.request.PageGoodsRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.goods.request.SkuListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.goods.response.SkuListResponse;
import com.xiaomi.nr.coupon.admin.api.enums.TranslationEnum;
import com.xiaomi.nr.coupon.admin.api.utils.I18nUtil;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.SsuStatusEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.UseChannelsEnum;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsItemTypeEnum;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.ResultValidator;
import com.xiaomi.nr.goods.api.SsuInfoService;
import com.xiaomi.nr.goods.api.request.BusinessEnum;
import com.xiaomi.nr.goods.api.request.PageDTO;
import com.xiaomi.nr.goods.api.request.ssu.GetSsuRequest;
import com.xiaomi.nr.goods.api.request.ssu.PageSsuRequest;
import com.xiaomi.nr.goods.api.request.ssu.SkuMultiChannelRequest;
import com.xiaomi.nr.goods.api.response.category.AggregationCategoryDTO;
import com.xiaomi.nr.goods.api.response.category.CategoryDTO;
import com.xiaomi.nr.goods.api.response.category.CategoryTreeDTO;
import com.xiaomi.nr.goods.api.response.category.constant.CategoryTypeEnum;
import com.xiaomi.nr.goods.api.response.ssu.*;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 调用gms接口获取goods信息
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class I18nGmsProxyService {


    @Autowired
    private ThreadPoolTaskExecutor queryGoodsAsyncExecutor;

    public static final int SIZE = 50;


    @Reference(interfaceClass = SsuInfoService.class, group = "${gis.dubbo.group}", version = "1.0", timeout = 3000)
    private SsuInfoService ssuInfoService;
    @Reference(check = false, interfaceClass = com.xiaomi.nr.goods.api.CategoryService.class, group = "${gis.dubbo.group}", version = "1.0", timeout = 5000)
    private com.xiaomi.nr.goods.api.CategoryService nrCategoryService;

    private static void validate(PageGoodsRequest pageSsuRequest) {
        Assert.notNull(pageSsuRequest, "pageSsuRequest is  null");
        Assert.notNull(pageSsuRequest.getPageNo(), "pageNo is  null");
        Assert.notNull(pageSsuRequest.getPageSize(), "pageSize is null");
    }
    private static void validate(SkuListRequest request) {
        Assert.notNull(request, "pageSsuRequest is  null");
        Assert.notNull(request.getSkuIds(), "skuIds is  null");
        Assert.notNull(request.getChannel(), "channel is null");
    }

    public List<SsuMultiChannelDTO> queryBySku(List<Long> skuIds, String areaId) throws Exception {
        if (skuIds == null || skuIds.isEmpty()) {
            return new ArrayList<>();
        }
        SkuMultiChannelRequest getSkuRequest = new SkuMultiChannelRequest();
        getSkuRequest.setSkuList(skuIds);
        getSkuRequest.setBusiness(BusinessEnum.I18N.getBusiness());
        getSkuRequest.setAreaId(areaId);

        Result<SkuMultiChannelResponse> result = ssuInfoService.getSkuMultiChannelList(getSkuRequest);
        log.info("query sku info from goods-api request: [{}], response: [{}]", getSkuRequest, result);
        ResultValidator.validate(result, TranslationEnum.COUPON_CONFIG_QUERY_ERROR_SKU.getTranslateContent());
        //临时过滤套装逻辑
        List<SsuMultiChannelDTO> ssuInfoDTOS = new ArrayList<>();
        for (Map.Entry<Long, SsuMultiChannelDTO> entry : result.getData().getSkuMap().entrySet()) {
            SsuMultiChannelDTO ssuInfoDTO = entry.getValue();
            ssuInfoDTOS.add(ssuInfoDTO);
        }
        return ssuInfoDTOS;
    }

    /**
     * 查询所有分类
     */
    public Map<Integer, List<CategoryResponse>> getCategoryList(String areaId) throws Exception {
        try {
            if (StringUtils.isBlank(areaId)) {
                return Collections.emptyMap();
            }
            com.xiaomi.nr.goods.api.request.category.CategoryTreeRequest categoryTreeRequest = new com.xiaomi.nr.goods.api.request.category.CategoryTreeRequest();
            categoryTreeRequest.setAreaId(areaId);
            categoryTreeRequest.setCategoryType(CategoryTypeEnum.I18N_BUSINESS_CATEGORY.getType());
            Result<com.xiaomi.nr.goods.api.response.category.CategoryTreeResponse> response = nrCategoryService.getCategoryTree(categoryTreeRequest);
            ResultValidator.validate(response, TranslationEnum.COUPON_CONFIG_QUERY_ERROR_CATEGORY.getTranslateContent());
            List<CategoryTreeDTO> categoryTreeDTOS = response.getData().getCategoryTree();
            List<CategoryResponse> responseList = new ArrayList<>();
            for (CategoryTreeDTO categoryTreeDTO : categoryTreeDTOS) {
                convertToCategoryResponse(categoryTreeDTO, responseList, 0);
            }
            return responseList.stream().collect(Collectors.groupingBy(CategoryResponse::getLevel));
        } catch (Exception e) {
            log.error("I18nGisProxyService getCategoryList Exception areaId {}", areaId, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, TranslationEnum.COUPON_CONFIG_QUERY_ERROR_CATEGORY.getTranslateContent());
        }
    }

    private static void convertToCategoryResponse(CategoryTreeDTO categoryTreeDTO, List<CategoryResponse> result, int parentCateId) {
        CategoryResponse categoryResponse = new CategoryResponse();
        categoryResponse.setCateId(categoryTreeDTO.getCategoryId());
        categoryResponse.setParentCateId(parentCateId);
        categoryResponse.setName(categoryTreeDTO.getCategoryName());
        categoryResponse.setLevel(categoryTreeDTO.getLevel());
        categoryResponse.setSort(0); // 排序可以设置为默认值或根据需要进行调整

        result.add(categoryResponse);

        // 如果存在子节点，递归地转换它们
        if (categoryTreeDTO.getChildrenList() != null && !categoryTreeDTO.getChildrenList().isEmpty()) {
            for (CategoryTreeDTO child : categoryTreeDTO.getChildrenList()) {
                convertToCategoryResponse(child, result, categoryTreeDTO.getCategoryId()); // 设置子节点的parentCateId为当前节点的categoryId
            }
        }
    }

    public BasePageResponse<SsuInfoVO> searchGoods(PageGoodsRequest pageGoodsRequest) {
        validate(pageGoodsRequest);
        log.info("searchGoods 准备请求 参数:{}", JSONUtil.toJsonStr(pageGoodsRequest));
        Stopwatch stopwatch = Stopwatch.createStarted();
        PageSsuRequest pageSsuRequest = convertPageSsuRequest(pageGoodsRequest);
        log.info("检索商品,参数：{}", pageSsuRequest);
        Result<com.xiaomi.nr.goods.api.response.PageResponse<PageSsuDTO>> pageResponseResult = null;
        try {
            pageResponseResult = ssuInfoService.pageSsu(pageSsuRequest);
        } catch (Exception e) {
            log.error("searchGoods exception,耗时:{},data:{}", stopwatch.elapsed(TimeUnit.MILLISECONDS), JSONUtil.toJsonStr(pageSsuRequest), e);
            throw new BaseException(-1, TranslationEnum.COUPON_CONFIG_QUERY_ERROR_PRODUCT.getTranslateContent());
        }
        if (GeneralCodes.OK.getCode() != pageResponseResult.getCode()) {
            log.error("检索商品失败,{}", JSONUtil.toJsonStr(pageResponseResult));
            throw new BaseException(-1, TranslationEnum.COUPON_CONFIG_QUERY_ERROR_PRODUCT.getTranslateContent());
        }
        if (ObjectUtils.isEmpty(pageResponseResult) || CollectionUtils.isEmpty(pageResponseResult.getData().getList())) {
            return new BasePageResponse<>(pageGoodsRequest.getPageNo(), pageGoodsRequest.getPageSize(), 0L, new ArrayList<>());
        }
        List<PageSsuDTO> pageSsuDTO = pageResponseResult.getData().getList();
        List<Long> goodsId = pageSsuDTO.stream().map(PageSsuDTO::getSsuId).collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
        List<SsuInfoVO> rList = getGoodsInfo(goodsId, I18nUtil.getGlobalAreaId(),pageGoodsRequest.getChannel());
        BasePageResponse<SsuInfoVO> pageResponse = new BasePageResponse<SsuInfoVO>();
        pageResponse.setList(rList);
        pageResponse.setTotalCount(Long.valueOf(pageResponseResult.getData().getPage().getTotal()));
        pageResponse.setPageSize(pageResponseResult.getData().getPage().getPageSize());
        pageResponse.setPageNo(pageResponseResult.getData().getPage().getPageNum());
        return pageResponse;
    }

    public List<SsuInfoVO> getGoodsInfo(Collection<Long> goodsId, String areaId,Integer channel) {
        log.info("getGoodsInfo 准备请求 参数:{}", JSONUtil.toJsonStr(goodsId));
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(goodsId)) {
            throw new IllegalArgumentException(TranslationEnum.COUPON_CONFIG_QUERY_ERROR_CODE.getTranslateContent());
        }
        List<SsuInfoVO> rList = Lists.newArrayList();
        List<List<Long>> parts = Lists.partition((new ArrayList<>(goodsId)), SIZE);
        parts.forEach(goods -> rList.addAll(getSingleBatch(goods, areaId,channel)));
        return rList;

    }

    private List<SsuInfoVO> getSingleBatch(List<Long> goodsId, String areaId,Integer channel) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        GetSsuRequest getSsuRequest = new GetSsuRequest();
        getSsuRequest.setSsuIdList(goodsId);
        getSsuRequest.setNeedOrgAllStock(Boolean.TRUE);
        getSsuRequest.setNeedRelatedItemInfo(Boolean.TRUE);
        getSsuRequest.setBusiness(BusinessEnum.I18N.getBusiness());
        getSsuRequest.setShelfChannel(convert2GoodsChannel(channel));
        getSsuRequest.setAreaId(areaId);
        Result<SsuResponseMap> result;
        try {
            result = ssuInfoService.getSsuInfoMap(getSsuRequest);
        } catch (Exception e) {
            log.error("getGoodsInfo exception,耗时:{},data:{}", stopwatch.elapsed(TimeUnit.MILLISECONDS), JSONUtil.toJsonStr(getSsuRequest), e);
            throw new BaseException(-1, TranslationEnum.COUPON_CONFIG_QUERY_ERROR_PRODUCT.getTranslateContent());
        }
        log.info("getGoodsInfo reqeust:{}, response:{}", GsonUtil.toJson(getSsuRequest), GsonUtil.toJson(result));
        if (GeneralCodes.OK.getCode() == result.getCode()) {
            SsuResponseMap ssuResponseMap = result.getData();
            return convertBaseGoodsDetailVo(ssuResponseMap,channel);
        } else {
            log.error("获取商品信息失败,{}", JSONUtil.toJsonStr(result));
            throw new BaseException(-1, TranslationEnum.COUPON_CONFIG_QUERY_ERROR_PRODUCT.getTranslateContent());
        }
    }

    public List<SsuInfoVO>  convertBaseGoodsDetailVo(SsuResponseMap ssuResponseMap,Integer channel) {
        if (!ObjectUtils.isEmpty(ssuResponseMap)) {
            List<SsuInfoVO> baseGood = new ArrayList<>();
            ssuResponseMap.getSsuMap().forEach((key, value) -> {
                baseGood.add(this.convertGoodVo(value,channel));
            });
            return baseGood;
        }
        return Lists.newArrayList();
    }

    public SsuInfoVO convertGoodVo(SsuInfoDTO ssuInfoDTO, Integer channel) {
        SsuInfoVO ssu = new SsuInfoVO();
        BaseSsuInfoDTO baseSsuInfoDTO = ssuInfoDTO.getBaseSsuInfo();
        if (baseSsuInfoDTO != null) {
            ssu.setSsuName(baseSsuInfoDTO.getName());
            ssu.setSku(baseSsuInfoDTO.getSku());
            ssu.setSsuId(baseSsuInfoDTO.getSsuId());
            if (ObjectUtils.isEmpty(baseSsuInfoDTO.getOrgAllStockNum())) {
                baseSsuInfoDTO.setOrgAllStockNum(0L);
            }
            String channelName = UseChannelsEnum.getNameByValue(channel);
            ssu.setChannel(channel);
            ssu.setSsuStatusText(channelName + ":" + SsuStatusEnum.getByStatus(baseSsuInfoDTO.getShelfStatus()).getTranslateContent());
            ssu.setSsuStatus(SsuStatusEnum.getByStatus(baseSsuInfoDTO.getShelfStatus()).getCode());
            ssu.setImage(baseSsuInfoDTO.getImage().getImg800());
        }
        if (ssuInfoDTO.getItemInfo() != null) {
            ssu.setItemId(ssuInfoDTO.getItemInfo().getItemId());
            ssu.setSsuType(GoodsItemTypeEnum.SINGLE.getValue());
            AggregationCategoryDTO category = ssuInfoDTO.getItemInfo().getCategory();
            if(Objects.nonNull(category) && Objects.nonNull(category.getBusinessCategory())){
                CategoryDTO businessCategory = category.getBusinessCategory();
                if(Objects.nonNull(businessCategory.getFirstCategoryName()) && Objects.nonNull(businessCategory.getSecondCategoryName())
                        && Objects.nonNull(businessCategory.getThirdCategoryName())){
                    ssu.setBusinessCategoryNames(StringUtils.join(Arrays.asList(businessCategory.getFirstCategoryName(),
                            businessCategory.getSecondCategoryName(), businessCategory.getThirdCategoryName()), ">"));
                }
                if(Objects.nonNull(businessCategory.getFirstCategoryId()) && Objects.nonNull(businessCategory.getSecondCategoryId())
                        && Objects.nonNull(businessCategory.getThirdCategoryId())){
                    ssu.setBusinessCategoryIds(StringUtils.join(Arrays.asList(businessCategory.getFirstCategoryId(),
                            businessCategory.getSecondCategoryId(), businessCategory.getThirdCategoryId()), ">"));
                }
            }
        }
        if (ssuInfoDTO.getPrice() != null) {
            ssu.setMarketPrice(ssuInfoDTO.getPrice().getMarketPrice());
        }

        return ssu;
    }


    public PageSsuRequest convertPageSsuRequest(PageGoodsRequest pageGoodsRequest) {
        PageSsuRequest pageSsuRequest = new PageSsuRequest();
        com.xiaomi.nr.goods.api.request.PageDTO page = new PageDTO();
        page.setPageNum(pageGoodsRequest.getPageNo());
        page.setPageSize(pageGoodsRequest.getPageSize());
        pageSsuRequest.setPage(page);
        if (!ObjectUtils.isEmpty(pageGoodsRequest.getItemId())) {
            pageSsuRequest.setItemIdList(Collections.singletonList(pageGoodsRequest.getItemId()));
        }
        if (!ObjectUtils.isEmpty(pageGoodsRequest.getSsuId())) {
            pageSsuRequest.setSsuIdList(Collections.singletonList(pageGoodsRequest.getSsuId()));
        }
        pageSsuRequest.setSsuName(pageGoodsRequest.getSsuName());

        //由于业务因素，上架渠道查询只能有一个 上架渠道，根据是否传入上架ssuStatus判断，具体传值，按传入的活动类型
        if (pageGoodsRequest.getSsuStatus() != null && SsuStatusEnum.getByCode(pageGoodsRequest.getSsuStatus()).isStatus()) {
            pageSsuRequest.setOnShelfChannelList(Lists.newArrayList(
                    convert2GoodsChannel(pageGoodsRequest.getChannel())));
        }
        pageSsuRequest.setSsuType(pageGoodsRequest.getSsuType());
        if (pageGoodsRequest.getSkuId() != null) {
            pageSsuRequest.setSkuList(Collections.singletonList(pageGoodsRequest.getSkuId()));
        }
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(pageGoodsRequest.getBusinessCategoryIdList())){
            pageSsuRequest.setBusinessCategoryIdList(pageGoodsRequest.getBusinessCategoryIdList());
        }
        pageSsuRequest.setBusiness(BusinessEnum.I18N.getBusiness());
        //先默认
        pageSsuRequest.setAreaId(I18nUtil.getGlobalAreaId());
        return pageSsuRequest;
    }

    public PageSsuRequest convertPageSsuRequest(SkuListRequest request) {
        PageSsuRequest pageSsuRequest = new PageSsuRequest();
        com.xiaomi.nr.goods.api.request.PageDTO page = new PageDTO();
        page.setPageNum(1);
        page.setPageSize(100);
        pageSsuRequest.setPage(page);
        pageSsuRequest.setSkuList(request.getSkuIds());
//        pageSsuRequest.setOnShelfChannelList(Lists.newArrayList(convert2GoodsChannel(request.getChannel())));
        pageSsuRequest.setBusiness(BusinessEnum.I18N.getBusiness());
        //先默认
        pageSsuRequest.setAreaId(I18nUtil.getGlobalAreaId());
        return pageSsuRequest;
    }

    public static String convert2GoodsChannel(Integer code){
        if(code == UseChannelsEnum.DIRECTSALE_STORE.getValue()){
            return com.xiaomi.nr.goods.api.response.ChannelEnum.DIRECT.getChannel();
        }else if(code == UseChannelsEnum.AUTHORIZED_STORE.getValue()){
            return com.xiaomi.nr.goods.api.response.ChannelEnum.AUTHORIZE.getChannel();
        }
        return null;
    }

    public Result<SkuListResponse> searchGoodsBySkuIds(SkuListRequest request) {
        SkuListResponse response = new SkuListResponse();
        response.setNotExistSkuIds(request.getSkuIds());
        validate(request);
        log.info("searchGoods 准备请求 参数:{}", JSONUtil.toJsonStr(request));
        PageSsuRequest pageSsuRequest = convertPageSsuRequest(request);
        log.info("检索商品,参数：{}", pageSsuRequest);
        Result<com.xiaomi.nr.goods.api.response.PageResponse<PageSsuDTO>> pageResponseResult = null;
        try {
            pageResponseResult = ssuInfoService.pageSsu(pageSsuRequest);
        } catch (Exception e) {
            log.error("searchGoods exception,data:{}", JSONUtil.toJsonStr(pageSsuRequest), e);
            throw new BaseException(-1, TranslationEnum.COUPON_CONFIG_QUERY_ERROR_PRODUCT.getTranslateContent());
        }
        if (GeneralCodes.OK.getCode() != pageResponseResult.getCode()) {
            log.error("检索商品失败,{}", JSONUtil.toJsonStr(pageResponseResult));
            throw new BaseException(-1, TranslationEnum.COUPON_CONFIG_QUERY_ERROR_PRODUCT.getTranslateContent());
        }
        if (ObjectUtils.isEmpty(pageResponseResult) || CollectionUtils.isEmpty(pageResponseResult.getData().getList())) {
            return Result.success(response);
        }
        List<PageSsuDTO> pageSsuDTO = pageResponseResult.getData().getList();
        List<Long> goodsId = pageSsuDTO.stream().map(PageSsuDTO::getSsuId).collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
        List<SsuInfoVO> rList = getGoodsInfo(goodsId, I18nUtil.getGlobalAreaId(),request.getChannel());
        response.setSsuInfoList(rList);
        request.getSkuIds().removeAll(rList.stream().map(SsuInfoVO::getSku).collect(Collectors.toList()));
        response.setNotExistSkuIds(request.getSkuIds());
        return Result.success(response);
    }
}
