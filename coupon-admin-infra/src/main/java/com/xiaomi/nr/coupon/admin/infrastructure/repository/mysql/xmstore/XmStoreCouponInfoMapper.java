package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.xmstore;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.xmstore.po.XmStoreCouponInfoPO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * @Description:
 * @Date: 2022.05.16 15:22
 */
public interface XmStoreCouponInfoMapper {

    @Insert("<script>" +
            "insert into tbl_coupon_info (class_id, mission_id, site_id, name, expire_day, remark, status, reduce_money, created_by, created_at) values" +
            "<foreach collection='xmStoreCouponInfoPOList' item='couponInfoPO' index='index' separator=','>" +
            "(#{couponInfoPO.classId}, #{couponInfoPO.missionId}, #{couponInfoPO.siteId}, #{couponInfoPO.name}, #{couponInfoPO.expireDay}," +
            "#{couponInfoPO.remark}, #{couponInfoPO.status}, #{couponInfoPO.reduceMoney}, #{couponInfoPO.createdBy}, #{couponInfoPO.createdAt})" +
            "</foreach>" +
            "</script>")
    Integer batchInsert(@Param("xmStoreCouponInfoPOList") List<XmStoreCouponInfoPO> xmStoreCouponInfoPOList);



    @Update("<script> update tbl_coupon_info" +
            "<set>" +
            "<if test='siteId!=null'>site_id=#{siteId},</if>"+
            "<if test='name!=null'>name=#{name},</if>"+
            "<if test='expireDay!=null'>expire_day=#{expireDay},</if>"+
            "<if test='remark!=null'>remark=#{remark},</if>"+
            "<if test='status!=null'>status=#{status},</if>"+
            "<if test='reduceMoney!=null'>reduce_money=#{reduceMoney},</if>"+
            "<if test='updatedBy!=null'>updated_by=#{updatedBy},</if>"+
            "<if test='updatedAt!=null'>updated_at=#{updatedAt},</if>"+
            "</set>" +
            "<where> class_id=#{classId}</where></script>")
    Integer update(XmStoreCouponInfoPO xmStoreCouponInfoPO);


    @Update("update tbl_coupon_info set status= #{status}, updated_at=unix_timestamp(now()) where class_id=#{classId} ")
    Integer updateStatus(@Param("status") Integer status, @Param("classId") Long classId);

    /**
     * 获取券配置id
     * @param configId
     * @return
     */
    @Select("select class_id from tbl_coupon_info where class_id = #{configId}")
    Long getXmStoreCouponInfoIdByConfigId(@Param("configId") long configId);
}
