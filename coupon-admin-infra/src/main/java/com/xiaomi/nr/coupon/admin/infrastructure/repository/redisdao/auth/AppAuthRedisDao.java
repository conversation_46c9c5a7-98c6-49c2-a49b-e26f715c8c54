package com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.auth;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.auth.po.AppAuthInfo;

import java.util.Map;

/**
 * AppAuthInfo 基础缓存信息
 */
public interface AppAuthRedisDao {

    /**
     * 从缓存获取，AppAuth信息(用于鉴权、发券渠道校验等)
     *
     * @return Map<String,AppAuthInfo> AppAuthInfo缓存信息 map
     */
    Map<String, AppAuthInfo> get();


    /**
     * 将格式化后的app信息写入新缓存
     *
     * @param map Map<String, AppAuthInfo>
     */
    void setNewAppAuth (Map<String, AppAuthInfo> map);


    /**
     * 获取新缓存的AppAuth
     *
     * @param appId  appId
     * @return AppAuthInfo
     */
    AppAuthInfo getNewAppAuth(String appId);
}
