package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.po.CouponWorkFlowPo;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface CouponWorkFlowMapper {

	@Select("<script>select * from nr_coupon_workflow where area_id=#{areaId}</script>")
	List<CouponWorkFlowPo> queryPromotionWorkFlowByAreaId(@Param("areaId") String areaId);

	@Select("<script>select * from nr_coupon_workflow where workflow_id=#{workflowId}</script>")
	CouponWorkFlowPo queryPromotionWorkFlowByWorkflowId(@Param("workflowId") Long workflowId);

	@Options(useGeneratedKeys = true, keyColumn = "id")
	@Insert(" insert into nr_coupon_workflow" +
			"(workflow_id,workflow_translate_key,bpm_key,area_id,channels,promotion_types,extend) " +
			"values (#{workflowId}, #{workflowTranslateKey}, #{bpmKey}, #{areaId}, #{channels}, #{promotionTypes}, #{extend})")
	Integer insertPromotionWorkFlow(CouponWorkFlowPo po);

	@Update("<script>update nr_coupon_workflow " +
			"<set>" +
			"<if test='workflowId!=null'> workflow_id=#{workflowId},</if>" +
			"<if test='workflowTranslateKey!=null'> workflow_translate_key=#{workflowTranslateKey},</if>" +
			"<if test='bpmKey!=null'> bpm_key=#{bpmKey},</if>" +
			"<if test='areaId!=null'> area_id=#{areaId},</if>" +
			"<if test='channels!=null'> channels=#{channels},</if>" +
			"<if test='promotionTypes!=null'> promotion_types=#{promotionTypes},</if>" +
			"<if test='extend!=null'> extend=#{extend},</if>" +
			"</set>" +
			" where id =#{id}" +
			"</script>")
	Integer updatePromotionWorkFlow(CouponWorkFlowPo po);
}
