package com.xiaomi.nr.coupon.admin.infrastructure.repository;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.optlog.CouponOptLogMapper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.optlog.po.CouponLogPO;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description: 优惠券日志
 * @Date: 2022.03.01 14:54
 */
@Component
public class CouponLogRepository {

    @Autowired
    private CouponOptLogMapper couponLogMapper;


    /**
     * 根据优惠券id查询最新日志
     * @param configId
     * @return
     */
    public String getLatestLogByConfigId(Long configId){
       return couponLogMapper.getLatestLogByConfigId(configId);
    }


    /**
     * 根据优惠券id分页查询日志
     * @param couponId
     * @param offset
     * @param limit
     * @return
     */
    public List<CouponLogPO> getLogPageByCouponId(Long couponId, int offset, int limit) {
        return couponLogMapper.getLogPageByCouponId(couponId, offset, limit);
    }

    /**
     * 获取日志总数
     * @param couponId
     * @return
     */
    public int getTotalByCouponId(Long couponId) {
        return couponLogMapper.getTotalCountByCouponId(couponId);
    }

    /**
     * 根据优惠券id查询最后修改日志
     * @param configIds
     * @return
     */
    public Map<String, List<Long>> getLatestModifyConfigIdMap(Set<Long> configIds) {

        Map<String, List<Long>> operatorMap = new HashMap<>(configIds.size());

        List<CouponLogPO> couponLogPOS = couponLogMapper.getLatestModifyByConfigId(configIds);

        for (CouponLogPO couponLogPO : couponLogPOS) {
            if (operatorMap.containsKey(couponLogPO.getOperator())) {
                operatorMap.get(couponLogPO.getOperator()).add(couponLogPO.getConfigId());
            } else {
                operatorMap.put(couponLogPO.getOperator(), Lists.newArrayList(couponLogPO.getConfigId()));
            }
        }

        return operatorMap;
    }

    /**
     * 根据日志id获取日志详情
     * @param id
     * @return
     */
    public CouponLogPO getLogById(long id) {
        return couponLogMapper.getLogById(id);
    }

    /**
     * 新增
     */
    public void insert(CouponLogPO couponLogPO) {
        couponLogMapper.insert(couponLogPO);
    }

}
