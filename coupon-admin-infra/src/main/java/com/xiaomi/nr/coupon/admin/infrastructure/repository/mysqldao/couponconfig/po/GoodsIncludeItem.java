package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.couponconfig.po;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * 可用商品 item
 *
 * <AUTHOR>
 */
@Data
public class GoodsIncludeItem implements Serializable {

    private static final long serialVersionUID = -6419864533693008874L;

    /**
     * id
     */
    @SerializedName("id")
    private Long id;

    /**
     * level  sku/goods/group/package
     */
    @SerializedName("level")
    private String level;

    /**
     * name
     */
    @SerializedName("name")
    private String name;
}