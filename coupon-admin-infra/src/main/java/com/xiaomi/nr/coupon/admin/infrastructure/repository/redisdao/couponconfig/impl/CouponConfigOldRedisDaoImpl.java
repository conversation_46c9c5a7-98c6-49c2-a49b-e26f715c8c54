package com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.impl;

import com.xiaomi.nr.coupon.admin.infrastructure.constant.coupon.CouponConstant;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.CouponConfigOldRedisDao;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.po.ConfigCacheItemPo;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.po.ConfigIdListCachePo;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.StringUtil;
import com.xiaomi.nr.coupon.admin.api.utils.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 原缓存-原key
 * 券配置的redis缓存操作对象
 *
 * <AUTHOR>
 */
@Deprecated
@Component
@Slf4j
public class CouponConfigOldRedisDaoImpl implements CouponConfigOldRedisDao {

    /**
     * 券配置cache key（180天以内创建且过期不超过3天）
     */
    private static final String KEY_COUPON_CONFIG_CACHE = "nr_coupon_config_info_cache_{configId}";

    /**
     * 所有－有效的无码券配置ID列表cache key（180天以内创建且未过期的）
     */
    private static final String KEY_COUPON_NO_CODE_CONFIG_VALID_LIST_CACHE_ALL = "nr_coupon_no_code_config_valid_id_list_cache";

    /**
     * 每次写入100个缓存
     */
    private static final int LIMIT_REDIS_SET_COUNT = 100;

    /**
     * 每次读取100个缓存
     */
    private static final int LIMIT_REDIS_GET_COUNT = 100;





    /**
     * 缓存操作对象
     */
    @Autowired
    @Qualifier("stringNewCouponRedisTemplate")
    private StringRedisTemplate redisTemplate;


    /**
     * 将优惠券配置缓存信息写入redis（支持批量）
     *
     * @param list List<CouponConfigGoodsCache>
     */
    @Override
    public void set(List<ConfigCacheItemPo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        long runStartTime = TimeUtil.getNowUnixMillis();

        ValueOperations<String, String> operations = redisTemplate.opsForValue();

        Map<String, String> data = new HashMap<>(LIMIT_REDIS_SET_COUNT);
        for (ConfigCacheItemPo item : list) {
            if (item.getId() == null || item.getId() <= 0) {
                continue;
            }

            String key = StringUtil.formatContent(KEY_COUPON_CONFIG_CACHE, String.valueOf(item.getId()));
            data.put(key, GsonUtil.toJson(item));

            if (data.size() >= LIMIT_REDIS_SET_COUNT) {
                operations.multiSet(data);
                data = new HashMap<>(LIMIT_REDIS_SET_COUNT);
            }
        }

        if (data.size() > 0) {
            operations.multiSet(data);
        }

        long runCostTime = TimeUtil.sinceMillis(runStartTime);
        if(runCostTime > CouponConstant.REDIS_TIMEOUT_LIMIT) {
            log.info("CouponConfigRedisDaoImpl.get, 设置券配置缓存时间比较长, runTime={}ms, keys={}", runCostTime, data.keySet());
        }
    }

    /**
     * 从redis里读取券配置缓存（支持批量）
     *
     * @param configIds List<Long>
     * @return CouponConfigGoodsCache
     */
    @Override
    public List<ConfigCacheItemPo> get(List<Long> configIds) {
        List<ConfigCacheItemPo> result = new ArrayList<>();
        if (configIds == null || configIds.size() <= 0) {
            return result;
        }

        long runStartTime = TimeUtil.getNowUnixMillis();

        ValueOperations<String, String> operations = redisTemplate.opsForValue();

        List<String> keys = new ArrayList<>();
        for (Long id : configIds) {
            if (id == null || id <= 0) {
                continue;
            }

            keys.add(StringUtil.formatContent(KEY_COUPON_CONFIG_CACHE, id.toString()));

            if (keys.size() >= LIMIT_REDIS_GET_COUNT) {
                List<String> jsonStrList = operations.multiGet(keys);
                List<ConfigCacheItemPo> infos = decodeBaseInfo(keys, jsonStrList);
                if(infos.size() > 0){
                    result.addAll(infos);
                }
                keys = new ArrayList<>();
            }
        }

        if (keys.size() > 0) {
            List<String> jsonStrList = new ArrayList<>();
            if (keys.size() > 1) {
                jsonStrList = operations.multiGet(keys);
            } else {
                jsonStrList.add(operations.get(keys.get(0)));
            }
            List<ConfigCacheItemPo> infos = decodeBaseInfo(keys, jsonStrList);
            if(infos.size() > 0){
                result.addAll(infos);
            }
        }

        List<String> ids = new ArrayList<>();
        for(ConfigCacheItemPo item : result) {
            ids.add(String.valueOf(item.getId()));
        }

        StringBuilder noFindIds = new StringBuilder();
        for(Long id : configIds) {
            if(!ids.contains(String.valueOf(id))) {
                noFindIds.append(id).append(",");
            }
        }
        if(!Strings.isNullOrEmpty(String.valueOf(noFindIds))) {
            log.info("CouponConfigRedisDaoImpl.get, 以下券配置在redis里未找到, noFindIds={}", noFindIds);
        }

        long runCostTime = TimeUtil.sinceMillis(runStartTime);
        if(runCostTime > CouponConstant.REDIS_TIMEOUT_LIMIT) {
            log.info("CouponConfigRedisDaoImpl.get, 获取券配置缓存时间比较长, runTime={}ms, configIds={}", runCostTime, configIds);
        }

        return result;
    }

    /**
     * 获取单个券配置信息缓存
     *
     * @param configId long
     * @return ConfigCacheItemPo
     */
    @Override
    public ConfigCacheItemPo get(long configId) {
        if (configId <= 0) {
            return null;
        }

        List<Long> ids = new ArrayList<>();
        ids.add(configId);
        List<ConfigCacheItemPo> r = get(ids);

        if (r == null || r.isEmpty() || r.get(0) == null) {
            return null;
        }

        if (r.get(0).getId() == configId) {
            return r.get(0);
        }

        return null;
    }

    /**
     * 解析券缓存基本信息
     *
     * @param keys List<String>
     * @param data List<String>
     * @return List<ConfigCacheItemPo>
     */
    private List<ConfigCacheItemPo> decodeBaseInfo(List<String> keys, List<String> data) {
        List<ConfigCacheItemPo> result = new ArrayList<>();
        if (data == null) {
            //log.info("couponConfig.cache, 从redis里取到的数据为null, keys={}", keys);
            return result;
        }
        if (data.size() == 0) {
            return result;
        }

        for (String resJson : data) {
            if (resJson == null || resJson.isEmpty()) {
                //log.warn("couponConfig.cache, 从redis里取到的数据为空, keys={}, resJson={}", keys, resJson);
                continue;
            }

            ConfigCacheItemPo info = GsonUtil.fromJson(resJson, ConfigCacheItemPo.class);
            if (info == null || info.getId() <= 0) {
                log.warn("couponConfig.cache, 从redis里取到的数据解析后发现不符合要求, keys={}, resJson={}", keys, resJson);
                continue;
            }

            result.add(info);
        }

        return result;
    }


    /**
     * 所有－有效的券配置ID列表写入redis
     *
     * @param list NoCodeConfigIdListCachePo
     */
    @Override
    public void setValidNoCodeConfigIdList(ConfigIdListCachePo list) {
        ValueOperations<String, String> operations = redisTemplate.opsForValue();
        operations.set(KEY_COUPON_NO_CODE_CONFIG_VALID_LIST_CACHE_ALL, GsonUtil.toJson(list));
    }

    /**
     * 所有－有效的券配置ID列表读redis
     *
     * @return NoCodeConfigIdListCachePo
     */
    @Override
    public ConfigIdListCachePo getNoCodeValidConfigIdList() {
        ConfigIdListCachePo result = new ConfigIdListCachePo();
        ValueOperations<String, String> operations = redisTemplate.opsForValue();
        String resJson = operations.get(KEY_COUPON_NO_CODE_CONFIG_VALID_LIST_CACHE_ALL);
        if (resJson == null || resJson.isEmpty()) {
            log.info("couponConfig.cache, 从redis里取到的有效券配置ID列表数据为空, key={}, resJson={}", KEY_COUPON_NO_CODE_CONFIG_VALID_LIST_CACHE_ALL, resJson);
            return result;
        }

        ConfigIdListCachePo info = GsonUtil.fromJson(resJson, ConfigIdListCachePo.class);
        if (info == null || info.getConfigIds() == null) {
            log.warn("couponConfig.cache, 从redis里取到的有效券配置ID列表解析后为空, key={}, resJson={}", KEY_COUPON_NO_CODE_CONFIG_VALID_LIST_CACHE_ALL, resJson);
            return result;
        }

        return result;
    }








}