package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.bdmall;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.bdmall.po.MarketCouponApplyPO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

/**
 * @Description: 优惠吗
 * @Date: 2022.03.11 0:24
 */
@Mapper
@Component
public interface MarketCouponApplyMapper {

    String COUPON_CONFIG_TABLE = "mall_market_coupon_apply";

    /**
     * 老券写入新表
     * @return
     */
    @Insert("insert into " + COUPON_CONFIG_TABLE + " (mission_id,coupon_id,mission_type,send_num,coupon_info," +
            "audit_status,add_time,add_user,update_time,update_user,apply_desc,apply_desc_img,attachment)"+
            " values " +
            "(#{missionId},#{couponId},#{missionType},#{sendNum},#{couponInfo},#{auditStatus},#{addTime}," +
            "#{addUser},#{updateTime},#{updateUser},'','','')")
    Long insertCouponApply(MarketCouponApplyPO po);

    /**
     * 更新优惠券
     * @param po
     * @return
     */
    @Update("<script>update " + COUPON_CONFIG_TABLE + " <set>" +
            "<if test='missionId!=null'>mission_id=#{missionId},</if>" +
            "<if test='couponId!=null'>coupon_id=#{couponId},</if>" +
            "<if test='missionType!=null'>mission_type=#{missionType},</if>" +
            "<if test='sendNum!=null'>send_num=#{sendNum},</if>" +
            "<if test='couponInfo!=null'>coupon_info=#{couponInfo},</if>" +
            "<if test='auditStatus!=null'>audit_status=#{auditStatus},</if>" +
            "<if test='addTime!=null'>add_time=#{addTime},</if>" +
            "<if test='addUser!=null'>add_user=#{addUser},</if>" +
            "<if test='updateTime!=null'>update_time=#{updateTime},</if>" +
            "<if test='updateUser!=null'>update_user=#{updateUser},</if>" +
            "</set><where> mission_id=#{missionId}</where></script>")
    Long updateCouponApply(MarketCouponApplyPO po);
}
