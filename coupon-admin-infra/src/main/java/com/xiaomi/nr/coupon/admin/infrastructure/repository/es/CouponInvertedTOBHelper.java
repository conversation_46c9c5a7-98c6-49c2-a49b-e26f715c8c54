package com.xiaomi.nr.coupon.admin.infrastructure.repository.es;

import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponTimeStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.es.po.CouponEsPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.po.SearchConfigParam;
import com.xiaomi.nr.coupon.admin.api.utils.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 券列表查询ES
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class CouponInvertedTOBHelper extends CouponConfigESHelper {

    /**
     * 后台默认查询最大数据量
     */
    private static final int ADMIN_CONFIGID_PAGE_SIZE = 5000;

    @Override
    public SearchSourceBuilder buildSourceBuilder(SearchConfigParam searchConfigParam, String level, long item, long timeNow) {
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        boolQuery.filter(QueryBuilders.termsQuery(level, Lists.newArrayList(item)));

        // TODO 当前3c券，useTimeType = 3的券只有耗材卡，后续改为根据useTimeType过滤
        // 构建 投放场景 = 米网耗材卡的查询
        BoolQueryBuilder miShopSaveMoneyCard = QueryBuilders.boolQuery();
        miShopSaveMoneyCard.filter(QueryBuilders.termQuery(CouponEsPO.SCENE, "9E3F69DD0B6801721251F2B8EAE18545"));

        // 构建 投放场景 != 米网耗材卡的查询
        BoolQueryBuilder notMiShopSaveMoneyCard = QueryBuilders.boolQuery();
        notMiShopSaveMoneyCard.mustNot(QueryBuilders.termQuery(CouponEsPO.SCENE, "9E3F69DD0B6801721251F2B8EAE18545"));
        if (timeNow > CommonConstant.ZERO_INT) {
            notMiShopSaveMoneyCard.filter(QueryBuilders.rangeQuery(CouponEsPO.END_USE_TIME).gt(timeNow));
        }

        // 合并两个查询
        BoolQueryBuilder combinedSceneQuery = QueryBuilders.boolQuery()
                .should(miShopSaveMoneyCard)
                .should(notMiShopSaveMoneyCard);

        boolQuery.filter(combinedSceneQuery);

        if (Objects.nonNull(searchConfigParam)) {
            if (CollectionUtils.isNotEmpty(searchConfigParam.getUseChannel())) {
                boolQuery.filter(QueryBuilders.termsQuery(CouponEsPO.USE_CHANNEL, searchConfigParam.getUseChannel()));
            }
            if (Objects.nonNull(searchConfigParam.getPromotionType())) {
                boolQuery.filter(QueryBuilders.termQuery(CouponEsPO.PROMOTION_TYPE, searchConfigParam.getPromotionType()));
            }
            if (Objects.nonNull(searchConfigParam.getBizPlatform())) {
                boolQuery.filter(QueryBuilders.termQuery(CouponEsPO.BIZ_PLATFORM, searchConfigParam.getBizPlatform()));
            }
        }

        sourceBuilder.query(boolQuery);
        sourceBuilder.size(ADMIN_CONFIGID_PAGE_SIZE);

        return sourceBuilder;
    }

    /**
     * 构造券列表查询条件
     *
     * @param searchConfigParam 查询条件
     * @return SearchSourceBuilder
     */
    @Override
    public SearchSourceBuilder buildSourceBuilder(SearchConfigParam searchConfigParam, long nowTime) {

        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        if (Objects.nonNull(searchConfigParam.getId())) {
            boolQuery.filter(QueryBuilders.termQuery(CouponEsPO.CONFIG_ID, searchConfigParam.getId()));
        }
        if (Objects.nonNull(searchConfigParam.getUseChannel()) && !searchConfigParam.getUseChannel().isEmpty()) {
            boolQuery.filter(QueryBuilders.termsQuery(CouponEsPO.USE_CHANNEL, searchConfigParam.getUseChannel()));
        }
        if (Objects.nonNull(searchConfigParam.getAreaId())) {
            boolQuery.filter(QueryBuilders.termsQuery(CouponEsPO.AREA_ID, searchConfigParam.getAreaId()));
        }
        if (Objects.nonNull(searchConfigParam.getSendScene())) {
            boolQuery.filter(QueryBuilders.termQuery(CouponEsPO.SCENE, searchConfigParam.getSendScene()));
        }
        if (Objects.nonNull(searchConfigParam.getCreator())) {
            boolQuery.filter(QueryBuilders.termQuery(CouponEsPO.CREATOR, searchConfigParam.getCreator()));
        }
        if (Objects.nonNull(searchConfigParam.getStatus())) {
            boolQuery.filter(QueryBuilders.termQuery(CouponEsPO.STATUS, searchConfigParam.getStatus()));
        }
        if (Objects.nonNull(searchConfigParam.getStartFetchTime())) {
            boolQuery.filter(QueryBuilders.rangeQuery(CouponEsPO.END_FETCH_TIME).gt(searchConfigParam.getStartFetchTime()));
        }
        if (Objects.nonNull(searchConfigParam.getEndFetchTime())) {
            boolQuery.filter(QueryBuilders.rangeQuery(CouponEsPO.START_FETCH_TIME).lt(searchConfigParam.getEndFetchTime()));
        }
        if (Objects.nonNull(searchConfigParam.getStartUseTime())) {
            boolQuery.filter(QueryBuilders.rangeQuery(CouponEsPO.END_USE_TIME).gt(searchConfigParam.getStartUseTime()));
        }
        if (Objects.nonNull(searchConfigParam.getEndUseTime())) {
            boolQuery.filter(QueryBuilders.rangeQuery(CouponEsPO.START_USE_TIME).lt(searchConfigParam.getEndUseTime()));
        }

        if (Objects.nonNull(searchConfigParam.getPromotionType())) {
            boolQuery.filter(QueryBuilders.termQuery(CouponEsPO.PROMOTION_TYPE, searchConfigParam.getPromotionType()));
        }

        if (Objects.nonNull(searchConfigParam.getServiceType())) {
            boolQuery.filter(QueryBuilders.termQuery(CouponEsPO.SERVICE_TYPE, searchConfigParam.getServiceType()));
        }

        if (Objects.nonNull(searchConfigParam.getFetchLimitType())) {
            boolQuery.filter(QueryBuilders.termQuery(CouponEsPO.FETCH_LIMIT_TYPE, searchConfigParam.getFetchLimitType()));
        }

        if (Objects.nonNull(searchConfigParam.getTimesLimit())) {
            boolQuery.filter(QueryBuilders.termQuery(CouponEsPO.TIMES_LIMIT_TYPE, searchConfigParam.getTimesLimit()));
        }

        if (Objects.nonNull(searchConfigParam.getSsu())) {
            boolQuery.filter(QueryBuilders.termQuery(CouponEsPO.SSU_ID, searchConfigParam.getSsu()));
        }

        //todo 干掉storeId
        if (Objects.nonNull(searchConfigParam.getStoreId())) {
            boolQuery.should(QueryBuilders.termQuery(CouponEsPO.STORE_IDS,"*"));
            boolQuery.should(QueryBuilders.termQuery(CouponEsPO.STORE_IDS,searchConfigParam.getStoreId()));
            boolQuery.minimumShouldMatch(1);
        }
        if (CollectionUtils.isNotEmpty(searchConfigParam.getStoreIds())) {
            boolQuery.should(QueryBuilders.termQuery(CouponEsPO.STORE_IDS, "*"));
            boolQuery.should(QueryBuilders.termsQuery(CouponEsPO.STORE_IDS, searchConfigParam.getStoreIds()));
            boolQuery.minimumShouldMatch(1);
        }


        // 优惠券状态-未开始、进行中、已结束
        Integer timeStatus = searchConfigParam.getTimeStatus();
        if (Objects.nonNull(timeStatus)) {
            CouponTimeStatusEnum timeStatusEnum = CouponTimeStatusEnum.findByCode(timeStatus);
            switch (timeStatusEnum) {
                case NOT_START:
                    boolQuery.filter(QueryBuilders.rangeQuery(CouponEsPO.START_FETCH_TIME).gt(nowTime));
                    break;
                case IN_PROGRESS:
                    boolQuery.filter(QueryBuilders.rangeQuery(CouponEsPO.START_FETCH_TIME).lt(nowTime));
                    boolQuery.filter(QueryBuilders.rangeQuery(CouponEsPO.END_FETCH_TIME).gt(nowTime));
                    break;
                case ENDED:
                    boolQuery.filter(QueryBuilders.rangeQuery(CouponEsPO.END_FETCH_TIME).lt(nowTime));
                    break;
                default:
                    log.warn("未知优惠券状态");
                    break;
            }
        }
        sourceBuilder.query(boolQuery);
        sourceBuilder.size(ADMIN_CONFIGID_PAGE_SIZE);
        return sourceBuilder;
    }

    @Override
    public SearchSourceBuilder buildSourceBuilder(SearchConfigParam searchConfigParam, boolean isRelativeTime, long itemId) {

        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        if (isRelativeTime) {
            boolQuery.filter(QueryBuilders.rangeQuery(CouponEsPO.END_USE_TIME).gt(TimeUtil.getNowUnixSecond()));
        } else {

            if (Objects.nonNull(searchConfigParam.getStartUseTime())) {
                boolQuery.filter(QueryBuilders.rangeQuery(CouponEsPO.END_USE_TIME).gt(searchConfigParam.getStartUseTime()));
            }
            if (Objects.nonNull(searchConfigParam.getEndUseTime())) {
                boolQuery.filter(QueryBuilders.rangeQuery(CouponEsPO.START_USE_TIME).lt(searchConfigParam.getEndUseTime()));
            }
        }

        boolQuery.filter(QueryBuilders.termsQuery(searchConfigParam.getLevel(), Lists.newArrayList(itemId)));

        if (Objects.nonNull(searchConfigParam.getUseChannel())) {
            boolQuery.filter(QueryBuilders.termsQuery(CouponEsPO.USE_CHANNEL, searchConfigParam.getUseChannel()));
        }

        sourceBuilder.query(boolQuery);
        sourceBuilder.size(ADMIN_CONFIGID_PAGE_SIZE);

        return sourceBuilder;
    }
}
