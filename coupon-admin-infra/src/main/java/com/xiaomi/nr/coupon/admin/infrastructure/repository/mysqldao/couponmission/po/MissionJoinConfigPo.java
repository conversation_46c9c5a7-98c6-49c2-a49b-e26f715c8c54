package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.couponmission.po;

import lombok.Data;

import java.io.Serializable;


/**
 * 根据发放渠道获取券发放任务列表实体
 */

@Data
public class MissionJoinConfigPo implements Serializable {

    private static final long serialVersionUID = -2327174360309004297L;

    /**
     * 券发放任务ID
     */
    private Long missionId;

    /**
     * 券发放任务名称
     */
    private String missionName;

    /**
     * 券发放任务状态
     */
    private String missionStatus;

    /**
     * 发放任务类型
     */
    private Integer missionType;

    /**
     * 生成数量
     */
    private Long sendNum;

    /**
     * 最大发放数量
     */
    private Long maxNum;

    /**
     * 发放后几天有效
     */
    private Integer couponDays;

    /**
     * 券真实开始时间
     */
    private Long couponStartTime;

    /**
     * 券真实结束时间
     */
    private Long couponEndTime;

    /**
     * 发放任务添加时间
     */
    private Long addTime;

    /**
     * 发放任务ID列表
     */
    private String groupIds;

    /**
     * 券配置ID
     */
    private Long couponConfigId;

    /**
     * 券配置名称
     */
    private String couponName;

    /**
     * 优惠券类型(cash:现金券，discount:折扣券，deductible:抵扣券)
     */
    private Integer couponTypeCode;

    /**
     * 优惠券政策
     */
    private String policy;

    /**
     * 优惠值  金额：分  折扣：折 900 = 9折
     */
    private Long promotionValue;

    /**
     * 优惠券发放渠道
     */
    private String sendChannel;

    /**
     * 优惠券最小开始时间
     */
    private Long globalCouponStartTime;

    /**
     * 优惠券最大结束时间
     */
    private Long globalCouponEndTime;

    /**
     * 申请部门
     */
    private String department;

    /**
     * 创建来源
     */
    private Integer source;

    /**
     * 优惠券类型  1: 商品券 2: 运费券
     */
    private Integer couponType;

    /**
     * 履约方式 -1: 所有方式 139: 门店闪送 (这个跟交易中台统一定义)
     */
    private Integer shipmentId;

}
