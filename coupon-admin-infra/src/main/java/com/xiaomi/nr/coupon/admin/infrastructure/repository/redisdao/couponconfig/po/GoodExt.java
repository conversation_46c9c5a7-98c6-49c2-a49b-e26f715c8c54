package com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.po;

import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

/**
 * 商品扩展信息
 *
 * <AUTHOR>
 * @date 2024/1/3
 **/
@Data
@Builder
public class GoodExt {

    /**
     * tag labor:工时，part:配件
     */
    @SerializedName("t")
    private String tag;

    /**
     * 数量，核销时可用券的工时或配件ssu个数
     */
    @SerializedName("c")
    private Integer count;

}
