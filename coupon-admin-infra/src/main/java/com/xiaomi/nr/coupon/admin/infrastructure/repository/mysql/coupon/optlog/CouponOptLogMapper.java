package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.optlog;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.optlog.po.CouponLogPO;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * @Description: 优惠券日志
 * @Date: 2022.03.01 14:29
 */
@Mapper
@Component
public interface CouponOptLogMapper {

    /**
     * 根据优惠券id查询最新日志
     * @return
     */
    @Select("<script>" +
            "select log.operator from nr_coupon_config_log log where id =(SELECT max(id) from nr_coupon_config_log where config_id=#{configId})" +
            "</script>")
    String getLatestLogByConfigId(@Param("configId") Long configId);


    /**
     * 根据优惠券id查询日志
     * @param couponId
     * @param offset
     * @param limit
     * @return
     */
    @Select("select * from nr_coupon_config_log where config_id=#{couponId} order by id desc limit #{offset},#{limit}")
    List<CouponLogPO> getLogPageByCouponId(@Param("couponId") Long couponId, @Param("offset") int offset, @Param("limit") int limit);

    /**
     * 根据优惠券id查询日志总数
     * @param couponId
     * @return
     */
    @Select("select count(*) from nr_coupon_config_log where config_id=#{couponId}")
    Integer getTotalCountByCouponId(Long couponId);

    /**
     * 根据日志id查询日志
     * @param id
     * @return
     */
    @Select("select * from nr_coupon_config_log where id=#{id}")
    CouponLogPO getLogById(Long id);

    /**
     * 插入couponReview
     *
     * @return
     */
    @Options(useGeneratedKeys = true, keyProperty = "id")
    @Insert("insert into nr_coupon_config_log (config_id,review_id,operation_type,operator,operation_content)" +
            " values " +
            "(#{configId,jdbcType=BIGINT},#{reviewId,jdbcType=BIGINT},#{operationType,jdbcType=TINYINT},#{operator,jdbcType=VARCHAR},#{operationContent,jdbcType=VARCHAR}" +
            ")")
    Long insert(CouponLogPO po);

    /**
     * 根据优惠券id查询最后修改日志
     * @param ids
     * @return
     */
    @Select("<script>" +
            "select max(id) as id,config_id,operator from nr_coupon_config_log where operation_type in (1,2)  " +
            "and config_id in <foreach item='id' index='index' collection='ids' open='(' separator=',' close=')'>#{id}</foreach>" +
            "group by config_id" +
            "</script>")
    List<CouponLogPO> getLatestModifyByConfigId(@Param("ids") Set<Long> ids);
}
