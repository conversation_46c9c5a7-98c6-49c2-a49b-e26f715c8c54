package com.xiaomi.nr.coupon.admin.application.dubbo.convert;

import com.google.gson.reflect.TypeToken;
import com.xiaomi.cnzone.maindataapi.model.OrgDto;
import com.xiaomi.com.i18n.area.Area;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.*;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponConfigDTO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.CostShareDTO;
import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.CouponConfigInfoDTO;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.api.enums.TranslationEnum;
import com.xiaomi.nr.coupon.admin.api.utils.I18nUtil;
import com.xiaomi.nr.coupon.admin.api.utils.TimeUtil;
import com.xiaomi.nr.coupon.admin.domain.coupon.goods.GoodsService;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.*;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.admin.enums.scene.SceneCodeEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.CouponSceneRepository;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.CouponTaskRepository;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.excel.GoodsCouponPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.po.*;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.scene.po.CouponScenePO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.po.ConfigInfoCachePo;
import com.xiaomi.nr.coupon.admin.infrastructure.rpc.BrProxy;
import com.xiaomi.nr.coupon.admin.infrastructure.rpc.StoreProxyService;
import com.xiaomi.nr.coupon.admin.util.CouponDiscountUtil;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.RpcContextUtil;
import com.xiaomi.nr.coupon.admin.util.StringUtil;
import com.xiaomi.nr.coupon.admin.util.beancopy.BeanMapper;
import com.xiaomi.nr.phoenix.api.dto.request.watermelon.WatermelonCouponTemplateReq;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CouponConfigConvert {

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    @Autowired
    private CouponSceneRepository couponSceneRepository;

    @Autowired
    private CouponTaskRepository couponTaskRepository;

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private StoreProxyService storeProxyService;

    @Autowired
    private BrProxy brProxy;


    public static CouponConfigPO voConvertPo(CouponConfigVO couponConfigVO) {
        CouponConfigPO couponConfigPO = new CouponConfigPO();
        // 兼容旧版代码, 业务平台默认为3C零售
        couponConfigPO.setBizPlatform(BizPlatformEnum.RETAIL.getCode());
        BeanMapper.copy(couponConfigVO, couponConfigPO);
        BeanMapper.copy(couponConfigVO.getUseTermVO(), couponConfigPO);

        PromotionRuleVO promotionRuleVO = couponConfigVO.getPromotionRuleVO();
        couponConfigPO.setPromotionType(promotionRuleVO.getPromotionType());
        couponConfigPO.setPromotionValue(promotionRuleVO.getPromotionValue());
        couponConfigPO.setMaxReduce(promotionRuleVO.getMaxReduce());
        couponConfigPO.setBottomPrice(promotionRuleVO.getBottomPrice());
        couponConfigPO.setBottomCount(promotionRuleVO.getBottomCount());
        couponConfigPO.setBottomType(promotionRuleVO.getBottomType());
        if (PromotionTypeEnum.DirectReduce.getValue() == promotionRuleVO.getPromotionType() || PromotionTypeEnum.NyuanBuy.getValue() == promotionRuleVO.getPromotionType() || PromotionTypeEnum.GIFT.getValue() == promotionRuleVO.getPromotionType()) {
            couponConfigPO.setBottomCount(1);
            couponConfigPO.setBottomType(BottomTypeEnum.OverCount.getValue());
        }


        BeanMapper.copy(couponConfigVO.getDistributionRuleVO(), couponConfigPO);
        if (couponConfigVO.getId() <= 0) {
            couponConfigPO.setStatus(CouponConfigStatusEnum.OFFLINE.getCode());
        }
        GoodsRuleVO goodsRuleVO = couponConfigVO.getGoodsRuleVO();
        couponConfigPO.setScopeType(goodsRuleVO.getScopeType());
        couponConfigPO.setGoodsDepartments(goodsRuleVO.getGoodsDepartments() == null ? "" : StringUtils.join(goodsRuleVO.getGoodsDepartments(), ","));
        couponConfigPO.setAutoUpdateGoods(Optional.ofNullable(goodsRuleVO.getAutoUpdateGoods()).orElse(2));
        if (MapUtils.isNotEmpty(goodsRuleVO.getGoodsInclude())) {
            GoodItemPO goodItemPO = new GoodItemPO();
            goodItemPO.setSku(Optional.ofNullable(goodsRuleVO.getGoodsInclude().get(GoodsLevelEnum.Sku.getValue())).orElse(null));
            goodItemPO.setPackages(Optional.ofNullable(goodsRuleVO.getGoodsInclude().get(GoodsLevelEnum.Package.getValue())).orElse(null));
            couponConfigPO.setGoodsInclude(GsonUtil.toJson(goodItemPO));
        }

        if (MapUtils.isNotEmpty(goodsRuleVO.getGoodsExclude())) {
            GoodItemPO goodItemPO = new GoodItemPO();
            goodItemPO.setSku(Optional.ofNullable(goodsRuleVO.getGoodsExclude().get(GoodsLevelEnum.Sku.getValue())).orElse(null));
            goodItemPO.setPackages(Optional.ofNullable(goodsRuleVO.getGoodsExclude().get(GoodsLevelEnum.Package.getValue())).orElse(null));
            couponConfigPO.setGoodsExclude(GsonUtil.toJson(goodItemPO));
        } else {
            couponConfigPO.setGoodsExclude(StringUtils.EMPTY);
        }

        couponConfigPO.setCategoryIds(goodsRuleVO.getCategoryIds() == null ? StringUtils.EMPTY : StringUtils.join(goodsRuleVO.getCategoryIds(), ","));

        couponConfigPO.setStartFetchTime(couponConfigVO.getStartFetchTime().getTime() / 1000);
        couponConfigPO.setEndFetchTime(couponConfigVO.getEndFetchTime().getTime() / 1000);
        if (UseTimeTypeEnum.RELATIVE.getValue() == couponConfigVO.getUseTermVO().getUseTimeType()) {
            couponConfigVO.getUseTermVO().setStartUseTime(couponConfigVO.getStartFetchTime());
            int useDuration = couponConfigVO.getUseTermVO().getUseDuration();
            couponConfigVO.getUseTermVO().setEndUseTime(DateUtils.addHours(couponConfigVO.getEndFetchTime(), useDuration));
        }
        couponConfigPO.setStartUseTime(couponConfigVO.getUseTermVO().getStartUseTime().getTime() / 1000);
        couponConfigPO.setEndUseTime(couponConfigVO.getUseTermVO().getEndUseTime().getTime() / 1000);

        couponConfigPO.setUseChannel(StringUtils.join(couponConfigVO.getUseChannel().keySet(), ","));
        Map<Integer, UseChannelPO> usePlatform = new HashMap<>();
        Map<Integer, UseChannelPO> useStore = new HashMap<>();
        for (Map.Entry<Integer, UseChannelVO> entry : couponConfigVO.getUseChannel().entrySet()) {
            if (UseChannelsEnum.XIAOMI_SHOP.getValue() != entry.getKey()) {
                UseChannelPO useLimitPO = new UseChannelPO();
                useLimitPO.setAll(entry.getValue().isAll());
                useLimitPO.setLimitIds(entry.getValue().getLimitIds());
                useStore.put(entry.getKey(), useLimitPO);
            } else {
                UseChannelPO useLimitPO = new UseChannelPO();
                useLimitPO.setAll(entry.getValue().isAll());
                useLimitPO.setLimitIds(entry.getValue().getLimitIds());
                usePlatform.put(entry.getKey(), useLimitPO);
            }
        }
        couponConfigPO.setCostShare(GsonUtil.toJson(couponConfigVO.getCostShare()));
        couponConfigPO.setUsePlatform(GsonUtil.toJson(usePlatform));
        couponConfigPO.setUseStore(GsonUtil.toJson(useStore));
        // TODO 会员项目固定逻辑，后续前端添加是否会员按钮可删除
        // 目前来说场景code不会改所以通过常量判断，不每次都查库
        if (SceneCodeEnum.PRO_MEMBER_SCENE.getCode().equals(couponConfigVO.getSendScene())) {
            couponConfigVO.getExtProp().setProMember(Integer.parseInt(IsProMemberEnum.Yes.getMysqlValue()));
        } else {
            couponConfigVO.getExtProp().setProMember(Integer.parseInt(IsProMemberEnum.No.getMysqlValue()));
        }
        couponConfigPO.setExtProp(GsonUtil.toJson(couponConfigVO.getExtProp()));
        couponConfigPO.setCreateTime(TimeUtil.getNowUnixSecond());
        couponConfigPO.setCode(Integer.parseInt(ModeTypeEnum.NoCode.getMysqlValue()));
        couponConfigPO.setSendChannel(SendChannelEnum.findByCode(couponConfigVO.getSendScene()).getValue());
        couponConfigPO.setSource(2);
        couponConfigPO.setCouponType(Optional.ofNullable(couponConfigVO.getCouponType()).orElse(1));
        couponConfigPO.setShipmentId(Optional.ofNullable(couponConfigVO.getShipmentId()).orElse(-1));
        couponConfigPO.setServiceType(0);
        couponConfigPO.setFetchLimitType(1);
        couponConfigPO.setFetchLimit(1);
        // 使用次数限制，默认为1（限制）
        couponConfigPO.setTimesLimit(1);
        couponConfigPO.setPublicPromotion(Optional.ofNullable(couponConfigVO.getExtProp().getPublicPromotion()).orElse(PublicPromotionEnum.No.getValue()));
        if(I18nUtil.isI18n()){
            couponConfigPO.setAreaIds(org.apache.commons.lang3.StringUtils.EMPTY);
        }
        return couponConfigPO;
    }

    public CouponConfigPO dtoConvertPo(CouponConfigDTO couponConfigDTO, Integer bizPlatform) {
        CouponConfigPO couponConfigPO = new CouponConfigPO();
        couponConfigPO.setBizPlatform(bizPlatform);
        BeanMapper.copy(couponConfigDTO, couponConfigPO);
        BeanMapper.copy(couponConfigDTO.getUseTermVO(), couponConfigPO);

        PromotionRuleVO promotionRuleVO = couponConfigDTO.getPromotionRuleVO();
        couponConfigPO.setPromotionType(promotionRuleVO.getPromotionType());
        couponConfigPO.setPromotionValue(promotionRuleVO.getPromotionValue());
        couponConfigPO.setMaxReduce(promotionRuleVO.getMaxReduce() == null ? 0L : promotionRuleVO.getMaxReduce());
        couponConfigPO.setBottomPrice(promotionRuleVO.getBottomPrice() == null ? 0L : promotionRuleVO.getBottomPrice());
        couponConfigPO.setBottomCount(promotionRuleVO.getBottomCount() == null ? 0 : promotionRuleVO.getBottomCount());
        couponConfigPO.setBottomType(promotionRuleVO.getBottomType() == null ? 0 : promotionRuleVO.getBottomType());
        if (PromotionTypeEnum.DirectReduce.getValue() == promotionRuleVO.getPromotionType() || PromotionTypeEnum.NyuanBuy.getValue() == promotionRuleVO.getPromotionType() || PromotionTypeEnum.GIFT.getValue() == promotionRuleVO.getPromotionType()) {
            couponConfigPO.setBottomCount(1);
            couponConfigPO.setBottomType(BottomTypeEnum.OverCount.getValue());
        }


        BeanMapper.copy(couponConfigDTO.getDistributionRuleVO(), couponConfigPO);
        if (couponConfigDTO.getId() <= 0) {
            couponConfigPO.setStatus(CouponConfigStatusEnum.ONLINE.getCode());
        }

        GoodsRuleVO goodsRuleVO = couponConfigDTO.getGoodsRuleVO();
        if (Objects.nonNull(goodsRuleVO)) {
            couponConfigPO.setScopeType(goodsRuleVO.getScopeType());
            couponConfigPO.setGoodsDepartments(goodsRuleVO.getGoodsDepartments() == null ? "" : StringUtils.join(goodsRuleVO.getGoodsDepartments(), ","));
            couponConfigPO.setAutoUpdateGoods(Optional.ofNullable(goodsRuleVO.getAutoUpdateGoods()).orElse(2));

            GoodItemPO includeGoodItem = new GoodItemPO();
            if (Objects.nonNull(goodsRuleVO.getGoodsInclude())) {
                includeGoodItem.setSku(Optional.ofNullable(goodsRuleVO.getGoodsInclude().get(GoodsLevelEnum.Sku.getValue())).orElse(null));
                includeGoodItem.setPackages(Optional.ofNullable(goodsRuleVO.getGoodsInclude().get(GoodsLevelEnum.Package.getValue())).orElse(null));
                includeGoodItem.setSsu(Optional.ofNullable(goodsRuleVO.getGoodsInclude().get(GoodsLevelEnum.Ssu.getValue())).orElse(null));
                includeGoodItem.setSuit(Optional.ofNullable(goodsRuleVO.getGoodsInclude().get(GoodsLevelEnum.Suit.getValue())).orElse(null));
            }
            includeGoodItem.setLabourHourSsu(Optional.ofNullable(goodsRuleVO.getLabourHourSsu()).orElse(null));
            includeGoodItem.setPartsSsu(Optional.ofNullable(goodsRuleVO.getPartsSsu()).orElse(null));
            includeGoodItem.setGoodsMsg(Optional.ofNullable(goodsRuleVO.getGoodsMsg()).orElse(null));
            couponConfigPO.setGoodsInclude(GsonUtil.toJson(includeGoodItem));

            if (MapUtils.isNotEmpty(goodsRuleVO.getGoodsExclude())) {
                GoodItemPO excludeGoodItem = new GoodItemPO();
                excludeGoodItem.setSku(Optional.ofNullable(goodsRuleVO.getGoodsExclude().get(GoodsLevelEnum.Sku.getValue())).orElse(null));
                excludeGoodItem.setPackages(Optional.ofNullable(goodsRuleVO.getGoodsExclude().get(GoodsLevelEnum.Package.getValue())).orElse(null));
                excludeGoodItem.setSsu(Optional.ofNullable(goodsRuleVO.getGoodsExclude().get(GoodsLevelEnum.Ssu.getValue())).orElse(null));
                excludeGoodItem.setSuit(Optional.ofNullable(goodsRuleVO.getGoodsExclude().get(GoodsLevelEnum.Suit.getValue())).orElse(null));
                couponConfigPO.setGoodsExclude(GsonUtil.toJson(excludeGoodItem));
            } else {
                couponConfigPO.setGoodsExclude(StringUtils.EMPTY);
            }
        }

        couponConfigPO.setCategoryIds(goodsRuleVO.getCategoryIds() == null ? StringUtils.EMPTY : StringUtils.join(goodsRuleVO.getCategoryIds(), ","));

        couponConfigPO.setStartFetchTime(couponConfigDTO.getStartFetchTime().getTime() / 1000);
        couponConfigPO.setEndFetchTime(couponConfigDTO.getEndFetchTime().getTime() / 1000);
        UseTimeTypeEnum useTimeType = UseTimeTypeEnum.getByValue(couponConfigDTO.getUseTermVO().getUseTimeType());

        long startUseTime = 0L;
        long endUseTime = 0L;
        if (UseTimeTypeEnum.RELATIVE.equals(useTimeType)) {
            startUseTime = couponConfigDTO.getStartFetchTime().getTime() / 1000;
            int useDuration = couponConfigDTO.getUseTermVO().getUseDuration();
            endUseTime = DateUtils.addHours(couponConfigDTO.getEndFetchTime(), useDuration).getTime() / 1000;
        } else if (UseTimeTypeEnum.ABSOLUTE.equals(useTimeType)) {
            startUseTime = couponConfigDTO.getUseTermVO().getStartUseTime().getTime() / 1000;
            endUseTime = couponConfigDTO.getUseTermVO().getEndUseTime().getTime() / 1000;
        }
        couponConfigPO.setStartUseTime(startUseTime);
        couponConfigPO.setEndUseTime(endUseTime);

        // 使用平台、使用门店
        couponConfigPO.setUseChannel(StringUtils.join(couponConfigDTO.getUseChannel().keySet(), ","));
        Map<Integer, UseChannelPO> usePlatform = new HashMap<>();
        Map<Integer, UseChannelPO> useStore = new HashMap<>();
        for (Map.Entry<Integer, UseChannelVO> entry : couponConfigDTO.getUseChannel().entrySet()) {
            if (UseChannelsEnum.XIAOMI_SHOP.getValue() != entry.getKey()) {
                UseChannelPO useLimitPO = new UseChannelPO();
                useLimitPO.setAll(entry.getValue().isAll());
                useLimitPO.setLimitIds(entry.getValue().getLimitIds());
                useStore.put(entry.getKey(), useLimitPO);
            } else {
                UseChannelPO useLimitPO = new UseChannelPO();
                useLimitPO.setAll(entry.getValue().isAll());
                useLimitPO.setLimitIds(entry.getValue().getLimitIds());
                usePlatform.put(entry.getKey(), useLimitPO);
            }
        }
        couponConfigPO.setUsePlatform(GsonUtil.toJson(usePlatform));
        couponConfigPO.setUseStore(GsonUtil.toJson(useStore));

        couponConfigPO.setCostShare(GsonUtil.toJson(couponConfigDTO.getCostShare()));

        // TODO 会员项目固定逻辑，后续前端添加是否会员按钮可删除
        // 目前来说场景code不会改所以通过常量判断，不每次都查库
        if(Objects.isNull(couponConfigDTO.getExtProp())){
            couponConfigDTO.setExtProp(new ExtPropVO());
        }
        if (SceneCodeEnum.PRO_MEMBER_SCENE.getCode().equals(couponConfigDTO.getSendScene())) {
            couponConfigDTO.getExtProp().setProMember(Integer.parseInt(IsProMemberEnum.Yes.getMysqlValue()));
        } else {
            couponConfigDTO.getExtProp().setProMember(Integer.parseInt(IsProMemberEnum.No.getMysqlValue()));
        }
        couponConfigPO.setExtProp(GsonUtil.toJson(couponConfigDTO.getExtProp()));
        couponConfigPO.setCreateTime(TimeUtil.getNowUnixSecond());
        couponConfigPO.setCode(Integer.parseInt(ModeTypeEnum.NoCode.getMysqlValue()));
        couponConfigPO.setSendChannel(SendChannelEnum.findByCode(couponConfigDTO.getSendScene()).getValue());
        couponConfigPO.setSource(2);
        couponConfigPO.setCouponType(Optional.ofNullable(couponConfigDTO.getCouponType()).orElse(1));
        couponConfigPO.setShipmentId(Optional.ofNullable(couponConfigDTO.getShipmentId()).orElse(-1));
        couponConfigPO.setServiceType(Optional.ofNullable(couponConfigDTO.getServiceType()).orElse(0));
        couponConfigPO.setFetchLimitType(Optional.ofNullable(couponConfigDTO.getFetchLimitType()).orElse(1));
        couponConfigPO.setFetchLimit(couponConfigPO.getFetchLimit());
        // 使用次数限制，默认为1（限制）
        couponConfigPO.setTimesLimit(Optional.ofNullable(couponConfigDTO.getTimesLimit()).orElse(1));
        // 是否公开推广
        couponConfigPO.setPublicPromotion(Optional.ofNullable(couponConfigDTO.getExtProp().getPublicPromotion()).orElse(PublicPromotionEnum.No.getValue()));

        // 预算信息
        couponConfigPO.setBudgetApplyNo(Optional.ofNullable(couponConfigDTO.getBudgetApplyNo()).orElse(StringUtils.EMPTY));
        couponConfigPO.setLineNum(Optional.ofNullable(couponConfigDTO.getLineNum()).orElse(0L));
        couponConfigPO.setBudgetCreateTime(Optional.ofNullable(couponConfigDTO.getBudgetCreateTime()).orElse(StringUtils.EMPTY));
        // 地区id
        couponConfigPO.setAreaId(Optional.ofNullable(couponConfigDTO.getAreaId()).orElse(CommonConstant.AREA_ID_CN));
        if(I18nUtil.isI18n()){
            couponConfigPO.setAreaIds(org.apache.commons.lang3.StringUtils.EMPTY);
        } else {
            couponConfigPO.setWorkflowId(CommonConstant.ZERO_LONG);
        }
        return couponConfigPO;
    }


    /**
     * 券配置查询参数转换
     *
     * @param req 请求参数
     * @return SearchConfigListParam
     */
    public SearchConfigParam transferToSearchParameter(CouponListRequest req) {
        SearchConfigParam param = new SearchConfigParam();
//        param.setBizPlatform(req.getBizPlatform());
        param.setBizPlatformList(req.getBizPlatformList());
        param.setCouponType(req.getCouponType());
        param.setId(req.getId());
        param.setName(req.getName());
        param.setPromotionType(req.getPromotionType());
        param.setUseChannel(req.getUseChannel());
        param.setSendScene(req.getSendScene());
        param.setTimeStatus(req.getTimeStatus());
        param.setStatus(req.getStatus());
        param.setCreator(req.getCreator());
        param.setOrderBy(convertOrderBy(req.getOrderBy()));
        param.setOrderDirection(StringUtils.equals(req.getOrderDirection(), null) ? "desc" : req.getOrderDirection());
        param.setPageNo(req.getPageNo());
        param.setPageSize(req.getPageSize());
        param.setStartFetchTime(req.getStartFetchTime() == null ? null : TimeUtil.convertDateToLong(req.getStartFetchTime()));
        param.setEndFetchTime(req.getEndFetchTime() == null ? null : TimeUtil.convertDateToLong(req.getEndFetchTime()));
        param.setStartUseTime(req.getStartUseTime() == null ? null : TimeUtil.convertDateToLong(req.getStartUseTime()));
        param.setEndUseTime(req.getEndUseTime() == null ? null : TimeUtil.convertDateToLong(req.getEndUseTime()));
        param.setServiceType(req.getServiceType());
        if (StringUtils.isNotBlank(req.getAreaId())) {
            param.setAreaId(req.getAreaId());
        }
        if (!Objects.isNull(req.getTimeStatus()) && req.getTimeStatus() == CouponTimeStatusEnum.STOP_FETCHING.getCode()) {
            param.setStatus(CouponConfigStatusEnum.STOP_FETCHING.getCode());
        }

        //商品与门店的特殊逻辑处理，商品与门店在json里，无法直接查询，先走一下ES获取ConfigId
        if (req.getSku() != null) {
            try {
                List<SkuInfoVO> skuInfoVOList = goodsService.searchSkuInfo(Collections.singletonList(req.getSku()), req.getAreaId());
                if (!skuInfoVOList.isEmpty()) {
                    SkuInfoVO skuInfoVO = skuInfoVOList.get(0);
                    param.setSsu(skuInfoVO.getSsu());
                } else {
                    //搜不到的场景，不该搜得到
                    param.setSsu(-1L);
                }
            } catch (Exception e) {
                param.setSsu(-1L);
            }
        }
        if (req.getSsu() != null) {
            param.setSsu(req.getSsu());
        }
        if (StringUtils.isNotBlank(req.getStoreId())) {
            param.setStoreId(req.getStoreId());
            try {
                List<OrgDto> orgDtos = storeProxyService.queryStoreList(Collections.singletonList(req.getStoreId()));
                if (orgDtos != null && !orgDtos.isEmpty()) {
                    OrgDto orgDto = orgDtos.get(0);
                    Integer channel = storeProxyService.getChannel(orgDto);
                    param.setUseChannel(Collections.singletonList(channel));
                }
            } catch (Exception e) {
                param.setStoreId(null);
            }
        }
        //只有结构化offer授权店才会用到这个，param.setUseChannel已在上文中设定了渠道：授权店第一个判断肯定为true
        if(CollectionUtils.isNotEmpty(req.getStoreIds()) && !req.getStoreIds().contains("*")){
            param.setStoreIds(req.getStoreIds());
        }

        //如果有门店id的话，根据门店id写useChannel
        return param;
    }


    /**
     * 优惠券列表转换 po -> vo
     *
     * @param pos 券配置实体
     * @return 券列表返回信息
     */
    public List<CouponConfigListVO> convertCouponConfigListPOToVO(List<CouponConfigPO> pos) throws BizError {
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }

        List<Long> configIdList = pos.stream().map(CouponConfigPO::getId).collect(Collectors.toList());
        Map<Long, Long> sendCountMap = couponConfigRepository.getCouponSendCount(configIdList);
        // List<String> sendScene = pos.stream().map(CouponConfigPO::getSendScene).collect(Collectors.toList());
        List<String> sendScene = pos.stream().flatMap(e -> Arrays.stream(e.getSendScene().split(","))).distinct().collect(Collectors.toList());
        Map<String, String> sceneMap = couponSceneRepository.selectBySceneCodes(sendScene);

        List<Long> codeConfigList = pos.stream().map(CouponConfigPO::getId).collect(Collectors.toList());
        Map<Long, Long> resultMap = couponTaskRepository.getCodeTaskResult(codeConfigList);

        List<CouponConfigListVO> vos = new ArrayList<>();
        for (CouponConfigPO po : pos) {
            CouponConfigListVO vo = convertCouponConfigListVO(sendCountMap, sceneMap, po);
            if (resultMap.containsKey(vo.getId())) {
                vo.setSendCount(resultMap.get(vo.getId()));
            }
            vos.add(vo);
        }

        return vos;
    }

    /**
     * 优惠券列表转换 po -> vo
     *
     * @param sendCountMap
     * @param sceneMap
     * @param po
     * @return
     */
    private CouponConfigListVO convertCouponConfigListVO(Map<Long, Long> sendCountMap, Map<String, String> sceneMap, CouponConfigPO po) {
        CouponConfigListVO vo = convertCouponConfigListVO(po);
        vo.setTimeStatus(transferToTimeStatus(po.getStartFetchTime(), po.getEndFetchTime(), po.getStatus()));
        vo.setSendCount(sendCountMap.get(po.getId()));
        List<String> codes = Arrays.asList(po.getSendScene().split(","));
        String sendSceneName = codes.stream().map(sceneMap::get).collect(Collectors.joining(","));
        vo.setSendSceneName(sendSceneName);
        vo.setUpdateTime(new Date(po.getUpdateTime().getTime()));
        vo.setSource(po.getSource());
        vo.setServiceType(po.getServiceType());
        vo.setFetchLimitType(po.getFetchLimitType());
        vo.setAreaId(po.getAreaId());
        vo.setStartFetchTimeStamp(vo.getStartFetchTime().getTime() / 1000);
        vo.setEndFetchTimeStamp(vo.getEndFetchTime().getTime() / 1000);
        vo.setCreateTimeStamp(vo.getCreateTime().getTime() / 1000);
        return vo;
    }

    /**
     * 转换券列表vo
     *
     * @param po
     * @return
     */
    private CouponConfigListVO convertCouponConfigListVO(CouponConfigPO po) {
        CouponConfigListVO vo = new CouponConfigListVO();
        vo.setId(po.getId());
        vo.setName(po.getName());
        vo.setPromotionType(po.getPromotionType());
        vo.setPromotionValue(po.getPromotionValue());
        if(Objects.nonNull(po.getPromotionType()) && PromotionTypeEnum.ConditionDiscount.getValue() == po.getPromotionType()){
            vo.setDiscountDesc(CouponDiscountUtil.formatDiscountCouponDesc(RpcContextUtil.headerLanguage(I18nUtil.getGlobalAreaId()), po.getPromotionValue()));
        }
        vo.setBottomType(po.getBottomType());
        vo.setBottomPrice(po.getBottomPrice());
        vo.setBottomCount(po.getBottomCount());
        vo.setApplyCount(po.getApplyCount());
        vo.setStartFetchTime(TimeUtil.convertLongToDate(po.getStartFetchTime()));
        vo.setEndFetchTime(TimeUtil.convertLongToDate(po.getEndFetchTime()));
        vo.setStartUseTimeStamp(TimeUtil.convertLongToDate(po.getStartUseTime()).getTime() / 1000);
        vo.setEndUseTimeStamp(TimeUtil.convertLongToDate(po.getEndUseTime()).getTime() / 1000);
        vo.setStatus(po.getStatus());
        vo.setCreator(po.getCreator());
        vo.setCreateTime(TimeUtil.convertLongToDate(po.getCreateTime()));
        vo.setUseChannel(convertUseChannel(po.getUseChannel()));
        vo.setChannel(Integer.parseInt(po.getUseChannel()));
        vo.setCouponType(po.getCouponType());

        if (I18nUtil.isI18n()) {
            vo.fillGlobalField();
        }
        return vo;
    }


    /**
     * 券详情转换 po -> vo
     *
     * @param couponConfigPO 券配置实体
     * @return 券详情返回信息
     */
    public CouponConfigDetailVO convertCouponConfigPOToVO(CouponConfigPO couponConfigPO, GoodsRuleVO goodsRuleVO) throws Exception {
        if (Objects.isNull(couponConfigPO)) {
            throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_TASK_COMMIT_ERROR_NULL.getTranslateContent());
        }
        CouponConfigDetailVO couponConfigVO = new CouponConfigDetailVO();
        couponConfigVO.setId(couponConfigPO.getId());
        couponConfigVO.setName(couponConfigPO.getName());
        couponConfigVO.setCouponDesc(couponConfigPO.getCouponDesc());
        couponConfigVO.setBudgetInfoDto(brProxy.queryBudgetDetail(couponConfigPO.getBudgetApplyNo(), couponConfigPO.getLineNum()));
        couponConfigVO.setCouponType(couponConfigPO.getCouponType());
        couponConfigVO.setShipmentId(couponConfigPO.getShipmentId());
        couponConfigVO.setSendScene(couponConfigPO.getSendScene());
        if (StringUtils.isNotBlank(couponConfigPO.getSendScene())) {
            List<CouponScenePO> scenePos = couponSceneRepository.selectBySceneCodes(couponConfigPO.getSendScene());
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(scenePos)) {
                throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_CONFIG_INSERT_ERROR_SCENE.getTranslateContent());
            }
            couponConfigVO.setSendSceneType(scenePos.get(0).getRelationSceneId());
            couponConfigVO.setSendMode(scenePos.get(0).getSendMode());
        }


        couponConfigVO.setStartFetchTime(TimeUtil.convertLongToDate(couponConfigPO.getStartFetchTime()));
        couponConfigVO.setEndFetchTime(TimeUtil.convertLongToDate(couponConfigPO.getEndFetchTime()));
        couponConfigVO.setStartFetchTimeStamp(couponConfigVO.getStartFetchTime().getTime() / 1000);
        couponConfigVO.setEndFetchTimeStamp(couponConfigVO.getEndFetchTime().getTime() / 1000);

        couponConfigVO.setBeginTimeShow(Area.of(couponConfigPO.getAreaId()).timeFormat(couponConfigPO.getStartFetchTime() * 1000));
        couponConfigVO.setEndTimeShow(Area.of(couponConfigPO.getAreaId()).timeFormat(couponConfigPO.getEndFetchTime() * 1000));


        couponConfigVO.setUseTermVO(convertUseTermVO(couponConfigPO));

        List<Integer> useChannlIds = StringUtil.convertToIntegerList(couponConfigPO.getUseChannel());
        Map<Integer, UseChannelVO> useChannel = new HashMap<>();
        for (Integer channelId : useChannlIds) {
            if (Objects.equals(UseChannelsEnum.XIAOMI_SHOP.getValue(), channelId)) {
                useChannel.putAll(Objects.requireNonNull(GsonUtil.fromMapJsonUseChannel(couponConfigPO.getUsePlatform())));
            } else {
                useChannel.putAll(Objects.requireNonNull(GsonUtil.fromMapJsonUseChannel(couponConfigPO.getUseStore())));
            }
        }
        couponConfigVO.setUseChannel(useChannel);

        PromotionRuleVO promotionRuleVO = convertPromotionRuleVO(couponConfigPO);
        couponConfigVO.setPromotionRuleVO(promotionRuleVO);

        couponConfigVO.setDistributionRuleVO(new DistributionRuleVO(couponConfigPO.getApplyCount(), couponConfigPO.getFetchLimit()));
        ExtPropVO extProp = GsonUtil.fromJson(couponConfigPO.getExtProp(), ExtPropVO.class);
        couponConfigVO.setExtProp(extProp);
        couponConfigVO.setAreaIds(StringUtil.convertToLongList(couponConfigPO.getAreaIds()));

        couponConfigVO.setGoodsRuleVO(goodsRuleVO);
        couponConfigVO.setSendPurpose(couponConfigPO.getSendPurpose());
        couponConfigVO.setCostShare(GsonUtil.fromMapJson(couponConfigPO.getCostShare(), new TypeToken<Map<Integer, Integer>>() {
        }.getType()));
        couponConfigVO.setCreator(couponConfigPO.getCreator());
        couponConfigVO.setDepartmentId(couponConfigPO.getDepartmentId());
        couponConfigVO.setStatus(couponConfigPO.getStatus());
        couponConfigVO.setFetchStatus(transferToTimeStatus(couponConfigPO.getStartFetchTime(), couponConfigPO.getEndFetchTime(), couponConfigPO.getStatus()));
        couponConfigVO.setUpdateTime(new Date(couponConfigPO.getUpdateTime().getTime()));
        couponConfigVO.setFetchLimit(couponConfigPO.getFetchLimit());
        couponConfigVO.setFetchLimitType(couponConfigPO.getFetchLimitType());
        if(null!=extProp){
            couponConfigVO.setCheckoutStage(extProp.getCheckoutStage());
            couponConfigVO.setDisplayDate(extProp.getDisplayDate());
        }
        couponConfigVO.setAreaId(couponConfigPO.getAreaId());
        couponConfigVO.setWorkflowId(couponConfigPO.getWorkflowId());
        return couponConfigVO;
    }

    /**
     * 转换商品规则详情vo
     *
     * @param goodsRuleVO
     * @return
     */
    private GoodsRuleDetailVO convertGoodsRuleDetailVO(GoodsRuleVO goodsRuleVO) {
        GoodsRuleDetailVO goodsRuleDetailVO = new GoodsRuleDetailVO();
        goodsRuleDetailVO.setScopeType(goodsRuleVO.getScopeType());
        goodsRuleDetailVO.setGoodsDepartments(goodsRuleVO.getGoodsDepartments());
        goodsRuleDetailVO.setCategoryIds(goodsRuleVO.getCategoryIds());
        goodsRuleDetailVO.setAutoUpdateGoods(goodsRuleVO.getAutoUpdateGoods());
        return goodsRuleDetailVO;
    }


    /**
     * 使用渠道文案映射
     *
     * @param userChannel 使用渠道id
     * @return 返回文案信息
     */
    private String convertUseChannel(String userChannel) {

        if (StringUtils.isEmpty(userChannel)) {
            return null;
        }

        List<String> useChannelDesc = new LinkedList<>();
        String[] channels = userChannel.split(",");
        for (String channel : channels) {
            useChannelDesc.add(UseChannelsEnum.getNameByValue(Integer.parseInt(channel)));
        }
        return StringUtils.join(useChannelDesc, ",");
    }


    /**
     * 预估优惠券总成本 元
     *
     * @return long
     */
    public BigDecimal estimatedCost(long applyCount, GoodsRuleDetailVO goodsRuleDetailVO, PromotionRuleVO promotionRuleVO) {
        if (I18nUtil.isI18n()) {
            return BigDecimal.ZERO;
        }
        PromotionTypeEnum promotionTypeEnum = PromotionTypeEnum.getByValue(promotionRuleVO.getPromotionType());
        long cost = CommonConstant.ZERO_LONG;
        if (Objects.isNull(promotionTypeEnum)) {
            return BigDecimal.ZERO;
        }
        switch (promotionTypeEnum) {
            case DirectReduce:
            case ConditionReduce:
                cost = applyCount * promotionRuleVO.getPromotionValue();
                break;
            case ConditionDiscount:
                cost = applyCount * promotionRuleVO.getMaxReduce();
                break;
            case NyuanBuy:
                long maxPrice = 0L;
                if (goodsRuleDetailVO.getMaxPriceSku() != null) {
                    maxPrice = goodsRuleDetailVO.getMaxPriceSku().getMarketPrice();
                }
                if (goodsRuleDetailVO.getMaxPricePackage() != null) {
                    maxPrice = Math.max(maxPrice, goodsRuleDetailVO.getMaxPricePackage().getMarketPrice());
                }
                cost = (maxPrice - promotionRuleVO.getPromotionValue()) * applyCount;
                break;
            case GIFT:
                break;
            default:
                log.warn("未知券类型, promotionType={}", promotionRuleVO.getPromotionType());
                break;
        }
        return BigDecimal.valueOf(cost).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP);
    }


    /**
     * 解析商品结构
     *
     * @param po CouponConfigPO
     * @return GoodsRuleVO
     */
    public GoodsRuleVO convertGoodsRuleVO(CouponConfigPO po) {
        GoodsRuleVO goodsRuleVO = new GoodsRuleVO();

        // 兼容老数据该字段为空
        if (StringUtils.isNotEmpty(po.getGoodsDepartments())) {
            goodsRuleVO.setGoodsDepartments(Arrays.stream(po.getGoodsDepartments().split(",")).map(Integer::parseInt).collect(Collectors.toList()));
        }
        goodsRuleVO.setScopeType(po.getScopeType());
        goodsRuleVO.setAutoUpdateGoods(po.getAutoUpdateGoods());
        fillIncludeGoods(po.getGoodsInclude(), goodsRuleVO);
        goodsRuleVO.setGoodsExclude(convertGoodsMap(po.getGoodsExclude()));
        goodsRuleVO.setCategoryIds(StringUtil.convertToLongSet(po.getCategoryIds()));
        goodsRuleVO.setLabourHourSsu(convertSsuMap(po.getGoodsInclude(), GoodsLevelEnum.LabourHourSsu));
        goodsRuleVO.setPartsSsu(convertSsuMap(po.getGoodsInclude(), GoodsLevelEnum.PartsSsu));

        return goodsRuleVO;
    }


    /**
     * 封装商品信息
     *
     * @param goodsStr goodsStr
     * @return 商品信息
     */
    private static Map<String, List<Long>> convertGoodsMap(String goodsStr) {
        if (StringUtils.isEmpty(goodsStr)) {
            return null;
        }
        GoodItemPO goodsItemPo = GsonUtil.fromJson(goodsStr, GoodItemPO.class);
        if (Objects.isNull(goodsItemPo)) {
            return null;
        }
        Map<String, List<Long>> goodsListMap = new HashMap<>();
        goodsListMap.put(GoodsLevelEnum.Sku.getValue(), goodsItemPo.getSku());
        goodsListMap.put(GoodsLevelEnum.Package.getValue(), goodsItemPo.getPackages());
        goodsListMap.put(GoodsLevelEnum.Ssu.getValue(), goodsItemPo.getSsu());
        goodsListMap.put(GoodsLevelEnum.Suit.getValue(), goodsItemPo.getSuit());

        return goodsListMap;
    }

    /**
     * 封装商品信息
     *
     * @param goodsStr goodsStr
     * @return 商品信息
     */
    private static void fillIncludeGoods(String goodsStr, GoodsRuleVO goodsRuleVO) {
        if (StringUtils.isEmpty(goodsStr)) {
            return;
        }
        GoodItemPO goodsItemPo = GsonUtil.fromJson(goodsStr, GoodItemPO.class);
        if (Objects.isNull(goodsItemPo)) {
            return;
        }
        Map<String, List<Long>> goodsListMap = new HashMap<>();
        goodsListMap.put(GoodsLevelEnum.Sku.getValue(), goodsItemPo.getSku());
        goodsListMap.put(GoodsLevelEnum.Package.getValue(), goodsItemPo.getPackages());
        goodsListMap.put(GoodsLevelEnum.Ssu.getValue(), goodsItemPo.getSsu());
        goodsListMap.put(GoodsLevelEnum.Suit.getValue(), goodsItemPo.getSuit());
        goodsRuleVO.setGoodsInclude(goodsListMap);
        goodsRuleVO.setGoodsMsg(goodsItemPo.getGoodsMsg());
    }

    /**
     * 封装服务券工时、配件信息
     *
     * @param goodsStr goodsStr
     * @return ssu信息
     */
    private static Map<Long, Integer> convertSsuMap(String goodsStr, GoodsLevelEnum goodsLevelEnum) {
        if (StringUtils.isEmpty(goodsStr)) {
            return null;
        }
        GoodItemPO goodsItemPo = GsonUtil.fromJson(goodsStr, GoodItemPO.class);
        if (Objects.isNull(goodsItemPo)) {
            return null;
        }

        if (GoodsLevelEnum.LabourHourSsu.equals(goodsLevelEnum)) {
            // 返回工时ssu
            return goodsItemPo.getLabourHourSsu();
        }

        // 返回配件ssu
        return goodsItemPo.getPartsSsu();
    }

    /**
     * 封装优惠逻辑
     *
     * @param po 优惠类型
     * @return PromotionRuleVO
     */
    public PromotionRuleVO convertPromotionRuleVO(CouponConfigPO po) {
        PromotionRuleVO promotionRuleVO = new PromotionRuleVO();
        promotionRuleVO.setPromotionType(po.getPromotionType());
        promotionRuleVO.setPromotionValue(po.getPromotionValue());
        promotionRuleVO.setBottomPrice(po.getBottomPrice());
        promotionRuleVO.setBottomCount(po.getBottomCount());
        promotionRuleVO.setBottomType(po.getBottomType());
        promotionRuleVO.setMaxReduce(po.getMaxReduce());

        if (I18nUtil.isI18n()) {
            promotionRuleVO.fillGlobalField();
        }
        return promotionRuleVO;
    }


    /**
     * 格式化优惠券使用有效期
     *
     * @return UseTermVO
     */
    public UseTermVO convertUseTermVO(CouponConfigPO couponConfigPO) {
        UseTermVO useTermVO = new UseTermVO();
        useTermVO.setUseTimeType(couponConfigPO.getUseTimeType());
        switch (UseTimeTypeEnum.getByValue(couponConfigPO.getUseTimeType())) {
            case ABSOLUTE:
                useTermVO.setStartUseTime(TimeUtil.convertLongToDate(couponConfigPO.getStartUseTime()));
                useTermVO.setStartUseTimeStamp(useTermVO.getStartUseTime().getTime() / 1000);
                useTermVO.setEndUseTime(TimeUtil.convertLongToDate(couponConfigPO.getEndUseTime()));
                useTermVO.setEndUseTimeStamp(useTermVO.getEndUseTime().getTime() / 1000);
                break;
            case RELATIVE:
                useTermVO.setUseDuration(couponConfigPO.getUseDuration());
                break;
            default:
                log.warn("非法的券配置有效期类型,useTimeType={}", couponConfigPO.getUseTimeType());
                break;
        }

        // 兼容闪送运费券上线前相对使用时间只能配置天的老逻辑
        long onlineTime = 1656493200; // 上线时间点
        if (couponConfigPO.getCreateTime() < onlineTime && Objects.equals(CouponTypeEnum.GOODS.getValue(), couponConfigPO.getCouponType()) && couponConfigPO.getUseDuration() % 24 == CommonConstant.ZERO_INT) {
            useTermVO.setTimeGranularity(UseTimeGranularityEnum.DAY.getValue());
        } else {
            useTermVO.setTimeGranularity(couponConfigPO.getTimeGranularity());
        }
        return useTermVO;
    }


    /**
     * 优惠券进行状态转换
     *
     * @param startFetchTime 优惠券开始领取时间
     * @param endFetchTime   优惠券结束领取时间
     * @param status         优惠券状态
     * @return int
     */
    public static int transferToTimeStatus(long startFetchTime, long endFetchTime, int status) {
        int timeStatus = CouponTimeStatusEnum.IN_PROGRESS.getCode();
        long nowTime = TimeUtil.getNowUnixSecond();
        if (status == CouponConfigStatusEnum.STOP_FETCHING.getCode()) {
            timeStatus = CouponTimeStatusEnum.STOP_FETCHING.getCode();
        } else if (startFetchTime > nowTime) {
            timeStatus = CouponTimeStatusEnum.NOT_START.getCode();
        } else if (endFetchTime != CommonConstant.ZERO_LONG && endFetchTime < nowTime) {
            timeStatus = CouponTimeStatusEnum.ENDED.getCode();
        } else if (startFetchTime == CommonConstant.ZERO_LONG && endFetchTime == CommonConstant.ZERO_LONG) {
            timeStatus = CouponTimeStatusEnum.IN_PROGRESS.getCode();
        }
        return timeStatus;
    }


    /**
     * 封装CouponConfigInfoDTO方法
     *
     * @param couponConfigPO 券配置PO
     * @param nowCount       发放数量
     * @return
     */
    public CouponConfigInfoDTO convertToCouponConfigInfoDTO(CouponConfigPO couponConfigPO, Long nowCount) {
        CouponConfigInfoDTO couponConfigInfoDTO = new CouponConfigInfoDTO();

        couponConfigInfoDTO.setConfigId(couponConfigPO.getId());
        couponConfigInfoDTO.setStatus(couponConfigPO.getStatus());
        couponConfigInfoDTO.setPromotionType(couponConfigPO.getPromotionType());
        couponConfigInfoDTO.setPromotionValue(couponConfigPO.getPromotionValue());
        couponConfigInfoDTO.setCouponName(couponConfigPO.getName());
        couponConfigInfoDTO.setSendScene(couponConfigPO.getSendScene());
        if (StringUtils.isNotBlank(couponConfigPO.getUseChannel())) {
            couponConfigInfoDTO.setUseChannelList(StringUtil.convertToLongList(couponConfigPO.getUseChannel()));
        }
        if (StringUtils.isNotBlank(couponConfigPO.getExtProp())) {
            ExtPropPO extPropPO = GsonUtil.fromJson(couponConfigPO.getExtProp(), ExtPropPO.class);
            if (extPropPO != null) {
                couponConfigInfoDTO.setPostFree(extPropPO.getPostFree());
            }
        }
        couponConfigInfoDTO.setCouponDesc(couponConfigPO.getCouponDesc());
        couponConfigInfoDTO.setStartFetchTime(couponConfigPO.getStartFetchTime());
        couponConfigInfoDTO.setEndFetchTime(couponConfigPO.getEndFetchTime());
        couponConfigInfoDTO.setUseTimeType(couponConfigPO.getUseTimeType());
        couponConfigInfoDTO.setStartUseTime(couponConfigPO.getStartUseTime());
        couponConfigInfoDTO.setEndUseTime(couponConfigPO.getEndUseTime());
        couponConfigInfoDTO.setSendCount(couponConfigPO.getApplyCount());
        couponConfigInfoDTO.setFetchLimit(couponConfigPO.getFetchLimit());
        couponConfigInfoDTO.setFetchLimitType(couponConfigPO.getFetchLimitType());
        couponConfigInfoDTO.setUseDuration(couponConfigPO.getUseDuration());
        couponConfigInfoDTO.setNowCount(couponConfigPO.getApplyCount() - nowCount.intValue());
        couponConfigInfoDTO.setNewType(couponConfigPO.getSource());

        couponConfigInfoDTO.setCreator(couponConfigPO.getCreator());
        couponConfigInfoDTO.setCreateTime(couponConfigPO.getCreateTime().intValue());
        couponConfigInfoDTO.setUpdateTime(Math.toIntExact((couponConfigPO.getUpdateTime().getTime() / 1000)));
        couponConfigInfoDTO.setCostShareDTOS(converCostShareDTOS(couponConfigPO));
        couponConfigInfoDTO.setCouponType(couponConfigPO.getCouponType());
        couponConfigInfoDTO.setBottomPrice(couponConfigPO.getBottomPrice());
        couponConfigInfoDTO.setPromotionType(couponConfigPO.getPromotionType());
        couponConfigInfoDTO.setBottomType(couponConfigPO.getBottomType());
        couponConfigInfoDTO.setBottomCount(couponConfigPO.getBottomCount());
        couponConfigInfoDTO.setMaxReduce(couponConfigPO.getMaxReduce());
        if (I18nUtil.isI18n()) {
            couponConfigInfoDTO.setAreaId(couponConfigPO.getAreaId());
            //国际金额格式化
            Area area = Area.of(couponConfigInfoDTO.getAreaId());
            couponConfigInfoDTO.fillGlobalField(area);
            if(Objects.nonNull(couponConfigPO.getPromotionType()) && PromotionTypeEnum.ConditionDiscount.getValue() == couponConfigPO.getPromotionType()){
                couponConfigInfoDTO.setDiscountDesc(CouponDiscountUtil.formatDiscountCouponDesc(RpcContextUtil.headerLanguage(I18nUtil.getGlobalAreaId()), couponConfigPO.getPromotionValue()));
            }else{
                couponConfigInfoDTO.setPromotionValueStr(area.moneyFormat(couponConfigInfoDTO.getPromotionValue()));
            }
            //国际描述格式化
            couponConfigInfoDTO.setCouponRuleDesc(getGlobalCouponCashRuleDesc(
                    couponConfigPO.getAreaId(),
                    couponConfigPO.getPromotionValue(),
                    couponConfigPO.getBottomType(),
                    couponConfigPO.getBottomCount(),
                    couponConfigPO.getBottomPrice(),
                    couponConfigPO.getCouponType()));
            couponConfigInfoDTO.setBenefit(area.moneyFormatSymbol(couponConfigPO.getPromotionValue()));

            // 可用门店
            Map<Integer, UseChannelVO> useChannel = GsonUtil.fromJson(couponConfigPO.getUseStore(),new TypeToken<Map<Integer, UseChannelVO>>(){}.getType());
            useChannel = Objects.isNull(useChannel) ? Collections.emptyMap() : useChannel;
            boolean isAllStore = false;
            List<String> storeIdList = new ArrayList<>();
            for (Map.Entry<Integer, UseChannelVO> entry : useChannel.entrySet()) {
                if (entry == null) continue;
                UseChannelVO e = entry.getValue();
                isAllStore = e.isAll();
                storeIdList = CollectionUtils.isNotEmpty(e.getLimitIds()) ? e.getLimitIds() : Collections.emptyList();
                break;
            }
            couponConfigInfoDTO.setIsAllStore(isAllStore);
            couponConfigInfoDTO.setStoreCodes(storeIdList);
        }

        return couponConfigInfoDTO;
    }


    /**
     * 封装CouponConfigInfoDTO方法
     *
     * @param configInfoCachePo 券配置缓存PO
     * @return
     */
    public CouponConfigInfoDTO convertToCouponConfigInfoDTO(ConfigInfoCachePo configInfoCachePo) {
        CouponConfigInfoDTO couponConfigInfoDTO = new CouponConfigInfoDTO();
        couponConfigInfoDTO.setConfigId(configInfoCachePo.getId().longValue());
        couponConfigInfoDTO.setStatus(configInfoCachePo.getStatus());
        couponConfigInfoDTO.setCouponName(configInfoCachePo.getName());
        couponConfigInfoDTO.setUseTimeType(configInfoCachePo.getUseTimeType());
        couponConfigInfoDTO.setStartUseTime(configInfoCachePo.getStartUseTime());
        couponConfigInfoDTO.setEndUseTime(configInfoCachePo.getEndUseTime());
        couponConfigInfoDTO.setUseDuration(configInfoCachePo.getUseDuration());

        return couponConfigInfoDTO;
    }

    /**
     * 封装分摊信息
     *
     * @param couponConfigPO
     * @return
     */
    private List<CostShareDTO> converCostShareDTOS(CouponConfigPO couponConfigPO) {
        List<CostShareDTO> costShareDTOS = Lists.newArrayList();
        Map<Integer, Integer> costShare = GsonUtil.fromJson(couponConfigPO.getCostShare(), new TypeToken<Map<Integer, Integer>>() {
        }.getType());

        if (MapUtils.isEmpty(costShare)) {
            return costShareDTOS;
        }

        for (Map.Entry<Integer, Integer> item : costShare.entrySet()) {
            CostShareDTO costShareDTO = new CostShareDTO();
            costShareDTO.setDepartment(item.getKey());
            costShareDTO.setShare(item.getValue());
            costShareDTOS.add(costShareDTO);
        }
        return costShareDTOS;
    }

    /**
     * 封装 List<CouponConfigInfoDTO> 方法
     *
     * @param couponConfigPOList 券配置PO列表
     * @param nowCountMap        发放数量集合
     * @return
     */

    public List<CouponConfigInfoDTO> convertToCouponConfigInfoDTOs(List<CouponConfigPO> couponConfigPOList, Map<Long, Long> nowCountMap) {
        List<CouponConfigInfoDTO> couponConfigInfoDTOList = Lists.newArrayList();

        for (CouponConfigPO couponConfigPO : couponConfigPOList) {
            if (couponConfigPO == null) {
                continue;
            }
            couponConfigInfoDTOList.add(convertToCouponConfigInfoDTO(couponConfigPO, Optional.ofNullable(nowCountMap.get(couponConfigPO.getId())).orElse(0L)));
        }

        return couponConfigInfoDTOList;
    }


    /**
     * 转换 CouponConfigDescVO
     *
     * @param configPOList
     * @return
     */
    public List<CouponConfigDescVO> convertCouponConfigDescVOS(List<CouponConfigPO> configPOList, Map<String, String> sceneMap) {
        if (CollectionUtils.isEmpty(configPOList)) {
            return Lists.newArrayList();
        }
        return configPOList.stream().map(x -> {
            CouponConfigDescVO couponConfigDescVO = new CouponConfigDescVO();
            couponConfigDescVO.setId(x.getId());
            couponConfigDescVO.setNameDesc(x.getId() + "-" + x.getName());
            List<String> sendScene = Arrays.asList(StringUtils.split(x.getSendScene(), ","));
            couponConfigDescVO.setSendSceneMap(sendScene.stream().collect(Collectors.toMap(code -> code, code -> sceneMap.get(code))));
            return couponConfigDescVO;
        }).collect(Collectors.toList());
    }


    public List<CouponConfigListVO> convertToCouponConfigListVO(List<CouponConfigPO> configPOList, Map<String, String> sceneMap) {
        Long nowTime = TimeUtil.getNowUnixSecond();
        List<CouponConfigListVO> couponConfigListVOS = Lists.newArrayList();
        if (CollectionUtils.isEmpty(configPOList)) {
            return couponConfigListVOS;
        }
        for (CouponConfigPO couponConfigPO : configPOList) {
            CouponConfigListVO vo = convertCouponConfigListVO(couponConfigPO);
            vo.setSendSceneName(sceneMap.get(couponConfigPO.getSendScene()));
            vo.setValidRemark(getCouponValidRemark(couponConfigPO.getStartFetchTime(), couponConfigPO.getEndFetchTime(), couponConfigPO.getStartUseTime(), couponConfigPO.getEndUseTime(), nowTime));
            couponConfigListVOS.add(vo);
        }
        return couponConfigListVOS;
    }

    /**
     * 排序字段映射(createTime -> create_time)
     *
     * @param str 参数字段
     * @return 数据库对应参数
     */
    private String convertOrderBy(String str) {
        if (StringUtils.isEmpty(str)) {
            return "id";
        }

        StringBuilder orderByStr = new StringBuilder();
        char[] ch = str.toCharArray();
        for (int i = 0; i < ch.length; i++) {
            if ((int) ch[i] >= 65 && (int) ch[i] <= 90) {
                orderByStr.append("_");
                ch[i] += 32;
                orderByStr.append(ch[i]);
                continue;
            }
            orderByStr.append(ch[i]);
        }
        return orderByStr.toString();
    }


    /**
     * 商品可用券数据下载实体转换
     *
     * @param configPOList 券实体
     * @param sceneMap     投放场景
     * @return List
     */
    public List<GoodsCouponPO> convertToGoodsCouponPO(List<CouponConfigPO> configPOList, Map<String, String> sceneMap) {

        List<GoodsCouponPO> goodsCouponPOS = Lists.newArrayList();
        if (CollectionUtils.isEmpty(configPOList)) {
            return goodsCouponPOS;
        }

        Long nowTime = TimeUtil.getNowUnixSecond();
        GoodsCouponPO po;
        for (CouponConfigPO configPO : configPOList) {

            po = new GoodsCouponPO();
            po.setId(configPO.getId());
            po.setName(configPO.getName());
            po.setSendSceneName(sceneMap.get(configPO.getSendScene()));
            po.setCouponType(CouponTypeEnum.getNameByValue(configPO.getCouponType()));
            po.setPromotionType(PromotionTypeEnum.getNameByValue(configPO.getPromotionType()));
            po.setPromotionBottom(getPromotionBottom(configPO));
            po.setUseChannel(convertUseChannel(configPO.getUseChannel()));
            po.setValidRemark(getCouponValidRemark(configPO.getStartFetchTime(), configPO.getEndFetchTime(), configPO.getStartUseTime(), configPO.getEndUseTime(), nowTime));
            po.setCreatorDesc(configPO.getCreator() + "(" + TimeUtil.formatSecond(configPO.getCreateTime()) + ")");
            goodsCouponPOS.add(po);
        }
        return goodsCouponPOS;
    }


    /**
     * 获取券是否可用信息
     *
     * @param startFetchTime 开始领取时间
     * @param endFetchTime   结束领取时间
     * @param startUseTime   开始使用时间
     * @param endUseTime     结束使用时间
     * @param nowTime        当前时间
     * @return 是否可用
     */
    private String getCouponValidRemark(Long startFetchTime, Long endFetchTime, Long startUseTime, Long endUseTime, Long nowTime) {
        if (startFetchTime > nowTime) {
            return "否,优惠券未到领取时间";
        }
        if (startUseTime > nowTime) {
            return "否,优惠券未到使用时间";
        }
        if (endUseTime < nowTime) {
            return "否,优惠券已过使用时间";
        }
        if (endFetchTime < nowTime) {
            return "是,优惠券已过领取时间";
        }
        return "是";
    }


    private static String getPromotionBottom(CouponConfigPO po) {

        PromotionTypeEnum promotionType = PromotionTypeEnum.getByValue(po.getPromotionType());
        if (Objects.isNull(promotionType)) {
            return null;
        }

        StringBuilder result;
        switch (promotionType) {
            case DirectReduce:
                result = new StringBuilder("￥");
                result.append(new BigDecimal(po.getPromotionValue()).divide(new BigDecimal(100)).setScale(2, RoundingMode.UP).stripTrailingZeros().toPlainString());
                result.append("/无门槛");
                break;

            case ConditionReduce:
                result = new StringBuilder("￥");
                if (Objects.equals(BottomTypeEnum.OverYuan.getValue(), po.getBottomType())) {
                    result.append(new BigDecimal(po.getPromotionValue()).divide(new BigDecimal(100)).setScale(2, RoundingMode.UP).stripTrailingZeros().toPlainString());
                    result.append("/满￥");
                    result.append(new BigDecimal(po.getBottomPrice()).divide(new BigDecimal(100)).setScale(2, RoundingMode.UP).stripTrailingZeros().toPlainString());
                }

                if (Objects.equals(BottomTypeEnum.OverCount.getValue(), po.getBottomType())) {
                    result.append(new BigDecimal(po.getPromotionValue()).divide(new BigDecimal(100)).setScale(2, RoundingMode.UP).stripTrailingZeros().toPlainString());
                    result.append("/满");
                    result.append(po.getBottomCount().toString());
                    result.append("件");
                }
                break;

            case ConditionDiscount:
                result = new StringBuilder("￥");
                if (Objects.equals(BottomTypeEnum.OverYuan.getValue(), po.getBottomType())) {
                    result.append(po.getPromotionValue().toString());
                    result.append("折/满￥");
                    result.append(new BigDecimal(po.getBottomPrice()).divide(new BigDecimal(100)).setScale(2, RoundingMode.UP).stripTrailingZeros().toPlainString());
                }

                if (Objects.equals(BottomTypeEnum.OverCount.getValue(), po.getBottomType())) {
                    result.append(po.getPromotionValue().toString());
                    result.append("折/满");
                    result.append(po.getBottomCount().toString());
                    result.append("件");
                }
                break;

            case NyuanBuy:
                result = new StringBuilder("直减至￥0.01");
                break;
            case GIFT:
                result = new StringBuilder("直减至￥0");
                break;

            default:
                log.warn("非法的优惠券折扣类型, promotionType={}", po.getPromotionType());
                result = new StringBuilder();
                break;
        }

        return result.toString();
    }

    /**
     * 将CouponConfigPO对象转换为WatermelonCouponTemplateReq对象
     *
     * @param po CouponConfigPO对象，包含优惠券配置信息
     * @return 转换后的WatermelonCouponTemplateReq对象
     */
    public static WatermelonCouponTemplateReq WatermelonCouponConvert(CouponConfigPO po) {
        WatermelonCouponTemplateReq req = new WatermelonCouponTemplateReq();
        if (po == null) {
            return req;
        }
        // 券模板id
        req.setTemplateCode(String.valueOf(po.getId()));
        // 券名称
        req.setName(po.getName());
        // 券描述
        req.setDescription(po.getCouponDesc());

        // 券使用时间类型
        UseTimeTypeEnum useTimeType = UseTimeTypeEnum.getByValue(po.getUseTimeType());
        req.setValidityType(useTimeType.getWatermelonDesc());
        if (useTimeType.equals(UseTimeTypeEnum.ABSOLUTE)) {
            // 固定有效期
            req.setValidBeginTime(TimeUtil.formatByPattern(po.getStartUseTime() * 1000L, "yyyy-MM-dd"));
            req.setValidEndTime(TimeUtil.formatByPattern(po.getEndUseTime() * 1000L, "yyyy-MM-dd"));
        } else if (useTimeType.equals(UseTimeTypeEnum.RELATIVE)) {
            // 相对有效期
            req.setValidUnit(UseTimeGranularityEnum.HOUR.getName());
            req.setValidPeriod(po.getUseDuration());
        }

        // 领取后多久可用
        req.setWaitDaysAfterReceive(0);
        // 自动退回
        req.setAutoRollback(false);
        // 用户领取限制
        req.setQuantityLimitPerUser(1);

        // 优惠券类型
        PromotionTypeEnum promotionType = PromotionTypeEnum.getByValue(po.getPromotionType());
        req.setType(promotionType.getWatermelonDesc());
        if (promotionType.getWatermelonDesc().equals("CashCoupon")) {
            // CashCoupon，单位元
            req.setAmount(BigDecimal.valueOf(po.getPromotionValue()).divide(BigDecimal.valueOf(100L), 2, RoundingMode.HALF_UP));
            req.setMaxAmount(BigDecimal.valueOf(po.getPromotionValue()).divide(BigDecimal.valueOf(100L), 2, RoundingMode.HALF_UP));
        } else if (promotionType.getWatermelonDesc().equals("PercentOff")) {
            // PercentOff，单位元
            req.setDiscount(BigDecimal.valueOf(po.getPromotionValue()).divide(BigDecimal.valueOf(1000L), 2, RoundingMode.HALF_UP));
            req.setMaxAmount(BigDecimal.valueOf(po.getMaxReduce()).divide(BigDecimal.valueOf(100L), 2, RoundingMode.HALF_UP));
        }

        // 券使用门槛，单位元
        req.setFloorAmount(BigDecimal.valueOf(po.getBottomPrice()).divide(BigDecimal.valueOf(100L), 2, RoundingMode.HALF_UP));
        // 可用规则
        req.setAvailableRuleType(AvailableRuleTypeEnum.ALL.getDesc());
        // 可用门店
        req.setAvailableShopCodes(new ArrayList<>());
        // 券售卖金额
        req.setCouponSellAmount(0);

        return req;
    }

    /**
     * 格式化现金券规则名称
     *
     * @param reduceMoney 优惠金额
     * @return CustomDetailDto 优惠券规则信息
     */
    public String getGlobalCouponCashRuleDesc(String areaId, Long reduceMoney, Integer bottomType,
                                                        Integer bottomCount, Long bottomMoney, Integer couponType) {
        if (Objects.isNull(reduceMoney) || reduceMoney < CommonConstant.ZERO_LONG) {
            log.info("coupopn.CouponCommonServiceImpl.getGlobalCouponCashRuleName(), 错误的减*元,reduceMoney={}", reduceMoney);
            return StringUtils.EMPTY;
        }

        Area area = Area.of(areaId);
        String reduceMoneyFormat = area.moneyFormatSymbol(reduceMoney);
        String quotaMoneyFormat = area.moneyFormatSymbol(bottomMoney);

        String couponRuleDesc = "";

        //满件
        if (Objects.equals(BottomTypeEnum.OverCount.getValue(), bottomType)) {
            if (bottomCount == CommonConstant.ONE_LONG) {
                if(CouponTypeEnum.GOODS.getValue().equals(couponType)) {
                    couponRuleDesc = MessageFormat.format(TranslationEnum.COUPON_DISCOUNT_TEMP_UNIT.getTranslateContent(), bottomCount, reduceMoneyFormat);
                }
            } else {
                couponRuleDesc = MessageFormat.format(TranslationEnum.COUPON_DISCOUNT_TEMP_UNIT.getTranslateContent(), bottomCount, reduceMoneyFormat);
            }
        }

        //满元
        if (Objects.equals(BottomTypeEnum.OverYuan.getValue(), bottomType)) {
            couponRuleDesc = MessageFormat.format(TranslationEnum.COUPON_DISCOUNT_TEMP_PRICE.getTranslateContent(), quotaMoneyFormat, reduceMoneyFormat);
        }
        return couponRuleDesc;
    }

}
