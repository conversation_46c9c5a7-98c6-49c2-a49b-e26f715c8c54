package com.xiaomi.nr.coupon.admin.infrastructure.notify.robotmessage;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sun.istack.NotNull;
import com.xiaomi.newretail.common.tools.base.exception.BaseException;
import com.xiaomi.nr.coupon.admin.enums.Env;
import com.xiaomi.nr.coupon.admin.infrastructure.notify.robotmessage.po.Card;
import com.xiaomi.nr.coupon.admin.util.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class RobotSendMessageUtil{

    @Autowired
    RobotSendMessageUtil robotMessageUtil;

    /**
     * 机器人id
     * 默认 促销小助手
     */
    private String appId = "cli_a26d06a9e078d063";

    /**
     * 机器人秘钥
     * 默认 促销小助手
     */
    private String appKey = "A44WbG14mwCAyubyAdiODf2PKAZDZoAu";

    /**
     * 当前所属环境
     */
    private Env env;


    /**
     * 修改机器人默认配置发送消息
     *
     * @param appId     机器人配置id
     * @param appKey 机器人配置秘钥
     * @return 修改后的机器人工具
     */
    public RobotSendMessageUtil initRobot(String appId, String appKey) {
        this.appId = appId;
        this.appKey = appKey;

        return robotMessageUtil;
    }

    /**
     * 修改发送消息环境
     *
     * @param env 环境枚举
     * @return 修改后的机器人工具
     */
    public RobotSendMessageUtil alterEnv(Env env) {
        this.env = env;

        return robotMessageUtil;
    }



    /**
     * 发送私聊消息
     * （需要机器人有发送消息权限）
     * （需要机器人有获取用户信息权限）
     *
     * @param msg  私聊消息
     * @param user 接收消息的一方
     * @return 消息反馈 #{"code":0,"data":{"message_id":"om_606b1e34d85160fa910709387d29c59c"},"msg":"ok"}
     */
    @NotNull
    public String sendPrivateChat(String msg, @NotNull String user) {
        if (StringUtils.isEmpty(msg) || StringUtils.isEmpty(user)) {
            throw new BaseException(-1, "sendPrivateChat error 飞书私聊消息发送失败，请检查方法参数");
        }
        if (Env.LOCAL.equals(env)) {
            log.info("本地环境消息不发送 msg:" + msg);
            return "";
        }
        String token = getTokenV2();
        if (StringUtils.isEmpty(token)) {
            log.error("获取飞书token失败");
        }
        try {
            Map<String, Object> jsonMap = new HashMap<>();
            Map<String, String> contentMap = new HashMap<>();
            jsonMap.put("user_id", user);
            jsonMap.put("msg_type", "text");
            contentMap.put("text", msg);
            jsonMap.put("content", contentMap);

            return requestServerV2(token, null, jsonMap);
        } catch (Exception e) {
            log.error("发送飞书消息失败", e);
        }
        return "";
    }


    /**
     * 发送私聊消息
     * （需要机器人有发送消息权限）
     * （需要机器人有获取用户信息权限）
     *
     * @param card  私聊卡片
     * @param user 接收消息的一方
     * @return 消息反馈 #{"code":0,"data":{"message_id":"om_606b1e34d85160fa910709387d29c59c"},"msg":"ok"}
     */
    @NotNull
    public String sendPrivateCard(Card card, @NotNull String user) {

        String token = getTokenV2();
        if (StringUtils.isEmpty(token)) {
            log.error("获取飞书token失败");
        }
        try {
            Map<String, Object> jsonMap = new HashMap<>();
            jsonMap.put("user_id", user);
            jsonMap.put("msg_type", "interactive");
            jsonMap.put("card", card);

            return requestServerV2(token, null, jsonMap);
        } catch (Exception e) {
            log.error("发送飞书消息失败", e);
        }
        return "";
    }



    private String requestServerV2(String token, String url, Map<String, Object> jsonObject) {
        if (StringUtils.isEmpty(token) || ObjectUtils.isEmpty(jsonObject)) {
            log.error("RobotMessageUtil error 参数错误或参数不完整");
            throw new BaseException(-1, "");
        }
        if (StringUtils.isEmpty(url)) {
            url = "https://open.f.mioffice.cn/open-apis/message/v4/send/";
        }

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Authorization", "Bearer " + token);

        try {
            return HttpClientUtil.doPostV2(url, jsonObject, headerMap, 3000, 3000, 5000);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }


    /**
     * 获取TOKEN
     *
     * @return token
     */
    private String getTokenV2() {

        Map<String, Object> map =new HashMap<>();
        map.put("app_secret", appKey);
        map.put("app_id", appId);
        String s = null;
        try {
            s = HttpClientUtil.doPost("https://open.f.mioffice.cn/open-apis/auth/v3/tenant_access_token/internal/", map, null, 3000, 3000, 5000);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (StringUtils.isEmpty(s)) {

            return null;
        }

        JsonObject jsonObject = new JsonParser().parse(s).getAsJsonObject();

        String token = jsonObject.get("tenant_access_token").getAsString();
        if (StringUtils.isEmpty(token)){
            throw new BaseException(-1,"RobotMessageUtil error 获取token失败");
        }
        return token;
    }
}
