package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.sketch.po;

import lombok.Data;

import java.util.Date;

@Data
public class CouponSketchListParam {
    /**
     * 优惠券类型  1: 商品券 2: 运费券
     */
    private Integer couponType;
    /**
     * 草稿id
     */
    private long sketchId;

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     *领取时间起
     */
    private Long startFetchTime;

    /**
     *领取时间止
     */
    private Long endFetchTime;

    /**
     * 提交人
     */
    private String creator;

    /**
     *提交时间起
     */
    private Date startCreateTime;

    /**
     *提交时间止
     */
    private Date endCreateTime;

    /**
     * offset
     */
    private int offset;

    /**
     * limit
     */
    private int limit;

    /**
     * 排序字段
     */
    private String orderBy = "id";

    /**
     * 排序顺序  desc倒序   asc顺序
     */
    private String orderDirection = "desc";

}
