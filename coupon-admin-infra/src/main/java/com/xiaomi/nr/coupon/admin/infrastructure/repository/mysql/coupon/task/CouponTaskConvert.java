package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.task;


import com.xiaomi.nr.coupon.admin.enums.task.CouponTaskTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.task.TaskStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.task.po.FillCouponTaskPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.task.po.Param;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;

public class CouponTaskConvert {

    public static FillCouponTaskPO convertFillCouponTaskPO(CouponConfigPO couponConfigPO){
        FillCouponTaskPO fillCouponTaskPO =new FillCouponTaskPO();
        fillCouponTaskPO.setTaskName(couponConfigPO.getName() + "_券码");
        fillCouponTaskPO.setStatus(TaskStatusEnum.PRE_AWAIT.code);
        fillCouponTaskPO.setType(CouponTaskTypeEnum.COUPON_CODE.code);
        Param param = new Param();
        param.setCount(couponConfigPO.getApplyCount());
        param.setEndFetchTime(couponConfigPO.getEndFetchTime());
        param.setSendScene(couponConfigPO.getSendScene());
        fillCouponTaskPO.setParams(GsonUtil.toJson(param));
        fillCouponTaskPO.setSource(String.valueOf(couponConfigPO.getSource()));
        fillCouponTaskPO.setCreateTime(System.currentTimeMillis() / 1000);
        fillCouponTaskPO.setConfigId(couponConfigPO.getId());
        fillCouponTaskPO.setDepartmentId(String.valueOf(couponConfigPO.getDepartmentId()));
        fillCouponTaskPO.setCreator(couponConfigPO.getCreator());
        fillCouponTaskPO.setVersion(0);
        fillCouponTaskPO.setBizPlatform(couponConfigPO.getBizPlatform());
        fillCouponTaskPO.setAreaId(couponConfigPO.getAreaId());
        return fillCouponTaskPO;

    }

}
