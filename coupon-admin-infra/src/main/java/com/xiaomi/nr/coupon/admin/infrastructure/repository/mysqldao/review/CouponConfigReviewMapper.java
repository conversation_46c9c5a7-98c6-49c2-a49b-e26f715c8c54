package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.po.CouponConfigReviewPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.entity.SearchReviewListParam;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface CouponConfigReviewMapper {

    /**
     * 插入couponReview
     *
     * @return
     */
    @Options(useGeneratedKeys = true, keyProperty = "id")
    @Insert("insert into nr_coupon_review (config_id,coupon_name,promotion_type,promotion_value,bottom_type,bottom_price,bottom_count," +
            "apply_count,start_use_time,end_use_time,config_compress,status,department_id,apply_attachment,creator,coupon_type,biz_platform,area_id,workflow_id)" +
            " values " +
            "(#{configId,jdbcType=BIGINT},#{couponName,jdbcType=VARCHAR},#{promotionType,jdbcType=TINYINT},#{promotionValue,jdbcType=BIGINT},#{bottomType,jdbcType=TINYINT}," +
            "#{bottomPrice,jdbcType=BIGINT},#{bottomCount,jdbcType=BIGINT},#{applyCount,jdbcType=BIGINT},#{startUseTime,jdbcType=TIMESTAMP},#{endUseTime,jdbcType=TIMESTAMP}," +
            "#{configCompress,jdbcType=VARCHAR},#{status,jdbcType=TINYINT}," +
            "#{departmentId,jdbcType=BIGINT},#{applyAttachment,jdbcType=VARCHAR},#{creator,jdbcType=VARCHAR},#{couponType,jdbcType=TINYINT},#{bizPlatform,jdbcType=TINYINT}," +
            "#{areaId,jdbcType=VARCHAR},#{workflowId,jdbcType=BIGINT}" +
            ")")
    Long insert(CouponConfigReviewPO po);

    @Select("<script>select count(1) from nr_coupon_review where 1=1 " +
            "<if test='configId!= 0'>and config_id=#{configId}</if>" +
            "<if test='statusList!=null '>and status in <foreach item='status' index='index' collection='statusList' open='(' close=')' separator=','>#{status}" +
            "</foreach></if></script>")
    Long count(@Param("configId") long configId,@Param("statusList")  List<Integer> statusList);


    @Update("<script>" +
            " update nr_coupon_review " +
            "<set>" +
            "<if test='configId!= 0'> config_id=#{configId},</if>" +
            "<if test='status!=0'> status=#{status},</if>" +
            "<if test='bpmReason!=null'> bpm_reason=#{bpmReason},</if>" +
            "<if test='approvedId!=null'> approved_id=#{approvedId},</if>" +
            "<if test='approvedTime!=null'> approved_time=#{approvedTime},</if>" +
            "</set>" +
            " where id =#{id}" +
            "</script>")
    Long updateReviewStatus(CouponConfigReviewPO po);



    @Update("<script>" +
            " update nr_coupon_review " +
            "<set>" +
            "<if test='bpmKey!=null'> bpm_key=#{bpmKey},</if>" +
            "</set>" +
            " where id =#{id}" +
            "</script>")
    Long updateBpm(@Param("id") long id,@Param("bpmKey")  String bpmKey);


    @Select("<script>select * from nr_coupon_review where bpm_key=#{bpmKey}</script>")
    CouponConfigReviewPO selectByBpmKey(@Param("bpmKey") String bpmKey);

    @Select("<script>select * from nr_coupon_review where id=#{id}</script>")
    CouponConfigReviewPO selectById(@Param("id") long id);


    @Select("<script>select * from nr_coupon_review where status!=3 and coupon_type=#{couponType} " +
            "<if test='reviewId!= 0'>and id=#{reviewId}</if>" +
            "<if test='configId!= 0'>and config_id=#{configId}</if>" +
            "<if test='couponName != null and couponName !=\"\"'> and coupon_name like concat(concat('%',#{couponName}),'%') </if>" +
            "<if test='status!=null and status.size()>0'> and status in <foreach collection='status' item='item' index='index' open='(' close=')' separator=','>#{item}</foreach> </if>"+
            "<if test='creator != null and creator !=\"\" '>and creator=#{creator}</if>" +
            "<if test='startCreateTime != null'>and create_time <![CDATA[>=]]> #{startCreateTime}</if>" +
            "<if test='endCreateTime != null'>and create_time <![CDATA[<=]]> #{endCreateTime}</if>" +
            "<if test='areaId != null and areaId != \"\"'>and area_id=#{areaId}</if>" +
            "order by ${orderBy} ${orderDirection}"+
            "</script>")
    List<CouponConfigReviewPO> selectList(SearchReviewListParam param);


    @Select("select apply_attachment from nr_coupon_review where config_id=#{configId} and status=3 order by approved_time desc limit 1")
    String selectByConfigId(@Param("configId") long configId);



}
