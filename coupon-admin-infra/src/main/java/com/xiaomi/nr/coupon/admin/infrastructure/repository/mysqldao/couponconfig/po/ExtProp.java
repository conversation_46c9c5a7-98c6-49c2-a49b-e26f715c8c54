package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.couponconfig.po;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * 附加属性
 *
 * <AUTHOR>
 */
@Data
public class ExtProp implements Serializable {

    private static final long serialVersionUID = -2623570663046553924L;

    /**
     * 是否包邮　1：包邮 2：不包邮
     */
    @SerializedName("prop_postfree")
    private String isPostFree;

    /**
     * 是否可分享　1：可分享 2：不可分享
     */
    @SerializedName("prop_is_share")
    private String isShare;
}