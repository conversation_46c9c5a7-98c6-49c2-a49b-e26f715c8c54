package com.xiaomi.nr.coupon.admin.infrastructure.repository;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.scene.po.CouponScenePO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.pointadmin.mapper.CarPointsScenePoMapper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.pointadmin.po.CarPointsScenePo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 优惠券场景
 * @Date: 2022.03.03 17:15
 */
@Component
public class CarPointSceneRepository {

    @Autowired
    private CarPointsScenePoMapper carPointsScenePoMapper;

    /**
     * 插入场景
     * @param carPointsScenePo
     * @return
     */
    public Integer insert(CarPointsScenePo carPointsScenePo) {
        return carPointsScenePoMapper.insert(carPointsScenePo);
    }

    /**
     * 更新场景
     * @param carPointsScenePo
     * @return
     */
    public Integer update(CarPointsScenePo carPointsScenePo) {
        return carPointsScenePoMapper.update(carPointsScenePo);
    }

    /**
     * 更新状态
     *
     * @param id
     * @param status
     * @return
     */
    public Integer updateStatusById(long id, int status) {
        return carPointsScenePoMapper.updateStatusById(id, status);
    }

    /**
     * 根据id查询场景
     *
     * @param id
     * @return
     */
    public CarPointsScenePo searchSceneById(Long id) {
        return carPointsScenePoMapper.searchSceneById(id);
    }

    /**
     * 根据sceneCode查询场景
     *
     * @param sceneCode
     * @return
     */
    public CarPointsScenePo searchSceneBySceneCode(String sceneCode) {
        return carPointsScenePoMapper.searchSceneBySceneCode(sceneCode);
    }

    /**
     * 获取所有有效的场景编码
     * @return
     */
    public List<CarPointsScenePo> getAllSceneList(Boolean onlyValid) {
        return carPointsScenePoMapper.searchAllValidScene(onlyValid);
    }

    /**
     * 分页获取所有有效的场景
     * @return
     */
    public List<CarPointsScenePo> getAllSceneListPage(Boolean onlyValid, String keyWord) {
        return carPointsScenePoMapper.searchAllValidScenePage(onlyValid, keyWord);
    }

    /**
     * 根据名称获取场景编码
     * @param name
     * @return
     */
    public CouponScenePO searchSceneByName(String name) {
        return carPointsScenePoMapper.searchSceneByName(name);
    }

    /**
     * 获取所有场景枚举代码
     * @param
     * @return
     */
    public List<String> searchAllEnumCode() {
        return carPointsScenePoMapper.searchAllEnumCode();
    }


}
