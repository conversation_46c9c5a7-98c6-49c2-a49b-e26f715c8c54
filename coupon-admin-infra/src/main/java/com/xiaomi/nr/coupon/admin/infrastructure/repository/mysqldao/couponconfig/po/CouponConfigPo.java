package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.couponconfig.po;


import lombok.Data;

import java.io.Serializable;

/**
 * v3_coupon_type 优惠券配置表
 *
 * <AUTHOR>
 */
@Deprecated
@Data
public class CouponConfigPo implements Serializable {

    private static final long serialVersionUID = 717128970575302914L;

    /**
     * 优惠券编号
     */
    private Long id;

    /**
     * 优惠券类型名称
     */
    private String name;

    /**
     * 优惠券名称描述
     */
    private String nameDesc;

    /**
     * 使用范围描述
     */
    private String rangeDesc;

    /**
     * 金额描述
     */
    private String valueDesc;

    /**
     * 优惠类型
     */
    private Integer type;

    /**
     * 抵扣类型
     */
    private Integer deductType;

    /**
     * 地区
     */
    private Integer areaId;

    /**
     * 图片地址
     */
    private String galleryUrl;

    /**
     * 线上，线下限制 online 仅线上使用 offline 仅线下使用 both 线上线下均可使用
     */
    private String offline;

    /**
     * 是否有券码 1有码券 2无码券
     */
    private String isCode;

    /**
     * 特价商品是否参与 no 不参与 yes 参与
     */
    private String checkPrice;

    /**
     * 是否拆分套装 no 不拆分，yes 拆分
     */
    private String checkPackage;

    /**
     * 可用App列表
     */
    private String client;

    /**
     * 包含商品
     */
    private String goodsInclude;

    /**
     * 包含排除商品
     */
    private String goodsInExclude;

    /**
     * 整单排除商品
     */
    private String goodsExclude;

    /**
     * 附加属性
     */
    private String extProp;

    /**
     * 限额
     */
    private String quota;

    /**
     * 政策
     */
    private String policy;

    /**
     * 条件
     */
    private String cond;

    /**
     * 部门ID
     */
    private Integer departmentId;

    /**
     * 成本中心
     */
    private String costcenterId;

    /**
     * 代理部门
     */
    private String agentDepartments;

    /**
     * 发券数量限制
     */
    private Long sendLimit;

    /**
     * 已发券总数
     */
    private Long nowCount;

    /**
     * 全局开始时间
     */
    private Long globalStartTime;

    /**
     * 全局结束时间
     */
    private Long globalEndTime;

    /**
     * 相关SKU列表
     */
    private String skuList;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 状态 enum('add','approving','approved','reject','cancel')
     */
    private String stat;

    /**
     * 描述
     */
    private String rangeLongDesc;

    /**
     * 修改时间
     */
    private Long modifyTime;

    /**
     * 优惠券投放渠道，store_manager：店长券，store_order_gift：下单赠券，空：其他渠道
     */
    private String sendChannel;

    /**
     * 优惠券使用渠道，mi_shop：小米商城，mi_home：小米之家，mi_authorized：授权店，空：老券配置（不改则不能提交）https://xiaomi.f.mioffice.cn/docs/dock49LXIsRVLH50Ah3P5wnzF8c
     */
    private String useChannel;

    /**
     * 最后更新时间
     */
    private String lastUpdateTime;
}

