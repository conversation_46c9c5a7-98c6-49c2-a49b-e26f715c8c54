package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.po;

import com.xiaomi.nr.coupon.admin.api.enums.TranslationEnum;
import com.xiaomi.nr.global.dev.base.RequestContextInfo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Data
public class CouponWorkFlowExtendLanguagePo {

    private String workflowName;

    private String language;


    private static final String Chinese = "zh-CN";
    public static String handlerTranslateText(CouponWorkFlowExtendPo workFlowExtendPo) throws BizError {
        if(Objects.isNull(workFlowExtendPo)){
            throw ExceptionHelper.create(GeneralCodes.InternalError, TranslationEnum.COUPON_WORKFLOW_QUERY_ANALYSIS.getTranslateContent());
        }
        List<CouponWorkFlowExtendLanguagePo> languagePoList = workFlowExtendPo.getLanguagePoList();
        if(CollectionUtils.isEmpty(languagePoList)){
            throw ExceptionHelper.create(GeneralCodes.InternalError, TranslationEnum.COUPON_WORKFLOW_QUERY_ANALYSIS.getTranslateContent());
        }
        Map<String, String> languageTranslateMap = languagePoList.stream()
                .collect(Collectors.toMap(CouponWorkFlowExtendLanguagePo::getLanguage, CouponWorkFlowExtendLanguagePo::getWorkflowName));
        if(!languageTranslateMap.containsKey(RequestContextInfo.getLanguage())){
            //如果传入的语言没有取到，返回一个中文
            if(languageTranslateMap.containsKey(Chinese)){
                return languageTranslateMap.get(Chinese);
            }
            //中文也没就抛异常
            throw ExceptionHelper.create(GeneralCodes.InternalError, TranslationEnum.COUPON_WORKFLOW_QUERY_ANALYSIS.getTranslateContent());
        }
        return languageTranslateMap.get(RequestContextInfo.getLanguage());
    }

    public static Boolean handlerNewFlag(CouponWorkFlowExtendPo workFlowExtendPo) throws BizError {
        if(Objects.isNull(workFlowExtendPo)){
            throw ExceptionHelper.create(GeneralCodes.InternalError, TranslationEnum.COUPON_WORKFLOW_QUERY_ANALYSIS.getTranslateContent());
        }
        if(Objects.nonNull(workFlowExtendPo.getNewFlag()) && workFlowExtendPo.getNewFlag()){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }


}
