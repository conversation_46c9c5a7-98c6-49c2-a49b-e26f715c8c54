package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.xmstore.po;

import lombok.Data;

/**
 * @description:
 * @author: heji<PERSON><PERSON>
 * @Date 2022/4/18 7:59 下午
 * @Version: 1.0
 **/
@Data
public class XmStoreCouponPO {

    private int siteId;
    private int classId;
    private int onlyCheck;
    private String name;
    private String rangeDesc;
    private int couponType;
    private int sendLimit;
    private int isCode;
    private int startDate;
    private int endDate;
    private int checkPackage;
    private int checkPrice;
    private String client;
    private String goodsInclude;
    private String policy;
    private int status;
    private int selectType;
    private int createdAt;
    private int createdBy;
    private int updatedAt;
    private int updatedBy;
}
