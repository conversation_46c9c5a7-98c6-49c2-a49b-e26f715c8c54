package com.xiaomi.nr.coupon.admin.infrastructure.rpc;

import com.google.common.collect.Lists;
import com.mi.oa.infra.oaucf.ems.enums.SystemSourceEnum;
import com.mi.oa.infra.oaucf.ems.req.BudgetReleaseReq;
import com.mi.oa.infra.oaucf.ems.req.BudgetVerifyReq;
import com.mi.oa.infra.oaucf.ems.resp.BudgetVerifyResp;
import com.mi.oa.infra.oaucf.ems.resp.EmsResp;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/03/19
 */

@Slf4j
@Component
public class CouponBudgetService {

    @Autowired
    private BrProxy brProxy;

    /**
     * 占用br预算金额
     *
     * @param
     * @return
     */
    public void preOccupyBudget(CouponConfigPO couponConfigPO) throws BizError {
        log.info("CouponBudgetService.preOccupyBudget begin, couponConfigPO = {}", couponConfigPO);
        try {
            BudgetVerifyReq req = new BudgetVerifyReq();
            req.setSystemSource(SystemSourceEnum.EQUITY_CENTER.getCode());
            req.setBusinessScenarioCode("EMS-BR01");
            req.setApplyNo(couponConfigPO.getBrApplyNo());//br申请单号（唯一标识）
            req.setProjectCode(couponConfigPO.getBudgetApplyNo() + "_" + couponConfigPO.getLineNum());
            req.setOccupyTime(couponConfigPO.getBudgetCreateTime());
            double applyAmount = (double)couponConfigPO.getApplyCount() * couponConfigPO.getPromotionValue() / 100;
            req.setApplyAmount(String.valueOf(applyAmount));
            req.setCurrencyCode("CNY");
            brProxy.preOccupyBudget(Lists.newArrayList(req));
        } catch (Exception e) {
            log.error("CouponBudgetService.preOccupyBudget error couponConfigPO:{}", couponConfigPO, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, e.getMessage());
        }
    }

    /**
     * 追加br预算金额
     *
     * @param
     * @return
     */
    public EmsResp<BudgetVerifyResp> addBudget(CouponConfigPO couponConfigPO, double gap, String brApplyNo) throws BizError {
        log.info("CouponBudgetService.addBudget begin, couponConfigPO = {}, gap = {}, brApplyNo = {}", couponConfigPO, gap, brApplyNo);
        try {
            BudgetVerifyReq req = new BudgetVerifyReq();
            req.setSystemSource(SystemSourceEnum.EQUITY_CENTER.getCode());
            req.setBusinessScenarioCode("EMS-BR01");
            req.setApplyNo(brApplyNo);//br申请单号（唯一标识）
            req.setProjectCode(couponConfigPO.getBudgetApplyNo() + "_" + couponConfigPO.getLineNum());
            req.setOccupyTime(couponConfigPO.getBudgetCreateTime());
            req.setApplyAmount(String.valueOf(gap));
            req.setCurrencyCode("CNY");
            return brProxy.addBudget(Lists.newArrayList(req));
        } catch (Exception e) {
            log.error("CouponBudgetService.addBudget error couponConfigPO:{}", couponConfigPO, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, e.getMessage());
        }
    }

    /**
     * 返还br预算金额
     *
     * @param
     * @return
     */
    public EmsResp<Boolean> reduceBudget(CouponConfigPO couponConfigPO, double gap, String brApplyNo) throws BizError {
        log.info("CouponBudgetService.reduceBudget begin, couponConfigPO = {}, gap = {}, brApplyNo = {}", couponConfigPO, gap, brApplyNo);
        try {
            BudgetReleaseReq req = new BudgetReleaseReq();
            req.setSystemSource(SystemSourceEnum.EQUITY_CENTER.getCode());
            req.setBusinessScenarioCode("EMS-BR01");
            req.setApplyNo(brApplyNo);//br申请单号（唯一标识）
            req.setProjectCode(couponConfigPO.getBudgetApplyNo() + "_" + couponConfigPO.getLineNum());
            req.setReleaseTime(couponConfigPO.getBudgetCreateTime());
            req.setReleaseAmount(String.valueOf(gap));
            req.setCurrencyCode("CNY");
            return brProxy.reduceBudget(Lists.newArrayList(req));
        } catch (Exception e) {
            log.error("CouponBudgetService.reduceBudget error couponConfigPO:{}", couponConfigPO, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, e.getMessage());
        }
    }
}
