package com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.po;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 货品或套装对应优惠券配置（缓存）
 *
 * <AUTHOR>
 */
@Deprecated
@Data
public class GoodsConfigRelationPo implements Serializable {

    private static final long serialVersionUID = 6988106082628274212L;

    /**
     * SKU或套装ID
     */
    private Long id;

    /**
     * 商品品级，sku:代表sku package:代表套装
     */
    private String level;

    /**
     * 可用货品和套装列表
     */
    private List<GoodsConfigRelationItem> list;

    /**
     * 缓存生成时间
     */
    private String cacheCreateTime;
}
