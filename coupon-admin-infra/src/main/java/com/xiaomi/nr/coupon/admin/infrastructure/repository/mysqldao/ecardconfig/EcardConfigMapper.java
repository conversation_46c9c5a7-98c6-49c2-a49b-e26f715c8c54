package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.ecardconfig;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.ecardconfig.po.EcardPo;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.ecardconfig.po.EcardTypePo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 */
@Mapper
@Component
public interface EcardConfigMapper {


    /**
     * 根据card_id查询券信息
     * @param cardIds List<Long>
     * @return ArrayList<EcardPo>
     */
    @Select("<script>" +
            "select * from tb_ecard " +
            "<if test=\"cardIds != null and cardIds.size >0\"> where card_id in " +
            "        <foreach item='card_id' index='index' collection='cardIds' open='(' separator=',' close=')'>" +
            "            #{card_id}" +
            "        </foreach> " +
            "</if>" +
            "order by add_time desc" +
            "</script>")
    ArrayList<EcardPo> queryByCardIds(@Param("cardIds") List<Long> cardIds);


    @Select("select * from tb_ecard where card_id=#{cardId}")
    EcardPo queryByCardId(@Param("cardId") Long cardId);


    @Select("<script>" +
            "select id,name from v3_ecard_type where 1=1 " +
            "<if test=\"ids != null and ids.size >0\"> and id in " +
            "        <foreach item='id' index='index' collection='ids' open='(' separator=',' close=')'>" +
            "            #{id}" +
            "        </foreach> " +
            "</if>" +
            "</script>")
    List<EcardTypePo> queryCardTypeById(@Param("ids") List<Integer> ids);


    @Update("update tb_ecard set " +
            "end_time=#{delayTime}," +
            "delay_times=delay_times+1 " +
            "where card_id=#{cardId}")
    int updateEcardEndTime(@Param("cardId") Long cardId, @Param("delayTime") String delayTime);


    /**
     * 删除用户礼品卡，自动化测试
     * @return
     */
    @Delete("<script>delete from tb_ecard where user_id=#{uid} and card_id =#{cardId}</script>")
    Long deleteEcard(@Param("uid") long uid, @Param("cardId") long cardId);


}
