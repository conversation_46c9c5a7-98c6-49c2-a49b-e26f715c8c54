package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.adsTidb.entity;

import com.google.common.collect.ImmutableMap;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.statistic.request.CouponDataStatisticRequest;
import com.xiaomi.nr.coupon.admin.util.StringUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class SearchCouponDataParam implements Serializable {

    private static final long serialVersionUID = 8741304516698345964L;

    public static final String configId = "configId";

    public static final String activityId = "activityId";

    public static final String applyCount = "applyCount";


    private Map<String,String> orderByMap = ImmutableMap.<String, String>builder()
            .put(configId, "coupon_id")
            .put(activityId, "act_id")
            .put(applyCount, "apply_cnt")
            .build();

    /**
     * 优惠券id
     */
    private List<Long> couponIds;

    /**
     * 优惠券id
     */
    private List<String> actIds;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 使用渠道
     */
    private List<String> useChannels;

    /**
     * 投放场景
     */
    private String sendType;

    /**
     * 支持分页
     */
    private Boolean canPage;

    /**
     * 排序字段
     */
    private String orderBy = "coupon_id";

    /**
     * 排序顺序  desc倒序   asc顺序
     */
    private String orderDirection = "desc";

    /**
     * 当前页码
     */
    private Integer startSize = 0;

    /**
     * 页面大小(数据条数)
     */
    private Integer pageSize = 10;

    /**
     * 创建人
     */
    private String creator;


    public static SearchCouponDataParam buildSearchCouponDataParam(CouponDataStatisticRequest request) {

        SearchCouponDataParam param = new SearchCouponDataParam();

        param.setStartTime(request.getStartTime());
        param.setEndTime(request.getEndTime());
        param.setStartSize((request.getPageNo() - 1) * request.getPageSize());
        param.setPageSize(request.getPageSize());
        param.setCanPage(true);
        if (StringUtils.isNotBlank(request.getConfigIds())) {
            param.setCouponIds(StringUtil.convertToLongList(request.getConfigIds()));
        }
        if (StringUtils.isNotBlank(request.getActivity())) {
            param.setActIds(StringUtil.convertToStringList(request.getActivity()));
        }

        if (StringUtils.isNotEmpty(request.getSendType())) {
            param.setSendType(request.getSendType());
        }

        if (StringUtils.isNotBlank(request.getOrderBy()) && param.getOrderByMap().containsKey(request.getOrderBy())) {
            param.setOrderBy(param.getOrderByMap().get(request.getOrderBy()));
        } else {
            param.setOrderBy(param.getOrderByMap().get(configId));
        }

        if(StringUtils.isNotEmpty(request.getOrderDirection())){
            param.setOrderDirection(request.getOrderDirection());
        }

        param.setUseChannels(request.getUseChannel());
        param.setCreator(request.getCreator());
        return param;
    }

}
