package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.couponconfig.po;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 优惠券配置可用商品
 *
 * <AUTHOR>
 */
@Data
public class GoodsIncludesTmp implements Serializable {

    private static final long serialVersionUID = -1555433710008370177L;

    /**
     * 分类
     */
    @SerializedName("cat")
    private List<String> cat;

    /**
     * 品类
     */
    @SerializedName("group")
    private List<String> group;

    /**
     * SKU
     */
    @SerializedName("sku")
    private List<String> sku;

    /**
     * 货品
     */
    @SerializedName("goods")
    private List<String> goods;

    /**
     * 套装
     */
    @SerializedName("package")
    private List<String> packages;

    /**
     * 商品
     */
    @SerializedName("commodity")
    private List<String> commodity;

    /**
     * 产品
     */
    @SerializedName("product")
    private List<String> product;

    /**
     * 所有商品
     */
    @SerializedName("all")
    private Integer all = -1;

    /**
     * 修改时间
     */
    @SerializedName("modify_index")
    private Long modifyIndex = 0L;
}