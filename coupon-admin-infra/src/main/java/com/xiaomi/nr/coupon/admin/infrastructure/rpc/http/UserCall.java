package com.xiaomi.nr.coupon.admin.infrastructure.rpc.http;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xiaomi.common.perfcounter.PerfCounter;
import com.xiaomi.nr.coupon.admin.infrastructure.rpc.http.entrty.UserResponse;
import com.xiaomi.nr.coupon.admin.api.utils.TimeUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.StatusLine;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.LaxRedirectStrategy;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;

@Slf4j
@Component
@Configuration
public class UserCall {

    @Value("${userApi.url}")
    private String host;

    @Value("${userApi.key}")
    private String key;

    private static ObjectMapper mapper = new ObjectMapper();

    private static int MAX_TRY_TIMES = 3;

    static {
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public Object call(String method, String param) throws Exception {

        long nowTime = TimeUtil.getNowUnixSecond();

        Map<String, Object> params = new HashMap<>();
        params.put("batchID", param);
        params.put("time", nowTime);
        params.put("token", makeToken(param, nowTime));

        String url = host + method + makeURL(params);
        String resp = null;
        for (int i = 1; i <= MAX_TRY_TIMES; i++) {
            try {
                resp = doPost(url);
            } catch (BizError bizError) {
                if (i == MAX_TRY_TIMES) {
                    log.error("UserCall finally fail.url:{},err:", url, bizError);
                    throw ExceptionHelper.create(GeneralCodes.InternalError, "UserCall finally fail", bizError);
                } else {
                    log.error("UserCall fail.url:{},i:{},err:", url, i, bizError);
                }
            }
        }
        try {
            UserResponse userResponse = mapper.readValue(resp, UserResponse.class);
            if (userResponse == null || userResponse.getCode() != 0) {
                throw ExceptionHelper.create(GeneralCodes.InternalError, "call UserResponse's code is not 0 finally. userResponse:" + userResponse);
            }
            return userResponse.getData();
        } catch (Exception e) {
            log.error("call UserResponse readValue error. method:{},resp:{},err:", method, resp, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "call UserResponse readValue error.", e);
        }
    }



    private String doPost(String host) throws BizError {
        HttpPost httpPost = new HttpPost(host);
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse response = null;
        long start = System.currentTimeMillis();
        try {
            httpClient = HttpClientBuilder.create().setRedirectStrategy(new LaxRedirectStrategy()).build();
            httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded");
            response = httpClient.execute(httpPost);
            StatusLine status = response.getStatusLine();
            int state = status.getStatusCode();
            if (state == HttpStatus.SC_OK) {

                HttpEntity responseEntity = response.getEntity();
                String jsonString = EntityUtils.toString(responseEntity);
                PerfCounter.count("coupon-adminCallUserSucc", 1, System.currentTimeMillis() - start);
                return jsonString;
            } else {
                throw ExceptionHelper.create(GeneralCodes.InternalError, "UserCall status is not 200.state="+state);
            }
        } catch (Exception e) {
            log.error("post error. host:{},err", host, e);
            PerfCounter.count("coupon-adminCallUserFail", 1, System.currentTimeMillis() - start);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "post error", e);
        } finally {
            try {
                httpPost.releaseConnection();
                if (response != null) {
                    response.close();
                }
                if (httpClient != null) {
                    httpClient.close();
                }
            } catch (IOException e) {
                log.error("post close error. host:{},err", host, e);
            }
        }
    }


    public String makeURL(Map<String, Object> map){
        return "?batchID="+map.get("batchID")+"&token="+map.get("token")+"&time="+map.get("time");
    }
    public  String makeToken(String param, long nowTime) {
        return DigestUtils.md5Hex("batchID="+param+"_"+ nowTime +"_"+key);
    }


}
