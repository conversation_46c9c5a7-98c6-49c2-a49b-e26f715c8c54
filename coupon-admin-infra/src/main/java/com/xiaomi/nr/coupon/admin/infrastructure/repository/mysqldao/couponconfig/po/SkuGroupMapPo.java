package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.couponconfig.po;


import lombok.Data;

import java.io.Serializable;

/**
 * v3_sku_group_map sku/套装与品类关联配置表
 *
 * <AUTHOR>
 */
@Data
public class SkuGroupMapPo implements Serializable {

    private static final long serialVersionUID = -4492035136833732474L;

    /**
     * 自增ID
     */
    private Long id;

    /**
     * sku或套装ID
     */
    private Long sku;

    /**
     * 品类ID
     */
    private Long groupId;

    /**
     * 部门ID
     */
    private Long departmentId;

    /**
     * 地区
     */
    private Long areaId;

    /**
     * 添加时间
     */
    private Long addTime;
}

