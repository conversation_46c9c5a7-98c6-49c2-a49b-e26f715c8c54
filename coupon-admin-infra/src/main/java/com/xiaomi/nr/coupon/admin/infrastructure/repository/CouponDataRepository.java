package com.xiaomi.nr.coupon.admin.infrastructure.repository;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.adsTidb.CouponDataMapper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.adsTidb.entity.SearchCouponDataParam;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.adsTidb.entity.SearchCouponDataResult;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.adsTidb.po.CouponStatisticPo;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.CouponConfigMapper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
public class CouponDataRepository {

    @Autowired
    private CouponDataMapper couponDataMapper;

    @Autowired
    private CouponConfigMapper couponConfigMapper;


    /**
     * 查询优惠券数据列表
     * @param searchCouponDataParam
     * @return
     */
    public SearchCouponDataResult searchCouponDataList(SearchCouponDataParam searchCouponDataParam){

        SearchCouponDataResult searchCouponDataResult = new SearchCouponDataResult();

        try{
            setCouponIdByCreator(searchCouponDataParam);
        }catch (BizError e){
            return searchCouponDataResult;
        }

        Integer total = couponDataMapper.getCouponDataDetailCountById(searchCouponDataParam);

        List<Long> couponIds =  couponDataMapper.getCouponDataDetailIdList(searchCouponDataParam);

        if(CollectionUtils.isEmpty(couponIds)) {
            return searchCouponDataResult;
        }

        searchCouponDataParam.setCouponIds(couponIds);

        searchCouponDataResult.setTotalCnt(total);
        searchCouponDataResult.setTotalPage(getTotalPage(searchCouponDataParam.getPageSize(), total));
        searchCouponDataResult.setCouponStatisticPos(couponDataMapper.getCouponDataDetailList(searchCouponDataParam));

        return searchCouponDataResult;
    }

    /**
     * 查询灌券数据列表
     * @param searchCouponDataParam
     * @return
     */
    public SearchCouponDataResult searchFillCouponDataList(SearchCouponDataParam searchCouponDataParam){

        SearchCouponDataResult searchCouponDataResult = new SearchCouponDataResult();

        try{
            setCouponIdByCreator(searchCouponDataParam);
        }catch (BizError e){
            return searchCouponDataResult;
        }

        Integer total = couponDataMapper.getFillCouponDataDetailCount(searchCouponDataParam);

        List<String> actIds =  couponDataMapper.getFillCouponDataDetailIdList(searchCouponDataParam);

        if(CollectionUtils.isEmpty(actIds)) {
            return searchCouponDataResult;
        }

        searchCouponDataParam.setActIds(actIds);

        searchCouponDataResult.setTotalCnt(total);
        searchCouponDataResult.setTotalPage(getTotalPage(searchCouponDataParam.getPageSize(), total));
        searchCouponDataResult.setCouponStatisticPos(couponDataMapper.getFillCouponDataDetailList(searchCouponDataParam));

        return searchCouponDataResult;
    }

    /**
     * 查询优惠券导出数据列表
     * @param searchCouponDataParam
     * @return
     */
    public List<CouponStatisticPo> exportCouponDataList(SearchCouponDataParam searchCouponDataParam){

        searchCouponDataParam.setCanPage(false);
        List<Long> couponIds = couponDataMapper.getCouponDataDetailIdList(searchCouponDataParam);

        searchCouponDataParam.setCouponIds(couponIds);

        return couponDataMapper.getCouponDataDetailList(searchCouponDataParam);
    }

    /**
     * 查询灌券数据到处列表
     * @param searchCouponDataParam
     * @return
     */
    public List<CouponStatisticPo> exportFillCouponDataList(SearchCouponDataParam searchCouponDataParam){
        searchCouponDataParam.setCanPage(false);
        List<String> actIds = couponDataMapper.getFillCouponDataDetailIdList(searchCouponDataParam);

        searchCouponDataParam.setActIds(actIds);
        return couponDataMapper.getFillCouponDataDetailList(searchCouponDataParam);
    }

    /**
     * 分页工具方法
     * @param pageSize
     * @param total
     * @return
     */
    private Integer getTotalPage(Integer pageSize, Integer total) {
        return total / pageSize + (total % pageSize == 0 ? 0 : 1);
    }


    /**
     * 根据创建人获取券id
     * @param searchParam
     * @return
     */
    public void setCouponIdByCreator(SearchCouponDataParam searchParam) throws BizError {

        if(StringUtils.isEmpty(searchParam.getCreator())){
            return;
        }

        List<Long> configIds = couponConfigMapper.selectIdByCreator(searchParam.getCreator());
        if(CollectionUtils.isEmpty(configIds)){
            throw ExceptionHelper.create(GeneralCodes.NotFound, "查询数据为空");
        }

        if(CollectionUtils.isEmpty(searchParam.getCouponIds())){
            searchParam.setCouponIds(configIds);
        }else {
            searchParam.getCouponIds().addAll(configIds);
        }

    }
}
