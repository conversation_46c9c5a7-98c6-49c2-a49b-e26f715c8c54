package com.xiaomi.nr.coupon.admin.enums.task;

public enum TaskStatusEnum {
    /**
     * 此状态为AWAIT的前置状态，处于此状态的任务未初始化,比如数据集地址
     */
    PRE_AWAIT(-2, "待初始化"),
    CANCEL(-1,"取消"),
    AWAIT(0,"等待"),
    READY(1,"就绪"),
    RUNNING(2,"运行中"),
    FAIL(3,"失败"),
    SUCCESS(4,"成功"),
    ERROR(5,"执行错误"),
    STOP(6, "暂停"),
    ;

    public int code;
    public String desc;

    public int getCode(){
        return this.code;
    }

    public String getDesc(){
        return this.desc;
    }

    TaskStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static TaskStatusEnum findByCode(int value) {
        switch(value) {
            case -1:
                return CANCEL;
            case 0:
                return AWAIT;
            case 1:
                return READY;
            case 2:
                return RUNNING;
            case 3:
                return FAIL;
            case 4:
                return SUCCESS;
            case 5:
                return ERROR;
            default:
                return null;
        }
    }
}
