package com.xiaomi.nr.coupon.admin.domain.coupon.goods.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.mi.framework.http.HttpClient;
import com.xiaomi.goods.gis.dto.goodsInfoNew.GoodsMultiInfoDTO;
import com.xiaomi.goods.gis.dto.price.BatchedChannelPriceDto;
import com.xiaomi.goods.gis.dto.price.GoodsChannelPriceDto;
import com.xiaomi.goods.gis.dto.sku.SkuBusinessCategory;
import com.xiaomi.goods.gis.dto.sku.offline.SkuAvailablePageDTO;
import com.xiaomi.goods.gms.dto.batch.BatchedInfoDto;
import com.xiaomi.goods.gms.dto.sku.SkuInfoDto;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.*;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.goods.request.GoodsDiscountLevelRequest;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.api.enums.BizSubTypeEnum;
import com.xiaomi.nr.coupon.admin.api.utils.I18nUtil;
import com.xiaomi.nr.coupon.admin.application.dubbo.convert.GoodsVoConvert;
import com.xiaomi.nr.coupon.admin.domain.coupon.goods.GoodsService;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.*;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsItemTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.infrastructure.constant.coupon.CouponConstant;
import com.xiaomi.nr.coupon.admin.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.excel.*;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.fds.FileService;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.po.SearchConfigParam;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.po.ConfigInfoCachePo;
import com.xiaomi.nr.coupon.admin.infrastructure.rpc.goods.GisProxyService;
import com.xiaomi.nr.coupon.admin.infrastructure.rpc.goods.GmsProxyService;
import com.xiaomi.nr.coupon.admin.infrastructure.rpc.goods.I18nGmsProxyService;
import com.xiaomi.nr.coupon.admin.util.FileUtils;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.NumberUtil;
import com.xiaomi.nr.coupon.admin.util.beancopy.BeanMapper;
import com.xiaomi.nr.goods.api.response.ssu.SsuMultiChannelDTO;
import com.xiaomi.nr.goods.tob.dto.response.ssu.SsuDTO;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.xiaomi.nr.coupon.admin.application.dubbo.convert.GoodsVoConvert.convertToSkuInfoVO;

@Slf4j
@Service
public class GoodsServiceImpl implements GoodsService {

    @Autowired
    private GisProxyService gisProxyService;

    @Autowired
    private GmsProxyService gmsProxyService;

    @Autowired
    private I18nGmsProxyService i18nGmsProxyService;

    @Autowired
    private FileService fileService;

    @Autowired
    private GoodsVoConvert goodsConvert;

    @Autowired
    private ThreadPoolTaskExecutor queryAsyncExecutor;

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    @Override
    public void uploadGoodsFailReason(List<UploadGoodsFailPO> uploadGoodsFailPOS, String fileName) throws Exception {
        File file = new File(fileName);
        try {
            FileUtils.writeExcelFile(file, "商品上传失败原因", uploadGoodsFailPOS, UploadGoodsFailPO.class);

            fileService.uploadFileToFds(getUploadGoodsObjectName(fileName), file, true);

        } catch (Exception e) {
            log.error("FileService uploadGoodsFailReason error fileName:{}", "商品上传失败原因", e);
            throw e;
        } finally {
            file.delete();
        }
    }


    @Override
    public void uploadGoodsExcelFile(Map<String, List<Long>> goodsInclude, String fileName) throws Exception {
        File file = new File(fileName);
        try {
            List<FileInfo> fileInfos = new ArrayList<>();
            if (goodsInclude.containsKey(GoodsLevelEnum.Sku.getValue()) && CollectionUtils.isNotEmpty(goodsInclude.get(GoodsLevelEnum.Sku.getValue()))) {
                List<Long> skuIds = goodsInclude.get(GoodsLevelEnum.Sku.getValue());
                List<SkuInfoDto> skuInfoDtoList = gmsProxyService.queryListBySkuIds(skuIds, false, true, false, Lists.newArrayList(BizSubTypeEnum.ORDINARY_3C.getCode()));
                FileInfo fileInfo = new FileInfo();
                fileInfo.setSheetName("货品");
                fileInfo.setSourceList(skuInfoDtoList.stream().map(x -> {
                    SkuInfoPO skuInfoPO = new SkuInfoPO();
                    BeanMapper.copy(x, skuInfoPO);
                    if (x.getCategory() != null) {
                        BeanMapper.copy(x.getCategory(), skuInfoPO);
                    }
                    return skuInfoPO;
                }).collect(Collectors.toList()));
                fileInfo.setDestinationClass(SkuInfoPO.class);

                fileInfos.add(fileInfo);
            }

            if (goodsInclude.containsKey(GoodsLevelEnum.Package.getValue()) && CollectionUtils.isNotEmpty(goodsInclude.get(GoodsLevelEnum.Package.getValue()))) {
                List<Long> packageIds = goodsInclude.get(GoodsLevelEnum.Package.getValue());
                List<BatchedInfoDto> packageInfoDtoList = gmsProxyService.queryListByPackageIds(packageIds, false);
                FileInfo fileInfo = new FileInfo();
                fileInfo.setSheetName("套装");
                fileInfo.setSourceList(packageInfoDtoList.stream().map(x -> {
                    PackageInfoPO packageInfoPO = new PackageInfoPO();
                    BeanMapper.copy(x, packageInfoPO);
                    return packageInfoPO;
                }).collect(Collectors.toList()));
                fileInfo.setDestinationClass(PackageInfoPO.class);
                fileInfos.add(fileInfo);
            }

            if (goodsInclude.containsKey(GoodsLevelEnum.Ssu.getValue()) && CollectionUtils.isNotEmpty(goodsInclude.get(GoodsLevelEnum.Ssu.getValue()))) {
                List<Long> ssuIds = goodsInclude.get(GoodsLevelEnum.Ssu.getValue());
                List<SkuAvailablePageDTO> skuAvailablePageDTOList = gisProxyService.pageAvailableSkuCarShop(ssuIds);
                FileInfo fileInfo = new FileInfo();
                fileInfo.setSheetName("货品");
                fileInfo.setSourceList(skuAvailablePageDTOList.stream().map(x -> {
                    SkuInfoPO skuInfoPO = new SkuInfoPO();
                    skuInfoPO.setSku(x.getSku());
                    skuInfoPO.setGoodsId(x.getGoodsId());
                    skuInfoPO.setProductId(x.getProductId());
                    skuInfoPO.setGoodsName(x.getGoodsName());
                    SkuBusinessCategory category = x.getBusinessCategory();
                    if (!Objects.isNull(category)) {
                        skuInfoPO.setFirstCategoryName(x.getBusinessCategory().getFirstCategoryName());
                        skuInfoPO.setSecondCategoryName(x.getBusinessCategory().getSecondCategoryName());
                        skuInfoPO.setThirdCategoryName(x.getBusinessCategory().getThirdCategoryName());
                    }
                    return skuInfoPO;
                }).collect(Collectors.toList()));
                fileInfo.setDestinationClass(SkuInfoPO.class);
                fileInfos.add(fileInfo);
            }

            if (goodsInclude.containsKey(GoodsLevelEnum.Suit.getValue()) && CollectionUtils.isNotEmpty(goodsInclude.get(GoodsLevelEnum.Suit.getValue()))) {
                List<Long> suitIds = goodsInclude.get(GoodsLevelEnum.Suit.getValue());
                List<GoodsMultiInfoDTO> goodsMultiInfoDTOList = gisProxyService.queryGoodsMultiInfoByGoodsIds(suitIds,
                        GoodsItemTypeEnum.SUIT);
                FileInfo fileInfo = new FileInfo();
                fileInfo.setSheetName("新套装");
                fileInfo.setSourceList(goodsMultiInfoDTOList.stream().map(x -> {
                    SuitInfoPO suitInfoPO = new SuitInfoPO();
                    suitInfoPO.setProductId(x.getProductId());
                    suitInfoPO.setGoodsId(x.getGoodsId());
                    suitInfoPO.setGoodsName(x.getGoodsName());
                    return suitInfoPO;
                }).collect(Collectors.toList()));
                fileInfo.setDestinationClass(SuitInfoPO.class);
                fileInfos.add(fileInfo);
            }

            if (CollectionUtils.isNotEmpty(fileInfos)) {
                FileUtils.writeExcelFile(file, fileInfos);
            }
            fileService.uploadFileToFds(getBpmGoodsObjectName(fileName), file, true);
        } catch (Exception e) {
            log.error("FileService uploadGoodsExcelFile error fileName:{}", fileName, e);
            throw e;
        } finally {
            file.delete();
        }
    }

    @Override
    public String getBpmGoodsFdsUrl(String fileName) {
        return "https://" + fileService.getEndpoint() + File.separator + fileService.getBucketName() + File.separator + getBpmGoodsObjectName(fileName);
    }

    private String getBpmGoodsObjectName(String fileName) {
        return "bpm/goods/" + fileName + ".xlsx";
    }


    public String getUploadGoodsFailFdsUrl(String fileName) {
        return "https://" + fileService.getEndpoint() + File.separator + fileService.getBucketName() + File.separator + getUploadGoodsObjectName(fileName);
    }

    private String getUploadGoodsObjectName(String fileName) {
        return "coupon/uploadGoods/fail/" + fileName + ".xlsx";
    }


    /**
     * 从fds文件，获取商品id
     *
     * @param fileUrl
     * @return
     */
    @Override
    public List<Long> queryGoodsList(String fileUrl, GoodsLevelEnum goodsLevelEnum) throws Exception {
        if (StringUtils.isBlank(fileUrl)) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "文件路径不能为空");
        }
        byte[] bytes = new HttpClient().get(fileUrl).executeAsFile();
        ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);
        List<GoodsIdListPO> goodsIdListPOs = FileUtils.getExcelList(inputStream, GoodsIdListPO.class, GoodsIdListPO.HEAD_ROW_NUMBER);

        Set<Long> goodsIdSet = new HashSet<>(goodsIdListPOs.size());
        if (CollectionUtils.isNotEmpty(goodsIdListPOs)) {
            for (int i = 0; i < goodsIdListPOs.size(); i++) {
                if (i == 0) {
                    if (goodsIdListPOs.get(0) == null || StringUtils.isBlank(goodsIdListPOs.get(0).getGoodsId())) {
                        throw ExceptionHelper.create(GeneralCodes.ParamError, "文件格式错误");
                    }
                    if (GoodsLevelEnum.Sku.equals(goodsLevelEnum) && !GoodsIdListPO.headSku1.equals(goodsIdListPOs.get(0).getGoodsId())) {
                        throw ExceptionHelper.create(GeneralCodes.ParamError, "文件格式错误");
                    }
                    if (GoodsLevelEnum.Package.equals(goodsLevelEnum) && !GoodsIdListPO.headPackage1.equals(goodsIdListPOs.get(0).getGoodsId())) {
                        throw ExceptionHelper.create(GeneralCodes.ParamError, "文件格式错误");
                    }
                    if (GoodsLevelEnum.Suit.equals(goodsLevelEnum) && !GoodsIdListPO.headSuit1.equals(goodsIdListPOs.get(0).getGoodsId())) {
                        throw ExceptionHelper.create(GeneralCodes.ParamError, "文件格式错误");
                    }
                    continue;
                }
                if (i == 1) {
                    if (goodsIdListPOs.get(1) == null || StringUtils.isBlank(goodsIdListPOs.get(1).getGoodsId())) {
                        throw ExceptionHelper.create(GeneralCodes.ParamError, "文件格式错误");
                    }
                    if (GoodsLevelEnum.Sku.equals(goodsLevelEnum) && !GoodsIdListPO.headSku2.equals(goodsIdListPOs.get(1).getGoodsId())) {
                        throw ExceptionHelper.create(GeneralCodes.ParamError, "文件格式错误");
                    }
                    if (GoodsLevelEnum.Package.equals(goodsLevelEnum) && !GoodsIdListPO.headPackage2.equals(goodsIdListPOs.get(1).getGoodsId())) {
                        throw ExceptionHelper.create(GeneralCodes.ParamError, "文件格式错误");
                    }
                    if (GoodsLevelEnum.Suit.equals(goodsLevelEnum) && !GoodsIdListPO.headSuit2.equals(goodsIdListPOs.get(1).getGoodsId())) {
                        throw ExceptionHelper.create(GeneralCodes.ParamError, "文件格式错误");
                    }
                    continue;
                }
                if (goodsIdListPOs.get(i) == null || StringUtils.isBlank(goodsIdListPOs.get(i).getGoodsId())) {
                    continue;
                }
                String goodsId = goodsIdListPOs.get(i).getGoodsId().trim();
                if (StringUtils.isNotBlank(goodsId)) {
                    try {
                        goodsIdSet.add(Long.parseLong(goodsId));
                    } catch (NumberFormatException e) {
                        throw ExceptionHelper.create(GeneralCodes.ParamError, "文件格式错误，存在非法字符:" + goodsId);
                    }
                }
            }

            if (CollectionUtils.isEmpty(goodsIdSet)) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "文件上传为空");
            }
            return new LinkedList<>(goodsIdSet);
        }

        return Collections.EMPTY_LIST;
    }

    @Override
    public GoodsRuleDetailVO searchGoodsDetailInfo(GoodsRuleVO goodsRuleVO, Integer promotionType, Integer bizPlatform, String areaId, Integer channel) throws Exception {

        GoodsRuleDetailVO goodsRuleDetailVO = new GoodsRuleDetailVO();
        goodsRuleDetailVO.setScopeType(goodsRuleVO.getScopeType());
        goodsRuleDetailVO.setGoodsDepartments(goodsRuleVO.getGoodsDepartments());
        goodsRuleDetailVO.setCategoryIds(goodsRuleVO.getCategoryIds());
        goodsRuleDetailVO.setAutoUpdateGoods(goodsRuleVO.getAutoUpdateGoods());

        // 可用商品信息
        Map<String, List<Long>> goodsInclude = goodsRuleVO.getGoodsInclude();
        if (MapUtils.isNotEmpty(goodsInclude)) {
            // sku
            if (goodsInclude.containsKey(GoodsLevelEnum.Sku.getValue())) {
                goodsRuleDetailVO.setSkuIncludeList(this.searchSkuInfo(goodsInclude.get(GoodsLevelEnum.Sku.getValue()), areaId));
            }

            // 老套装
            if (goodsInclude.containsKey(GoodsLevelEnum.Package.getValue())) {
                goodsRuleDetailVO.setPackageIncludeList(this.searchPackageInfo(goodsInclude.get(GoodsLevelEnum.Package.getValue())));
            }

            // 老套装新套装共用packageIncludeList
            if (goodsInclude.containsKey(GoodsLevelEnum.Suit.getValue())) {
                if (Objects.equals(bizPlatform, BizPlatformEnum.CAR_SHOP.getCode())
                        && CollectionUtils.isNotEmpty(goodsRuleDetailVO.getPackageIncludeList())) {
                    throw ExceptionHelper.create(ErrCode.COUPON, "车商城新套装和老套装不能同时存在");
                }

                List<PackageInfoVO> packageInfoVOS = Optional.ofNullable(goodsRuleDetailVO.getPackageIncludeList()).orElse(Lists.newArrayList());
                packageInfoVOS.addAll(this.searchSuitInfo(goodsInclude.get(GoodsLevelEnum.Suit.getValue())));
                goodsRuleDetailVO.setPackageIncludeList(packageInfoVOS);
            }

            if (goodsInclude.containsKey(GoodsLevelEnum.Ssu.getValue())) {
                if (Objects.equals(bizPlatform, BizPlatformEnum.CAR_SHOP.getCode())) {
                    List<Long> ssuList = goodsInclude.get(GoodsLevelEnum.Ssu.getValue());
                    // queryGoodsInfoBySsuIds查询到的类目是后台类目，优惠券管理后台用的类目是业务类目，处理逻辑为ssu转为sku，再通过sku查询sku信息
                    Map<Long, GoodsMultiInfoDTO> ssuGoodsInfoMap = gisProxyService.queryGoodsInfoBySsuIds(ssuList, true);
                    List<Long> skuList = ssuGoodsInfoMap.values().stream().map(GoodsMultiInfoDTO::getSku).collect(Collectors.toList());
                    if (skuList.size() != ssuList.size()) {
                        List<Long> notFoundSsuList = ssuList.stream().filter(ssu -> !ssuGoodsInfoMap.containsKey(ssu)).collect(Collectors.toList());
                        log.info("notFoundSsuList is {}", GsonUtil.toJson(notFoundSsuList));
                        throw ExceptionHelper.create(ErrCode.COUPON, "存在未查询到的ssu");
                    }
                    List<SkuInfoVO> skuIncludeList = this.searchSkuInfo(skuList, areaId);
                    if(CollectionUtils.isNotEmpty(skuIncludeList) && MapUtils.isNotEmpty(goodsRuleVO.getGoodsMsg())){
                        skuIncludeList.forEach(ssuInfoVO -> {
                            if(goodsRuleVO.getGoodsMsg().containsKey(ssuInfoVO.getSsu())) {
                                GoodMsgVO goodMsgVO = goodsRuleVO.getGoodsMsg().get(ssuInfoVO.getSsu());
                                ssuInfoVO.setRebatesPricePerUnit(goodMsgVO.getRebatesPricePerUnit());
                                ssuInfoVO.setExpectedSaleAmount(goodMsgVO.getExpectedSaleAmount());
                            }
                        });
                    }
                    goodsRuleDetailVO.setSkuIncludeList(skuIncludeList);
                } else {
                    List<SsuInfoVO> ssuIncludeList = this.searchSsuInfo(goodsInclude.get(GoodsLevelEnum.Ssu.getValue()), areaId, channel);
                    if(CollectionUtils.isNotEmpty(ssuIncludeList) && MapUtils.isNotEmpty(goodsRuleVO.getGoodsMsg())){
                        ssuIncludeList.forEach(ssuInfoVO -> {
                            if(goodsRuleVO.getGoodsMsg().containsKey(ssuInfoVO.getSsuId())) {
                                GoodMsgVO goodMsgVO = goodsRuleVO.getGoodsMsg().get(ssuInfoVO.getSsuId());
                                ssuInfoVO.setRebatesPricePerUnit(goodMsgVO.getRebatesPricePerUnit());
                                ssuInfoVO.setExpectedSaleAmount(goodMsgVO.getExpectedSaleAmount());
                            }
                        });
                    }
                    goodsRuleDetailVO.setSsuIncludeList(ssuIncludeList);
                }
            }
        }

        // 排除商品信息
        Map<String, List<Long>> goodsExclude = goodsRuleVO.getGoodsExclude();
        if (MapUtils.isNotEmpty(goodsExclude)) {
            if (goodsExclude.containsKey(GoodsLevelEnum.Sku.getValue())) {
                goodsRuleDetailVO.setSkuExcludeList(this.searchSkuInfo(goodsExclude.get(GoodsLevelEnum.Sku.getValue()), areaId));
            }

            if (goodsExclude.containsKey(GoodsLevelEnum.Package.getValue())) {
                goodsRuleDetailVO.setPackageExcludeList(this.searchPackageInfo(goodsExclude.get(GoodsLevelEnum.Package.getValue())));
            }

            if (goodsExclude.containsKey(GoodsLevelEnum.Ssu.getValue())) {
                goodsRuleDetailVO.setSsuExcludeList(this.searchSsuInfo(goodsExclude.get(GoodsLevelEnum.Ssu.getValue()), areaId, channel));
            }
        }

        if (PromotionTypeEnum.NyuanBuy.getValue() == promotionType) {
            if (CollectionUtils.isNotEmpty(goodsRuleDetailVO.getSkuIncludeList())) {
                SkuInfoVO skuInfoVO = goodsRuleDetailVO.getSkuIncludeList().stream().max(Comparator.comparing(SkuInfoVO::getMarketPrice)).get();
                goodsRuleDetailVO.setMaxPriceSku(skuInfoVO);
            }
            if (CollectionUtils.isNotEmpty(goodsRuleDetailVO.getPackageIncludeList())) {
                PackageInfoVO packageInfoVO = goodsRuleDetailVO.getPackageIncludeList().stream().max(Comparator.comparing(PackageInfoVO::getMarketPrice)).get();
                goodsRuleDetailVO.setMaxPricePackage(packageInfoVO);
            }
        }

        return goodsRuleDetailVO;
    }


    /**
     * 查询sku信息（支持普通商品 + 运营商泛全商品），支持国际
     *
     * @param skuList sku
     * @return sku信息
     * @throws Exception 业务异常
     */
    @Override
    public List<SkuInfoVO> searchSkuInfo(List<Long> skuList, String areaId) throws Exception {
        // question for i18n
        if (I18nUtil.isI18n()) {
            List<SsuMultiChannelDTO> ssuMultiChannelDTOS = i18nGmsProxyService.queryBySku(skuList, areaId);
            return goodsConvert.convert2SkuInfoDTOI18n(ssuMultiChannelDTOS);
        }
        if (CollectionUtils.isEmpty(skuList)) {
            return Collections.emptyList();
        }
        List<SkuInfoDto> skuInfoDtoList = gmsProxyService.queryListBySkuIds(skuList, false, true, false, Lists.newArrayList(BizSubTypeEnum.ORDINARY_3C.getCode(), BizSubTypeEnum.CARRIER_PAN_COMPLETE.getCode()));
        return goodsConvert.convert2SkuInfoVO(skuInfoDtoList);
    }

    /**
     * 查询车商城ssu信息
     *
     * @param ssuList ssu
     * @return sku信息
     * @throws Exception 业务异常
     */
    private List<SkuInfoVO> searchSkuInfoForCarShop(List<Long> ssuList) throws Exception {
        if (CollectionUtils.isEmpty(ssuList)) {
            return Collections.emptyList();
        }
        Map<Long, GoodsMultiInfoDTO> goodsMultiInfoDTOMap = gisProxyService.queryGoodsInfoBySsuIds(ssuList, true);
        return goodsConvert.convert2SkuInfoVO(goodsMultiInfoDTOMap);
    }

    /**
     * 查询套装信息
     *
     * @param packageList 套装id
     * @return 套装信息
     * @throws Exception 业务异常
     */
    @Override
    public List<PackageInfoVO> searchPackageInfo(List<Long> packageList) throws Exception {
        if (CollectionUtils.isEmpty(packageList)) {
            return Lists.newArrayList();
        }
        List<BatchedInfoDto> packageInfoDtoList = gmsProxyService.queryListByPackageIds(packageList, false);
        return goodsConvert.convert2PackageDto(packageInfoDtoList);
    }

    @Override
    public List<PackageInfoVO> searchSuitInfo(List<Long> suitList) throws Exception {
        if (CollectionUtils.isEmpty(suitList)) {
            return Collections.emptyList();
        }
        List<GoodsMultiInfoDTO> goodsMultiInfoDTOList = gisProxyService.queryGoodsMultiInfoByGoodsIds(suitList, GoodsItemTypeEnum.SUIT);
        return goodsConvert.convert2PackageInfoVO(goodsMultiInfoDTOList);
    }

    /**
     * 查询ssu
     *
     * @param ssuList ssu
     * @return ssu信息
     * @throws Exception 业务异常
     */
    @Override
    public List<SsuInfoVO> searchSsuInfo(List<Long> ssuList, String areaId, Integer channel) throws Exception {
        if (CollectionUtils.isEmpty(ssuList)) {
            return Collections.emptyList();
        }
        // question for i18n
        if (I18nUtil.checkI18n(areaId)) {

            return i18nGmsProxyService.getGoodsInfo(ssuList, areaId, channel);
        }
        List<SsuDTO> ssuDTOS = gmsProxyService.queryListBySsuIds(ssuList);
        return goodsConvert.convert2SsuInfoDTO(ssuDTOS);
    }

    /**
     * 多线程根据类目获取商品
     *
     * @param needMiSupportShipment 为true时，SkuInfoDto的参数miSupportShipment才有意义，
     *                              1. miSupportShipment = True, 代表米家大仓发货商品
     *                              2. miSupportShipment = False, 代表米家非大仓发货商品
     *                              3. miSupportShipment = null, 代表没有配置是否米家大仓发货属性
     * @param categoryIds
     * @return
     * @throws Exception
     */
    @Override
    public List<SkuInfoDto> getSkuByCategoryId(Set<Integer> categoryIds, List<Integer> businessType, boolean needMiSupportShipment, List<Integer> bizSubTypeList) throws Exception {
        List<Future<List<SkuInfoDto>>> futureList = new ArrayList<>();
        for (Integer categoryId : categoryIds) {
            Future<List<SkuInfoDto>> future = queryAsyncExecutor.submit(() -> {
                try {
                    return gmsProxyService.getProductDetailListByCate(categoryId, businessType, needMiSupportShipment, bizSubTypeList);
                } catch (Exception e) {
                    log.error("GoodsService getSkuByCategoryId Exception categoryId:{}", categoryId, e);
                    throw ExceptionHelper.create(GeneralCodes.InternalError, "查询sku失败");
                }
            });
            futureList.add(future);
        }

        List<SkuInfoDto> returnList = new ArrayList<>();
        for (Future<List<SkuInfoDto>> future : futureList) {
            if (future != null) {
                List<SkuInfoDto> infoDtoList = future.get();
                returnList.addAll(infoDtoList);
            }
        }

        return returnList;
    }


    /**
     * 获取预估折扣力度
     *
     * @param request
     * @return
     */
    @Override
    public GoodsDiscountLevelVO getGoodsDiscountLevel(GoodsDiscountLevelRequest request, Map<Long, Long> skuPriceMap, Map<Long, Long> packagePriceMap) throws Exception {
        GoodsDiscountLevelVO response = new GoodsDiscountLevelVO();
        response.setDiscountLevel(request.getDiscountLevel());

        BigDecimal discountLevel = request.getDiscountLevel();

        List<Integer> useChannel = request.getUseChannel();
        GoodsRuleVO goodsRuleVO = request.getGoodsRuleVO();

        PromotionRuleVO promotionRuleVO = request.getPromotionRuleVO();

        switch (PromotionTypeEnum.getByValue(promotionRuleVO.getPromotionType())) {
            case ConditionDiscount:
                BigDecimal conditionNowDiscountLevel = BigDecimal.valueOf(promotionRuleVO.getPromotionValue()).divide(BigDecimal.valueOf(100));
                if (conditionNowDiscountLevel.compareTo(request.getDiscountLevel()) < 0) {
                    response.setHasLowLevelGoods(true);
                    response.setNowDiscountLevel(conditionNowDiscountLevel);
                }
                break;
            case ConditionReduce:
                if (BottomTypeEnum.OverYuan.getValue() == promotionRuleVO.getBottomType()) {
                    BigDecimal nowDiscountLevel = getNowDiscountLevel(promotionRuleVO, 0);
                    if (discountLevel.compareTo(nowDiscountLevel) > 0) {
                        response.setHasLowLevelGoods(true);
                        response.setNowDiscountLevel(nowDiscountLevel);
                    }
                    break;
                }
            case GIFT:
            case NyuanBuy:
            case DirectReduce:
                Map<Long, GoodsInfoVO> skuInfoMap = getSkuInfos(goodsRuleVO, promotionRuleVO, discountLevel, useChannel, skuPriceMap);
                Map<Long, GoodsInfoVO> packageInfoMap = getPackageInfos(goodsRuleVO, promotionRuleVO, discountLevel, useChannel, packagePriceMap);
                if (MapUtils.isNotEmpty(skuInfoMap)) {
                    response.setHasLowLevelGoods(true);
                    response.setSkuInfoVOList(new LinkedList<>(skuInfoMap.values()));
                }
                if (MapUtils.isNotEmpty(packageInfoMap)) {
                    response.setHasLowLevelGoods(true);
                    response.setPackageInfoVOList(new LinkedList<>(packageInfoMap.values()));
                }
        }

        return response;
    }

    private Map<Long, GoodsInfoVO> getPackageInfos(GoodsRuleVO goodsRuleVO, PromotionRuleVO promotionRuleVO,
                                                   BigDecimal discountLevel, List<Integer> useChannel, Map<Long, Long> goodsPriceMap) throws Exception {
        Map<Long, GoodsInfoVO> goodsInfoMap = new HashMap<>();

        Map<String, List<Long>> goodsInclude = goodsRuleVO.getGoodsInclude();
        // 老套装
        if (goodsInclude.containsKey(GoodsLevelEnum.Package.getValue())) {
            for (Integer channel : useChannel) {
                Map<Long, BatchedChannelPriceDto> packageMap = gisProxyService.getBatchedChannelPriceMap(goodsInclude.get(GoodsLevelEnum.Package.getValue()), channel);
                if (MapUtils.isNotEmpty(packageMap)) {
                    packageMap.forEach((k, v) -> {
                        getGoodsMap(goodsInfoMap, promotionRuleVO, discountLevel, channel, k, v.getPrice(), goodsPriceMap, GoodsLevelEnum.Package.getValue());
                    });
                }
            }
        }

        // 新套装
        if (goodsInclude.containsKey(GoodsLevelEnum.Suit.getValue())) {
            List<Long> ssuIds = goodsInclude.get(GoodsLevelEnum.Suit.getValue());
            List<GoodsMultiInfoDTO> goodsMultiInfoDTOS = gisProxyService.queryGoodsMultiInfoByGoodsIds(ssuIds, GoodsItemTypeEnum.SUIT);
            for (Integer channel : useChannel) {
                for (GoodsMultiInfoDTO goodsMultiInfoDTO : goodsMultiInfoDTOS) {
                    getGoodsMap(goodsInfoMap, promotionRuleVO, discountLevel, channel, goodsMultiInfoDTO.getGoodsId(), goodsMultiInfoDTO.getPrice(), goodsPriceMap, GoodsLevelEnum.Suit.getValue());
                }
            }
        }

        return goodsInfoMap;
    }

    private Map<Long, GoodsInfoVO> getSkuInfos(GoodsRuleVO goodsRuleVO, PromotionRuleVO promotionRuleVO, BigDecimal discountLevel, List<Integer> useChannel, Map<Long, Long> goodsPriceMap) throws Exception {
        Map<Long, GoodsInfoVO> goodsInfoMap = new HashMap<>();

        Map<String, List<Long>> goodsInclude = goodsRuleVO.getGoodsInclude();
        if (goodsInclude.containsKey(GoodsLevelEnum.Sku.getValue())) {
            for (Integer channel : useChannel) {
                Map<Long, GoodsChannelPriceDto> skuMap = gisProxyService.getSkuChannelPriceMap(goodsInclude.get(GoodsLevelEnum.Sku.getValue()), channel);
                if (MapUtils.isNotEmpty(skuMap)) {
                    skuMap.forEach((k, v) -> {
                        getGoodsMap(goodsInfoMap, promotionRuleVO, discountLevel, channel, k, v.getPrice(), goodsPriceMap, GoodsLevelEnum.Sku.getValue());
                    });
                }
            }
        }
        return goodsInfoMap;
    }

    private void getGoodsMap(Map<Long, GoodsInfoVO> goodsInfoMap, PromotionRuleVO promotionRuleVO, BigDecimal discountLevel,
                             Integer channel, Long goodsId, Long price, Map<Long, Long> goodsPriceMap, String goodsLevel) {
        BigDecimal nowDiscountLevel = getNowDiscountLevel(promotionRuleVO, price);
        if (discountLevel.compareTo(nowDiscountLevel) > 0) {
            if (!goodsInfoMap.containsKey(goodsId)) {
                GoodsInfoVO goodsInfoVO = new GoodsInfoVO();
                goodsInfoVO.setGoodsId(goodsId);
                goodsInfoVO.setGoodsChannelVOs(new LinkedList<>());
                switch (goodsLevel) {
                    case "sku":
                        goodsInfoVO.setLevel(GoodsLevelEnum.Sku.getValue());
                        break;
                    case "package":
                        // 老套装
                        goodsInfoVO.setLevel(GoodsLevelEnum.Package.getValue());
                        goodsInfoVO.setSsuType(GoodsItemTypeEnum.PACKAGE.getValue());
                        break;
                    case "suit":
                        // 新套装
                        goodsInfoVO.setLevel(GoodsLevelEnum.Ssu.getValue());
                        goodsInfoVO.setSsuType(GoodsItemTypeEnum.SUIT.getValue());
                        break;
                    default:
                        // 处理未匹配的情况
                        break;
                }

                goodsInfoMap.put(goodsId, goodsInfoVO);
            }
            GoodsChannelVO goodsChannelVO = new GoodsChannelVO();
            goodsChannelVO.setUseChannel(channel);
            goodsChannelVO.setUseChannelName(UseChannelsEnum.getByValue(channel).getName());
            goodsChannelVO.setPrice(BigDecimal.valueOf(price).divide(BigDecimal.valueOf(100)));
            goodsChannelVO.setDiscount(nowDiscountLevel);
            goodsInfoMap.get(goodsId).getGoodsChannelVOs().add(goodsChannelVO);
            goodsPriceMap.put(goodsId, price);
        }
    }

    /**
     * 获取折扣力度
     */
    private BigDecimal getNowDiscountLevel(PromotionRuleVO promotionRuleVO, long price) {
        switch (PromotionTypeEnum.getByValue(promotionRuleVO.getPromotionType())) {
            case ConditionReduce:
                if (BottomTypeEnum.OverYuan.getValue() == promotionRuleVO.getBottomType()) {
                    BigDecimal discount = BigDecimal.ONE.subtract(BigDecimal.valueOf(promotionRuleVO.getPromotionValue()).
                            divide(BigDecimal.valueOf(promotionRuleVO.getBottomPrice()), 2, BigDecimal.ROUND_HALF_UP));
                    return BigDecimal.ZERO.compareTo(discount) > 0 ? BigDecimal.ZERO : discount.multiply(BigDecimal.TEN);
                } else if (BottomTypeEnum.OverCount.getValue() == promotionRuleVO.getBottomType()) {
                    BigDecimal priceN = BigDecimal.valueOf(price).multiply(BigDecimal.valueOf(promotionRuleVO.getBottomCount()));
                    BigDecimal discount = (price == 0 ? BigDecimal.ZERO : BigDecimal.ONE.subtract(BigDecimal.valueOf(promotionRuleVO.getPromotionValue()).
                            divide(priceN, 2, BigDecimal.ROUND_HALF_UP)));
                    return BigDecimal.ZERO.compareTo(discount) > 0 ? BigDecimal.ZERO : discount.multiply(BigDecimal.TEN);
                }
            case ConditionDiscount:
                BigDecimal discount = BigDecimal.valueOf(promotionRuleVO.getPromotionValue()).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP);
                return BigDecimal.ZERO.compareTo(discount) > 0 ? BigDecimal.ZERO : discount;
            case NyuanBuy:
                BigDecimal nyuanBuyDiscount = (price == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(promotionRuleVO.getPromotionValue()).
                        divide(BigDecimal.valueOf(price), 2, BigDecimal.ROUND_HALF_UP));
                return BigDecimal.ZERO.compareTo(nyuanBuyDiscount) > 0 ? BigDecimal.ZERO : nyuanBuyDiscount.multiply(BigDecimal.TEN);
            case DirectReduce:
                BigDecimal directReduceDiscount = (price == 0 ? BigDecimal.ZERO : BigDecimal.ONE.subtract(BigDecimal.valueOf(promotionRuleVO.getPromotionValue()).
                        divide(BigDecimal.valueOf(price), 2, BigDecimal.ROUND_HALF_UP)));
                return BigDecimal.ZERO.compareTo(directReduceDiscount) > 0 ? BigDecimal.ZERO : directReduceDiscount.multiply(BigDecimal.TEN);
            case GIFT:
                return BigDecimal.ZERO;

        }
        return BigDecimal.ZERO;
    }


    /**
     * 生成商品表格
     *
     * @return
     * @throws Exception
     */
    public List<GoodsPriceVO> getGoodsPriceVO(Map<String, List<Long>> goodsInclude) throws Exception {
        List<GoodsPriceVO> goodsSuitableInfoList = new LinkedList<>();
        List<Long> skuList = goodsInclude.get(GoodsLevelEnum.Sku.getValue());
        List<Long> packageList = goodsInclude.get(GoodsLevelEnum.Package.getValue());
        List<SkuInfoDto> skuInfoDtoList = null;
        List<BatchedInfoDto> batchedInfoDtoList = null;
        if (skuList.size() + packageList.size() <= CouponConstant.COUPON_GOODS_LIMIT) {
            skuInfoDtoList = gmsProxyService.queryListBySkuIds(skuList, false, false, false, Lists.newArrayList(BizSubTypeEnum.ORDINARY_3C.getCode()));
            skuInfoDtoList.forEach(skuInfo -> goodsSuitableInfoList.add(new GoodsPriceVO(skuInfo.getGoodsName(), NumberUtil.centToYuanBigDecimal(skuInfo.getMarketPrice(), 2, RoundingMode.DOWN))));
            batchedInfoDtoList = gmsProxyService.queryListByPackageIds(packageList, false);
            batchedInfoDtoList.forEach(batchedInfo -> goodsSuitableInfoList.add(new GoodsPriceVO(batchedInfo.getBatchedName(), NumberUtil.centToYuanBigDecimal(batchedInfo.getMarketPriceMax(), 2, RoundingMode.DOWN))));
        } else {
            if (skuList.size() >= CouponConstant.COUPON_GOODS_LIMIT) {
                skuInfoDtoList = gmsProxyService.queryListBySkuIds(skuList.subList(0, CouponConstant.COUPON_GOODS_LIMIT), false, false, false, Lists.newArrayList(BizSubTypeEnum.ORDINARY_3C.getCode()));
                skuInfoDtoList.forEach(skuInfo -> goodsSuitableInfoList.add(new GoodsPriceVO(skuInfo.getGoodsName(), NumberUtil.centToYuanBigDecimal(skuInfo.getMarketPrice(), 2, RoundingMode.DOWN))));
            } else {
                skuInfoDtoList = gmsProxyService.queryListBySkuIds(skuList, false, false, false, Lists.newArrayList(BizSubTypeEnum.ORDINARY_3C.getCode()));
                skuInfoDtoList.forEach(skuInfo -> goodsSuitableInfoList.add(new GoodsPriceVO(skuInfo.getGoodsName(), NumberUtil.centToYuanBigDecimal(skuInfo.getMarketPrice(), 2, RoundingMode.DOWN))));
                batchedInfoDtoList = gmsProxyService.queryListByPackageIds(packageList.subList(0, CouponConstant.COUPON_GOODS_LIMIT - skuList.size()), false);
                batchedInfoDtoList.forEach(batchedInfo -> goodsSuitableInfoList.add(new GoodsPriceVO(batchedInfo.getBatchedName(), NumberUtil.centToYuanBigDecimal(batchedInfo.getMarketPriceMax(), 2, RoundingMode.DOWN))));
            }
        }
        return goodsSuitableInfoList;
    }

    /**
     * 多线程根据类目获取商品
     *
     * @param categoryIds
     * @return
     * @throws Exception
     */
    @Override
    public Map<Long, List<SkuInfoDto>> getSkuMapByCategoryId(List<Integer> categoryIds, List<Integer> bussinessTypes, boolean needMiSupportShipment) throws Exception {
        List<Future<List<SkuInfoDto>>> futureList = new ArrayList<>();
        for (Integer categoryId : categoryIds) {
            Future<List<SkuInfoDto>> future = queryAsyncExecutor.submit(() -> {
                try {
                    return gmsProxyService.getProductDetailListByCate(categoryId, bussinessTypes, needMiSupportShipment, Lists.newArrayList(BizSubTypeEnum.ORDINARY_3C.getCode()));
                } catch (Exception e) {
                    log.error("GoodsService getSkuByCategoryId Exception categoryId:{}", categoryId, e);
                    throw ExceptionHelper.create(GeneralCodes.InternalError, "查询sku失败");
                }
            });
            futureList.add(future);
        }

        Map<Long, List<SkuInfoDto>> returnMap = new HashMap<>(categoryIds.size());
        for (Future<List<SkuInfoDto>> future : futureList) {
            if (future != null) {
                List<SkuInfoDto> infoDtoList = future.get();
                if (CollectionUtils.isNotEmpty(infoDtoList)) {
                    returnMap.put(infoDtoList.get(0).getCategory().getThirdCategoryId(), infoDtoList);
                }
            }
        }

        return returnMap;
    }


    public boolean filterSku(Integer couponType, SkuInfoDto skuInfoDto, List<UploadGoodsFailPO> uploadGoodsFailPOS) {
        //商品券 支持自营和采销；运费券支持自营,米家非大仓
        if (CouponTypeEnum.POSTFREE.getValue().equals(couponType)) {
            if (skuInfoDto.getMiSupportShipment() == null || skuInfoDto.getMiSupportShipment()) {
                if (uploadGoodsFailPOS != null) {
                    uploadGoodsFailPOS.add(new UploadGoodsFailPO(skuInfoDto.getSku(), "运费券只能配置米家非大仓的商品"));
                }
                return false;
            }
            if (CouponConstant.BUSINESS_TYPE_SELF != skuInfoDto.getBusinessType()) {
                if (uploadGoodsFailPOS != null) {
                    uploadGoodsFailPOS.add(new UploadGoodsFailPO(skuInfoDto.getSku(), "非自商品"));
                }
                return false;
            }
            return true;
        } else if (CouponTypeEnum.GOODS.getValue().equals(couponType) || CouponTypeEnum.SUBSIDY.getValue().equals(couponType)) {
            if (CouponConstant.BUSINESS_TYPE_SELF != skuInfoDto.getBusinessType() && CouponConstant.BUSINESS_TYPE_CAIXIAO != skuInfoDto.getBusinessType()) {
                if (uploadGoodsFailPOS != null) {
                    uploadGoodsFailPOS.add(new UploadGoodsFailPO(skuInfoDto.getSku(), "非自非采销商品"));
                }
                return false;
            }
            return true;
        }
        return false;
    }

    public boolean filterPackage(BatchedInfoDto batchedInfoDto, List<UploadGoodsFailPO> uploadGoodsFailPOS) {
        if (CouponConstant.BUSINESS_TYPE_SELF != batchedInfoDto.getBusinessType() && CouponConstant.BUSINESS_TYPE_CAIXIAO != batchedInfoDto.getBusinessType()) {
            if (uploadGoodsFailPOS != null) {
                uploadGoodsFailPOS.add(new UploadGoodsFailPO(batchedInfoDto.getBatchedId(), "非自非采销商品"));
            }
            return false;
        }
        return true;
    }

    @Override
    public boolean filterSuit(GoodsMultiInfoDTO goodsMultiInfoDTO, List<UploadGoodsFailPO> uploadGoodsFailPOS) {
        return true;
    }

    public Map<Long, Map<Integer, GoodsCouponPromVO>> matchGoodsCouponType(Map<Long, Long> skuPriceMap, Map<Long, Long> packagePriceMap, GoodsDiscountLevelRequest request) throws Exception {

        if (!Objects.equals(CouponTypeEnum.SUBSIDY.getValue(), request.getCouponType()) || (MapUtils.isEmpty(skuPriceMap) && MapUtils.isEmpty(skuPriceMap))) {
            return null;
        }

        boolean isRelativeTime = Objects.equals(UseTimeTypeEnum.RELATIVE.getValue(), request.getUseTermVO().getUseTimeType());

        int size = skuPriceMap.size() + packagePriceMap.size();
        Set<Long> configIds = Sets.newHashSet();
        Map<Long, Set<Long>> goodsMap = Maps.newHashMapWithExpectedSize(size);
        Map<Long, Long> goodsPriceMap = Maps.newHashMapWithExpectedSize(size);

        if (MapUtils.isNotEmpty(skuPriceMap)) {
            SearchConfigParam param = buildSearchConfigParam(request.getUseChannel(), request.getUseTermVO());
            param.setLevel(GoodsLevelEnum.Sku.getValue());
            Map<Long, Set<Long>> skuMap = couponConfigRepository.getGoodCouponId(param, isRelativeTime, Lists.newArrayList(skuPriceMap.keySet()));
            skuMap.values().forEach(configIds::addAll);
            goodsMap.putAll(skuMap);
            goodsPriceMap.putAll(skuPriceMap);
        }

        if (MapUtils.isNotEmpty(packagePriceMap)) {
            SearchConfigParam param = buildSearchConfigParam(request.getUseChannel(), request.getUseTermVO());
            param.setLevel(GoodsLevelEnum.Package.getValue());
            Map<Long, Set<Long>> packageMap = couponConfigRepository.getGoodCouponId(param, isRelativeTime, Lists.newArrayList(packagePriceMap.keySet()));
            packageMap.values().forEach(configIds::addAll);
            goodsMap.putAll(packageMap);
            goodsPriceMap.putAll(packagePriceMap);
        }


        Map<Long, ConfigInfoCachePo> configMap = couponConfigRepository.batchGetConfigInfo(configIds);

        return sortMatchCouponType(goodsMap, configMap, goodsPriceMap);
    }

    @Override
    public List<SkuInfoVO> buildSkuInfoBySkuIds(List<Long> skuIds, boolean needMiSupportShipment, List<Long> existSkuIds,
                                                Integer couponType, List<UploadGoodsFailPO> uploadGoodsFailPOS) throws Exception{
        try {
            // question for i18n
            if (I18nUtil.isI18n()) {
                return buildSkuInfoBySkuIdsI18n(skuIds, existSkuIds, uploadGoodsFailPOS);
            }
            List<SkuInfoDto> skuInfoDtos = gmsProxyService.queryListBySkuIds(
                    skuIds,
                    false,
                    true,
                    needMiSupportShipment,
                    org.assertj.core.util.Lists.newArrayList(BizSubTypeEnum.ORDINARY_3C.getCode(), BizSubTypeEnum.CARRIER_PAN_COMPLETE.getCode()));
            List<SkuInfoVO> skuInfoList = new ArrayList<>(skuInfoDtos.size());
            for (SkuInfoDto skuInfoDto : skuInfoDtos) {
                existSkuIds.add(skuInfoDto.getSku());
                if (this.filterSku(couponType, skuInfoDto, uploadGoodsFailPOS)) {
                    SkuInfoVO skuInfoVO = new SkuInfoVO();
                    BeanMapper.copy(skuInfoDto, skuInfoVO);
                    if (skuInfoDto.getCategory() != null) {
                        BeanMapper.copy(skuInfoDto.getCategory(), skuInfoVO);
                    }
                    skuInfoList.add(skuInfoVO);
                }
            }
            return skuInfoList;
        } catch (Exception e) {
            log.error("上传商品解析失败，", e);
            throw ExceptionHelper.create(ErrCode.COUPON, "上传商品解析失败", e);
        }

    }

    private List<SkuInfoVO> buildSkuInfoBySkuIdsI18n(
            List<Long> skuIds,
            List<Long> existSkuIds,
            List<UploadGoodsFailPO> uploadGoodsFailPOS) throws Exception{
        List<SsuMultiChannelDTO> ssuInfoDTOS = i18nGmsProxyService.queryBySku(skuIds, I18nUtil.getGlobalAreaId());
        List<SkuInfoVO> skuInfoList = new ArrayList<>(ssuInfoDTOS.size());
        for (SsuMultiChannelDTO ssuInfoDTO : ssuInfoDTOS) {
            existSkuIds.add(ssuInfoDTO.getSku());
            SkuInfoVO skuInfoVO = convertToSkuInfoVO(ssuInfoDTO);
            skuInfoList.add(skuInfoVO);
        }
        return skuInfoList;
    }


    /**
     * 匹配出当前商品不同折扣类型下的最优券
     *
     * @param goodsMap  商品对应券id
     * @param configMap 券配置信息
     * @param priceMap  商品价格信息
     * @return 商品对应折扣力度最高商品券
     */
    private Map<Long, Map<Integer, GoodsCouponPromVO>> sortMatchCouponType(Map<Long, Set<Long>> goodsMap, Map<Long, ConfigInfoCachePo> configMap, Map<Long, Long> priceMap) {

        Map<Long, Map<Integer, GoodsCouponPromVO>> goodsCouponMap = Maps.newHashMapWithExpectedSize(goodsMap.size());

        for (Map.Entry<Long, Set<Long>> item : goodsMap.entrySet()) {

            for (Long id : item.getValue()) {

                if (Objects.isNull(configMap.get(id))) {
                    continue;
                }

                ConfigInfoCachePo po = configMap.get(id);
                BigDecimal nowDiscount = getCouponDiscountLevel(po, priceMap.get(item.getKey()));
                PromotionRuleVO prom = new PromotionRuleVO(po.getPromotionValue(), po.getBottomPrice(), po.getBottomCount(), po.getBottomType());
                List<Integer> useChannel = Arrays.stream(po.getUseChannel().split(",")).map(Integer::parseInt).collect(Collectors.toList());

                if (!goodsCouponMap.containsKey(item.getKey())) {
                    Map<Integer, GoodsCouponPromVO> couponMap = new HashMap<>();
                    couponMap.put(po.getPromotionType(), GoodsCouponPromVO.builder().promotionRuleVO(prom).configId(po.getId()).discountLevel(nowDiscount).useChannel(useChannel).build());
                    goodsCouponMap.put(item.getKey(), couponMap);
                    continue;
                }

                if (!goodsCouponMap.get(item.getKey()).containsKey(po.getPromotionType())) {
                    goodsCouponMap.get(item.getKey()).put(po.getPromotionType(), GoodsCouponPromVO.builder().promotionRuleVO(prom).configId(po.getId()).discountLevel(nowDiscount).useChannel(useChannel).build());
                    continue;
                }

                BigDecimal max = goodsCouponMap.get(item.getKey()).get(po.getPromotionType()).getDiscountLevel();
                if (nowDiscount.compareTo(max) < CommonConstant.ZERO_INT) {
                    goodsCouponMap.get(item.getKey()).put(po.getPromotionType(), GoodsCouponPromVO.builder().promotionRuleVO(prom).configId(po.getId()).discountLevel(nowDiscount).useChannel(useChannel).build());
                }
            }
        }

        return goodsCouponMap;
    }


    /**
     * 获取优惠券折扣力度
     *
     * @param cachePo 券信息
     * @param price   商品价格
     * @return 折扣力度
     */
    private BigDecimal getCouponDiscountLevel(ConfigInfoCachePo cachePo, long price) {
        switch (PromotionTypeEnum.getByValue(cachePo.getPromotionType())) {
            case ConditionReduce:
                if (BottomTypeEnum.OverYuan.getValue() == cachePo.getBottomType()) {
                    BigDecimal discount = BigDecimal.ONE.subtract(BigDecimal.valueOf(cachePo.getPromotionValue()).
                            divide(BigDecimal.valueOf(cachePo.getBottomPrice()), CommonConstant.TWO_INT, BigDecimal.ROUND_HALF_UP));
                    return BigDecimal.ZERO.compareTo(discount) > CommonConstant.ZERO_INT ? BigDecimal.ZERO : discount.multiply(BigDecimal.TEN);
                } else if (BottomTypeEnum.OverCount.getValue() == cachePo.getBottomType()) {
                    BigDecimal priceN = BigDecimal.valueOf(price).multiply(BigDecimal.valueOf(cachePo.getBottomCount()));
                    BigDecimal discount = (price == CommonConstant.ZERO_LONG ? BigDecimal.ZERO : BigDecimal.ONE.subtract(BigDecimal.valueOf(cachePo.getPromotionValue()).
                            divide(priceN, CommonConstant.TWO_INT, BigDecimal.ROUND_HALF_UP)));
                    return BigDecimal.ZERO.compareTo(discount) > CommonConstant.ZERO_INT ? BigDecimal.ZERO : discount.multiply(BigDecimal.TEN);
                }

            case ConditionDiscount:
                BigDecimal discount = BigDecimal.valueOf(cachePo.getPromotionValue()).divide(BigDecimal.valueOf(100L), CommonConstant.TWO_INT, BigDecimal.ROUND_HALF_UP);
                return BigDecimal.ZERO.compareTo(discount) > CommonConstant.ZERO_INT ? BigDecimal.ZERO : discount;

            case DirectReduce:
                BigDecimal directReduceDiscount = (price == CommonConstant.ZERO_LONG ? BigDecimal.ZERO : BigDecimal.ONE.subtract(BigDecimal.valueOf(cachePo.getPromotionValue()).
                        divide(BigDecimal.valueOf(price), CommonConstant.TWO_INT, BigDecimal.ROUND_HALF_UP)));
                return BigDecimal.ZERO.compareTo(directReduceDiscount) > CommonConstant.TWO_INT ? BigDecimal.ZERO : directReduceDiscount.multiply(BigDecimal.TEN);
        }
        return BigDecimal.ZERO;
    }


    /**
     * 构建ES搜索参数
     *
     * @param useChannel 使用渠道
     * @param useTime    使用时间类型
     * @return SearchConfigParam
     */
    private SearchConfigParam buildSearchConfigParam(List<Integer> useChannel, UseTermVO useTime) {

        SearchConfigParam param = new SearchConfigParam();
        param.setUseChannel(useChannel);

        if (Objects.equals(UseTimeTypeEnum.ABSOLUTE.getValue(), useTime.getUseTimeType())) {
            param.setStartUseTime(useTime.getStartUseTime().getTime() / 1000);
            param.setEndUseTime(useTime.getEndUseTime().getTime() / 1000);
        }

        return param;
    }


}