package com.xiaomi.nr.coupon.admin.infrastructure.repository.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CouponDataStatisticPO {


    @ExcelProperty("券Id")
    private	Integer configId;

    @ExcelProperty("任务Id")
    private	Integer activityId;

    @ExcelProperty("券名称")
    private	String name;

    @ExcelProperty("折扣类型")
    private String promotionTypeName;

    @ExcelProperty("面额/使用条件")
    private String promotionDesc;

    @ExcelProperty("投放场景")
    private String sendScene;

    @ExcelProperty("投放方式")
    private String sendType;

    @ExcelProperty("计划发放数量")
    private Integer applyCount;

    @ExcelProperty("领取数量")
    private Integer sendCount;

    @ExcelProperty("使用数量")
    private Long useCount;

    @ExcelProperty("使用率")
    private String useRatio;

    @ExcelProperty("累计领取数量")
    private Integer totalSendCount;

    @ExcelProperty("累计使用数量")
    private Long totalUseCount;

    @ExcelProperty("累计使用率")
    private String totalUseRatio;

    @ExcelProperty("使用渠道")
    private	String useChannel;

    @ExcelProperty("优惠金额")
    private BigDecimal reduceAmount;

    @ExcelProperty("订单金额")
    private BigDecimal orderAmount;

    @ExcelProperty("ROI")
    private String roi;

}
