package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.coupon;


import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.coupon.po.CouponCodePO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.coupon.po.SearchCodeListParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 优惠码mapper
 *
 * <AUTHOR>
 */
@Mapper
@Component
public interface UserCouponCodeMapper {


    /**
     * 根据券配置id获取优惠码信息
     *
     * @param configId 券配置id
     * @param start
     * @param pageSize
     * @return
     */
    @Select("select coupon_code,stat,end_time from tb_codecoupon where type_id=#{configId} order by id asc limit #{start},#{pageSize}")
    List<CouponCodePO> getCouponCodeByConfigId(@Param("configId") long configId, @Param("start") int start, @Param("pageSize") int pageSize);


    /**
     * 根据参数查询优惠码信息
     *
     * @param searchCodeListParam searchCodeListParam
     * @return List<>
     */
    @Select("<script>" +
            "select coupon_code,stat,type_id,user_id,use_time,end_time,use_mode,coupon_id,order_id " +
            " from tb_codecoupon where id!=0 " +
            "<if test='couponIndex != null'> and coupon_index=#{couponIndex} </if>" +
            "<if test='userId>0 and userId != null'> and user_id=#{userId} </if>" +
            "<if test='stat!=null'> and stat=#{stat} </if>" +
            "<if test='useMode>0'> and use_mode=#{useMode} </if>" +
            " order by add_time desc " +
            "</script>")
    List<CouponCodePO> getCodeListByParam(SearchCodeListParam searchCodeListParam);

    /**
     * 获取未同步的优惠券代码列表
     *
     * @param sendType      优惠券发送类型
     * @param nowUnixSecond 当前的Unix时间戳（秒）
     * @return 未同步的优惠券代码列表
     */
    @Select("select * from tb_codecoupon where send_type = #{sendType} and sync_status = 0 and start_time < #{nowUnixSecond} and #{nowUnixSecond} < end_time order by type_id limit 1000")
    List<CouponCodePO> getNotSyncCouponCodes(@Param("sendType") String sendType, @Param("nowUnixSecond") long nowUnixSecond);

    /**
     * 根据优惠券代码和索引获取优惠券信息
     *
     * @param couponCode  优惠券代码
     * @param couponIndex 优惠券索引
     * @return 优惠券信息
     */
    @Select("select * from tb_codecoupon where coupon_code = #{couponCode} and coupon_index = #{couponIndex}")
    CouponCodePO getByCouponCode(@Param("couponCode") String couponCode, @Param("couponIndex") String couponIndex);

    /**
     * 根据优惠券索引列表查询优惠券信息
     *
     * @param couponIndexList 优惠券索引列表
     * @param limit           查询结果的限制条数
     * @param offset          查询结果的偏移量
     * @return 优惠券信息列表
     */
    @Select("<script>select * from tb_codecoupon " +
            "where coupon_index in <foreach item='couponIndex' index='index' collection='couponIndexList' open='(' separator=',' close=')'>#{couponIndex}</foreach> " +
            "order by id limit #{limit} offset #{offset}" +
            "</script>")
    List<CouponCodePO> getByCouponIndex(@Param("couponIndexList") List<String> couponIndexList, @Param("limit") Integer limit, @Param("offset") Integer offset);
}
