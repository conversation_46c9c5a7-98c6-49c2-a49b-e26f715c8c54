package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.bdmall.po;

import com.google.gson.annotations.SerializedName;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.xmstore.po.XmStoreGoodsItemPO;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: he<PERSON><PERSON><PERSON>
 * @Date 2022/3/29 9:48 上午
 * @Version: 1.0
 **/
@Data
public class PolicyPO {

    private String code;

    private Integer value;

    @SerializedName("reduce_money")
    private String reduceMoney;

    @SerializedName("reduce_discount")
    private String reduceDiscount;

    @SerializedName("max_price")
    private String maxPrice;

    @SerializedName("target_goods")
    private List<XmStoreGoodsItemPO> targetGoods;
}
