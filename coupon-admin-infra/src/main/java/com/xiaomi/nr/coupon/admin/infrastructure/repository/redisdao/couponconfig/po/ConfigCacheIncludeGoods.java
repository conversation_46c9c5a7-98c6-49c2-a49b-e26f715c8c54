package com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.po;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 优惠券配置缓存-可用商品原始配置
 *
 * <AUTHOR>
 */
@Deprecated
@Data
public class ConfigCacheIncludeGoods implements Serializable {

    private static final long serialVersionUID = -4098947448580613155L;

    /**
     * 品类
     */
    private List<Long> group;

    /**
     * SKU
     */
    private List<Long> sku;

    /**
     * 货品
     */
    private List<Long> goods;

    /**
     * 套装
     */
    private List<Long> packages;

    /**
     * 所有商品
     */
    private Boolean all;

    /**
     * 券配置编辑时间
     */
    private Long modifyIndex;
}
