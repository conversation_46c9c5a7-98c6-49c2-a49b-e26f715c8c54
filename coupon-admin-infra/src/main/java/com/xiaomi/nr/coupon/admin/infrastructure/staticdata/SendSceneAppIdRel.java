package com.xiaomi.nr.coupon.admin.infrastructure.staticdata;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @description: 使用渠道和client对应关系数据
 * @author: he<PERSON><PERSON><PERSON>
 * @Date 2022/3/12 5:08 下午
 * @Version: 1.0
 **/
@Component
public class SendSceneAppIdRel {

    private static Map<String, String> result = new HashMap<>();

    static{
        //商城营销活动
        result.put("101", "136A560EF374DC3F6945EB3325AE28CD");
        //店长券活动
        //result.put("XM2106", "7C0938DFA6023303195221E669AFDE24");
        //门店下单赠券活动
        //result.put("1011", "0FBBAE2FD347172EFF6669E3BA1F82D4");
        result.put("XM2057", "0FBBAE2FD347172EFF6669E3BA1F82D4");
        //result.put("XM2058", "0FBBAE2FD347172EFF6669E3BA1F82D4");
        result.put("XM2059", "0FBBAE2FD347172EFF6669E3BA1F82D4");
        //门店换新活动
        result.put("XM2095", "92BA19A96694E3FD53690E3FFA03981D");
        //其他灌券活动
        result.put("104", "7FEC45995702920286B3E8D1850CAB73");
        //门店换异业合作
        result.put("XM2106", "504E45C3444F637331C8E6995F5725BE");
        //小米金融合作
        result.put("1009", "C08278EEA7CF3A376D49E7DA1BF269CA");


        //大市场部合作
        //result.put("104", "94CFD51E9EBB986E0F15EF6669499431");
        //服务部合作
        //result.put("104", "5AD763EFACA237DA4726F63E9BF22EF6");
        //小米社区合作
        result.put("XM2097", "809B71DB0C5D3D69000DFF0042B5DED7");

        //门店补差
        result.put("1011", "818ADB0168079DA36E5BAFA1D36267C6");
        result.put("XM2058", "818ADB0168079DA36E5BAFA1D36267C6");
        //客诉补偿
        result.put("XM2049", "566427EBF2814E7F70FA44EDF8900257");
        //换新保服务
        result.put("XM2072", "86ED65E673EC3C8E429D4A011F9A7C0B");
        //测试专用
        result.put("XM2107", "DCCC8C085E79839661D9B93E9A996EC8");
        //新员工券
        result.put("XM2040", "3C4675D2084760F374C666AB6AD9D224");
        //校园招聘券
        //result.put("", "A306A1AD9AB1C2925F7DBEA051858C09");
    }

    /**
     * 获取使用与client_id关系
     *
     * @return Map<String, String>
     */
    public Map<String, String> getSendSceneAppIdRel() {
        return result;
    }

}
