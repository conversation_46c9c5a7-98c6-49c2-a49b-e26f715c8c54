/**
 * Autogenerated by Thrift Compiler (0.9.2)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.xiaomi.nr.coupon.admin.infrastructure.rpc.sdk;

import org.mi.thrift.protocol.*;
import org.mi.thrift.protocol.TProtocolException;
import org.mi.thrift.rpc.*;
import org.mi.thrift.transport.*;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.concurrent.ConcurrentHashMap;


public class SidHostWrapper { 
 
    private String host_ = null; 
    private int port_ = 0; 
 
    private int connTimeout_ = 1000; 
    private int readTimeout_ = 1000; 
 
    private static int MaxConnRetry = 3; 
    private static int DefReadTimeout = 1000; 
    private int maxRetry_ = 0; 
 
    private String transportName_ = "xm_header"; 
    private String protocolName_ = "binary"; 
 
    private ConcurrentHashMap<String, Integer> timeoutMap_ = new ConcurrentHashMap<String, Integer>(); 
    private TConnectionPool pool_ = TConnectionPool.getInstance(); 
    private XContext context_ = null; 
 
    public SidHostWrapper(String host, int port, long logId) { 
        host_ = host; 
        port_ = port; 
 
        pool_ = TConnectionPool.getInstance(); 
        context_ = new XContext("JavaByHost", logId, 0); 
    } 
 
    public SidHostWrapper(String host, int port) { 
        this(host, port, 0); 
    } 
 
 
    public void setAppId(String appId) { 
        context_.setAppId(appId); 
    } 
 
    public void setLogId(long logId) { 
        context_.setLogId(logId); 
    } 
 
    public void setTransportName(String name) { 
        transportName_ = name; 
    } 
 
    public void setProtocolName(String name) { 
        protocolName_ = name; 
    } 
 
    public void setConnTimeout(int timeout) { 
        connTimeout_ = timeout; 
    } 
 
    public void setReadTimeout(int timeout) { 
        readTimeout_ = timeout; 
    } 
 
    public void setMaxRetry(int retry) { 
        maxRetry_ = retry; 
    } 
 
    public String getHostIP(String host) throws UnknownHostException{ 
 
        InetAddress addr = InetAddress.getByName(host_); 
        return addr.getHostAddress(); 
 
    } 
 
    public void setMethodTimeout(String method, int timeout) { 
        if (timeout <= 0) { 
            return ; 
        } 
 
        timeoutMap_.put(method, new Integer(timeout)); 
    } 
 
    public int getTimeout(String method) { 
 
        int timeout = DefReadTimeout; 
        Integer timeoutInt = timeoutMap_.get(method); 
 
        if (timeoutInt != null) { 
            timeout = timeoutInt.intValue(); 
        } else { 
            if (readTimeout_ > 0) { 
                timeout = readTimeout_; 
            } 
        } 
 
        if (timeout <= 0) { 
            timeout = DefReadTimeout; 
        } 
 
        return timeout; 
    } 
 
    public TSocket getConn(String method) throws Exception  { 
 
        TSocket sock = null; 
        Exception save = null; 
        for (int i = 0; i < MaxConnRetry; i++) { 
            try { 
                String ip = getHostIP(host_); 
                sock = pool_.getConn(ip, port_, connTimeout_); 
                int timeout = getTimeout(method); 
                sock.setTimeout(timeout); 
                return sock; 
            } catch (Exception e) { 
                save = e; 
                pool_.putConn(sock, true); 
                continue; 
            } 
        } 
 
        throw save; 
    } 
 
    public TTransport getTransport(TSocket sock) throws Exception { 
 
        TTransport transport = null; 
 
        if (transportName_.equals("xm_header")) { 
            return new TXmHeaderTransport(sock); 
        } else if (transportName_.equals("frame")) { 
            return new TFramedTransport(sock); 
        } else if (transportName_.equals("tcp")) { 
            return sock; 
        } 
 
        throw new Exception("not found transport " + transportName_  + ", host=" + host_); 
    } 
 
    public TProtocol getProtocol(TTransport transport) throws Exception { 
 
        TProtocol protocol = null; 
        if (protocolName_.equals("binary")) { 
            return new TBinaryProtocol(transport); 
        } else if (protocolName_.equals("compact")) { 
            return new TCompactProtocol(transport); 
        } 
 
        throw new Exception("not found protocol " + protocolName_ + ", host=" + host_); 
    } 
    public Result Get(int number, String appId, String xnamespace, String password) throws Exception{
    Exception save = new Exception("Unknown Exception");
    for (int i = 0; i < maxRetry_ + 1; i++)    {
        TSocket sock = null;
        boolean exception = false;
        try        {
            sock = getConn("Get");
            TTransport trans = getTransport(sock);
            TProtocol proto = getProtocol(trans);
            Sid.Client client = new Sid.Client(proto);
            client.setContext(context_);
            return client.Get(number, appId, xnamespace, password);
        }
        catch(TTransportException e)        {
            save = e;
            continue;
} catch (TProtocolException e) {
            save = e;
            continue;
        }
        finally        {
            pool_.putConn(sock, exception);
        }
    }
    throw save;
}


} 

