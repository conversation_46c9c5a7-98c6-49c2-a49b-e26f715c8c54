package com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.po;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @description: 商品包装类
 * @author: he<PERSON><PERSON><PERSON>
 * @Date 2022/3/13 3:39 下午
 * @Version: 1.0
 **/
@Data
public class GoodsItemPo {

    private List<Long> skuList;

    private List<Long> gidList;

    private List<Long> packageList;

    private List<Long> ssuList;

    private List<Long> suitList;

    private Map<Long, Integer> labourHourSsu;

    private Map<Long, Integer> partsSsu;

}
