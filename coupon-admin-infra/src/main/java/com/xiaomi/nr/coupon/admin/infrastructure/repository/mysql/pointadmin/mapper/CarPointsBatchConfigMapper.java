package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.pointadmin.mapper;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.pointadmin.po.CarPointsBatchConfigPo;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/6 11:03
 */
@Mapper
public interface CarPointsBatchConfigMapper {

    /**
     * 插入批次配置数据
     *
     * @param po po
     * @return 影响记录数
     */
    @Insert("INSERT INTO car_points_batch_config (" +
            "   name, budget_id, budget_apply_no, line_num, budget_create_time, br_apply_no, send_scene, start_time, end_time, use_time_type, " +
            "   apply_count, send_count, warning_ratio, status, creator" +
            ") VALUES (" +
            "   #{name}, #{budgetId}, #{budgetApplyNo}, #{lineNum}, #{budgetCreateTime}, #{brApplyNo}, #{sendScene}, #{startTime}, #{endTime}, #{useTimeType}, " +
            "   #{applyCount}, #{sendCount}, #{warningRatio}, #{status}, #{creator}" +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(CarPointsBatchConfigPo po);

    /**
     * 根据批次配置id查询
     *
     * @param batchId batchId
     * @return 积分批次配置
     */
    @Select("select * from car_points_batch_config where id = #{batchId}")
    CarPointsBatchConfigPo findById(@Param("batchId") Long batchId);

    /**
     * 批理获取批次配置
     *
     * @param batchIdList List
     * @return List
     */
    @Select("<script>" +
                "select * " +
                "from car_points_batch_config " +
                "<if test='batchIdList!=null and batchIdList.size() > 0'>" +
                    " where id in <foreach collection='batchIdList' item='batchId' index='index' open='(' separator=',' close=')'>#{batchId}</foreach>" +
                "</if>" +
            "</script>")
    List<CarPointsBatchConfigPo> batchGetConfigInfo(@Param("batchIdList") List<Long> batchIdList);

    @Update("<script>" +
            " update car_points_batch_config " +
            "<set>" +
            "<if test='batchConfigPo.name != null and batchConfigPo.name != \"\"'> name=#{batchConfigPo.name},</if>" +
            "<if test='batchConfigPo.budgetId != null and batchConfigPo.budgetId != 0'> budget_id=#{batchConfigPo.budgetId},</if>" +
            "<if test='batchConfigPo.budgetApplyNo != null and batchConfigPo.budgetApplyNo != \"\"'> budget_apply_no=#{batchConfigPo.budgetApplyNo},</if>" +
            "<if test='batchConfigPo.lineNum != null and batchConfigPo.lineNum != 0'> line_num=#{batchConfigPo.lineNum},</if>" +
            "<if test='batchConfigPo.budgetCreateTime != null and batchConfigPo.budgetCreateTime != \"\"'> budget_create_time=#{batchConfigPo.budgetCreateTime},</if>" +
            "<if test='batchConfigPo.brApplyNo != null and batchConfigPo.brApplyNo != \"\"'> br_apply_no=#{batchConfigPo.brApplyNo},</if>" +
            "<if test='batchConfigPo.sendScene != null and batchConfigPo.sendScene != \"\"'> send_scene=#{batchConfigPo.sendScene},</if>" +
            "<if test='batchConfigPo.startTime != null and batchConfigPo.startTime != 0'> start_time=#{batchConfigPo.startTime},</if>" +
            "<if test='batchConfigPo.endTime != null and batchConfigPo.endTime != 0'> end_time=#{batchConfigPo.endTime},</if>" +
            "<if test='batchConfigPo.applyCount != null and batchConfigPo.applyCount != 0'> apply_count=#{batchConfigPo.applyCount},</if>" +
            "<if test='batchConfigPo.status != null'> status=#{batchConfigPo.status},</if>" +
            "<if test='batchConfigPo.useTimeType != null'> use_time_type=#{batchConfigPo.useTimeType},</if>" +
            "<if test='batchConfigPo.releaseCount != null'> release_count=#{batchConfigPo.releaseCount},</if>" +
            "<if test='batchConfigPo.creator != null'> creator=#{batchConfigPo.creator},</if>" +
            "</set>" +
            " where id =#{batchConfigPo.id}" +
            "</script>")
    int update(@Param("batchConfigPo") CarPointsBatchConfigPo batchConfigPo);

    /**
     * 根据周期状态类型查询数据
     *
     * @param periodStatus  周期状态类型  1-进行中 2-未开始 3-已结束
     * @param queryTime         查询时间
     * @return                  批次列表
     */
    @Select("<script>" +
            "   SELECT *" +
            "   FROM car_points_batch_config " +
            "   <where>" +
            "     <if test='periodStatus != null'>" +
            "        <choose>" +
            "           <when test='periodStatus == 1'>" +
            "               AND start_time &lt;= #{queryTime} AND end_time &gt;= #{queryTime} " +
            "           </when>" +
            "           <when test='periodStatus == 2'>" +
            "               AND start_time &gt; #{queryTime} " +
            "           </when>" +
            "           <when test='periodStatus == 3'>" +
            "               AND end_time &lt; #{queryTime} " +
            "           </when>" +
            "        </choose>" +
            "     </if>" +
            "     <if test='id != null'>" +
            "               AND id = #{id} " +
            "     </if>" +
            "     <if test='name != null'>" +
            "               AND name LIKE CONCAT(CONCAT('%',#{name}),'%') " +
            "     </if>" +
            "   </where>" +
            "</script>")
    List<CarPointsBatchConfigPo> selectByPeriodStatus(@Param("periodStatus") Integer periodStatus,
                                                      @Param("queryTime") Long queryTime,
                                                      @Param("id") Long id,
                                                      @Param("name") String name
    );


    /**
     * 获取当前时间所有可用的积分批次配置
     *
     * @param currentTimestamp  当前时间
     * @return List<CarPointsBatchConfigPo>
     */
    @Select("select * from car_points_batch_config where start_time <= #{currentTimestamp} and #{currentTimestamp} <= end_time and status = 1")
    List<CarPointsBatchConfigPo> findAllValidPointConfig(@Param("currentTimestamp") long currentTimestamp);

    @Select("select * " +
            "from car_points_batch_config " +
            "where id > #{startId} and status=1 and start_time <= #{queryTime} and end_time >= #{queryTime} " +
            "limit #{batchSize} ")
    List<CarPointsBatchConfigPo> selectOnlineInProgressBatch(@Param("startId") long startId,
                                                             @Param("queryTime") long queryTime,
                                                             @Param("batchSize") int batchSize);

    @Select("select * " +
            "from car_points_batch_config " +
            "where id > #{startId} and end_time < #{queryTime} " +
            "limit #{batchSize} ")
    List<CarPointsBatchConfigPo> selectCompletedBatch(@Param("startId") long startId,
                                                      @Param("queryTime") long queryTime,
                                                      @Param("batchSize") int batchSize);
    /**
     * 获取存在时间交集，处于上线状态的积分批次配置
     *
     * @param startTime startTime
     * @param endTime endTime
     * @param sendScene sendScene
     * @param batchId batchId
     * @return 积分批次列表
     */
    @Select("<script>" +
            " select * from car_points_batch_config where status = 1 " +
            "<if test=\"sendScene != null\"> and send_scene = #{sendScene} </if>" +
            "<if test=\"batchId != null\"> and id != #{batchId} </if>" +
            "and ((start_time between #{startTime} and #{endTime}) or (end_time between #{startTime} and #{endTime}))" +
            "</script>")
    List<CarPointsBatchConfigPo> findTimeIntersectionPointConfig(@Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("sendScene") String sendScene, @Param("batchId") Long batchId);

    /**
     * 根据id+场景批次查询有效积分
     *
     * @param batchIdList batchIdList
     * @param sceneCodeList sceneCodeList
     * @param onlyAvailable onlyAvailable
     * @param queryTime queryTime
     * @return 积分批次id列表
     */
    @Select("<script>" +
            "  select * " +
            "  from car_points_batch_config " +
            "  <where>" +
            "     <if test='batchIdList != null and batchIdList.size() > 0'>  " +
            "        id in <foreach item='batchId' index='index' collection='batchIdList' open='(' separator=',' close=')'>#{batchId}</foreach> " +
            "    </if>" +
            "     <if test='sceneCodeList != null and sceneCodeList.size() > 0'>  " +
            "        and send_scene in <foreach item='sceneCode' index='index' collection='sceneCodeList' open='(' separator=',' close=')'>#{sceneCode}</foreach> " +
            "    </if>" +
            "     <if test='onlyAvailable != null and onlyAvailable'>  " +
            "        and status = 1 and start_time &lt;= #{queryTime} and end_time &gt;= #{queryTime}  " +
            "    </if>" +
            "  </where>" +
            "</script>")
    List<CarPointsBatchConfigPo> selectAvailableBatch(@Param("batchIdList") List<Long> batchIdList,
                                                      @Param("sceneCodeList") List<String> sceneCodeList,
                                                      @Param("onlyAvailable") Boolean onlyAvailable,
                                                      @Param("queryTime") Long queryTime);

    /**
     * 根据名称查询积分批次id
     *
     * @param batchName batchName
     * @return 积分批次id列表
     */
    @Select("<script>" +
            "select id from car_points_batch_config " +
            "<if test='batchName!=null'>where name like concat(concat('%',#{batchName}),'%')</if>" +
            "</script>")
    List<Long> getBatchConfigIdByName(@Param("batchName") String batchName);

    @Select("<script>" +
            "select * from car_points_batch_config " +
            "where id in " +
            "<foreach item='batchId' index='index' collection='batchIdList' open='(' separator=',' close=')'>#{batchId}</foreach>" +
            "</script>")
    List<CarPointsBatchConfigPo> getBatchConfigByIds(@Param("batchIdList") List<Long> batchIdList);
}
