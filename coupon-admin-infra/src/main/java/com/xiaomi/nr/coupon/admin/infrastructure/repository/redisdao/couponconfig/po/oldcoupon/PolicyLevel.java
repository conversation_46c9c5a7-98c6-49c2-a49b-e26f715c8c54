package com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.po.oldcoupon;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 政策优先级
 *
 * <AUTHOR>
 * @date 2021/5/14
 */
@Data
public class PolicyLevel implements Serializable {
    private static final long serialVersionUID = -1950350347269886561L;

    /**
     * 什么商品可以参与
     */
    @SerializedName("included_goodsgroup")
    private List<FillGoodsGroup> includedGoodsGroup;

    /**
     * 可以优惠的具体政策
     */
    private RuleEle rule;

    /**
     * 需要满足的条件
     */
    private List<QuotaEle> quota;
}
