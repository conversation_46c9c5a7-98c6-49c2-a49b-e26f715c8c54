package com.xiaomi.nr.coupon.admin.infrastructure.auth;

import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 应用权限配置
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "auth.config")
@Data
public class AppConfigs {
    /**
     * 这个名称不能改变，它是与配置文件里面的一致
     */
    private List<AppConfig> apps;

    /**
     * 用于存储所有权限
     */
    private static Map<String, Map<String, List<String>>> appPermissions = new HashMap<>();

    static {
        //所有权限人工配置（非常重要），如果权限有调整，目前只能在这里配置
        String conf = "{" +
                            //小米游戏中心
                            "XM2104:{" +
                                "com.xiaomi.nr.coupon.admin.api.service.DubboEcardService:[" +
                                    "listEcardStat," +
                                    "listEcardDesc" +
                                "]" +
                            "}" +
                        "}";

        appPermissions = GsonUtil.fromJson(conf, appPermissions.getClass());
    }

    /**
     * 获取所有不需要验证token和appId的服务方法
     * 背景是权限上线前接入的服务没传这些字段
     *
     * @return List<String>
     */
    public static List<String> getNotCheckAuthServices() {
        //serviceName + . + methodName
        return new ArrayList<String>() {{
            add("com.xiaomi.nr.coupon.admin.api.service.CouponConfigService.changeNotify");
            add("com.xiaomi.nr.coupon.admin.api.service.CouponConfigService.assignGenCouponConfigCache");
            add("com.xiaomi.nr.coupon.admin.api.service.CouponConfigService.genAllConfigRedisCache");
            add("com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponBpmCallBackService.onStatusChanged");
            add("com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponBpmCallBackService.queryReviewInfo");
        }};
    }

    /**
     * 获取所有权限配置
     *
     * @return Map<String, Map < String, List < String>>>
     */
    public static Map<String, Map<String, List<String>>> getPermissions() {
        return appPermissions;
    }

}
