package com.xiaomi.nr.coupon.admin.infrastructure.repository;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaomi.goods.gms.dto.batch.BatchedInfoDto;
import com.xiaomi.goods.gms.dto.sku.SkuInfoDto;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.GoodConfigIdsRequest;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.BaseData;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponCreateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateStatusEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.EventContext;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CarMaintenanceSsuInfo;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.admin.enums.optrecord.OptRecordTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.scene.SceneSendModeEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.infrastructure.nacosconfig.NacosSwitchConfig;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.es.CouponConfigESHelper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.es.CouponInvertedTOBHelper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.es.CouponInvertedTOCHelper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.es.po.CouponEsPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.CouponConfigMapper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.po.SearchConfigInfoParam;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.po.SearchConfigParam;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.mission.CouponMissionMapper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.mission.MissionConvert;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.optrecord.CouponOptRecordMapper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.optrecord.OptRecordConvert;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.optrecord.po.CouponOptRecordPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.scene.po.CouponScenePO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.task.CouponTaskConvert;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.task.FillCouponTaskMapper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.CouponConfigRedisDao;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.OldCouponRedisDao;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.po.*;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.po.oldcoupon.OldCouponConvert;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.po.oldcoupon.OldCouponInfo;
import com.xiaomi.nr.coupon.admin.infrastructure.rpc.CouponBudgetService;
import com.xiaomi.nr.coupon.admin.infrastructure.rpc.goods.GisProxyService;
import com.xiaomi.nr.coupon.admin.api.utils.I18nUtil;
import com.xiaomi.nr.coupon.admin.api.utils.TimeUtil;
import com.xiaomi.nr.goods.tob.dto.response.ssu.SsuDTO;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 优惠券配置资源库
 * @author: hejiapeng
 * @Date 2022/3/1 10:42 上午
 * @Version: 1.0
 **/
@Component
@Slf4j
// todo 有时间将 命令操作和查询操作拆为俩仓库
public class CouponConfigRepository {

    @Autowired
    private CouponConfigMapper couponConfigMapper;

    @Autowired
    private CouponConfigRedisDao couponConfigRedisDao;

    @Autowired
    private CouponInvertedTOCHelper couponInvertedTOCHelper;

    @Autowired
    private CouponInvertedTOBHelper couponInvertedTOBHelper;

    @Autowired
    private OldCouponRedisDao oldCouponRedisDao;

    @Autowired
    private OldCouponConvert oldCouponConvert;

    @Autowired
    private CouponConfigPoConvert couponConfigPoConvert;

    @Autowired
    private CouponMissionMapper couponMissionMapper;

    @Autowired
    private FillCouponTaskMapper fillCouponTaskMapper;

    @Autowired
    private CouponOptRecordMapper couponOptRecordMapper;

    @Autowired
    @Qualifier("couponInvertedTOBHelper")
    private CouponConfigESHelper couponConfigESHelper;

    @Autowired
    private NacosSwitchConfig nacosSwitchConfig;

    @Autowired
    private ThreadPoolTaskExecutor queryGoodsAsyncExecutor;

    @Autowired
    private GisProxyService gisProxyService;

    @Autowired
    private CouponSceneRepository couponSceneRepository;

    @Autowired
    private CouponBudgetService couponBudgetService;

    /**
     * 获取有效券ID
     *
     * @param scopeType
     * @param endFetchTime
     * @param createTime
     * @return
     */
    public List<Long> selectValidConfigId(int scopeType, long endFetchTime, long createTime) {
        return couponConfigMapper.selectValidConfigId(scopeType, endFetchTime, createTime);
    }

    /**
     * 根据查询条件查询券信息
     *
     * @param param SearchConfigParam
     * @return 券信息
     */
    public List<CouponConfigPO> searchCouponList(SearchConfigParam param) {
        return couponConfigMapper.searchConfig(param);
    }

    /**
     * 根据批次Id批量获取优惠券配置
     *
     * @param ids
     * @return
     */
    public List<CouponConfigPO> searchCouponListById(List<Long> ids) {
        return couponConfigMapper.getConfigByIds(ids);
    }

    /**
     * 根据券id查询券信息
     *
     * @param ids        券id集合
     * @param couponType 券类型
     * @param name       券名称
     * @param sortField  排序字段
     * @param sortValue  排序方式
     * @return 券信息
     */
    public List<CouponConfigPO> searchCouponListByIdSort(Set<Long> ids, Integer couponType, String name, String sortField, String sortValue) {
        return couponConfigMapper.getConfigByIdsSort(ids, couponType, name, sortField, sortValue);
    }

    /**
     * 根据券id查询券信息(单个)
     *
     * @param id 券id
     * @return 券信息
     */
    public CouponConfigPO searchCouponById(long id) {
        return couponConfigMapper.getCouponConfigById(id);
    }

    /**
     * 根据券id查询券名称、可发放数量等信息
     *
     * @param id
     * @return
     */
    public List<CouponConfigPO> getCouponNameById(long id, List<String> sceneCodeList, long endFetchTime,String areaId) {
        return couponConfigMapper.getCouponNameById(id, sceneCodeList, endFetchTime,areaId);
    }


    public List<CouponConfigPO> searchCouponByParam(SearchConfigInfoParam param){
        return couponConfigMapper.getConfigByParam(param);
    }

    /**
     * 根据券Id精确查或名称模糊查询券
     *
     * @param queryItem
     * @return
     */
    public List<CouponConfigPO> searchCouponByIdOrName(String queryItem, Integer bizType) {
        List<CouponConfigPO> configPOList = Lists.newArrayList();
        if (StringUtils.isEmpty(queryItem)) {
            return configPOList;
        }
        if (StringUtils.isNumeric(queryItem)) {
            CouponConfigPO couponConfigPO = couponConfigMapper.getCouponConfigById(Long.parseLong(queryItem));
            if (couponConfigPO != null) {
                configPOList.add(couponConfigPO);
            }
        } else {
            configPOList = couponConfigMapper.getCouponByName(queryItem);
        }
        if(Objects.equals(BizPlatformEnum.CAR_AFTER_SALE.getCode(), bizType)){
            return configPOList.stream().filter(couponConfigPO -> Objects.equals(couponConfigPO.getBizPlatform(), BizPlatformEnum.CAR_AFTER_SALE.getCode())).collect(Collectors.toList());
        } else {
            return configPOList.stream().filter(couponConfigPO -> !Objects.equals(couponConfigPO.getBizPlatform(), BizPlatformEnum.CAR_AFTER_SALE.getCode())).collect(Collectors.toList());
        }

    }

    public Timestamp searchUpdateTimeById(long id) {
        return couponConfigMapper.searchUpdateTimeById(id);
    }


    /**
     * 修改券状态
     * @return 更新范围
     */
    @Transactional(transactionManager = "xmPulseCouponTransactionManager", rollbackFor = {Exception.class, Error.class})
    public int updateStatus(CouponConfigPO newPo, CouponUpdateStatusEvent event) throws Exception{
        int count =  couponConfigMapper.updateStatus(newPo.getId(),newPo.getStatus());

        if (I18nUtil.isNotI18n()) {
            couponMissionMapper.update(MissionConvert.convertCouponMissionPO(newPo));
        }

        event.setIdempotentKey(newPo.getId()+"_"+event.getTime()+"_"+UUID.randomUUID());
        CouponOptRecordPO couponOptRecordPO = OptRecordConvert.convertToOptRecordPO(OptRecordTypeEnum.UPDATE_COUPON_STATUS,event);
        couponOptRecordMapper.insert(couponOptRecordPO);
        event.setRecordId(couponOptRecordPO.getId());

        if (I18nUtil.isNotI18n()) {
            updateOldCouponInfoCache(newPo);
        }
        return count;
    }


    public List<Long> getCouponIdByNameAndType(String couponName, Integer couponType, String bizType, String areaId) {
        return couponConfigMapper.getCouponIdByNameAndType(couponName, couponType, bizType,areaId);
    }

    /**
     * 优惠券列表ES查询
     *
     * @param param   SearchConfigParam
     * @param nowTime 当前时间
     * @return 券id
     * @throws IOException io异常
     */
    public Set<Long> getESCouponIdByParam(SearchConfigParam param, long nowTime) throws IOException {
        return couponInvertedTOBHelper.batchGetConfigIds(param, nowTime);
    }

    /**
     * 获取所有有效券配置Id
     *
     * @return
     */
    public List<Long> getAllValidConfigId() {
        long validFinalTime = TimeUtil.getNowUnixSecond() - 1800;
        return couponConfigMapper.getValidConfigIdByEndFetchTime(validFinalTime);
    }

    /**
     * 获取所有有效券配置Id
     *
     * @return
     */
    public List<Long> getAllValidConfigIdWarning() {
        long validFinalTime = TimeUtil.getNowUnixSecond();
        return couponConfigMapper.getValidConfigIdByEndFetchTime(validFinalTime);
    }

    /**
     * 获取所有券配置
     *
     * @return
     */
    public List<CouponConfigPO> getAllCouponConfig() {
        List<CouponConfigPO> couponConfigs = Lists.newArrayList();
        long lastId = 0;
        while (lastId >= 0) {
            List<CouponConfigPO> tmpCouponConfigs = couponConfigMapper.getAllConfigByOffset(lastId, 50);
            if (CollectionUtils.isEmpty(tmpCouponConfigs)) {
                break;
            }
            if (tmpCouponConfigs.size() < 50) {
                lastId = -1;
            } else {
                lastId = tmpCouponConfigs.get(tmpCouponConfigs.size() - 1).getId();
            }
            couponConfigs.addAll(tmpCouponConfigs);
        }
        return couponConfigs;
    }

    /**
     * 批量获取优惠券已发放数量
     *
     * @param configIds 券配置列表
     * @return
     * @throws BizError
     */
    public Map<Long, Long> getCouponSendCount(List<Long> configIds) throws BizError {
        if (CollectionUtils.isEmpty(configIds)) {
            return MapUtils.EMPTY_MAP;
        }
        if (configIds.size() <= CommonConstant.GET_COUPON_SENDCOUT_NUM) {
            return couponConfigRedisDao.batchGetCouponSendCount(configIds);
        }
        Map<Long, Long> sendCountMap = new HashMap<>(configIds.size());
        List<List<Long>> partitionList = Lists.partition(configIds, CommonConstant.GET_COUPON_SENDCOUT_NUM);
        for (List<Long> configIdList : partitionList) {
            Map<Long, Long> couponSendCounts = couponConfigRedisDao.batchGetCouponSendCount(configIdList);
            sendCountMap.putAll(couponSendCounts);
        }
        return sendCountMap;
    }

    /**
     * 更新券缓存数据和ES数据
     *
     * @param couponConfigPO
     * @throws Exception
     */
    public void updateCouponConfigCache(CouponConfigPO couponConfigPO, EventContext eventContext) throws Exception {

        updateConfigInfoCache(couponConfigPO);

        // 车商城直接根据goodsItemPo进行缓存更新
        if (BizPlatformEnum.CAR_SHOP.getCode().equals(couponConfigPO.getBizPlatform())) {
            updateCouponGoodsCacheV2(couponConfigPO, eventContext.getGoodsItemPo());
        } else {
            updateCouponGoodsCache(couponConfigPO, eventContext.getSkuInfoDtos(), eventContext.getBatchedInfoDtos());
            updateCouponGoodsCacheV2(couponConfigPO, eventContext.getSkuInfoDtos(), eventContext.getBatchedInfoDtos(), eventContext.getSsuDtos(),
                    eventContext.getLabourHourSsuInfos(), eventContext.getPartsSsuInfos(), eventContext.getGoodsItemPo());
        }
        updateCouponConfigEsInfo(couponConfigPO, eventContext.getGoodsItemPo());
    }

    /**
     * 更新券缓存数据和ES数据
     *
     * @param couponConfigPO
     * @throws Exception
     */
    public void updateCouponConfigCache(CouponConfigPO couponConfigPO, BaseData common) throws Exception {

        GoodsItemPo goodsItemPo = couponConfigPoConvert.serializeGoodsItemPo(couponConfigPO);

        updateConfigInfoCache(couponConfigPO);

        updateCouponGoodsCache(couponConfigPO, goodsItemPo, common);

        updateCouponGoodsCacheV2(couponConfigPO, goodsItemPo, common);

        updateCouponConfigEsInfo(couponConfigPO, goodsItemPo);
    }

    /**
     * 更新基本信息
     *
     * @param couponConfigPO
     * @throws Exception
     */
    public void updateCouponBaseInfoCache(CouponConfigPO couponConfigPO) throws Exception {

        GoodsItemPo goodsItemPo = couponConfigPoConvert.serializeGoodsItemPo(couponConfigPO);

        updateConfigInfoCache(couponConfigPO);

        updateCouponConfigEsInfo(couponConfigPO, goodsItemPo);
    }


    /**
     * 保存优惠券
     *
     * @return
     */
    @Transactional(transactionManager = "xmPulseCouponTransactionManager", rollbackFor = {Exception.class, Error.class})
    public long insert(CouponConfigPO couponConfigPO, CouponCreateEvent event) throws Exception {
        String brApplyNo = UUID.randomUUID().toString().replaceAll("-", "");
        couponConfigPO.setBrApplyNo(brApplyNo);
        couponConfigMapper.insert(couponConfigPO);

        if (I18nUtil.isNotI18n()) {
            couponMissionMapper.insert(MissionConvert.convertCouponMissionPO(couponConfigPO));
        }

        // CouponScenePO couponScenePO = couponSceneMapper.selectBySceneCode(couponConfigPO.getSendScene());
        List<String> sendSceneList = Arrays.asList(couponConfigPO.getSendScene().split(","));
        Map<String, CouponScenePO> scenePOMap = couponSceneRepository.selectPoBySceneCodes(sendSceneList);

        // 是否需要创建优惠码任务
        boolean needFillCouponTask = scenePOMap.entrySet().stream().anyMatch(e -> SceneSendModeEnum.COUPON_CODE.getCode() == e.getValue().getSendMode());
        if (needFillCouponTask) {
            fillCouponTaskMapper.insert(CouponTaskConvert.convertFillCouponTaskPO(couponConfigPO));
        }

        event.setData(couponConfigPO);
        event.setIdempotentKey(couponConfigPO.getId() + "_" + event.getTime() + "_" + UUID.randomUUID());
        CouponOptRecordPO couponOptRecordPO = OptRecordConvert.convertToOptRecordPO(OptRecordTypeEnum.ADD_COUPON, event);
        couponOptRecordMapper.insert(couponOptRecordPO);
        event.setRecordId(couponOptRecordPO.getId());


        updateOldCouponInfoCache(couponConfigPO);

        //占用br预算金额
        if (couponConfigPO.getBizPlatform().equals(BizPlatformEnum.CAR.getCode()) && StringUtils.isNotEmpty(couponConfigPO.getBrApplyNo())) {
            couponBudgetService.preOccupyBudget(couponConfigPO);
        }
        return couponConfigPO.getId();
    }

    /**
     * 更新优惠券
     *
     * @param po
     * @return
     */
    @Transactional(transactionManager = "xmPulseCouponTransactionManager", rollbackFor = {Exception.class, Error.class})
    public long update(CouponConfigPO oldPo, CouponConfigPO po, CouponUpdateEvent event) throws Exception {
        long count = couponConfigMapper.update(po);
        event.setIdempotentKey(po.getId() + "_" + event.getTime() + "_" + UUID.randomUUID());
        CouponOptRecordPO couponOptRecordPO = OptRecordConvert.convertToOptRecordPO(OptRecordTypeEnum.UPDATE_COUPON, event);
        couponOptRecordMapper.insert(couponOptRecordPO);
        event.setRecordId(couponOptRecordPO.getId());

        //如果不是国际化，不走接下来的流程
        if (I18nUtil.isNotI18n()) {
            couponMissionMapper.update(MissionConvert.convertCouponMissionPO(po));
            updateOldCouponInfoCache(po);
            // 进行br预算金额的追加、返还
            handleBr(oldPo, po);
        }
        return count;
    }

    /**
     * 处理br增加、返还
     *
     * @param po
     * @return
     */
    public void handleBr(CouponConfigPO oldPo, CouponConfigPO po) throws Exception {
        if (!po.getBizPlatform().equals(BizPlatformEnum.CAR.getCode()) || StringUtils.isEmpty(po.getBrApplyNo())) {
            return;
        }
        // 进行br预算金额的追加、返还
        double oldCost = (double)oldPo.getApplyCount() * oldPo.getPromotionValue()/100;
        double newCost = (double)po.getApplyCount() * po.getPromotionValue()/100;
        double gapCost = newCost - oldCost;
        double gap = Math.round(gapCost * 100.0) / 100.0;
        if (gap > 0) {
            //追加预算
            couponBudgetService.addBudget(po, gap, oldPo.getBrApplyNo());
        } else if (gap < 0) {
            //返还预算
            couponBudgetService.reduceBudget(po, -gap, oldPo.getBrApplyNo());
        }
    }

    /**
     * 仅更新基本信息缓存
     * @param couponConfigPO
     */
    private void updateConfigInfoCache(CouponConfigPO couponConfigPO) {
        ConfigInfoCachePo configInfoCachePo = couponConfigPoConvert.serializeConfigInfoCachePo(couponConfigPO);
        couponConfigRedisDao.setConfigInfoCache(configInfoCachePo, getExpireTimeStamp(couponConfigPO));
    }

    /**
     * 仅更新商品信息缓存
     *
     * @param couponConfigPO
     * @param skuInfoDtos
     * @param batchedInfoDtos
     * @throws Exception
     */
    private void updateCouponGoodsCache(CouponConfigPO couponConfigPO, List<SkuInfoDto> skuInfoDtos, List<BatchedInfoDto> batchedInfoDtos) throws Exception {
        if(!nacosSwitchConfig.isRefreshOldGoodsData()){
            return;
        }
        ConfigGoodsCachePo couponGoodsCachePo = couponConfigPoConvert.serializeConfigGoodsCachePo(couponConfigPO, skuInfoDtos, batchedInfoDtos);
        couponConfigRedisDao.setCouponGoodsCache(couponGoodsCachePo, getExpireTimeStamp(couponConfigPO));
    }

    /**
     * 仅更新商品信息缓存
     *
     * @param couponConfigPO
     * @param goodsItemPo
     * @param common
     * @throws Exception
     */
    private void updateCouponGoodsCache(CouponConfigPO couponConfigPO, GoodsItemPo goodsItemPo, BaseData common) throws Exception {
        if(!nacosSwitchConfig.isRefreshOldGoodsData()){
            return;
        }
        ConfigGoodsCachePo couponGoodsCachePo = couponConfigPoConvert.serializeConfigGoodsCachePo(couponConfigPO, goodsItemPo, common);
        couponConfigRedisDao.setCouponGoodsCache(couponGoodsCachePo, getExpireTimeStamp(couponConfigPO));
    }

    /**
     * 仅更新ES数据
     *
     * @param couponConfigPO
     * @param goodsItemPo
     * @throws Exception
     */
    public void updateCouponConfigEsInfo(CouponConfigPO couponConfigPO, GoodsItemPo goodsItemPo) throws Exception {
        CouponEsPO couponEsPO = couponConfigPoConvert.serializeCouponEsPO(couponConfigPO, goodsItemPo);
        couponInvertedTOCHelper.saveCouponConfig(couponEsPO);
    }

    /**
     * 更新老redis券缓存
     *
     * @param couponConfigPO
     */
    public void updateOldCouponInfoCache(CouponConfigPO couponConfigPO) {
        if (couponConfigPO.getBizPlatform() != 0) {
            return;
        }
        OldCouponInfo oldCouponCachePo = oldCouponConvert.serializeOldCouponInfo(couponConfigPO);
        oldCouponRedisDao.setOldCouponInfo(oldCouponCachePo);
    }

    /**
     * 更新老redis有效券ID列表缓存
     *
     * @param configIds
     */
    public void updateOldConfigIdsCache(List<Long> configIds) {
        oldCouponRedisDao.setValidConfigIds(configIds);
    }

    /**
     * 获取券配置写入缓存过期时间
     *
     * @param couponConfigPO
     * @return
     */
    private long getExpireTimeStamp(CouponConfigPO couponConfigPO) {
        long endTime = Math.max(couponConfigPO.getEndUseTime(), couponConfigPO.getEndFetchTime());
        return endTime - TimeUtil.getNowUnixSecond() + CommonConstant.YEAR_TIMESTAMP_LENGTH;
    }

    /**
     * 获取券信息，用于自动更券新品
     *
     * @param configIds 券ids
     * @return
     */
    public List<CouponConfigPO> getConfigForRefresh(Set<Long> configIds) {
        return couponConfigMapper.getConfigForRefresh(configIds);
    }


    /**
     * 更新优惠券
     *
     * @param po
     * @return
     */
    @Transactional(transactionManager = "xmPulseCouponTransactionManager", rollbackFor = {Exception.class, Error.class})
    public long updateGoodsInclude(CouponConfigPO po, CouponUpdateEvent event) throws Exception {

        long count = couponConfigMapper.updateGoodsInclude(po);

        event.setIdempotentKey(po.getId() + "_" + event.getTime() + "_" + UUID.randomUUID());
        CouponOptRecordPO couponOptRecordPO = OptRecordConvert.convertToOptRecordPO(OptRecordTypeEnum.UPDATE_COUPON, event);
        couponOptRecordMapper.insert(couponOptRecordPO);
        event.setRecordId(couponOptRecordPO.getId());
        po.setUpdateTime(TimeUtil.getNowTimestamp());

        updateOldCouponInfoCache(po);
        return count;
    }

    /**
     * 获取需要更新品类券的
     *
     * @param nowTime
     * @param startSize
     * @param pageSize
     * @return
     */
    public List<CouponConfigPO> getConfigForRefreshSchedule(long nowTime, int startSize, int pageSize) {
        return couponConfigMapper.getConfigForRefreshSchedule(nowTime, startSize, pageSize);
    }

    /**
     * 获取有效券id，用于更新券新品
     *
     * @param configIds 券ids
     * @return
     */
    public List<Long> getConfigForRefreshIds(Set<Long> configIds) {
        return couponConfigMapper.getConfigForRefreshIds(configIds);
    }


    public Map<Long, Set<Long>> getGoodsConfigSetMap(List<Long> goodList, GoodsLevelEnum sku, SearchConfigParam searchConfigParam) throws Exception {
        return couponConfigESHelper.batchGetInverted(sku.getValue(), goodList, TimeUtil.getNowUnixSecond(), searchConfigParam);
    }

    /**
     * 获取商品可用券配置信息
     * @return
     * @throws Exception
     */
    public List<CouponConfigPO> getGoodCouponConfigs(GoodConfigIdsRequest request) throws Exception {

        long nowTime = request.getUseAbleType() == CommonConstant.ONE_INT ? TimeUtil.getNowUnixSecond() : CommonConstant.ZERO_INT;

        SearchConfigParam searchParam = new SearchConfigParam();
        searchParam.setUseChannel(request.getUseChannel());
        searchParam.setPromotionType(request.getPromotionType());

        String goodsLevel = request.getLevel();
        Long itemId = request.getItemId();

        if(Objects.equals(GoodsLevelEnum.Goods.getValue(), request.getLevel())){
            itemId = gisProxyService.getSkuByGoodsId(request.getItemId());
            if(Objects.isNull(itemId)){
                return null;
            }

            goodsLevel = GoodsLevelEnum.Sku.getValue();
        }

        if (Objects.equals(GoodsLevelEnum.Suit.getValue(), goodsLevel)) {
            // 新套装es缓存goodsLevel=ssu，适配一下
            goodsLevel = GoodsLevelEnum.Ssu.getValue();
        }

        Map<Long, Set<Long>> skuCouponRelMap = couponConfigESHelper.batchGetInverted(goodsLevel, Lists.newArrayList(itemId), nowTime, searchParam);

        Set<Long> configIds = Optional.ofNullable(skuCouponRelMap.get(itemId)).orElse(new HashSet<>());

        if (CollectionUtils.isEmpty(configIds)) {
            return null;
        }

        if(Objects.isNull(request.getConfigId())){
            return couponConfigMapper.getConfigByTimeIds(Lists.newArrayList(configIds), nowTime, request.getCouponType(), request.getSendScene());
        }

        if(configIds.contains(request.getConfigId())){
            return couponConfigMapper.getConfigByTimeIds(Lists.newArrayList(request.getConfigId()), nowTime, request.getCouponType(), request.getSendScene());
        }

        return null;
    }

    /**
     * 获取需要预计的券配置
     *
     * @return
     * @throws Exception
     */
    public Map<Long, CouponConfigPO> getWarningCouponConfigs() throws Exception {

        List<Long> configIds = getAllValidConfigIdWarning();

        if (CollectionUtils.isEmpty(configIds)) {
            return MapUtils.EMPTY_MAP;
        }

        List<CouponConfigPO> couponConfigPOS = Lists.newArrayList();
        List<List<Long>> partitionList = Lists.partition(configIds, CommonConstant.GET_COUPON_SENDCOUT_NUM);
        for (List<Long> subConfigIds : partitionList) {
            couponConfigPOS.addAll(couponConfigMapper.getConfigByIds(subConfigIds));
        }

        Map<Long, CouponConfigPO> couponConfigPOMap = couponConfigPOS.stream().collect(Collectors.toMap(CouponConfigPO::getId, Function.identity()));

        return couponConfigPOMap;
    }

    /**
     * 根据券id获取创建人信息
     * @param configId
     * @return
     */
    public CouponConfigPO getCreatorInfoByConfigId(long configId) {
        return couponConfigMapper.getCreatorInfoByConfigId(configId);
    }



    /**
     * 仅更新商品信息缓存(去除pid版本)
     *
     * @param couponConfigPO
     * @param skuInfoDtos
     * @param batchedInfoDtos
     * @param labourHourSsuInfos
     * @param partsSsuInfos
     * @param goodsItemPo
     * @throws Exception
     */
    private void updateCouponGoodsCacheV2(CouponConfigPO couponConfigPO, List<SkuInfoDto> skuInfoDtos, List<BatchedInfoDto> batchedInfoDtos, List<SsuDTO> ssuDTOs, List<CarMaintenanceSsuInfo> labourHourSsuInfos, List<CarMaintenanceSsuInfo> partsSsuInfos, GoodsItemPo goodsItemPo) throws Exception {
        NewConfigGoodsCachePo couponGoodsCachePo = couponConfigPoConvert.serializeConfigGoodsCachePoV2(couponConfigPO, skuInfoDtos, batchedInfoDtos, ssuDTOs, labourHourSsuInfos, partsSsuInfos, goodsItemPo);
        couponConfigRedisDao.setCouponGoodsCacheV2(couponGoodsCachePo, getExpireTimeStamp(couponConfigPO));
    }

    /**
     * 仅更新商品信息缓存(去除pid版本)
     *
     * @param couponConfigPO
     * @param goodsItemPo
     * @throws Exception
     */
    private void updateCouponGoodsCacheV2(CouponConfigPO couponConfigPO, GoodsItemPo goodsItemPo) throws Exception {
        NewConfigGoodsCachePo couponGoodsCachePo = couponConfigPoConvert.serializeConfigGoodsCachePoV2(couponConfigPO.getId(), goodsItemPo);
        couponConfigRedisDao.setCouponGoodsCacheV2(couponGoodsCachePo, getExpireTimeStamp(couponConfigPO));
    }


    /**
     * 仅更新商品信息缓存(去除pid版本)
     *
     * @param couponConfigPO
     * @param goodsItemPo
     * @param common
     * @throws Exception
     */
    private void updateCouponGoodsCacheV2(CouponConfigPO couponConfigPO, GoodsItemPo goodsItemPo, BaseData common) throws Exception {
        NewConfigGoodsCachePo couponGoodsCachePo = couponConfigPoConvert.serializeConfigGoodsCachePoV2(couponConfigPO, goodsItemPo, common);
        couponConfigRedisDao.setCouponGoodsCacheV2(couponGoodsCachePo, getExpireTimeStamp(couponConfigPO));
    }


    /**
     * 获取商品可用券配置信息
     * @return
     * @throws Exception
     */
    public Map<Long, Set<Long>> getGoodCouponId(SearchConfigParam searchConfigParam, boolean isRelativeTime, List<Long> itemIds) throws Exception {

        List<Future<Map<Long, Set<Long>>>> futureList = new ArrayList<>();
        List<List<Long>> skuPartList = Lists.partition(itemIds,10);

        for (List<Long> skuIdsPart : skuPartList) {
            Future<Map<Long, Set<Long>>> future = queryGoodsAsyncExecutor.submit(() -> {
                try {
                    Map<Long, Set<Long>> skuCouponRelMap = couponConfigESHelper.batchGetGoodsCoupon(searchConfigParam, itemIds, isRelativeTime);
                    if (MapUtils.isNotEmpty(skuCouponRelMap)) {
                        return skuCouponRelMap;
                    }

                    return Collections.emptyMap();
                }catch (Exception e) {
                    log.error("getGoodsDiscountLevel getGoodCouponType Exception skuIdsPart:{}", skuIdsPart, e);
                    throw ExceptionHelper.create(GeneralCodes.InternalError, "根据商品查询ES券数据失败");
                }
            });

            futureList.add(future);
        }

        Map<Long, Set<Long>> goodsCouponMap = new HashMap<>();
        for (Future<Map<Long, Set<Long>>> future : futureList) {
            if (future != null) {
                Map<Long, Set<Long>> skuMap = future.get();
                goodsCouponMap.putAll(skuMap);
            }
        }

        return goodsCouponMap;
    }



    /**
     * 批量获取商品可用券配置信息
     * @return 券缓存信息
     * @throws Exception
     */
    public Map<Long, ConfigInfoCachePo> batchGetConfigInfo(Set<Long> configIdSet) throws Exception {

        Map<Long, ConfigInfoCachePo> resultMap = Maps.newHashMapWithExpectedSize(configIdSet.size());

        List<Long> configIds = Lists.newArrayList(configIdSet);
        List<Future<Map<Long, ConfigInfoCachePo>>> futureList = new ArrayList<>();
        List<List<Long>> configPartList = Lists.partition(configIds,10);

        for (List<Long> partIds : configPartList) {
            Future<Map<Long, ConfigInfoCachePo>> future = queryGoodsAsyncExecutor.submit(() -> {
                try {
                    Map<Long, ConfigInfoCachePo> skuCouponRelMap = couponConfigRedisDao.batchGetConfigInfoCache(partIds);
                    if (MapUtils.isNotEmpty(skuCouponRelMap)) {
                        return skuCouponRelMap;
                    }

                    return Collections.emptyMap();
                }catch (Exception e) {
                    log.error("CouponConfigRepository batchGetConfigInfo Exception configs:{}", partIds, e);
                    throw ExceptionHelper.create(GeneralCodes.InternalError, "批量查询券配置数据失败");
                }
            });

            futureList.add(future);
        }

        for (Future<Map<Long, ConfigInfoCachePo>> future : futureList) {
            if (future != null) {
                Map<Long, ConfigInfoCachePo> skuMap = future.get();
                resultMap.putAll(skuMap);
            }
        }

        return resultMap;
    }

    /**
     * 扫描已过期的记录列表方法
     *
     * @param startId       起始id，不包含当前id
     * @param queryTime     查询时间
     * @param batchSize     批大小
     * @return              记录列表
     */
    public List<CouponConfigPO> selectCompletedBatch(long startId, long queryTime, int batchSize) {
        return couponConfigMapper.selectCompletedBatch(startId, queryTime, batchSize);
    }

    @Transactional(transactionManager = "xmPulseCouponTransactionManager", rollbackFor = {Exception.class, Error.class})
    public void budgetRelease(CouponConfigPO couponConfigPO, long releaseCount) throws BizError {
        // 先更新DB后调用接口释放预算，释放预算失败后进行回滚
        // 1、更新释放总额字段
        couponConfigPO.setReleaseCount(releaseCount);
        couponConfigMapper.update(couponConfigPO);
        // 2、调用总额释放
        log.info("call br release budget, batchId {}, releaseCount {}", couponConfigPO.getId(), releaseCount);
        double gap = (double)releaseCount * couponConfigPO.getPromotionValue()/100;
        couponBudgetService.reduceBudget(couponConfigPO, gap , couponConfigPO.getBrApplyNo());
    }

    /**
     * 获取当前投放场景下所有已上线、未过期的
     *
     * @param sendScene 投放场景
     * @return 记录列表
     */
    public List<CouponConfigPO> getCouponConfigBySendScene(String sendScene) {
        return couponConfigMapper.getCouponConfigBySendScene(sendScene);
    }

    /**
     * es索引 forceMerge
     */
    public void forceMergeIndex (){
        couponInvertedTOBHelper.forceMergeIndex();
    }
}
