package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.po;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.request.ReviewGroupSaveRequest;
import com.xiaomi.nr.coupon.admin.util.beancopy.BeanMapper;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


@Data
public class CouponReviewRelPO implements Serializable {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 提交审批人邮箱前缀
     */
    private String creator;
    /**
     * 一级审批组
     */
    private String reviewGroup;
    /**
     * 备注
     */
    private String remarks;
    /**
     *生成时间
     */
    private Date createTime;
    /**
     * 添加人
     */
    private String addUser;

    /**
     * 生成审核关系PO
     * @param request
     * @return
     */
    public static CouponReviewRelPO buildCouponReviewRelPO(ReviewGroupSaveRequest request) {
        CouponReviewRelPO couponReviewRelPO = new CouponReviewRelPO();
        BeanMapper.copy(request, couponReviewRelPO);
        couponReviewRelPO.setCreateTime(new Date());
        return couponReviewRelPO;
    }
}
