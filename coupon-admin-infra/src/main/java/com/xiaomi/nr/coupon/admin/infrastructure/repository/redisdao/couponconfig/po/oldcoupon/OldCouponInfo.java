package com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.po.oldcoupon;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: he<PERSON><PERSON><PERSON>
 * @Date 2022/3/9 9:33 上午
 * @Version: 1.0
 **/
@Data
public class OldCouponInfo implements Serializable {

    /**
     * 基本信息
     */
    private TypeBase basetype;

    /**
     * 条件信息
     */
    private Condition condition;

    /**
     * 政策信息
     */
    @SerializedName(value = "policy", alternate = {"policy_new"})
    private Policy policy;

}
