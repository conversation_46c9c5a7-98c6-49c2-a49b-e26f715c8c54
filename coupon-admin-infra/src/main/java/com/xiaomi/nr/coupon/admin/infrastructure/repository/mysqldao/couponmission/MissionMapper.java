package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.couponmission;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.couponmission.po.MissionCountItem;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.couponmission.po.MissionJoinConfigPo;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.couponmission.po.MissionPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 券发放任务mapper
 *
 * <AUTHOR>
 */
@Mapper
@Component
public interface MissionMapper {

    String SELECT_SQL = "select id,stat,name,group_id,send_num,type_id,coupon_start_time,coupon_end_time,send_time,send_end_time," +
            "admin_id,admin_name,add_time,approved_admin_id,approved_admin_name,approved_time,area_id,mission_type,email," +
            "tel,department,coupon_days,max_num,now_num,approved_id,group_ids from v3_coupon_mission ";
    String TABLE = "v3_coupon_mission";

    String SELECT_JOIN_SQL = "select m.id as missionId,m.name as missionName,m.stat as missionStatus,m.mission_type as missionType," +
            "m.send_num as sendNum,m.max_num as maxNum,m.coupon_days as couponDays,m.coupon_start_time as couponStartTime," +
            "m.coupon_end_time as couponEndTime,m.add_time as addTime,m.group_ids as groupIds,m.type_id as couponConfigId,t.name as couponName," +
            "t.type as couponTypeCode,t.policy as policy,t.send_channel as sendChannel, " +
            "t.global_start_time as globalCouponStartTime, t.global_end_time as globalCouponEndTime from " +
            "v3_coupon_mission as m left outer join v3_coupon_type as t on m.type_id = t.id ";


    String SELECT_SQL_MISSSION = "select m.id,m.stat,m.name,m.group_id,m.send_num,m.type_id,m.coupon_start_time,m.coupon_end_time,m.send_time,m.send_end_time," +
            "m.admin_id,m.admin_name,m.add_time,m.approved_admin_id,m.approved_admin_name,m.approved_time,m.area_id,m.mission_type,m.email," +
            "m.tel,m.department,m.coupon_days,m.max_num,m.now_num,m.approved_id,m.group_ids from v3_coupon_mission as m left outer join v3_coupon_type as t " +
            " on m.type_id = t.id  ";


    /**
     * 获取区域为中国大陆、12小时内有过变更、或者目前有效的发放任务信息列表
     *
     * @param nowTime   当前时间
     * @param startSize 页面开始值
     * @param pageSize  页面大小
     * @return List<>   券发放任务实体列表
     */
    @Select(SELECT_SQL_MISSSION +
            " where m.area_id=2" +
            " and (unix_timestamp(m.last_update_time) > #{nowTime}" +
            " or unix_timestamp(now())+ m.coupon_days*24*3600 <= t.global_end_time and m.coupon_end_time <= t.global_end_time) " +
            " order by m.add_time desc" +
            " limit #{startSize,jdbcType=BIGINT},#{pageSize,jdbcType=BIGINT}")
    List<MissionPo> getMissionAll(@Param("nowTime") long nowTime, @Param("startSize") int startSize, @Param("pageSize") int pageSize);


    /**
     * 根据missionId查询发放任务现相关信息
     *
     * @param missionId 券发放任务id
     * @return MissionJoinConfigPo 券发放任务和券配置结果实体
     */
    @Select(SELECT_JOIN_SQL + " where m.id = #{missionId}")
    MissionJoinConfigPo getMissionByIdOne(@Param("missionId") long missionId);


    /**
     * 根据sendChannel等字段分页获取有效的发放任务列表
     *
     * @param nowTime       当前时间
     * @param lastMissionId 上一页面的最后一个券发放任务id
     * @param pageSize      页面大小
     * @return List<>       券发放任务和券配置实体
     */
    @Select(
            SELECT_JOIN_SQL + " where t.area_id = 2 and t.is_code = 2 " +
                    " and t.global_end_time > #{nowTime} " +
                    " and unix_timestamp(now())+ m.coupon_days*24*3600 <= t.global_end_time " +
                    " and t.send_channel=#{sendChannel} "+
                    " and m.coupon_end_time <= t.global_end_time " +
                    " and m.id > #{lastMissionId} order by m.id asc limit #{pageSize}")
    List<MissionJoinConfigPo> getMissionByChannel(@Param("nowTime") long nowTime, @Param("sendChannel")String sendChannel,
                                                  @Param("lastMissionId") long lastMissionId,@Param("pageSize") Integer pageSize);


    /**
     * 根据sendChannel等字段获取有效的券发放任务总数
     *
     * @param nowTime     当前时间
     * @return int        有效券发放任务总数
     */
    @Select(
            "select count(1) as total, t.send_channel as sendChannel from " +
                    " v3_coupon_mission as m left outer join v3_coupon_type as t on m.type_id = t.id " +
                    " where t.area_id = 2 and t.is_code = 2 " +
                    " and t.global_end_time > #{nowTime} " +
                    " and unix_timestamp(now())+ m.coupon_days*24*3600 <= t.global_end_time " +
                    " and m.coupon_end_time <= t.global_end_time " +
                    " group by t.send_channel")
    List<MissionCountItem> getMissionByChannelCount(@Param("nowTime") long nowTime, @Param("sendChannel")String sendChannel);


    /**
     * 根据发放任务ID列表批量获取发放任务信息
     * @param missionIdList 发放任务ID列表
     * @return List<>       发放任务信息列表
     */
    @Select("<script>" +
            "select m.id as missionId, m.type_id as couponConfigId, m.department as department, t.send_channel as sendChannel " +
            " from v3_coupon_mission as m left outer join v3_coupon_type as t on m.type_id = t.id "+
            " where m.area_id=2 and m.stat = 'approved'" +
            "<if test=\"missionIdList != null and missionIdList.size >0\"> and m.id in " +
            "    <foreach item='missionId' index='index' collection='missionIdList' open='(' separator=',' close=')'>" +
            "         #{missionId}" +
            "    </foreach> " +
            "</if>" +
            "</script>")
    List<MissionJoinConfigPo> getMissionByIdList(@Param("missionIdList") List<Long> missionIdList);

    /**
     * 根据missionId查询发放任务现相关信息
     *
     * @param typeIds 券Id
     * @return MissionJoinConfigPo 券发放任务和券配置结果实体
     */
    @Select("<script>select * from " +TABLE+
            " where type_id in <foreach collection='typeIds' item='typeId' index='index' open='(' close=')' separator=','>#{typeId}</foreach></script>")
    List<MissionPo> getMissionByTypeIds(@Param("typeIds") List<Long> typeIds);



    @Select("<script>select * from " +TABLE+
            " where id in <foreach collection='missionIds' item='id' index='index' open='(' close=')' separator=','>#{id}</foreach></script>")
    List<MissionPo> getMissionByMissionIds(@Param("missionIds") List<Long> missionIds);

    @Select("select id from "+TABLE+" where last_update_time> #{updateTime}")
    List<Long> getMissionIdByUpdateTime(@Param("updateTime") String updateTime);


    /**
     * 查询是否券的第一条任务的id
     * @return
     */
    @Select("<script>select min(id) from " +TABLE+
            " where type_id =#{typeId}</script>")
    Long getMissionIdByTypeId(@Param("typeId") Long typeId);

}
