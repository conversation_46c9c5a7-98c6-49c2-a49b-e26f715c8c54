package com.xiaomi.nr.coupon.admin.infrastructure.rpc;

import com.xiaomi.nr.coupon.api.dto.autotest.CouponCleanDataRequest;
import com.xiaomi.nr.coupon.api.service.DubboAutoTestService;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class CouponProxyService {

    @Reference(check = false, interfaceClass = DubboAutoTestService.class, group = "${dubbo.group}", version = "1.0", timeout = 5000,cluster = "broadcast")
    private DubboAutoTestService dubboAutoTestService;

    /**
     * 清除券配置本地缓存
     * @return
     */
    public void cleanCouponData(List<Long> configIds) throws Exception {
        try {
            CouponCleanDataRequest request = new CouponCleanDataRequest();
            request.setConfigIds(configIds);
            dubboAutoTestService.cleanCouponData(request);
        } catch (Exception e) {
            log.error("CouponProxyService cleanCouponData Exception configIds:{}", configIds, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "清除券缓存失败");
        }
    }









}
