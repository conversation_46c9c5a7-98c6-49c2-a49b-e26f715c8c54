package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.userpoint.po;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/12/6 17:01
 */
@Data
public class UserPointsLogPo implements Serializable {
    private static final long serialVersionUID = -8183326206075339588L;

    /**
     * 日志ID
     */
    private Long id;

    /**
     * 积分记录ID
     */
    private Long pointId;

    /**
     * 用户编号
     */
    private Long mid;

    /**
     * 日志类型 0 发放 1 消费 2 退还
     */
    private Integer type;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 变动积分
     */
    private Long incomePoints;

    /**
     * 原可用积分
     */
    private Long oldPoints;

    /**
     * 新可用积分
     */
    private Long newPoints;

    /**
     * 描述
     */
    private String typeDesc;

    /**
     * 添加时间
     */
    private Long addTime;
}
