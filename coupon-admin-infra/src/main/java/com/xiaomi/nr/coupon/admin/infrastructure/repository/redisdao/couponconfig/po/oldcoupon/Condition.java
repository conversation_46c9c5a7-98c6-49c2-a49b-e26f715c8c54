package com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.po.oldcoupon;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 券条件信息
 */
@Data
public class Condition implements Serializable {


    @SerializedName("id")
    private long id;
    /**
     * 活动类型
     */
    private Integer type = 0;

    /**
     * 区域id
     */
    @SerializedName("area_id")
    private Long areaId = 0L;


    /**
     * 能参与的客户端列表
     */
    private List<String> client;

    /**
     * 政策里的限件限额等信息，这里单独复制了一份
     */
    private List<QuotaEle> quota;

    /**
     * 可参与的商品
     */
    @SerializedName("goods_include")
    private List<CompareItem> goodsInclude;

    /**
     * 排除商品
     */
    @SerializedName("goods_inexclude")
    private CompareItem goodsInexclude;

    /**
     * 整单不可用商品
     */
    @SerializedName("goods_exclude")
    private CompareItem goodsExclude;

    /**
     * 套装是否允许部分商品参加活动(套装是否拆分) 1允许 2不允许
     */
    @SerializedName("check_package")
    private Integer checkPackage = 2;

    /**
     * 特价商品是否参与  1表示不参与，2表示参与
     */
    @SerializedName("check_price")
    private Integer checkPrice = 2;

    /**
     * 是否线下可用 1仅线上使用，2仅线下使用，3均可使用
     */
    private Integer offline = 0;

    /**
     * 使用渠道
     */
    @SerializedName("use_channel")
    private String useChannel;

    /**
     * 是否限制指定地区可用 true：是 false：否
     */
    @SerializedName("assign_area")
    private boolean assignArea;

    /**
     * 指定可用地区配置
     * 区域类型（1:省 2:市） => 区域ID列表
     */
    @SerializedName("assign_area_config")
    private Map<Integer, List<Integer>> assignAreaConfig;

    /**
     * 使用平台对应的门店 使用渠道类型(1:小米商城 2:直营店/专卖店 3:授权店 4:堡垒店) => 渠道可用门店配置
     */
    @SerializedName("use_channel_store")
    private Map<Integer, UseChannelStoreItem> useChannelStore;

}
