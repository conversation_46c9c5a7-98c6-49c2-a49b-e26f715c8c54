package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.xmstore;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.xmstore.po.XmStoreCouponPO;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Component;

/**
 * @Description: 优惠吗
 * @Date: 2022.03.11 0:24
 */
@Mapper
@Component
public interface XmStoreCouponMapper {

    String COUPON_CONFIG_TABLE = "tbl_coupon_class";

    /**
     * 获取券配置id
     * @param configId
     * @return
     */
    @Select("select class_id from " + COUPON_CONFIG_TABLE + " where class_id = #{configId}")
    Long getXmStoreCouponIdByConfigId(@Param("configId") long configId);

    /**
     * 老券写入新表
     * @return
     */
    @Insert("insert into " + COUPON_CONFIG_TABLE + " (site_id,class_id,only_check,name,range_desc,coupon_type,send_limit,is_code,start_date,end_date" +
            ",check_package,check_price,client,goods_include,policy,status,select_type,created_at,created_by,updated_at,updated_by)"+
            " values " +
            "(#{siteId},#{classId},#{onlyCheck},#{name},#{rangeDesc},#{couponType},#{sendLimit},#{isCode},#{startDate},#{endDate}," +
            "#{checkPackage},#{checkPrice},#{client},#{goodsInclude},#{policy},#{status},#{selectType},#{createdAt},#{createdBy},#{updatedAt},#{updatedBy})")
    Long insertXmStoreCoupon(XmStoreCouponPO po);

    /**
     * 更新优惠券
     * @param po
     * @return
     */
    @Update("<script>update " + COUPON_CONFIG_TABLE + " <set>" +
            "<if test='name!=null'>name=#{name},</if>"+
            "<if test='rangeDesc!=null'>range_desc=#{rangeDesc},</if>"+
            "<if test='couponType!=null'>coupon_type=#{couponType},</if>"+
            "<if test='sendLimit!=null'>send_limit=#{sendLimit},</if>"+
            "<if test='startDate!=null'>start_date=#{startDate},</if>"+
            "<if test='endDate!=null'>end_date=#{endDate},</if>"+
            "<if test='client!=null'>client=#{client},</if>"+
            "<if test='goodsInclude!=null'>goods_include=#{goodsInclude},</if>"+
            "<if test='policy!=null'>policy=#{policy},</if>"+
            "<if test='status!=null'>status=#{status},</if>"+
            "<if test='selectType!=null'>select_type=#{selectType},</if>"+
            "<if test='updatedAt!=null'>updated_at=#{updatedAt},</if>"+
            "<if test='updatedBy!=null'>updated_by=#{updatedBy},</if>"+
            "</set><where> class_id=#{classId}</where></script>")
    Long updateXmStoreCoupon(XmStoreCouponPO po);

    /**
     * 更新券状态
     * @param status
     * @param couponId
     * @return
     */
    @Update("update " + COUPON_CONFIG_TABLE + " set status= #{status}, updated_at=unix_timestamp(now()) where class_id=#{couponId} ")
    Long updateXmStoreCouponStatus(@Param("status") int status, @Param("couponId") long couponId);
}
