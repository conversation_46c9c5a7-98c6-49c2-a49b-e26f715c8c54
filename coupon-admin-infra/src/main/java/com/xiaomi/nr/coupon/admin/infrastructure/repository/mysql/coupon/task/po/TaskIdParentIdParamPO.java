package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.task.po;

import lombok.Data;

import java.io.Serializable;

/**
 灌券任务id，父任务id, params
 */
@Data
public class TaskIdParentIdParamPO implements Serializable {

    private static final long serialVersionUID = 7373543268937066328L;
    /**
     * 任务id
     */
    private Long taskId;
    /**
     * 父任务id
     */
    private Long parentId;
    /**
     * 参数
     */
    private String params;

    /**
     *快慢任务类型（0-慢 1-快），后期根据param转换
     */
    private int speed;
}
