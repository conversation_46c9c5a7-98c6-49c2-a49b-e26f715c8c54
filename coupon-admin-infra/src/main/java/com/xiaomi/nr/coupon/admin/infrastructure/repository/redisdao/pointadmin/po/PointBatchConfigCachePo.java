package com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.pointadmin.po;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/12/6 16:46
 */
@Data
public class PointBatchConfigCachePo implements Serializable {
    private static final long serialVersionUID = 4257062556825215863L;

    /**
     * 配置ID
     */
    @SerializedName("id")
    private Long id;

    /**
     * 积分批次名称
     */
    @SerializedName("name")
    private String name;

    /**
     * 预算池id
     */
    @SerializedName("bi")
    private Long budgetId;

    /**
     * 预算申请单号
     */
    @SerializedName("budget_apply_no")
    private String budgetApplyNo;

    /**
     * 行号
     */
    @SerializedName("line_num")
    private Long lineNum;

    /**
     * 预算单创建时间
     */
    @SerializedName("budget_create_time")
    private String budgetCreateTime;

    /**
     * 发放场景
     */
    @SerializedName("ss")
    private String sendScene;

    /**
     * 发放场景
     */
    @SerializedName("status")
    private Integer status;

    /**
     * 发放周期开始时间
     */
    @SerializedName("st")
    private Long startTime;

    /**
     * 发放周期结束时间
     */
    @SerializedName("et")
    private Long endTime;

    /**
     * 积分总额
     */
    @SerializedName("tp")
    private Long totalPoints;

    /**
     * 预警阈值
     */
    @SerializedName("wr")
    private Integer warningRatio;

    /**
     * 使用时间类型（1相对时间 2绝对时间）
     */
    @SerializedName("utt")
    private Integer useTimeType;

}
