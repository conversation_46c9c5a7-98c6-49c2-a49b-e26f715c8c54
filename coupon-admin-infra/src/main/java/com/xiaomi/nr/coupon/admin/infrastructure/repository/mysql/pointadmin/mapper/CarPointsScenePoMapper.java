package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.pointadmin.mapper;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.scene.po.CouponScenePO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.pointadmin.po.CarPointsScenePo;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/6
 */
@Mapper
public interface CarPointsScenePoMapper {

    /**
     * 场景添加
     * @param carPointsScenePo
     * @return
     */
    @Insert("insert into car_points_scene (name,parent_id,scene_code,scene_desc,status,creator,enum_code,assign_mode,content) values " +
            "(#{name},#{parentId},#{sceneCode},#{sceneDesc},#{status},#{creator},#{enumCode},#{assignMode},#{content})")
    Integer insert(CarPointsScenePo carPointsScenePo);

    /**
     * 场景修改
     * @param carPointsScenePo
     * @return
     */
    @Update("update car_points_scene set name=#{name},parent_id=#{parentId},scene_desc=#{sceneDesc},modifier=#{modifier},content=#{content} where scene_code=#{sceneCode}")
    Integer update(CarPointsScenePo carPointsScenePo);

    /**
     * 根据id精确查询场景
     * @param id
     * @return
     */
    @Select("select * from car_points_scene where id=#{id}")
    CarPointsScenePo searchSceneById(@Param("id") Long id);

    /**
     * 根据code精确查询场景
     * @param sceneCode
     * @return
     */
    @Select("select * from car_points_scene where scene_code=#{sceneCode}")
    CarPointsScenePo searchSceneBySceneCode(@Param("sceneCode") String sceneCode);

    /**
     * 查询有效的场景列表
     * @param online
     * @return
     */
    @Select("<script>select name,parent_id,scene_code,scene_desc,status,creator,create_time,content " +
            "from car_points_scene " +
            "where 1=1 " +
            "<if test='online'> and status=1 </if>" +
            "</script>")
    List<CarPointsScenePo> searchAllValidScene(@Param("online") boolean online);

    /**
     * 分页查询有效的场景列表
     * @param online
     * @return
     */
    @Select("<script>select name,parent_id,scene_code,scene_desc,status,creator,create_time,content " +
            "from car_points_scene " +
            "where 1=1 " +
            "<if test='online'> and status=1 </if>" +
            "<if test='keyWord != null and keyWord != \"\"'> and name like CONCAT('%', #{keyWord}, '%') </if>" +
            "order by create_time desc" +
            "</script>")
    List<CarPointsScenePo> searchAllValidScenePage(@Param("online") boolean online, @Param("keyWord") String keyWord);

    /**
     * 更新场景状态
     * @param id
     * @param status
     * @return
     */
    @Update("update car_points_scene set status=#{status} where id=#{id}")
    Integer updateStatusById(@Param("id") long id, @Param("status") int status);

    /**
     * 根据编码查询状态
     * @param sceneCode
     * @return
     */
    @Select("select * from car_points_scene where scene_code=#{sceneCode}")
    CouponScenePO selectBySceneCode(@Param("sceneCode")String sceneCode);

    /**
     * 通过
     * @param name
     * @return
     */
    @Select("select * from car_points_scene where name=#{name}")
    CouponScenePO searchSceneByName(String name);


    /**
     * 批理获取场景信息
     *
     * @param sceneCodeList List
     * @return List
     */
    @Select("<script>" +
                "select * " +
                "from car_points_scene " +
                "<if test='sceneCodeList!=null and sceneCodeList.size() > 0'>" +
                    " where scene_code in <foreach item='sceneCode' index='index' collection='sceneCodeList' open='(' separator=',' close=')'>#{sceneCode}</foreach>" +
                "</if>" +
            "</script>")
    List<CarPointsScenePo> batchGetSceneInfo(@Param("sceneCodeList") List<String> sceneCodeList);

    /**
     * 查询所有场景枚举代码
     * @param
     * @return
     */
    @Select("select enum_code from car_points_scene ")
    List<String> searchAllEnumCode();
}