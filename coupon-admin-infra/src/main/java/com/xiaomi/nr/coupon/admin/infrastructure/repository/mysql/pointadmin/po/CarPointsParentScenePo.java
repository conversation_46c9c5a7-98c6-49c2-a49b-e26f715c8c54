package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.pointadmin.po;


import lombok.Data;

import java.util.Date;

/**
 * @Description 汽车积分一级场景
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
public class CarPointsParentScenePo {
    /**
     * 一级场景id
     */
    private Long id;

    /**
     * 一级发放场景名称
     */
    private String name;

    /**
     * 一级场景描述
     */
    private String sceneDesc;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;
}
