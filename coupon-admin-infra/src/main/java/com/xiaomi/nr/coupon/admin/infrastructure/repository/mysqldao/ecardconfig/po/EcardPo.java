package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.ecardconfig.po;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class EcardPo implements Serializable {
    /**
     * ecard编号
     */
    @SerializedName("card_id")
    private Long cardId;

    /**
     * ecard密码
     */
    @SerializedName("password")
    private String password;

    /**
     * ecard密码索引
     */
    @SerializedName("password_idx")
    private String passwordIdx;

    /**
     * sn
     */
    @SerializedName("sn")
    private String sn;

    /**
     * 用户ID
     */
    @SerializedName("user_id")
    private Long userId;

    /**
     * ecard类型ID
     */
    @SerializedName("type_id")
    private Integer typeId;

    /**
     * e卡原始SKU
     */
    @SerializedName("sku")
    private String sku;

    /**
     * 现金券金额(元)
     */
    @SerializedName("money")
    private BigDecimal money;

    /**
     * 现金券余额(元)
     */
    @SerializedName("balance")
    private BigDecimal balance;

    /**
     * 销售金额(元)
     */
    @SerializedName("sale_price")
    private String salePrice;

    /**
     * 生成任务ID
     */
    @SerializedName("mission_id")
    private String missionId;

    /**
     * 区域id
     */
    @SerializedName("area_id")
    private Integer areaId;

    /**
     * 有效期开始时间
     */
    @SerializedName("start_time")
    private String startTime;

    /**
     * 有效期结束时间
     */
    @SerializedName("end_time")
    private String endTime;

    /**
     * 0预开卡，1开卡，2开卡作废，3激活，4激活作废，5绑定，6绑定作废
     */
    @SerializedName("stat")
    private Integer stat;

    /**
     * 0未锁定，1锁定
     */
    @SerializedName("is_locked")
    private Integer isLocked;

    /**
     * 1虚拟卡,0实物卡
     */
    @SerializedName("is_virtual")
    private Integer isVirtual;

    /**
     * 是否是临时卡
     */
    @SerializedName("is_casual")
    private Integer isCasual;

    /**
     * 购买的用户ID
     */
    @SerializedName("from_user_id")
    private Long fromUserId;

    /**
     * 购买的订单号
     */
    @SerializedName("from_order_id")
    private String fromOrderId;

    /**
     * 生成类型, reissue(后台生成),marketing(任务生成),external(接口生成)
     */
    @SerializedName("send_type")
    private String sendType;

    /**
     * 添加时间
     */
    @SerializedName("add_time")
    private Long addTime;

    /**
     * 作废时间
     */
    @SerializedName("invalid_time")
    private Long invalidTime;

    /**
     * 激活时间
     */
    @SerializedName("active_time")
    private Long activeTime;

    /**
     * 绑定时间
     */
    @SerializedName("bind_time")
    private Long bindTime;

    /**
     * 延期次数
     */
    @SerializedName("delay_times")
    private Integer delayTimes;

    /**
     * 最后更新时间
     */
    @SerializedName("last_update_time")
    private String lastUpdateTime;
}
