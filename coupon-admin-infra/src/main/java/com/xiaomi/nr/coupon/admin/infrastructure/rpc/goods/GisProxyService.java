package com.xiaomi.nr.coupon.admin.infrastructure.rpc.goods;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaomi.goods.gis.api.*;
import com.xiaomi.goods.gis.dto.PageInfo;
import com.xiaomi.goods.gis.dto.batched.offline.BatchedAvailablePageRequest;
import com.xiaomi.goods.gis.dto.batched.offline.BatchedAvailablePageResponse;
import com.xiaomi.goods.gis.dto.category.CategoryListRequest;
import com.xiaomi.goods.gis.dto.category.CategoryListResponse;
import com.xiaomi.goods.gis.dto.category.CategoryResponse;
import com.xiaomi.goods.gis.dto.goodsInfoNew.*;
import com.xiaomi.goods.gis.dto.price.*;
import com.xiaomi.goods.gis.dto.sku.offline.SkuAvailablePageDTO;
import com.xiaomi.goods.gis.dto.sku.offline.SkuAvailablePageRequest;
import com.xiaomi.goods.gis.dto.sku.offline.SkuAvailablePageResponse;
import com.xiaomi.goods.gis.enums.PromotionSaleChannelEnum;
import com.xiaomi.goods.gms.api.batch.BatchedInfoService;
import com.xiaomi.goods.gms.api.sku.SkuInfoService;
import com.xiaomi.goods.gms.dto.batch.BatchedListRequest;
import com.xiaomi.goods.gms.dto.batch.BatchedPageResponse;
import com.xiaomi.goods.gms.dto.sku.SkuPageRequest;
import com.xiaomi.goods.gms.dto.sku.SkuPageResponse;
import com.xiaomi.nr.coupon.admin.api.enums.BizSubTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.UseChannelsEnum;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsItemTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.constant.coupon.CouponConstant;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.ResultValidator;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * https://xiaomi.f.mioffice.cn/docs/dock4PpH4L00KLK9HPDUXX5irVe#
 */
@Slf4j
@Service
public class GisProxyService {

    @Reference(check = false, interfaceClass = CategoryService.class, group = "${gis.dubbo.group}", version = "0.1", timeout = 5000)
    private CategoryService categoryService;

    @Reference(check = false, interfaceClass = SkuInfoService.class, group = "${gis.dubbo.group}", version = "1.0", timeout = 5000)
    private SkuInfoService skuInfoService;

    @Reference(check = false, interfaceClass = BatchedInfoService.class, group = "${gis.dubbo.group}", version = "1.0", timeout = 5000)
    private BatchedInfoService batchedInfoService;

    @Reference(check = false, interfaceClass = PriceInfoService.class, group = "${gis.dubbo.group}", version = "0.1", timeout = 5000)
    private PriceInfoService priceInfoService;

    //分页查询sku信息
    @Reference(check = false, interfaceClass = SkuInfoOfflineService.class, group = "${gis.dubbo.group.offline}", version = "0.1", timeout = 5000)
    private SkuInfoOfflineService skuInfoOfflineService;

    //分页查询套装信息
    @Reference(check = false, interfaceClass = BatchedOfflineService.class, group = "${gis.dubbo.group.offline}", version = "0.1", timeout = 5000)
    private BatchedOfflineService batchedOfflineService;

    @Reference(check = false, interfaceClass = GoodsInfoNewService.class, timeout = 3000, group = "${gis.dubbo.group}", version = "0.1")
    private GoodsInfoNewService goodsInfoNewService;

    @Autowired
    private ThreadPoolTaskExecutor queryGoodsAsyncExecutor;

    /**
     * 根据skuid查询sku售价
     */
    @SuppressWarnings("unchecked")
    public Map<Long, GoodsChannelPriceDto> getSkuChannelPriceMap(List<Long> skuIds, Integer channel) throws Exception {
        String promotionChannel = getChannel(channel);
        if (promotionChannel == null) {
            return Collections.EMPTY_MAP;
        }

        List<Future<Map<Long, GoodsChannelPriceDto>>> futureList = new ArrayList<>();

        List<List<Long>> skuPartList = Lists.partition(skuIds, 100);
        for (List<Long> skuIdsPart : skuPartList) {
            Future<Map<Long, GoodsChannelPriceDto>> future = queryGoodsAsyncExecutor.submit(() -> {
                try {
                    SkuChannelPriceMapRequest request = new SkuChannelPriceMapRequest();
                    request.setSkuList(skuIdsPart);
                    request.setPromotionChannel(promotionChannel);
                    Result<SkuChannelPriceMapResponse> result = priceInfoService.getSkuChannelPriceMap(request);
                    ResultValidator.validate(result, "查询sku售价失败");
                    if (result.getData() != null) {
                        return result.getData().getPriceInfoMap();
                    }
                    return Collections.EMPTY_MAP;
                } catch (BizError bizError) {
                    log.error("GmsProxyService getSkuChannelPriceMap bizError skuIdsPart:{}", skuIdsPart, bizError);
                    throw bizError;
                } catch (Exception e) {
                    log.error("GmsProxyService getSkuChannelPriceMap Exception skuIdsPart:{}", skuIdsPart, e);
                    throw ExceptionHelper.create(GeneralCodes.InternalError, "查询sku售价失败");
                }
            });
            futureList.add(future);
        }

        Map<Long, GoodsChannelPriceDto> returnMap = new HashMap<>();
        for (Future<Map<Long, GoodsChannelPriceDto>> future : futureList) {
            if (future != null) {
                Map<Long, GoodsChannelPriceDto> infoDto = future.get();
                returnMap.putAll(infoDto);
            }
        }

        return returnMap;


    }


    /**
     * 根据套装id查询套装售价
     */
    @SuppressWarnings("unchecked")
    public Map<Long, BatchedChannelPriceDto> getBatchedChannelPriceMap(List<Long> packageIds, Integer channel) throws Exception {
        String promotionChannel = getChannel(channel);
        if (promotionChannel == null) {
            return Collections.EMPTY_MAP;
        }

        List<Future<Map<Long, BatchedChannelPriceDto>>> futureList = new ArrayList<>();

        List<List<Long>> packagePartList = Lists.partition(packageIds, 100);
        for (List<Long> packageIdsPart : packagePartList) {
            Future<Map<Long, BatchedChannelPriceDto>> future = queryGoodsAsyncExecutor.submit(() -> {
                try {
                    BatchedChannelPriceMapRequest request = new BatchedChannelPriceMapRequest();
                    request.setBatchedIds(packageIdsPart);
                    request.setPromotionChannel(promotionChannel);
                    Result<BatchedChannelPriceMapResponse> result = priceInfoService.getBatchedChannelPriceMap(request);
                    ResultValidator.validate(result, "查询套装售价失败");
                    if (result.getData() != null) {
                        return result.getData().getPriceInfoMap();
                    }
                    return Collections.EMPTY_MAP;
                } catch (BizError bizError) {
                    log.error("GmsProxyService getBatchedChannelPriceMap bizError packageIdsPart:{}", packageIdsPart, bizError);
                    throw bizError;
                } catch (Exception e) {
                    log.error("GmsProxyService getBatchedChannelPriceMap Exception packageIdsPart:{}", packageIdsPart, e);
                    throw ExceptionHelper.create(GeneralCodes.InternalError, "查询套装售价失败");
                }
            });
            futureList.add(future);
        }

        Map<Long, BatchedChannelPriceDto> returnMap = new HashMap<>();
        for (Future<Map<Long, BatchedChannelPriceDto>> future : futureList) {
            if (future != null) {
                Map<Long, BatchedChannelPriceDto> infoDto = future.get();
                returnMap.putAll(infoDto);
            }
        }

        return returnMap;


    }

    private String getChannel(Integer channel) {
        if (UseChannelsEnum.XIAOMI_SHOP.getValue() == channel) {
            return PromotionSaleChannelEnum.MALL.getMsg();
        }
        if (UseChannelsEnum.DIRECTSALE_STORE.getValue() == channel) {
            return PromotionSaleChannelEnum.MI.getMsg();
        }
        if (UseChannelsEnum.EXCLUSIVE_SHOP.getValue() == channel) {
            return PromotionSaleChannelEnum.JM.getMsg();
        }
        return null;
    }


    /**
     * 查询所有分类
     */
    public Map<Integer, List<CategoryResponse>> getCategoryList(int serviceId, int cateId) throws Exception {
        try {
            CategoryListRequest request = new CategoryListRequest();
            request.setServiceId(serviceId);
            request.setCateId(cateId);
            Result<CategoryListResponse> result = categoryService.getCategoryList(request);
            log.info("GisProxyService getCategoryList serviceId:{},cateId:{},result:{}", serviceId, cateId, result);
            ResultValidator.validate(result, "查询分类失败");
            if (result.getData() != null) {
                return result.getData().getLevelMap();
            }
            return Collections.EMPTY_MAP;
        } catch (Exception e) {
            log.error("GisProxyService getCategoryList Exception serviceId:{},cateId:{}", serviceId, cateId, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "查询分类失败");
        }
    }


    /**
     * 分页查询商品信息
     *
     * @param request GmsGoodsListRequest
     * @return GmsGoodsListDto
     * @throws BizError 业务异常
     */
    public SkuPageResponse querySkuList(SkuPageRequest request) throws BizError {
        try {
            Result<SkuPageResponse> result = skuInfoService.querySkuList(request);
            ResultValidator.validate(result, "gms分页查询SKU信息失败");
            return result.getData();
        } catch (BizError bizError) {
            log.error("GisProxyService querySkuList bizError request:{}", request, bizError);
            throw bizError;
        } catch (Exception e) {
            log.error("GisProxyService querySkuList Exception request:{}", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "gms分页查询SKU信息失败");
        }
    }


    /**
     * 分页查询套装信息
     *
     * @param request BatchedListRequest
     * @return BatchedPageResponse
     * @throws BizError 业务异常
     */
    public BatchedPageResponse queryPackageList(BatchedListRequest request) throws BizError {
        try {
            Result<BatchedPageResponse> result = batchedInfoService.queryBatched(request);
            ResultValidator.validate(result, "分页查询套装信息失败");
            return result.getData();
        } catch (BizError bizError) {
            log.error("GisProxyService queryPackageList bizError request:{}", request, bizError);
            throw bizError;
        } catch (Exception e) {
            log.error("GisProxyService queryPackageList Exception request:{}", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "gms分页查询套装信息失败");
        }
    }


    /**
     * gis分页查询SKU信息
     *
     * @param request request
     * @return SkuAvailablePageResponse
     * @throws BizError 业务异常
     */
    public SkuAvailablePageResponse pageAvailableSkuGis(SkuAvailablePageRequest request) throws BizError {
        log.info("GisProxyService.pageAvailableSkuGis begin, request = {}", request);
        try {
            Result<SkuAvailablePageResponse> result = skuInfoOfflineService.pageAvailableSku(request);
            ResultValidator.validate(result, "gis分页查询SKU信息code不为0");

            log.info("GisProxyService.pageAvailableSkuGis finished, request = {}, result = {}", request, result);
            return result.getData();
        } catch (BizError bizError) {
            log.error("GisProxyService pageAvailableSkuGis bizError request:{}", request, bizError);
            throw bizError;
        } catch (Exception e) {
            log.error("GisProxyService pageAvailableSkuGis Exception request:{}", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "gis分页查询SKU信息异常");
        }

    }


    /**
     * gis分页查询套装信息
     *
     * @param request request
     * @return BatchedAvailablePageResponse
     * @throws BizError 业务异常
     */
    public BatchedAvailablePageResponse pageAvailableBatchedGis(BatchedAvailablePageRequest request) throws BizError {
        try {
            Result<BatchedAvailablePageResponse> result = batchedOfflineService.pageAvailableBatched(request);
            ResultValidator.validate(result, "gis分页查询套装信息code不为0");
            return result.getData();
        } catch (BizError bizError) {
            log.error("GisProxyService pageAvailableBatchedGis bizError request:{}", request, bizError);
            throw bizError;
        } catch (Exception e) {
            log.error("GisProxyService pageAvailableBatchedGis Exception request:{}", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "gis分页查询套装信息异常");
        }

    }

    /**
     * 根据gid获取sku
     *
     * @param gid
     * @return
     * @throws BizError
     */
    public Long getSkuByGoodsId(long gid) throws BizError {
        try {
            GoodsMultiInfoRequest request = new GoodsMultiInfoRequest();
            request.setGoodsIdList(Lists.newArrayList(gid));
            request.setNeedPrice(false);
            request.setNeedSpec(false);

            Result<GoodsMultiInfoResponse> result = goodsInfoNewService.getGoodsMultiInfo(request);
            ResultValidator.validate(result, "gis根据gid查询sku信息code不为0");

            if (Objects.isNull(result.getData().getGoodsMap()) || Objects.isNull(result.getData().getGoodsMap().get(gid))) {
                return null;
            }

            return result.getData().getGoodsMap().get(gid).getSku();
        } catch (BizError bizError) {
            log.error("GisProxyService getSkuByGoodsId bizError gid:{}", gid, bizError);
            throw bizError;
        } catch (Exception e) {
            log.error("GisProxyService getSkuByGoodsId Exception gid:{}", gid, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "gis根据gid查询sku信息异常");
        }
    }

    /**
     * 根据ssuId获取ssu信息 , 默认不获取ProductBase
     *
     * @param ssuIds
     * @return
     * @throws Exception
     */
    public Map<Long, GoodsMultiInfoDTO> queryGoodsInfoBySsuIds(List<Long> ssuIds) throws Exception {
        return this.queryGoodsInfoBySsuIds(ssuIds, false);
    }

    /**
     * 根据ssuId获取ssu信息
     *
     * @param ssuIds ssuIds
     * @throws BizError
     */
    public Map<Long, GoodsMultiInfoDTO> queryGoodsInfoBySsuIds(List<Long> ssuIds, boolean needProductBase) throws Exception {
        List<List<Long>> ssuIdPartList = Lists.partition(ssuIds, 100);

        List<Future<Map<Long, GoodsMultiInfoDTO>>> futureList = new ArrayList<>();

        for (List<Long> ssuIdPart : ssuIdPartList) {
            Future<Map<Long, GoodsMultiInfoDTO>> future = queryGoodsAsyncExecutor.submit(() -> {
                try {
                    GoodsMultiInfoRequest request = new GoodsMultiInfoRequest();
                    request.setGoodsIdList(ssuIdPart);
                    request.setNeedPrice(true);
                    request.setNeedProductBase(needProductBase);

                    // 调用gis查询商品信息
                    Result<GoodsMultiInfoResponse> result = goodsInfoNewService.getGoodsMultiInfo(request);

                    ResultValidator.validate(result, "查询ssu失败");

                    GoodsMultiInfoResponse resp = result.getData();
                    if (resp != null && MapUtils.isNotEmpty(resp.getGoodsMap())) {
                        return resp.getGoodsMap();
                    }

                    return Maps.newHashMap();
                } catch (Exception e) {
                    log.error("GisPorxyService.queryGoodsInfoBySsuIds Exception ssuIdPart = {}", ssuIdPart, e);
                    throw ExceptionHelper.create(GeneralCodes.InternalError, "查询ssu失败");
                }
            });
            futureList.add(future);
        }

        Map<Long, GoodsMultiInfoDTO> goodsMap = new HashMap<>();
        for (Future<Map<Long, GoodsMultiInfoDTO>> future : futureList) {
            if (future != null) {
                Map<Long, GoodsMultiInfoDTO> dtoMap = future.get();
                goodsMap.putAll(dtoMap);
            }
        }

        return goodsMap;
    }

    /**
     * GIS分页查询货品信息
     *
     * @param request
     * @return
     * @throws Exception
     */
    public GoodsPageResponse pageGoodsInfoGis(GoodsPageRequest request) throws Exception {
        try {
            Result<GoodsPageResponse> result = goodsInfoNewService.pageGoodsInfo(request);
            ResultValidator.validate(result, "gis分页查询货品信息code不为0");

            log.info("GisPorxyService.pageGoodsInfoGis finished, req = {}, resp = {}", GsonUtil.toJson(request), GsonUtil.toJson(result));

            return result.getData();
        } catch (BizError bizError) {
            log.error("GisPorxyService.pageGoodsInfoGis BizError request = {}", request, bizError);
            throw bizError;
        } catch (Exception e) {
            log.error("GisPorxyService.pageGoodsInfoGis Exception request = {}", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "gis分页查询货品信息异常");
        }
    }

    /**
     * 根据goodsId查货品信息
     *
     * @param goodsIds          货品Ids
     * @param goodsItemTypeEnum 查询的货品类型
     * @return List<GoodsMultiInfoDTO>
     */
    public List<GoodsMultiInfoDTO> queryGoodsMultiInfoByGoodsIds(List<Long> goodsIds, GoodsItemTypeEnum goodsItemTypeEnum) throws Exception {

        List<List<Long>> goodsIdPartList = Lists.partition(goodsIds, 30);

        List<Future<List<GoodsMultiInfoDTO>>> futureList = new ArrayList<>();

        for (List<Long> goodsIdPart : goodsIdPartList) {
            Future<List<GoodsMultiInfoDTO>> future = queryGoodsAsyncExecutor.submit(() -> {
                try {
                    GoodsMultiInfoRequest request = new GoodsMultiInfoRequest();
                    request.setGoodsIdList(goodsIdPart);
                    request.setNeedPrice(true);
                    request.setNeedPriceCarShop(true);
                    Result<GoodsMultiInfoResponse> result = goodsInfoNewService.getGoodsMultiInfo(request);
                    ResultValidator.validate(result, "查询货品信息失败");
                    if (Objects.nonNull(result.getData()) && MapUtils.isNotEmpty(result.getData().getGoodsMap())) {
                        List<GoodsMultiInfoDTO> infoDTOs = new LinkedList<>();
                        Map<Long, GoodsMultiInfoDTO> goodsMap = result.getData().getGoodsMap();
                        Map<Long, GoodsMultiInfoDTO> filterGoodsMap = goodsMap.entrySet().stream().filter(entry -> entry.getValue().getItemType() == goodsItemTypeEnum.getValue()).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
                        infoDTOs.addAll(filterGoodsMap.values());
                        return infoDTOs;
                    }
                    return Collections.emptyList();
                } catch (Exception e) {
                    log.error("GisProxyService.queryGoodsMultiInfoByGoodsIds Exception goodsIdPart = {}", goodsIdPart, e);
                    throw ExceptionHelper.create(GeneralCodes.InternalError, "调用GIS查询货品信息失败");
                }
            });
            futureList.add(future);
        }

        List<GoodsMultiInfoDTO> goodsList = new ArrayList<>();
        for (Future<List<GoodsMultiInfoDTO>> future : futureList) {
            if (Objects.nonNull(future)) {
                List<GoodsMultiInfoDTO> infoDTOList = future.get();
                goodsList.addAll(infoDTOList);
            }
        }

        return goodsList;
    }

    public List<SkuAvailablePageDTO> pageAvailableSkuCarShop(List<Long> ssuIds) throws BizError {
        int pageSize = 20;
        if (ssuIds.size() <= pageSize) {
            return pageAvailableSkuGis(buildSkuAvailablePageRequest(ssuIds, pageSize)).getSkuList();
        }

        List<SkuAvailablePageDTO> resultList = Collections.synchronizedList(new ArrayList<>(ssuIds.size()));
        List<List<Long>> ssuIdPartList = Lists.partition(ssuIds, pageSize);
        final CompletableFuture<?>[] cfs = new CompletableFuture[ssuIdPartList.size()];
        for (int i = 0; i < ssuIdPartList.size(); i++) {
            List<Long> ssuIdPart = ssuIdPartList.get(i);
            cfs[i] = CompletableFuture.runAsync(() -> {
                try {
                    resultList.addAll(pageAvailableSkuGis(buildSkuAvailablePageRequest(ssuIdPart, pageSize)).getSkuList());
                } catch (Exception e) {
                    log.error("pageAvailableSkuGis-{} error", GsonUtil.toJson(ssuIdPart), e);
                }
            }, queryGoodsAsyncExecutor);
        }
        CompletableFuture.allOf(cfs).join();
        return resultList;
    }

    private SkuAvailablePageRequest buildSkuAvailablePageRequest(List<Long> ssuIds, int pageSize) {
        SkuAvailablePageRequest skuPageRequest = new SkuAvailablePageRequest();
        int pageNo = 1;
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(pageNo);
        pageInfo.setPageSize(pageSize);
        skuPageRequest.setPage(pageInfo);
        skuPageRequest.setNeedStock(false);
        skuPageRequest.setNeedBusinessCategory(true);
        // 商品业务类型 仅支持普通3c商品、泛全商品
        skuPageRequest.setBusinessTypeList(Lists.newArrayList(CouponConstant.BUSINESS_TYPE_SELF, CouponConstant.BUSINESS_TYPE_CAIXIAO));
        skuPageRequest.setBizSubType(Lists.newArrayList(BizSubTypeEnum.ORDINARY_3C.getCode(), BizSubTypeEnum.CARRIER_PAN_COMPLETE.getCode()));
        skuPageRequest.setGoodsIdList(ssuIds);
        return skuPageRequest;
    }
}
