package com.xiaomi.nr.coupon.admin.infrastructure.repository;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.ecardconfig.EcardConfigMapper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.ecardconfig.EcardLogConfigMapper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.ecardconfig.po.EcardLogPo;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.ecardconfig.po.EcardPo;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.ecardconfig.po.EcardTypePo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class EcardConfigRepository {

    @Autowired
    private EcardConfigMapper ecardConfigMapper;

    @Autowired
    private EcardLogConfigMapper ecardLogConfigMapper;


    public EcardPo getEcardById(Long cardId) {
        return ecardConfigMapper.queryByCardId(cardId);
    }


    public Map<Integer, String> getEcardTypeNameById(List<Integer> cardTypeIdList) {

        List<EcardTypePo> ecardTypePoList = ecardConfigMapper.queryCardTypeById(cardTypeIdList);

        if (CollectionUtils.isEmpty(ecardTypePoList)) {
            return Collections.emptyMap();
        }

        return ecardTypePoList.stream().collect(Collectors.toMap(EcardTypePo::getId, EcardTypePo::getName, (ironMan, superMan) -> ironMan));
    }

    @Transactional(transactionManager = "xmPulseNatlTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public void updateTime(Long cardId, Long delayTime, EcardLogPo logPo) throws Exception {

        ecardConfigMapper.updateEcardEndTime(cardId, String.valueOf(delayTime));

        ecardLogConfigMapper.insertEcardLog(logPo);
    }

}
