package com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.po.oldcoupon;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 使用门店
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @Date 2022/3/11 10:39 上午
 * @Version: 1.0
 **/
@Data
public class UseChannelStoreItem implements Serializable {
    /**
     * 门店范围 1:全部门店 2:部分门店
     */
    private Integer scope;

    /**
     * 门店ID列表 如果门店范围是全部门店，则此列表为空
     */
    @SerializedName("store_ids")
    private List<String> storeIds;
}
