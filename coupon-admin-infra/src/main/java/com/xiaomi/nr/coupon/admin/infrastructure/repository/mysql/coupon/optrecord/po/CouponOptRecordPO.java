package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.optrecord.po;

import lombok.Data;

import java.sql.Timestamp;


@Data
public class CouponOptRecordPO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 操作类型 1-创建券 2-修改券
     */
    private Integer optType;

    /**
     * 操作信息
     */
    private byte[] optInfo;

    /**
     * 操作状态 0默认 1成功 2失败
     */
    private int optStatus;

    /**
     * 重试次数
     */
    private Integer retryTime;

    /**
     * 创建时间
     */
    private Timestamp createTime;

    /**
     * 修改时间
     */
    private Timestamp updateTime;

}