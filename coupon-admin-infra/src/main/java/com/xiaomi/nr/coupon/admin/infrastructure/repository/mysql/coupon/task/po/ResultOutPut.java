package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.task.po;

import lombok.Data;

import java.io.Serializable;

/**
 * 任务执行结果统计
 */
@Data
public class ResultOutPut implements Serializable {
    private static final long serialVersionUID = -3632521351016633888L;

    /**
     * 任务执行总数
     */
    private long totalCount;
    /**
     * 当前执行总数
     */
    private long currentCount;
    /**
     * 执行成功数
     */
    private long successCount;

    public ResultOutPut(){}
    public ResultOutPut(long totalCount){
        this.totalCount = totalCount;
    }
}
