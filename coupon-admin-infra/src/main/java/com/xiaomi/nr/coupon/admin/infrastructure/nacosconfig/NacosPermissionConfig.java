package com.xiaomi.nr.coupon.admin.infrastructure.nacosconfig;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * @description: nacos渠道券类型配置
 * @author: lvtian
 * @Date 2025/08/06 7:14 下午
 * @Version: 1.0
 **/
@Slf4j
@Component
@Data
@NacosConfigurationProperties(dataId = NacosPermissionConfig.dataId, autoRefreshed = true, ignoreNestedProperties = true, type = ConfigType.PROPERTIES)
public class NacosPermissionConfig {

    public static final String dataId = "coupon_admin_global_permission_config";

    private String permissionConfig;

    @PostConstruct
    public void init() {
        log.info("NacosPermissionConfig loaded: permissionConfig={}", permissionConfig);
    }

}
