package com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.po.oldcoupon;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 商品信息
 *
 * <AUTHOR>
 * @date 2021/3/23
 */
@Data
public class CompareItem implements Serializable {
    private static final long serialVersionUID = -300248743347244862L;

    /**
     * 分类id
     */
    private List<String> cat = new ArrayList<>();

    /**
     * sku
     */
    private List<String> sku = new ArrayList<>();

    /**
     * 商品id
     */
    private List<String> commodity = new ArrayList<>();

    /**
     * 货品id
     */
    private List<String> goods = new ArrayList<>();

    /**
     * 产品ID
     */
    private List<String> product = new ArrayList<>();

    /**
     * 套装ID
     * 注： 因为关键词 对应字段package
     */
    @SerializedName("package")
    private List<String> packages = new ArrayList<>();

    /**
     * ssuId
     */
    private List<String> ssu = new ArrayList<>();

    /**
     * 是否所有商品可参与，0否 1是
     */
    private Integer all = 0;

    /**
     * 活动的修改时间
     */
    @SerializedName("modify_index")
    private Long modifyIndex = 0L;
}
