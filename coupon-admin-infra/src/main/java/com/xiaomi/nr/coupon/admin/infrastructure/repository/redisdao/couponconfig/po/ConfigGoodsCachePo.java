package com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.po;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.*;

/**
 * @description:
 * @author: he<PERSON><PERSON><PERSON>
 * @Date 2022/3/1 4:48 下午
 * @Version: 1.0
 **/
@Data
public class ConfigGoodsCachePo implements Serializable {

    /**
     * 券配置id
     */
    @SerializedName("id")
    private Long configId;

    /**
     * SKU => 产品ID
     */
    @SerializedName("s")
    private Map<Long, Long> skus;

    /**
     * 货品ID => 产品ID
     */
    @SerializedName("gi")
    private Map<Long, Long> goodsIds;

    /**
     * 套装ID => 产品ID
     */
    @SerializedName("pi")
    private Map<Long, Long> packageIds;

    /**
     * 最后更新时间
     */
    @SerializedName("ut")
    private Timestamp updateTime;


}
