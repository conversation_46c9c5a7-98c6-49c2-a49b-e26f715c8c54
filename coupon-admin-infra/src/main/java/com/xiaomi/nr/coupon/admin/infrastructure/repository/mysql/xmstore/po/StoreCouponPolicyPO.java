package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.xmstore.po;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: heji<PERSON><PERSON>
 * @Date 2022/3/29 9:48 上午
 * @Version: 1.0
 **/
@Data
public class StoreCouponPolicyPO {

    private String code;

    private Integer value;

    @SerializedName("reduce_money")
    private Long reduceMoney;

    @SerializedName("reduce_discount")
    private Long reduceDiscount;

    @SerializedName("max_price")
    private Long maxPrice;

    @SerializedName("target_goods")
    private List<XmStoreGoodsItemPO> targetGoods;
}
