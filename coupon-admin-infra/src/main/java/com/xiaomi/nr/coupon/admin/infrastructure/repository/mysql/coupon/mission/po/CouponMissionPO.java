package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.mission.po;

import lombok.Data;

import java.io.Serializable;

@Data
public class CouponMissionPO implements Serializable {
    private static final long serialVersionUID = -8618664816502377401L;
    /**
     * 主键
     */
    private long id;
    /**
     * 'add','history','approved','reject','cancel','send','failed'
     */
    private String stat;
    /**
     * 任务名称
     */
    private String name;
    /**
     * 对应人群id
     */
    private long groupId;
    /**
     * 生成数量
     */
    private long sendNum;
    /**
     * '优惠券配置id'
     */
    private long typeId;
    /**
     * 优惠券有效开始时间
     */
    private long couponStartTime;
    /**
     * 优惠券有效结束时间
     */
    private long couponEndTime;
    /**
     * 发券开始时间
     */
    private long sendTime;
    /**
     * 发券结束时间
     */
    private long sendEndTime;
    /**
     * adminId
     */
    private long adminId;
    /**
     * adminName
     */
    private String adminName;
    /**
     * 添加时间
     */
    private long addTime;
    /**
     * 区域id
     */
    private long areaId;
    /**
     * 下载地址
     */
    private String download;
    /**
     * 发放任务的类型
     * 1-接口发放
     * 2-任务发放(人群发放)
     */
    private int missionType;
    /**
     * 优惠券有效天数
     */
    private long couponDays;
    /**
     * 最大发放数量
     */
    private long maxNum;
    /**
     * 已经发送数量
     */
    private long nowNum;
    /**
     * 幂等所用审核任务ID
     */
    private long approvedId;




}
