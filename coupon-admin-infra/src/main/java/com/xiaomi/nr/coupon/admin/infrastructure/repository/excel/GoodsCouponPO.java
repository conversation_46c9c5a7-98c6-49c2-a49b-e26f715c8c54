package com.xiaomi.nr.coupon.admin.infrastructure.repository.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 商品可用券下载实体
 *
 * <AUTHOR>
 * @date 2023-01-06
 */
@Data
public class GoodsCouponPO {

    /**
     * 优惠券id
     */
    @ExcelProperty("优惠券ID")
    private	long id;

    /**
     * 优惠券名称
     */
    @ExcelProperty("优惠券名称")
    private	String	name;

    /**
     * 投放场景
     */
    @ExcelProperty("投放场景")
    private String sendSceneName;

    /**
     * 优惠券类型  1: 商品券 2: 运费券 2：超级补贴券
     */
    @ExcelProperty("优惠券类型")
    private String couponType;

    /**
     * 优惠类型 (1:满减, 2:满折, 3:N元券, 4:立减)
     */
    @ExcelProperty("折扣类型")
    private String promotionType;

    /**
     * 面额/门槛
     */
    @ExcelProperty("面额/门槛")
    private String promotionBottom;

    /**
     * 使用渠道
     */
    @ExcelProperty("渠道")
    private String useChannel;

    /**
     * 可用描述信息 查询商品可用券列表使用
     */
    @ExcelProperty("是否可用")
    private String validRemark;

    /**
     * 创建信息
     */
    @ExcelProperty("创建信息")
    private String creatorDesc;
}