package com.xiaomi.nr.coupon.admin.infrastructure.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * 限流注解
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface MethodLimit {
    /**
     * 限流接口的名称(接口方法名)
     */
    String name() default "";

    /**
     * 限流速率(1秒钟允许调用几次)
     */
    double frequency() default 10.0;

    /**
     * 超时时长
     */
    int timeout() default 3000;

    /**
     * 超时时间单位
     */
    TimeUnit timeUnit() default TimeUnit.MILLISECONDS;
}
