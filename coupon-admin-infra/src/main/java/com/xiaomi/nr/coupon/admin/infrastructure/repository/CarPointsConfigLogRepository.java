package com.xiaomi.nr.coupon.admin.infrastructure.repository;

import com.xiaomi.nr.coupon.admin.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.pointadmin.mapper.CarPointsConfigLogMapper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.pointadmin.po.CarPointsConfigLogPo;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/6 17:27
 */
@Component
@Slf4j
public class CarPointsConfigLogRepository {

    @Autowired
    private CarPointsConfigLogMapper carPointsConfigLogMapper;

    /**
     * 日志插入
     *
     * @param po        po
     * @throws BizError bizError
     */
    public void insert(CarPointsConfigLogPo po) throws BizError {

        Integer effectRows = carPointsConfigLogMapper.insert(po);

        if (effectRows <= 0) {
            log.error("CarPointsConfigLogRepository.insert failed, po = {}", GsonUtil.toJson(po));
            throw ExceptionHelper.create(ErrCode.POINT, "积分批次日志插入失败");
        }

    }

    /**
     * 获取最近一次更新人
     *
     * @param batchId   批次id
     * @return          更新人
     */
    public String selectUpdater(Long batchId) {
        return carPointsConfigLogMapper.selectUpdater(batchId);
    }

    public List<CarPointsConfigLogPo> getLogPageByBatch(Long batchId, int offset, int limit) {
        return carPointsConfigLogMapper.getLogByPage(batchId, offset, limit);
    }

    /**
     * 获取日志总数
     * @param couponId
     * @return
     */
    public int getTotalByBatchId(Long couponId) {
        return carPointsConfigLogMapper.getCountByBatchId(couponId);
    }

}
