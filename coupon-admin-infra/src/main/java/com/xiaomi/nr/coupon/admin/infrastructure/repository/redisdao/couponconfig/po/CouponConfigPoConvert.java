package com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.po;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;
import com.xiaomi.goods.gms.dto.batch.BatchedInfoDto;
import com.xiaomi.goods.gms.dto.sku.SkuInfoDto;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.UseChannelVO;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.BaseData;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CarMaintenanceSsuInfo;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponScopeTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsExtTagEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.es.po.CouponEsPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.po.GoodItemPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.goods.po.GoodsPo;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.goods.po.PackagePo;
import com.xiaomi.nr.coupon.admin.util.CouponCollectionUtil;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.StringUtil;
import com.xiaomi.nr.coupon.admin.api.utils.TimeUtil;
import com.xiaomi.nr.coupon.admin.util.beancopy.BeanMapper;
import com.xiaomi.nr.goods.tob.dto.response.ssu.SsuDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @description:
 * @author: hejiapeng
 * @Date 2022/3/14 5:13 下午
 * @Version: 1.0
 **/
@Component
@Slf4j
public class CouponConfigPoConvert {

    /**
     * 转换券配置Po
     *
     * @param couponConfigPO
     * @return
     */
    public ConfigInfoCachePo serializeConfigInfoCachePo(CouponConfigPO couponConfigPO) {

        ConfigInfoCachePo configInfoCachePo = new ConfigInfoCachePo();

        BeanMapper.copy(couponConfigPO, configInfoCachePo);

        return configInfoCachePo;
    }

    /**
     * 商品过滤及转换
     *
     * @param skuInfoDtos
     * @param batchedInfoDtos
     * @return
     * @throws Exception
     */
    public ConfigGoodsCachePo serializeConfigGoodsCachePo(CouponConfigPO po, List<SkuInfoDto> skuInfoDtos, List<BatchedInfoDto> batchedInfoDtos) throws Exception {

        ConfigGoodsCachePo configGoodsCachePo = new ConfigGoodsCachePo();

        Map<Long, Long> skuMap = new HashMap<>();
        Map<Long, Long> gidMap = new HashMap<>();
        Map<Long, Long> packageMap = new HashMap<>();

        if (CollectionUtils.isNotEmpty(skuInfoDtos)) {
            for (SkuInfoDto skuInfoDto : skuInfoDtos) {
                skuMap.put(skuInfoDto.getSku(), skuInfoDto.getProductId());
                gidMap.put(skuInfoDto.getGoodsId(), skuInfoDto.getProductId());
            }
        }

        if (CollectionUtils.isNotEmpty(batchedInfoDtos)) {
            for (BatchedInfoDto batchedInfoDto : batchedInfoDtos) {
                packageMap.put(batchedInfoDto.getBatchedId(), batchedInfoDto.getProductId());
            }
        }
        configGoodsCachePo.setConfigId(po.getId());
        configGoodsCachePo.setSkus(skuMap);
        configGoodsCachePo.setGoodsIds(gidMap);
        configGoodsCachePo.setPackageIds(packageMap);
        configGoodsCachePo.setUpdateTime(TimeUtil.getNowTimestamp());

        return configGoodsCachePo;
    }


    /**
     * 商品过滤及转换
     *
     * @param skuInfoDtos
     * @param batchedInfoDtos
     * @param labourHourSsuInfos
     * @param partsSsuInfos
     * @param goodsItemPo
     * @return
     * @throws Exception
     */
    public NewConfigGoodsCachePo serializeConfigGoodsCachePoV2(CouponConfigPO po,
                                                               List<SkuInfoDto> skuInfoDtos,
                                                               List<BatchedInfoDto> batchedInfoDtos,
                                                               List<SsuDTO> ssuDTOs,
                                                               List<CarMaintenanceSsuInfo> labourHourSsuInfos,
                                                               List<CarMaintenanceSsuInfo> partsSsuInfos,
                                                               GoodsItemPo goodsItemPo) throws Exception {

        NewConfigGoodsCachePo configGoodsCachePo = new NewConfigGoodsCachePo();

        Set<Long> skuSet = new HashSet<>();
        Set<Long> gidSet = new HashSet<>();
        Set<Long> packageSet = new HashSet<>();
        Set<Long> ssuSet = new HashSet<>();
        Map<Long, GoodExt> goodsExt = new HashMap<>();

        if (CollectionUtils.isNotEmpty(skuInfoDtos)) {
            for (SkuInfoDto skuInfoDto : skuInfoDtos) {
                skuSet.add(skuInfoDto.getSku());
                gidSet.add(skuInfoDto.getGoodsId());
            }
        }

        if (CollectionUtils.isNotEmpty(batchedInfoDtos)) {
            for (BatchedInfoDto batchedInfoDto : batchedInfoDtos) {
                packageSet.add(batchedInfoDto.getBatchedId());
            }
        }

        if (CollectionUtils.isNotEmpty(ssuDTOs)) {
            for (SsuDTO ssuDTO : ssuDTOs) {
                ssuSet.add(ssuDTO.getSsuId());
            }
        }

        if (CollectionUtils.isNotEmpty(labourHourSsuInfos)) {
            for (CarMaintenanceSsuInfo labourHourSsuInfo : labourHourSsuInfos) {
                ssuSet.add(labourHourSsuInfo.getSsuId());
                goodsExt.put(labourHourSsuInfo.getSsuId(), GoodExt.builder().count(labourHourSsuInfo.getCount()).tag(GoodsExtTagEnum.LABOR.getTag()).build());
            }
        }

        if (CollectionUtils.isNotEmpty(partsSsuInfos)) {
            for (CarMaintenanceSsuInfo partsSsuInfo : partsSsuInfos) {
                ssuSet.add(partsSsuInfo.getSsuId());
                goodsExt.put(partsSsuInfo.getSsuId(), GoodExt.builder().count(partsSsuInfo.getCount()).tag(GoodsExtTagEnum.PART.getTag()).build());
            }
        }

        // 新套装
        if (CollectionUtils.isNotEmpty(goodsItemPo.getSuitList())) {
            ssuSet.addAll(goodsItemPo.getSuitList());
        }

        configGoodsCachePo.setConfigId(po.getId());
        configGoodsCachePo.setSkus(skuSet);
        configGoodsCachePo.setGoodsIds(gidSet);
        configGoodsCachePo.setPackageIds(packageSet);
        configGoodsCachePo.setSsuIds(ssuSet);
        configGoodsCachePo.setGoodsExt(goodsExt);
        configGoodsCachePo.setUpdateTime(TimeUtil.getNowTimestamp());

        return configGoodsCachePo;
    }

    /**
     * 商品过滤及转换
     *
     * @param goodsItemPo goodsItemPo
     * @return
     */
    public NewConfigGoodsCachePo serializeConfigGoodsCachePoV2(Long configId, GoodsItemPo goodsItemPo) {

        NewConfigGoodsCachePo configGoodsCachePo = new NewConfigGoodsCachePo();
        configGoodsCachePo.setConfigId(configId);
        configGoodsCachePo.setUpdateTime(TimeUtil.getNowTimestamp());
        Set<Long> ssuSet = new HashSet<>(goodsItemPo.getSsuList());
        ssuSet.addAll(goodsItemPo.getSuitList());
        configGoodsCachePo.setSsuIds(ssuSet);
        return configGoodsCachePo;
    }

    /**
     * 商品过滤及转换
     *
     * @param po
     * @return
     * @throws Exception
     */
    public ConfigGoodsCachePo serializeConfigGoodsCachePo(CouponConfigPO po, GoodsItemPo goodsItem, BaseData common) throws Exception {

        ConfigGoodsCachePo configGoodsCachePo = new ConfigGoodsCachePo();

        Map<Long, Long> skuMap = new HashMap<>();
        Map<Long, Long> gidMap = new HashMap<>();
        Map<Long, Long> packageMap = new HashMap<>();

        for (Long sku : goodsItem.getSkuList()) {
            GoodsPo skuInfo = common.getSkuInfo(sku);
            if (skuInfo == null) {
                continue;
            }
            skuMap.put(skuInfo.getSku(), skuInfo.getProductId());
            gidMap.put(skuInfo.getGoodsId(), skuInfo.getProductId());
        }

        for (Long packageId : goodsItem.getPackageList()) {
            PackagePo packageInfo = common.getPackageInfo(packageId);
            if (packageInfo == null) {
                continue;
            }
            packageMap.put(packageInfo.getPackageId(), packageInfo.getProductId());
        }
        configGoodsCachePo.setConfigId(po.getId());
        configGoodsCachePo.setSkus(skuMap);
        configGoodsCachePo.setGoodsIds(gidMap);
        configGoodsCachePo.setPackageIds(packageMap);
        configGoodsCachePo.setUpdateTime(TimeUtil.getNowTimestamp());

        return configGoodsCachePo;
    }

    /**
     * 商品过滤及转换
     *
     * @param po
     * @return
     * @throws Exception
     */
    public NewConfigGoodsCachePo serializeConfigGoodsCachePoV2(CouponConfigPO po, GoodsItemPo goodsItem, BaseData common) throws Exception {

        NewConfigGoodsCachePo configGoodsCachePo = new NewConfigGoodsCachePo();

        Set<Long> skuSet = new HashSet<>();
        Set<Long> gidSet = new HashSet<>();
        Set<Long> packageSet = new HashSet<>();
        Set<Long> ssuSet = new HashSet<>();
        Map<Long, GoodExt> goodsExt = new HashMap<>();

        for (Long sku : goodsItem.getSkuList()) {
            GoodsPo skuInfo = common.getSkuInfo(sku);
            if (skuInfo == null) {
                continue;
            }
            skuSet.add(skuInfo.getSku());
            gidSet.add(skuInfo.getGoodsId());
        }

        for (Long packageId : goodsItem.getPackageList()) {
            PackagePo packageInfo = common.getPackageInfo(packageId);
            if (packageInfo == null) {
                continue;
            }
            packageSet.add(packageInfo.getPackageId());
        }

        for (Long ssuId : goodsItem.getSsuList()) {
            ssuSet.add(ssuId);
        }

        if (MapUtils.isNotEmpty(goodsItem.getLabourHourSsu())) {
            for (Map.Entry<Long, Integer> integerEntry : goodsItem.getLabourHourSsu().entrySet()) {
                ssuSet.add(integerEntry.getKey());
                goodsExt.put(integerEntry.getKey(), GoodExt.builder().count(integerEntry.getValue()).tag(GoodsExtTagEnum.LABOR.getTag()).build());
            }
        }

        if (MapUtils.isNotEmpty(goodsItem.getPartsSsu())) {
            for (Map.Entry<Long, Integer> integerEntry : goodsItem.getPartsSsu().entrySet()) {
                ssuSet.add(integerEntry.getKey());
                goodsExt.put(integerEntry.getKey(), GoodExt.builder().count(integerEntry.getValue()).tag(GoodsExtTagEnum.PART.getTag()).build());
            }
        }

        configGoodsCachePo.setConfigId(po.getId());
        configGoodsCachePo.setSkus(skuSet);
        configGoodsCachePo.setGoodsIds(gidSet);
        configGoodsCachePo.setPackageIds(packageSet);
        configGoodsCachePo.setSsuIds(ssuSet);
        configGoodsCachePo.setGoodsExt(goodsExt);
        configGoodsCachePo.setUpdateTime(TimeUtil.getNowTimestamp());

        return configGoodsCachePo;
    }

    /**
     * 构建券配置ES索引文档
     *
     * @param couponConfigPO
     * @return
     */
    public CouponEsPO serializeCouponEsPO(CouponConfigPO couponConfigPO, GoodsItemPo goodsItem) {

        CouponEsPO couponEsPO = new CouponEsPO();

        couponEsPO.setId(couponConfigPO.getId());
        couponEsPO.setName(couponConfigPO.getName());
        couponEsPO.setScene(couponConfigPO.getSendScene());
        couponEsPO.setStatus(couponConfigPO.getStatus());
        couponEsPO.setPromotionType(couponConfigPO.getPromotionType());
        couponEsPO.setUseChannel(StringUtil.convertToIntegerList(couponConfigPO.getUseChannel()));
        couponEsPO.setStartUseTime(couponConfigPO.getStartUseTime());
        couponEsPO.setEndUseTime(couponConfigPO.getEndUseTime());
        couponEsPO.setStartFetchTime(couponConfigPO.getStartFetchTime());
        couponEsPO.setEndFetchTime(couponConfigPO.getEndFetchTime());
        couponEsPO.setBizPlatform(couponConfigPO.getBizPlatform());
        couponEsPO.setCreator(couponConfigPO.getCreator());
        // 转换数据
        Map<Integer, UseChannelVO> useChannel = GsonUtil.fromJson(couponConfigPO.getUseStore(),new TypeToken<Map<Integer, UseChannelVO>>(){}.getType());
        // 数据转化为具体门店 或者 *
        List<String> storeIdList = useChannel.values().stream().flatMap(e -> {
            if (CollectionUtils.isNotEmpty(e.getLimitIds())) {
                return e.getLimitIds().stream();
            } else {
                return Stream.of("*");
            }
        }).collect(Collectors.toList());
        couponEsPO.setStoreIds(storeIdList);

        couponEsPO.setSkuId(goodsItem.getSkuList());

        couponEsPO.setPackageId(goodsItem.getPackageList());

        List<Long> ssuList = Lists.newArrayList();
        ssuList.addAll(goodsItem.getSsuList());
        ssuList.addAll(goodsItem.getLabourHourSsu().keySet());
        ssuList.addAll(goodsItem.getPartsSsu().keySet());
        ssuList.addAll(goodsItem.getSuitList());
        couponEsPO.setSsuId(ssuList);

        couponEsPO.setGid(goodsItem.getGidList());

        couponEsPO.setTimesLimit(couponConfigPO.getTimesLimit());

        couponEsPO.setPublicPromotion(couponConfigPO.getPublicPromotion());

        couponEsPO.setAreaId(couponConfigPO.getAreaId());

        couponEsPO.setUseTimeType(couponConfigPO.getUseTimeType());

        return couponEsPO;
    }

    /**
     * 转换商品
     *
     * @param couponConfig
     * @return
     */
    public GoodsItemPo serializeGoodsItemPo(CouponConfigPO couponConfig) {
        GoodsItemPo goodsItem = new GoodsItemPo();

        if (StringUtils.isBlank(couponConfig.getGoodsInclude())) {
            log.error("CouponConfigPoConvert.serializeGoodsItemPo err goodItemInclude is null, configId:{}", couponConfig.getId());
        }

        GoodItemPO goodItemInclude = GsonUtil.fromJson(couponConfig.getGoodsInclude(), GoodItemPO.class);

        // 是否需要排除黑名单商品
        boolean isNeedExceptGood = checkNeedExceptGood(couponConfig);

        GoodItemPO goodItemExclude;
        if (isNeedExceptGood) {
            goodItemExclude = GsonUtil.fromJson(couponConfig.getGoodsExclude(), GoodItemPO.class);
        } else {
            goodItemExclude = new GoodItemPO();
        }

        goodsItem.setSkuList(getGoodList(goodItemInclude.getSku(), isNeedExceptGood, goodItemExclude.getSku()));
        goodsItem.setPackageList(getGoodList(goodItemInclude.getPackages(), isNeedExceptGood, goodItemExclude.getPackages()));
        goodsItem.setSsuList(getGoodList(goodItemInclude.getSsu(), isNeedExceptGood, goodItemExclude.getSsu()));
        goodsItem.setSuitList(getGoodList(goodItemInclude.getSuit(), isNeedExceptGood, goodItemExclude.getSuit()));
        goodsItem.setLabourHourSsu(getGoodMap(goodItemInclude.getLabourHourSsu(), isNeedExceptGood, goodItemExclude.getLabourHourSsu()));
        goodsItem.setPartsSsu(getGoodMap(goodItemInclude.getPartsSsu(), isNeedExceptGood, goodItemExclude.getPartsSsu()));

        return goodsItem;
    }

    /**
     * 排除黑名单商品
     *
     * @param includeGoods     includeGoods包含的商品
     * @param isNeedExceptGood 是否需要排除黑名单商品
     * @param excludeGoods     excludeGoods包含的商品
     * @return 商品列表
     */
    private Map<Long, Integer> getGoodMap(Map<Long, Integer> includeGoods, boolean isNeedExceptGood, Map<Long, Integer> excludeGoods) {
        if (MapUtils.isEmpty(includeGoods)) {
            return Maps.newHashMap();
        }
        if (isNeedExceptGood && MapUtils.isNotEmpty(excludeGoods)) {
            for (Long ssu : excludeGoods.keySet()) {
                includeGoods.remove(ssu);
            }
        }
        return includeGoods;
    }

    /**
     * 校验是否需要排除黑名单商品
     *
     * @param couponConfig couponConfig
     * @return 是否需要排除黑名单商品
     */
    private boolean checkNeedExceptGood(CouponConfigPO couponConfig) {
        return couponConfig.getScopeType() == CouponScopeTypeEnum.Categories.getValue() && StringUtils.isNotEmpty(couponConfig.getGoodsExclude());
    }

    /**
     * 排除黑名单商品
     *
     * @param includeGoods     includeGoods包含的商品
     * @param isNeedExceptGood 是否需要排除黑名单商品
     * @param excludeGoods     excludeGoods包含的商品
     * @return 商品列表
     */
    private List<Long> getGoodList(List<Long> includeGoods, boolean isNeedExceptGood, List<Long> excludeGoods) {
        if (CollectionUtils.isEmpty(includeGoods)) {
            return Lists.newArrayList();
        }
        if (isNeedExceptGood && CollectionUtils.isNotEmpty(excludeGoods)) {
            return CouponCollectionUtil.removeAll(includeGoods, excludeGoods);
        } else {
            return includeGoods;
        }
    }
}
