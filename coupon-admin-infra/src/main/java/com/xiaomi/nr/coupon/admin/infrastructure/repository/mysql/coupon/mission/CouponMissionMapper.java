package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.mission;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.mission.po.CouponMissionPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.couponmission.po.MissionCountItem;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.couponmission.po.MissionJoinConfigPo;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.couponmission.po.MissionPo;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.List;

/**
 * 券发放配置操作mapper
 */
@Mapper
@Component
public interface CouponMissionMapper {


    String TABLE = "nr_coupon_mission";

    String NEW_SELECT_JOIN_SQL = "select m.id as missionId,m.name as missionName,m.stat as missionStatus,m.mission_type as missionType," +
            "m.send_num as sendNum,m.max_num as maxNum,m.coupon_days as couponDays,m.coupon_start_time as couponStartTime," +
            "m.coupon_end_time as couponEndTime,m.add_time as addTime,m.type_id as couponConfigId,t.name as couponName," +
            "t.promotion_type as couponTypeCode,t.promotion_value as promotionValue,t.send_channel as sendChannel, " +
            "t.start_fetch_time as globalCouponStartTime, t.end_fetch_time as globalCouponEndTime, t.source, t.coupon_type as couponType, t.shipment_id as shipmentId" +
            " from nr_coupon_mission as m left outer join nr_coupon_config as t on m.type_id = t.id ";

    /**
     * 插入优惠券任务
     * @return
     */
    @Insert("insert into " + TABLE + " (id,stat,name,group_id,send_num,type_id,coupon_start_time,coupon_end_time,send_time,send_end_time" +
            ",admin_id,admin_name,add_time,area_id,download,mission_type" +
            ",coupon_days,max_num,now_num,approved_id)"+
            " values " +
            "(#{id},#{stat},#{name},#{groupId},#{sendNum},#{typeId},#{couponStartTime},#{couponEndTime},#{sendTime},#{sendEndTime}" +
            ",#{adminId},#{adminName},#{addTime},#{areaId},#{download},#{missionType}" +
            ",#{couponDays},#{maxNum},#{nowNum},#{approvedId})")
    Long insert(CouponMissionPO po);


    @Update("<script>update " + TABLE + " <set>" +
            "<if test='stat!=null'>stat=#{stat},</if>" +
            "<if test='name!=null'>name=#{name},</if>" +
            "<if test='sendNum!=null'>send_num=#{sendNum},</if>" +
            "<if test='couponStartTime!=null'>coupon_start_time=#{couponStartTime},</if>" +
            "<if test='couponEndTime!=null'>coupon_end_time=#{couponEndTime},</if>" +
            "<if test='sendTime!=null'>send_time=#{sendTime},</if>" +
            "<if test='sendEndTime!=null'>send_end_time=#{sendEndTime},</if>" +
            "<if test='adminId!=null'>admin_id=#{adminId},</if>" +
            "<if test='adminName!=null'>admin_name=#{adminName},</if>" +
            "<if test='areaId!=null'>area_id=#{areaId},</if>" +
            "<if test='download!=null'>download=#{download},</if>" +
            "<if test='missionType!=null'>mission_type=#{missionType},</if>" +
            "<if test='couponDays!=null'>coupon_days=#{couponDays},</if>" +
            "<if test='maxNum!=null'>max_num=#{maxNum},</if>" +
            "<if test='nowNum!=null'>now_num=#{nowNum},</if>" +
            "<if test='approvedId!=null'>approved_id=#{approvedId},</if>" +
            "</set><where> type_id=#{typeId}</where></script>")
    Long update(CouponMissionPO po);

    /**
     * 根据missionId查询发放任务现相关信息
     *
     * @param configId 券Id
     * @return MissionJoinConfigPo 券发放任务和券配置结果实体
     */
    @Select("select * from " + TABLE + " where type_id = #{configId}")
    List<CouponMissionPO> getMissionByConfigId(@Param("configId") long configId);

    /**
     * 根据missionId查询发放任务现相关信息
     *
     * @param typeIds 券Id
     * @return MissionJoinConfigPo 券发放任务和券配置结果实体
     */
    @Select("<script>select * from " +TABLE+
            " where type_id in <foreach collection='typeIds' item='typeId' index='index' open='(' close=')' separator=','>#{typeId}</foreach></script>")
    List<MissionPo> getMissionByConfigIds(@Param("typeIds") List<Long> typeIds);

    /**
     * 根据missionId查询发放任务现相关信息
     *
     * @param missionId 券发放任务id
     * @return MissionJoinConfigPo 券发放任务和券配置结果实体
     */
    @Select(NEW_SELECT_JOIN_SQL + " where m.id = #{missionId}")
    MissionJoinConfigPo getMissionByIdOne(@Param("missionId") long missionId);


    /**
     * 根据sendChannel等字段分页获取有效的发放任务列表
     *
     * @param nowTime       当前时间
     * @param lastMissionId 上一页面的最后一个券发放任务id
     * @param pageSize      页面大小
     * @return List<>       券发放任务和券配置实体
     */
    @Select(
            NEW_SELECT_JOIN_SQL + " where t.code = 2 " +
                    " and t.end_fetch_time > #{nowTime} " +
                    " and (unix_timestamp(now())+ m.coupon_days*3600 <= t.end_fetch_time or unix_timestamp(now())+ m.coupon_days*3600 <= t.end_use_time) " +
                    " and t.send_channel=#{sendChannel} "+
                    " and (m.coupon_end_time <= t.end_fetch_time or m.coupon_end_time <= t.end_use_time)" +
                    " and m.id > #{lastMissionId} order by m.id asc limit #{pageSize}")
    List<MissionJoinConfigPo> getMissionByChannel(@Param("nowTime") long nowTime, @Param("sendChannel")String sendChannel,
                                                  @Param("lastMissionId") long lastMissionId,@Param("pageSize") Integer pageSize);


    /**
     * 根据sendChannel等字段获取有效的券发放任务总数
     *
     * @param nowTime     当前时间
     * @return int        有效券发放任务总数
     */
    @Select(
            "select count(1) as total, t.send_channel as sendChannel from " +
                    " nr_coupon_mission as m left outer join nr_coupon_config as t on m.type_id = t.id " +
                    " where t.code = 2 " +
                    " and t.end_fetch_time > #{nowTime} " +
                    " and (unix_timestamp(now())+ m.coupon_days*3600 <= t.end_fetch_time or unix_timestamp(now())+ m.coupon_days*3600 <= t.end_use_time) " +
                    " and (m.coupon_end_time <= t.end_fetch_time or m.coupon_end_time <= t.end_use_time)" +
                    " group by t.send_channel")
    List<MissionCountItem> getMissionByChannelCount(@Param("nowTime") long nowTime, @Param("sendChannel")String sendChannel);


    /********************************老数据迁移使用 迁移完删掉 ****************************/
    /**
     * 查询券迁移的更新时间
     * @return
     */
    @Select("select max(last_update_time) from "+ TABLE)
    Timestamp getMissionMoveLastTime();

    /**
     * 判断数据是否存在
     * @param id
     * @return
     */
    @Select("select id from "+ TABLE +" where id=#{id}")
    Long checkoutMissionExit(@Param("id") long id);


    /**
     * 迁移老mission
     * @return
     */
    @Insert("insert into " + TABLE + " (id,stat,name,group_id,send_num,type_id,coupon_start_time,coupon_end_time,send_time,send_end_time" +
            ",admin_id,admin_name,add_time,area_id,download,mission_type" +
            ",coupon_days,max_num,now_num,approved_id)"+
            " values " +
            "(#{id},#{stat},#{name},#{groupId},#{sendNum},#{typeId},#{couponStartTime},#{couponEndTime},#{sendTime},#{sendEndTime}" +
            ",#{adminId},#{adminName},#{addTime},#{areaId},#{download},#{missionType}" +
            ",#{couponDays},#{maxNum},#{nowNum},#{approvedId})")
    Long insertOldMission(CouponMissionPO po);


    @Update("<script>update " + TABLE + " <set>" +
            "<if test='name!=null'>name=#{name},</if>" +
            "<if test='sendNum!=null'>send_num=#{sendNum},</if>" +
            "<if test='couponStartTime!=null'>coupon_start_time=#{couponStartTime},</if>" +
            "<if test='couponEndTime!=null'>coupon_end_time=#{couponEndTime},</if>" +
            "<if test='sendTime!=null'>send_time=#{sendTime},</if>" +
            "<if test='sendEndTime!=null'>send_end_time=#{sendEndTime},</if>" +
            "<if test='adminId!=null'>admin_id=#{adminId},</if>" +
            "<if test='adminName!=null'>admin_name=#{adminName},</if>" +
            "<if test='areaId!=null'>area_id=#{areaId},</if>" +
            "<if test='download!=null'>download=#{download},</if>" +
            "<if test='missionType!=null'>mission_type=#{missionType},</if>" +
            "<if test='couponDays!=null'>coupon_days=#{couponDays},</if>" +
            "<if test='maxNum!=null'>max_num=#{maxNum},</if>" +
            "<if test='nowNum!=null'>now_num=#{nowNum},</if>" +
            "<if test='approvedId!=null'>approved_id=#{approvedId},</if>" +
            "</set><where> id=#{id}</where></script>")
    Long updateOldMission(CouponMissionPO po);

}
