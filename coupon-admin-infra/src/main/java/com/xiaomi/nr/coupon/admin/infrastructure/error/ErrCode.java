package com.xiaomi.nr.coupon.admin.infrastructure.error;

import com.xiaomi.youpin.infra.rpc.errors.*;

/**
 * 错误码常量
 * <p>
 * 错误码值分三部分 400{scope}{internalCode},
 * 400:          固定值
 * scope:        定义级别 {@link Scopes}
 * internalCode：错误级别的确切问题
 *
 * </p>
 *
 * <AUTHOR>
 */
public class ErrCode {
    /**
     * 优惠券配置
     */
    public static final ErrorCode COUPON = ErrorCode.createOnce(ErrScope.COUPON, 100);

    /**
     * 礼品卡
     */
    public static final ErrorCode ECARD = ErrorCode.createOnce(ErrScope.COUPON, 201);

    /**
     * 积分
     */
    public static final ErrorCode POINT = ErrorCode.createOnce(ErrScope.COUPON, 301);

    /**
     * 汽车服务券
     */
    public static final ErrorCode SERVICE_COUPON = ErrorCode.createOnce(ErrScope.COUPON, 401);
}
