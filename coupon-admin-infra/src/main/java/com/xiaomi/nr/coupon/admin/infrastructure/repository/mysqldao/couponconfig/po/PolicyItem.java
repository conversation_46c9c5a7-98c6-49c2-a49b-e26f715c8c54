package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.couponconfig.po;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 政策item
 *
 * <AUTHOR>
 */
@Data
public class PolicyItem implements Serializable {

    private static final long serialVersionUID = 5771569295203364468L;

    /**
     * 规则
     */
    @SerializedName("rule")
    private Rule rule;

    /**
     * suffix
     */
    @SerializedName("suffix")
    private List<SuffixItem> suffix;
}