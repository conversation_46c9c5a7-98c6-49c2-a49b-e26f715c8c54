package com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.usercoupon;

import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.usercoupon.po.CodeCouponReceiveRecordPo;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

/**
 * 优惠码领取记录mapper
 *
 * <AUTHOR>
 * @date 2024/6/26 16:37
 */
@Component
@Mapper
public interface CodeCouponReceiveRecordMapper {
    @Insert("<script>" +
            "insert into tb_codecoupon_receive_record(coupon_code, coupon_index, type_id, mobile_no, external_user_id, assign_type, assign_time, assign_market) " +
            "values(#{po.couponCode}, #{po.couponIndex}, #{po.typeId}, #{po.mobileNo}, #{po.externalUserId}, #{po.assignType}, #{po.assignTime}, #{po.assignMarket}) " +
            "</script>")
    int insert(@Param("po") CodeCouponReceiveRecordPo receiveRecordPo);

    @Select("select * from tb_codecoupon_receive_record where coupon_code = #{couponCode} and coupon_index = #{couponIndex}")
    CodeCouponReceiveRecordPo getReceiveRecordByCouponCode(@Param("couponCode") String couponCode, @Param("couponIndex") String couponIndex);
}
