package com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponmission.po;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 单个券发放任务信息
 *
 * <AUTHOR>
 */
@Data
public class MissionCacheItemPo implements Serializable {

    private static final long serialVersionUID = 3654121257889778285L;

    /**
     * 发放任务id
     * id
     */
    private Long id = 0L;

    /**
     * 发放任务名称
     * name
     */
    private String name = "";

    /**
     * 发放任务状态
     * stat
     */
    private String status = "";

    /**
     * 发放任务类型1:interface(接口调用发放) 2:mission(自动任务按人群批量发放)
     * mission_type
     */
    private String missionType = "";

    /**
     * 券的真实有效期类别（section:时间段内有效 days:从发放起*天内有效）
     *
     */
    private String timeType = "";

    /**
     * 券的真实有效开始时间
     * coupon_start_time
     */
    private Long couponStartTime = 0L;

    /**
     * 券的真实有效结束时间
     * coupon_end_time
     */
    private Long couponEndTime = 0L;

    /**
     * 券从发放起*天内有效（真实有效时间）
     * coupon_days
     */
    private Integer days = 0;

    /**
     * 券从发放起*小时内有效（真实有效时间）
     * coupon_days
     */
    private Integer hours = 0;

    /**
     * 发券开始时间
     * send_time
     */
    private Long sendStartTime = 0L;

    /**
     * 发券结束时间
     * send_end_time
     */
    private Long sendEndTime = 0L;

    /**
     * 发放任务最多可发放的券数量
     * max_num
     */
    private Long maxSendNum = 0L;

    /**
     * 发放任务按人群发放的券数量
     * send_num
     */
    private Long groupSendNum = 0L;

    /**
     * 发放的人群ID
     * group_ids
     */
    private List<String> groupIds;

    /**
     * 可发放部门ID
     */
    private String departmentId = "";

    /**
     * 发放任务的创建时间
     * add_time
     */
    private Long addTime = 0L;

    /**
     * 审核时间
     * approved_time
     */
    private Long approvedTime = 0L;

    /**
     * 缓存的创建时间
     *
     */
    private Long cacheCreateTime = 0L;

    /**
     * 券配置id
     */
    private Long couponConfigId = 0L;

    /**
     * 审核人ID
     */
    private Long adminId;

    /**
     * 审核人姓名
     */
    private String adminName;

    /**
     * 新老任务版本  老券任务1 新任务2
     */
    private Integer version;
}
