<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>coupon-admin</artifactId>
        <groupId>com.xiaomi.nr</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>coupon-admin-infra</artifactId>
    <version>${project.parent.version}</version>

    <dependencies>
        <!-- mq -->
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
            <version>2.1.2-mdh2.1.4-RELEASE</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-validator</artifactId>
                    <groupId>commons-validator</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty-all</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.infra</groupId>
            <artifactId>rocketmq-client-java</artifactId>
            <version>1.0.5-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 翻译平台sdk(热更新版本)-->
        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>global-nr-dev-common</artifactId>
            <version>1.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <artifactId>commons-validator</artifactId>
            <groupId>commons-validator</groupId>
            <version>1.6</version>
        </dependency>
        <!-- mq -->


        <!-- FDS -->
        <dependency>
            <groupId>com.xiaomi.infra.galaxy</groupId>
            <artifactId>galaxy-fds-sdk-java</artifactId>
            <version>3.0.41</version>
        </dependency>

        <dependency>
            <artifactId>gis-api</artifactId>
            <groupId>com.xiaomi.goods</groupId>
            <version>1.1.40-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.nr.goods</groupId>
            <artifactId>nr-goods-service-tob-api</artifactId>
            <version>1.0.22-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.nr.goods</groupId>
            <artifactId>nr-goods-service-api</artifactId>
            <version>4.5-i18n-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.nr-promotion</groupId>
            <artifactId>framework</artifactId>
            <version>0.0.3-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-slf4j-impl</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi-ooxml</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-core</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>2.3.28</version>
        </dependency>

        <dependency>
            <groupId>ma.glasnost.orika</groupId>
            <artifactId>orika-core</artifactId>
            <version>1.5.4</version>
        </dependency>

        <!--验签需加上auth，如果有冲突可尝试将dubbo-tools-autoconfigure去除-->
        <dependency>
            <groupId>com.xiaomi.newretail</groupId>
            <artifactId>dubbo-auth</artifactId>
            <version>1.0.3-SNAPSHOT</version>
        </dependency>
        <!--bpm-->

        <!--数字门店-->
        <dependency>
            <groupId>com.xiaomi.cnzone</groupId>
            <artifactId>maindata-api</artifactId>
            <version>1.5-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.cnzone</groupId>
            <artifactId>maindata-common</artifactId>
            <version>1.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jetty</artifactId>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <artifactId>coupon-api</artifactId>
            <groupId>com.xiaomi.nr</groupId>
            <version>1.0.9-SNAPSHOT</version>
        </dependency>

        <!--sid服务-->
        <dependency>
            <groupId>org.mi</groupId>
            <artifactId>thrift</artifactId>
            <version>0.9.2-mi-v1.5-SNAPSHOT</version>
        </dependency>

        <!--发送短信相关-->
        <dependency>
            <groupId>com.xiaomi.sms</groupId>
            <artifactId>sms-v3-sdk</artifactId>
            <version>1.7-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>5.3.8</version>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.7.30</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.slf4j/slf4j-log4j12 -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
            <version>1.7.30</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
            <version>5.3.8</version>
        </dependency>

        <dependency>
            <artifactId>coupon-admin-api</artifactId>
            <groupId>com.xiaomi.nr</groupId>
            <version>${coupon.admin.api.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
            <version>2.7.0-mone-v14-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
            <version>1.2.1-mone-v6-SNAPSHOT</version>
        </dependency>

        <!-- 数据库相关 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>1.1.10</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>1.1.10</version>
        </dependency>

        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>3.5.3</version>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>5.1.35</version>
        </dependency>

        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>1.2.0</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>aop-utils</artifactId>
            <version>1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.xiaomi</groupId>
                    <artifactId>passportsdk</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>junit</artifactId>
                    <groupId>junit</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>3.8.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.lettuce</groupId>
                    <artifactId>lettuce-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>1.10.0</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.infra.galaxy</groupId>
            <artifactId>galaxy-talos-sdk</artifactId>
            <version>2.6.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.newretail</groupId>
            <artifactId>basic-tools</artifactId>
            <version>1.2.0</version>
        </dependency>

        <!--引入分布式锁-->
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>3.16.0</version>
        </dependency>

        <!-- 压缩、解压工具 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>1.12</version>
        </dependency>

        <dependency>
            <artifactId>xiaomi-common-utils</artifactId>
            <groupId>com.xiaomi</groupId>
            <version>2.8.15</version>
        </dependency>

        <!-- 解析Excel数据 -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>3.17</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>3.17</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.0.5</version>
            <exclusions>
                <exclusion>
                    <artifactId>poi-ooxml</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi-ooxml-schemas</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>2.8.8</version>
        </dependency>

        <!--es 依赖-->
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
            <version>7.2.0</version>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-client</artifactId>
            <version>7.2.0</version>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
            <version>7.2.0</version>
        </dependency>


        <!-- hdfs -->
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-hdfs</artifactId>
            <version>3.1.0-mdh3.1.1.9</version>
        </dependency>
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-hdfs-client</artifactId>
            <version>3.1.0-mdh3.1.1.9</version>
            <exclusions>
                <exclusion>
                    <artifactId>okhttp</artifactId>
                    <groupId>com.squareup.okhttp</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-common</artifactId>
            <version>3.1.0-mdh3.1.1.9</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 人群包服务 -->
        <dependency>
            <artifactId>retail-data-service-rpc-api</artifactId>
            <groupId>com.xiaomi.retail</groupId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!-- 查询商品服务 -->
        <dependency>
            <groupId>com.xiaomi.goods</groupId>
            <artifactId>gms-api</artifactId>
            <version>1.0.42-SNAPSHOT</version>
        </dependency>

        <!-- 商城订单服务 -->
        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>order-api</artifactId>
            <version>1.5.32-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--汽车积分服务-->
        <dependency>
            <groupId>com.xiaomi.youpin</groupId>
            <artifactId>aries-client</artifactId>
            <version>1.1.2-SNAPSHOT</version>
        </dependency>

        <!--  finance budget  -->
        <dependency>
            <groupId>com.mi.oa.finance.sdk</groupId>
            <artifactId>ems-spring-boot-starter</artifactId>
            <version>1.0.40-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>nr-job-core</artifactId>
            <version>2.6.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>nr-job-api</artifactId>
            <version>2.6.0-SNAPSHOT</version>
        </dependency>
    </dependencies>


</project>