package com.xiaomi.nr.coupon.admin.core.domain.icrm;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponConfigVO;
import com.xiaomi.nr.coupon.admin.api.enums.TranslationEnum;
import com.xiaomi.nr.coupon.admin.enums.icrm.IcrmEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.error.ErrCode;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import org.apache.commons.lang3.tuple.Pair;

/**
 * @author: zhangliwei6
 * @date: 2025/8/4 10:57
 * @description:
 */
public interface IICRMProxyService {

    default Pair<Boolean, String> icrmCheck(CouponConfigVO couponConfigVO) throws Exception {
        throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_REGION_NOT_SUPPORT.getTranslateContent());
    };

    default void icrmOccupy(CouponConfigVO couponConfigVO) throws Exception {
        throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_REGION_NOT_SUPPORT.getTranslateContent());
    };

    default void icrmNotify(CouponConfigVO couponConfigVO, IcrmEnum status, Integer applyType) throws Exception {
        throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_REGION_NOT_SUPPORT.getTranslateContent());
    };

    default void icrmOffline(Long id) throws Exception {
        throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_REGION_NOT_SUPPORT.getTranslateContent());
    }
}
