package com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.check;

import com.xiaomi.nr.coupon.admin.api.enums.FetchLimitTypeEnum;
import com.xiaomi.nr.coupon.admin.api.enums.TranslationEnum;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponBaseInfo;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.GivenAreaEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.UseTimeTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.pointadmin.PointBatchStatusEnum;
import com.xiaomi.nr.coupon.admin.enums.scene.SceneSendModeEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.CouponSceneRepository;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.scene.po.CouponScenePO;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @author: zhangliwei6
 * @date: 2025/5/28 14:26
 * @description:
 */
@Component
public class CouponConfigCommonValidator implements CouponConfigValidator {

    @Autowired
    private CouponSceneRepository couponSceneRepository;

    @Override
    public void validate(CouponConfigItem couponConfigItem) throws BizError {
        CouponBaseInfo info = couponConfigItem.getCouponBaseInfo();

        if (StringUtils.isBlank(info.getName())) {
            throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_CONFIG_INSERT_NULL_NAME.getTranslateContent());
        }
        if (StringUtils.isBlank(info.getCouponDesc())) {
            throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_CONFIG_INSERT_NULL_EXPLAIN.getTranslateContent());
        }
        if (StringUtils.isBlank(info.getSendScene())) {
            throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_CONFIG_INSERT_NULL_SCENE.getTranslateContent());
        }
        if (info.getStartFetchTime() == null || info.getEndFetchTime() == null ||
                (!info.getStartFetchTime().before(info.getEndFetchTime())) || info.getEndFetchTime().before(new Date())) {
            throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_CONFIG_INSERT_ERROR_TIME.getTranslateContent());
        }

        UseTimeTypeEnum useTimeType = UseTimeTypeEnum.getByValue(info.getUseTimeType());
        if (Objects.isNull(useTimeType)) {
            throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_CONFIG_INSERT_NULL_USE.getTranslateContent());
        }
        if (UseTimeTypeEnum.ABSOLUTE.equals(useTimeType)) {
            if (info.getStartUseTime() == null || info.getEndUseTime() == null) {
                throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_CONFIG_INSERT_NULL_TIME.getTranslateContent());
            }
        } else if (UseTimeTypeEnum.RELATIVE.equals(useTimeType)) {
            if (info.getUseDuration() <= 0) {
                throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_CONFIG_INSERT_NULL_TIME_2.getTranslateContent());
            }
        }

        if (info.getPromotionType() <= 0) {
            throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_CONFIG_INSERT_ERROR_TYPE.getTranslateContent());
        }

        // 发放场景
        List<String> sendSceneList = Arrays.asList(info.getSendScene().split(","));
        Map<String, CouponScenePO> scenePoMap = couponSceneRepository.selectPoBySceneCodes(sendSceneList);
        for (String sceneCode : sendSceneList) {
            CouponScenePO scenePO = scenePoMap.getOrDefault(sceneCode, null);
            if (Objects.isNull(scenePO)) {
                throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_CONFIG_INSERT_ERROR_SCENE.getTranslateContent() + sceneCode);
            }
            if (!Objects.equals(scenePO.getStatus(), PointBatchStatusEnum.ONLINE.getCode())) {
                throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_CONFIG_INSERT_STATUS_SCENE.getTranslateContent() + sceneCode);
            }

            // 限领校验
            if (FetchLimitTypeEnum.LIMIT.getCode().equals(info.getFetchLimitType())) {
                if (SceneSendModeEnum.COUPON.getCode() == scenePO.getSendMode() && (info.getApplyCount() <= 0 || info.getFetchLimit() <= 0 || info.getApplyCount() < info.getFetchLimit())) {
                    throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_CONFIG_INSERT_ERROR_APPLY_COUNT.getTranslateContent());
                }
            }
        }

        // 不限领
        if (FetchLimitTypeEnum.NO_LIMIT.getCode().equals(info.getFetchLimitType())) {
            if (info.getFetchLimit() != 0) {
                throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_CONFIG_INSERT_ERROR_LIMIT.getTranslateContent());
            }
        }

        if (Objects.nonNull(info.getExtProp()) &&Objects.nonNull(info.getExtProp().getArea()) && info.getExtProp().getArea() == GivenAreaEnum.YES.getType()) {
            if (CollectionUtils.isEmpty(info.getAreaIds())) {
                throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_CONFIG_INSERT_ERROR_AREA.getTranslateContent());
            }
        }
    }
}
