package com.xiaomi.nr.coupon.admin.global.domain.icrm;

import com.google.common.collect.Lists;
import com.xiaomi.com.i18n.area.Area;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponConfigVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.GoodMsgVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.GoodsRuleVO;
import com.xiaomi.nr.coupon.admin.api.utils.I18nUtil;
import com.xiaomi.nr.coupon.admin.core.domain.icrm.IICRMProxyService;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.UseChannelsEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.UseTimeTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.icrm.IcrmEnum;
import com.xiaomi.nr.coupon.admin.global.domain.icrm.dto.ActivityApplyLineDto;
import com.xiaomi.nr.coupon.admin.global.domain.icrm.dto.IcrmBudgetRequest;
import com.xiaomi.nr.coupon.admin.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastructure.login.UserInfoItemFactory;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.StringUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;


@Service
@Slf4j
public class IcrmProxyService implements IICRMProxyService {

    @Autowired
    private ICRMProxy icrmProxy;

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    @Override
    public Pair<Boolean, String> icrmCheck(CouponConfigVO couponConfigVO) throws Exception {
        log.info("IcrmProxyService icrmCheck request is {}", GsonUtil.toJson(couponConfigVO));
        String icrmRequest = convertToIcrmRequest(couponConfigVO);
        return icrmProxy.budgetVerification(icrmRequest);
    }

    @Override
    public void icrmOccupy(CouponConfigVO couponConfigVO) throws Exception {
        log.info("IcrmProxyService icrmOccupy request is {}", GsonUtil.toJson(couponConfigVO));
        String icrmRequest = convertToIcrmRequest(couponConfigVO);
        Pair<Boolean, String> result = couponConfigVO.getId() == 0 ? icrmProxy.activityCreate(icrmRequest) : icrmProxy.activityAdjustment(icrmRequest);
        if (Objects.equals(result.getLeft(), Boolean.FALSE)) {
            throw ExceptionHelper.create(ErrCode.COUPON, result.getRight());
        }
    }

    @Override
    public void icrmNotify(CouponConfigVO couponConfigVO, IcrmEnum status, Integer applyType) throws Exception {
        log.info("IcrmProxyService icrmNotify request is {}", GsonUtil.toJson(couponConfigVO));
        if (I18nUtil.isI18n() && couponConfigVO.getUseChannel().keySet().contains(UseChannelsEnum.AUTHORIZED_STORE.getValue())) {
            IcrmBudgetRequest icrmBudgetRequest = new IcrmBudgetRequest();
            icrmBudgetRequest.setActivityApplyUid(couponConfigVO.getUniqId());
            icrmBudgetRequest.setActivityApplyUniqueId(String.valueOf(couponConfigVO.getId()));
            icrmBudgetRequest.setStateCode(status.getCode());
            icrmBudgetRequest.setApplyType(applyType);
            icrmProxy.activityBPM(GsonUtil.toJson(icrmBudgetRequest));
        }
    }

    @Override
    public void icrmOffline(Long id) throws Exception {
        CouponConfigPO po = couponConfigRepository.searchCouponById(id);
        if (po == null || StringUtils.isEmpty(po.getUseChannel())) {
            return;
        }
        List<Integer> channels = StringUtil.convertToIntegerList(po.getUseChannel());
        if (I18nUtil.isI18n() && channels.contains(UseChannelsEnum.AUTHORIZED_STORE.getValue())) {
            IcrmBudgetRequest icrmBudgetRequest = new IcrmBudgetRequest();
            icrmBudgetRequest.setActivityApplyUniqueId(String.valueOf(id));
            icrmProxy.activityCancel(GsonUtil.toJson(icrmBudgetRequest));
        }

    }

    private String convertToIcrmRequest(CouponConfigVO couponConfigVO) throws BizError {
        IcrmBudgetRequest request = new IcrmBudgetRequest();
        request.setActivityApplyUid(couponConfigVO.getUniqId());
        request.setActivityApplyUniqueId(String.valueOf(couponConfigVO.getId()));
        request.setName(couponConfigVO.getName());
        request.setApplyType(couponConfigVO.getId() == 0 ? IcrmEnum.COUPON_CREATE.getCode() : IcrmEnum.COUPON_UPDATE.getCode());
        request.setActivityType("XMCI-016");
        if (UseTimeTypeEnum.RELATIVE.getValue() == couponConfigVO.getUseTermVO().getUseTimeType()) {
            couponConfigVO.getUseTermVO().setStartUseTime(couponConfigVO.getStartFetchTime());
            couponConfigVO.getUseTermVO().setEndUseTime(DateUtils.addHours(couponConfigVO.getEndFetchTime(), couponConfigVO.getUseTermVO().getUseDuration()));
        }
        request.setDateFrom(Area.of(I18nUtil.getGlobalAreaId()).timeFormat(couponConfigVO.getUseTermVO().getStartUseTime().getTime(),"yyyy/MM/dd"));
        request.setDateTo(Area.of(I18nUtil.getGlobalAreaId()).timeFormat(couponConfigVO.getUseTermVO().getEndUseTime().getTime(),"yyyy/MM/dd"));
        request.setActiveCustomer("ALL");
        request.setCountryCode(I18nUtil.getGlobalAreaId());
        request.setEmail(UserInfoItemFactory.createUserInfoItem().getEmail());

        List<ActivityApplyLineDto> list = Lists.newArrayList();
        GoodsRuleVO goodsRuleVO = couponConfigVO.getGoodsRuleVO();
        if(goodsRuleVO != null && MapUtils.isNotEmpty(goodsRuleVO.getGoodsMsg())) {
            for (Map.Entry<Long, GoodMsgVO> entry : goodsRuleVO.getGoodsMsg().entrySet()) {
                ActivityApplyLineDto dto = new ActivityApplyLineDto();
                dto.setSkuid(String.valueOf(entry.getValue().getSku()));
                dto.setUniqueId(String.valueOf(entry.getValue().getSku()));
                dto.setSingleSetinput(entry.getValue().getRebatesPricePerUnit());
                dto.setForecastQuantity(entry.getValue().getExpectedSaleAmount());
                list.add(dto);
            }
        }
        request.setActityApplyLine(list);
        return GsonUtil.toJson(request);
    }
}
