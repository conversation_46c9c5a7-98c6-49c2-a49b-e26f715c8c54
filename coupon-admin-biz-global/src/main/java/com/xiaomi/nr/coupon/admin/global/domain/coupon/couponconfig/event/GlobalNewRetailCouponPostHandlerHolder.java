package com.xiaomi.nr.coupon.admin.global.domain.coupon.couponconfig.event;

import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.common.domain.coupon.couponconfig.event.posthandler.*;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.event.BaseCouponPostHandler;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.event.ICouponPostHandlerHolder;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: zhangliwei6
 * @date: 2025/6/4 15:37
 * @description:
 */
@Component
public class GlobalNewRetailCouponPostHandlerHolder implements ICouponPostHandlerHolder {

    @Override
    public List<Class<? extends BaseCouponPostHandler>> getCouponPostHandlers() {
        return Lists.newArrayList(DataPreparePostHandler.class, RedisRefreshPostHandler.class,
                CouponOptLogPostHandler.class, CacheRefreshPostHandler.class, TaskUpdatePostHandler.class);
    }

    @Override
    public BizPlatformEnum getBizPlatformEnum() {
        return BizPlatformEnum.GLOBAL_NEW_RETAIL;
    }
}
