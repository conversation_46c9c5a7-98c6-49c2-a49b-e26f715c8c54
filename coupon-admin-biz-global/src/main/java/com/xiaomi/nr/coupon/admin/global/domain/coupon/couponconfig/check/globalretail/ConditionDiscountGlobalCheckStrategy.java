package com.xiaomi.nr.coupon.admin.global.domain.coupon.couponconfig.check.globalretail;

import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.common.domain.coupon.couponconfig.check.PromotionValueValidator;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.check.AbstractCheckStrategy;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.check.CouponConfigCommonValidator;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.check.CouponConfigUpdateCommonValidator;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.check.promotiontype.ConditionDiscountCreateValidator;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.PromotionTypeEnum;
import org.springframework.stereotype.Component;

/**
 * @author: zhangliwei6
 * @date: 2025/9/3 15:05
 * @description:
 */
@Component
public class ConditionDiscountGlobalCheckStrategy extends AbstractCheckStrategy {


    @Override
    public PromotionTypeEnum getPromotionType() {
        return PromotionTypeEnum.ConditionDiscount;
    }

    @Override
    public BizPlatformEnum getBizPlatformEnum() {
        return BizPlatformEnum.GLOBAL_NEW_RETAIL;
    }

    @Override
    public void initCheckList() {
        // common check
        CREATE_CHECK_LIST.addValidator(CouponConfigCommonValidator.class)
                // create common check
                .addValidator(ConditionDiscountCreateValidator.class)
                // create special check
                .addValidator(ConditionDiscountGlobalValidator.class)
                // goods check
                .addValidators(validatorFactory.getCouponScopes())
        ;

        // common check
        UPDATE_CHECK_LIST.addValidator(CouponConfigCommonValidator.class)
                // update base check
                .addValidator(CouponConfigUpdateCommonValidator.class)
                // update special check
                .addValidator(PromotionValueValidator.class)
                // goods check
                .addValidators(validatorFactory.getCouponScopes())
        ;
    }
}
