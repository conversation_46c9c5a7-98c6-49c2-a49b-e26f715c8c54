package com.xiaomi.nr.coupon.admin.global.domain.coupon.couponconfig.check.globalretail;

import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.common.domain.coupon.couponconfig.check.PromotionValueValidator;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.check.AbstractCheckStrategy;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.check.CouponConfigCommonValidator;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.check.CouponConfigUpdateCommonValidator;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.check.promotiontype.DirectReduceCreateValidator;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.PromotionTypeEnum;
import org.springframework.stereotype.Component;

/**
 * @author: zhangliwei6
 * @date: 2025/5/30 14:14
 * @description:
 */
@Component
public class DirectReduceGlobalCheckStrategy extends AbstractCheckStrategy {

    @Override
    public PromotionTypeEnum getPromotionType() {
        return PromotionTypeEnum.DirectReduce;
    }

    @Override
    public BizPlatformEnum getBizPlatformEnum() {
        return BizPlatformEnum.GLOBAL_NEW_RETAIL;
    }

    @Override
    public void initCheckList() {
        // common check
        CREATE_CHECK_LIST.addValidator(CouponConfigCommonValidator.class)
                // create common check
                .addValidator(DirectReduceCreateValidator.class)
                // create special check
                .addValidator(DirectReduceGlobalValidator.class)
                // goods check
                .addValidators(validatorFactory.getCouponScopes())
        ;

        // common check
        UPDATE_CHECK_LIST.addValidator(CouponConfigCommonValidator.class)
                // update base check
                .addValidator(CouponConfigUpdateCommonValidator.class)
                // update special check
                .addValidator(PromotionValueValidator.class)
                // goods check
                .addValidators(validatorFactory.getCouponScopes())
        ;
    }
}
