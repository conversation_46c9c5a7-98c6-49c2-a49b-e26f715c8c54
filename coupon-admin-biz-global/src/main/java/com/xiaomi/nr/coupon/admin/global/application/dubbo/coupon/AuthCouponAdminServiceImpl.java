package com.xiaomi.nr.coupon.admin.global.application.dubbo.coupon;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.google.common.collect.Lists;
import com.xiaomi.com.i18n.area.Area;
import com.xiaomi.mone.dubbo.docs.annotations.ApiModule;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponConfigListVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.PromotionRuleVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.UseTermVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponInfoRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.response.CouponInfoResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.store.StoreInfoVo;
import com.xiaomi.nr.coupon.admin.api.enums.TranslationEnum;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.auth.AuthCouponAdminService;
import com.xiaomi.nr.coupon.admin.api.utils.I18nUtil;
import com.xiaomi.nr.coupon.admin.api.utils.TimeUtil;
import com.xiaomi.nr.coupon.admin.common.application.dubbo.coupon.DubboCouponAdminServiceImpl;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.PromotionTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.UseChannelsEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.UseTimeTypeEnum;
import com.xiaomi.nr.coupon.admin.global.domain.coupon.couponconfig.bpm.strayegy.CouponPromotionStrategyFactory;
import com.xiaomi.nr.coupon.admin.global.domain.coupon.couponreview.CouponAuthBizService;
import com.xiaomi.nr.coupon.admin.global.rpc.job.NrJobServiceProxy;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.ResultValidator;
import com.xiaomi.nr.coupon.admin.util.RpcContextUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.config.annotation.Service;
import org.apache.dubbo.rpc.RpcContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: 优惠券管理后台服务接口
 * @Date 2025年7月22日14:59:36
 * @Version: 1.0
 **/
@Service(timeout = 10000, group = "${dubbo.group}", version = "1.0")
@Component
@Slf4j
@ApiModule(value = "授权认证发券服务", apiInterface = AuthCouponAdminService.class)
public class AuthCouponAdminServiceImpl implements AuthCouponAdminService {


    @Autowired
    private CouponAuthBizService couponAuthBizService;

    @Autowired
    private DubboCouponAdminServiceImpl couponAdminService;

    @Autowired
    private NrJobServiceProxy nrJobServiceProxy;

    @Autowired
    CouponPromotionStrategyFactory couponPromotionStrategyFactory;

    @Override
    public Result<BasePageResponse<CouponConfigListVO>> authCouponConfigList(CouponListRequest request) {
        try {
            // 1、岗位校验 + 门店列表查询
            List<String> orgList = checkAuthorizationForList(request);
            log.info("AuthCouponAdminServiceImpl authCouponConfigList orgList {}", GsonUtil.toJson(orgList));

            // 1.1、查询活动列表
            fillParam(request, orgList);


            // 2、获取券配置列表
            Result<BasePageResponse<CouponConfigListVO>> resp = couponAdminService.couponConfigList(request);

            // 3、校验数据
            ResultValidator.validate(resp, TranslationEnum.COUPON_AUTH_ERROR_LIST.getTranslateContent());

            // 4、数据处理
            resp.getData().setList(resp.getData().getList().stream().peek(item -> {
                item.setBeginTimeShow(Area.of(request.getAreaId()).timeFormat(item.getStartFetchTime().getTime()));
                item.setEndTimeShow(Area.of(request.getAreaId()).timeFormat(item.getEndFetchTime().getTime()));
                item.setAreaIdShow(Area.of(request.getAreaId()).getShortNameEn());
            }).collect(Collectors.toList()));

            //5.兼容一下返还给pc工作站的分页参数
            resp.getData().setTotal(resp.getData().getTotalCount());
            log.info("AuthCouponAdminServiceImpl pageQueryConfigList total:{}", resp.getData().getTotalCount());
            return resp;
        } catch (Exception e) {
            log.error("AuthCouponAdminServiceImpl pageQueryConfigList error", e);
            return Result.fromException(e);
        }
    }

    @Override
    public Result<CouponInfoResponse> queryConfigDetail(CouponInfoRequest request) {
        try {
            // 1、岗位校验 + 门店列表查询
            List<String> orgCodes = checkAuthorizationForDetail(request);
            log.info("AuthCouponAdminServiceImpl authCouponConfigList orgList {}", GsonUtil.toJson(orgCodes));

            // 2、获取数据
            Result<CouponInfoResponse> resp = couponAdminService.couponConfigDetail(request);

            // 3、校验数据
            ResultValidator.validate(resp, TranslationEnum.COUPON_AUTH_ERROR_DETAIL.getTranslateContent());

            // 4、按门店权限过滤门店数据 a.人员权限不含 * ，且含有门店权限的时候，才做过滤 b.当且仅当活动门店列表有数据的时候才做过滤
            List<StoreInfoVo> storeInfoVoList = resp.getData().getCouponConfigVO().getStoreInfoVoList();
            //因为orgCodes为空已经抛过异常所以不再对是否为空做冗余判断
            if(CollectionUtils.isNotEmpty(storeInfoVoList)){
                if (!orgCodes.contains("*")) {
                    storeInfoVoList.removeIf(storeInfoVo -> !orgCodes.contains(storeInfoVo.getOrgCode()));
                }
                //如果过滤完没有门店信息，返回空
                if(CollectionUtils.isEmpty(storeInfoVoList)){
                    return Result.success(new CouponInfoResponse());
                }
            }

            //5、价格处理，增加劵后价可读和划线价（平台价）可读 + 券固定使用时间
            handlerShowArgs(resp);


            return resp;
        } catch (Exception e) {
            log.error("AuthCouponAdminServiceImpl queryConfigDetail error", e);
            return Result.fromException(e);
        }
    }

    /**
     * 处理结构化offer需要后端可读化的字段
     * @param resp
     */
    private  void handlerShowArgs(Result<CouponInfoResponse> resp) {
        String areaId = I18nUtil.getGlobalAreaId();
        resp.getData().getGoodsRuleDetailVO().getSsuIncludeList().forEach(e -> {
            PromotionRuleVO promotionRuleVO = resp.getData().getCouponConfigVO().getPromotionRuleVO();
            e.setMarketPriceShow(Area.of(areaId).moneyFormatSymbol(e.getMarketPrice()));
            e.setPromotionPriceShow(couponPromotionStrategyFactory.getStrategy(PromotionTypeEnum.getByValue(promotionRuleVO.getPromotionType())).buildPromotionPriceShow(e.getMarketPrice(), promotionRuleVO, areaId));
        });
        UseTermVO useTermVO = resp.getData().getCouponConfigVO().getUseTermVO();
        if(useTermVO.getUseTimeType() == UseTimeTypeEnum.ABSOLUTE.getValue()){
            useTermVO.setStartUseTimeShow(Area.of(I18nUtil.getGlobalAreaId()).timeFormat(useTermVO.getStartUseTime().getTime()));
            useTermVO.setEndUseTimeShow(Area.of(I18nUtil.getGlobalAreaId()).timeFormat(useTermVO.getEndUseTime().getTime()));
        }
    }

    private static final Integer pageSize = 1000;

    @Override
    public Result<String> export2TaskCenter(CouponListRequest request) {
        //异步导出到任务中心
        return nrJobServiceProxy.doNrJob(request);
    }

    public List<CouponConfigListVO> queryExportConfigList(CouponListRequest request) throws Exception {
        // 岗位校验 + 门店列表查询
        List<String> orgList = checkAuthorizationForList(request);

        //查询活动列表
        fillParam(request, orgList);

        List<CouponConfigListVO> rList = Lists.newArrayList();

        //当前查询总记录数
        long currentCount = pageSize;
        //设置从第一页开始查，每次查100条
        request.setPageNo(1);
        request.setPageSize(pageSize);
        Result<BasePageResponse<CouponConfigListVO>> pageResponseResult = couponAdminService.couponConfigList(request);

        rList.addAll(pageResponseResult.getData().getList());
        while (pageResponseResult.getData().getTotalCount() > currentCount) {
            request.setPageNo(request.getPageNo() + 1);
            pageResponseResult = couponAdminService.couponConfigList(request);
            rList.addAll(pageResponseResult.getData().getList());
            //每次添加查询记录数
            currentCount += pageResponseResult.getData().getList().size();
        }
        return rList;
    }

    // 岗位校验 + 门店列表查询
    private List<String> checkAuthorizationForDetail(CouponInfoRequest request) throws Exception {
        List<String> orgList = new ArrayList<>();
        if (StringUtils.isNotEmpty(request.getViewLocale()) && CollectionUtils.isNotEmpty(request.getViewIds())) {//下钻模式：业务人员登录后可指定商进行查看
            request.setAreaId(request.getViewLocale());
            orgList = couponAuthBizService.getMerchantOrgList(request.getViewLocale(), request.getViewIds());
            if (orgList.size() == 0) {
                log.warn("商门店列表为空");
                throw ExceptionHelper.create(GeneralCodes.NotAuthorized, TranslationEnum.COUPON_AUTH_ERROR_MERCHANT_NULL.getTranslateContent());
            }
        } else {//普通模式
            if(StringUtils.isEmpty(request.getAreaId())) {
                request.setAreaId(RpcContextUtil.getAreaId());
            }
            Pair<Boolean, List<String>> auth = couponAuthBizService.authRoleAndOrgList(request.getAreaId()
                    , RpcContextUtil.getUserEmail(RpcContext.getContext()), RpcContextUtil.getMiId(RpcContext.getContext()),false);
            if (!auth.getLeft()) {
                log.warn("岗位校验未通过");
                throw ExceptionHelper.create(GeneralCodes.NotAuthorized, TranslationEnum.COUPON_AUTH_ERROR_STATION_CHECK.getTranslateContent());
            }
            orgList = auth.getRight();
        }
        return orgList;
    }

    //添加请求的参数
    private void fillParam(CouponListRequest request, List<String> orgList) throws Exception {
        request.setUseChannel(Collections.singletonList(UseChannelsEnum.AUTHORIZED_STORE.getValue()));
        request.setStoreIds(orgList);
        long setTime = LocalDateTime.now().atZone(ZoneId.systemDefault()).plusHours(12).toEpochSecond();
        long checkTime = request.getEndFetchTime() == null ? setTime : Math.min(TimeUtil.convertDateToLong(request.getEndFetchTime()), setTime);
        request.setEndFetchTime(TimeUtil.convertLongToDate(checkTime));//设定只有开始时间12小时内得活动才可查询到
    }

    // 岗位校验 + 门店列表查询
    private List<String> checkAuthorizationForList(CouponListRequest request) throws Exception {
        List<String> orgList = new ArrayList<>();
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(request.getViewLocale()) && CollectionUtils.isNotEmpty(request.getViewIds())) {//下钻模式：业务人员登录后可指定商进行查看
            request.setAreaId(request.getViewLocale());
            orgList = couponAuthBizService.getMerchantOrgList(request.getAreaId(), request.getViewIds());
            if (orgList.isEmpty()) {
                log.warn("商门店列表为空");
                throw ExceptionHelper.create(GeneralCodes.InternalError, TranslationEnum.COUPON_AUTH_ERROR_MERCHANT_NULL.getTranslateContent());
            }
        } else {//普通模式
            if(org.apache.commons.lang3.StringUtils.isEmpty(request.getAreaId())) {
                request.setAreaId(I18nUtil.getGlobalAreaId());
            }
            if(StringUtils.isEmpty(request.getEmail())) {
                request.setEmail(RpcContextUtil.getUserEmail(RpcContext.getContext()));
            }
            if(request.getMiId() == null || request.getMiId().equals(0L)) {
                request.setMiId(RpcContextUtil.getMiId(RpcContext.getContext()));
            }
            Pair<Boolean, List<String>> auth = couponAuthBizService.authRoleAndOrgList(request.getAreaId(), request.getEmail(), request.getMiId(),request.getIsVip());
            if (!auth.getLeft()) {
                log.warn("岗位校验未通过");
                throw ExceptionHelper.create(GeneralCodes.NotAuthorized, TranslationEnum.COUPON_AUTH_ERROR_STATION_CHECK.getTranslateContent());
            }
            orgList = auth.getRight();
        }
        return orgList;
    }
}
