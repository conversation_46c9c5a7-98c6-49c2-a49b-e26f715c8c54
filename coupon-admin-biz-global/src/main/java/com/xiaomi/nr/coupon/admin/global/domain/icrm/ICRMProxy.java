package com.xiaomi.nr.coupon.admin.global.domain.icrm;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.xiaomi.nr.coupon.admin.enums.icrm.IcrmErrorEnum;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class ICRMProxy {

    @Value("${adfs.url}")
    private String adfsUrl;

    @Value("${adfs.client_id}")
    private String clientId;

    @Value("${adfs.client_secret}")
    private String clientSecret;

    @Value("${adfs.resource}")
    private String resource;

    @Value("${adfs.grantType}")
    private String grantType;

    @Value("${adfs.username}")
    private String username;

    @Value("${adfs.password}")
    private String password;

    @Value("${icrm.url}")
    private String icrmUrl;

    private static final String BUDGET_VERIFICATION_INTERFACE = "BudgetVerification";
    private static final String ACTIVITY_CREATE_INTERFACE = "ActivityCreate";
    private static final String ACTIVITY_ADJUSTMENT_INTERFACE = "ActivityAdjustment";
    private static final String ACTIVITY_BPM_INTERFACE = "ActivityBPM";
    private static final String ACTIVITY_CANCEL_INTERFACE = "ActivityCancel";

    private static final String RESULT_CODE = "code";
    private static final String RESULT_MESSAGE = "message";
    private static final String RESULT_DATA = "data";
    private static final String PROMOTION_CODE = "promotionCode";
    private String buildBody(String actionName, String requestParameters) {
        Map<String, String> body = new HashMap<>();
        body.put("actionName", actionName);
        body.put("requestParameters", requestParameters);
        return GsonUtil.toJson(body);
    }

    /**
     * 预算校验：https://xiaomi.f.mioffice.cn/wiki/S09Jw7ZoFinL15kzK8DkYbbJ4qb
     */
    public Pair<Boolean, String> budgetVerification(String requestJson) throws BizError {
        String body = buildBody(BUDGET_VERIFICATION_INTERFACE, requestJson);
        try {
            JSONObject jsonObject = send(body);
            log.info("ICRMProxy budgetVerification send request: {}, result: {}", body, jsonObject);
            Integer code = jsonObject.getInt(RESULT_CODE);
            if (!Objects.equals(code, 200)) {
                return Pair.of(Boolean.FALSE, jsonObject.getStr(RESULT_MESSAGE));
            }
            log.info("ICRMProxy budgetVerification is success, request: {}, result: {}", body, jsonObject);
            return Pair.of(Boolean.TRUE, jsonObject.getStr(RESULT_MESSAGE));
        } catch (Exception e) {
            log.error("ICRMProxy budgetVerification is error, request: {} ", body, e);
            throw ExceptionHelper.create(GeneralCodes.ServerIsBuzy, e.getMessage());
        }
    }

    /**
     * 新建活动
     */
    public Pair<Boolean, String> activityCreate(String requestJson) throws BizError {
        String body = buildBody(ACTIVITY_CREATE_INTERFACE, requestJson);
        try {
            JSONObject jsonObject = send(body);
            log.info("ICRMProxy activityCreate send request: {}, result: {}", body, jsonObject);
            Integer code = jsonObject.getInt(RESULT_CODE);
            if (!Objects.equals(code, 200)) {
                return Pair.of(Boolean.FALSE, jsonObject.getStr(RESULT_MESSAGE));
            }
            log.info("ICRMProxy activityCreate is success, request: {}, result: {}", body, jsonObject);
            return Pair.of(Boolean.TRUE, jsonObject.getStr(RESULT_MESSAGE));
        } catch (Exception e) {
            log.error("ICRMProxy activityCreate is error, request: {} ", body, e);
            throw ExceptionHelper.create(GeneralCodes.ServerIsBuzy, e.getMessage());
        }
    }

    /**
     * 更新活动
     */
    public Pair<Boolean, String> activityAdjustment(String requestJson) throws BizError {
        String body = buildBody(ACTIVITY_ADJUSTMENT_INTERFACE, requestJson);
        try {
            JSONObject jsonObject = send(body);
            log.info("ICRMProxy activityAdjustment send request: {}, result: {}", body, jsonObject);
            Integer code = jsonObject.getInt(RESULT_CODE);
            if (!Objects.equals(code, 200)) {
                return Pair.of(Boolean.FALSE, jsonObject.getStr(RESULT_MESSAGE));
            }
            log.info("ICRMProxy activityAdjustment is success, request: {}, result: {}", body, jsonObject);
            return Pair.of(Boolean.TRUE, jsonObject.getStr(RESULT_MESSAGE));
        } catch (Exception e) {
            log.error("ICRMProxy activityAdjustment is error, request: {} ", body, e);
            throw ExceptionHelper.create(GeneralCodes.ServerIsBuzy, e.getMessage());
        }
    }

    /**
     * 审核回调
     */
    public Pair<Boolean, String> activityBPM(String requestJson) throws BizError {
        log.info("ICRMProxy activityBPM request is {}", requestJson);
        String body = buildBody(ACTIVITY_BPM_INTERFACE, requestJson);
        try {
            JSONObject jsonObject = send(body);
            Integer code = jsonObject.getInt(RESULT_CODE);
            if (!Objects.equals(code, 200)) {
                log.error("ICRMProxy activityBPM send icrm fail request: {} , result: {} ", body, jsonObject);
                return Pair.of(Boolean.FALSE, jsonObject.getStr(RESULT_MESSAGE));
            }
            log.info("ICRMProxy activityBPM is success, request: {}, result: {}", body, jsonObject);
            return Pair.of(Boolean.TRUE, jsonObject.getStr(RESULT_MESSAGE));
        } catch (Exception e) {
            log.error("ICRMProxy activityBPM send icrm fail request: {} ", body, e);
            throw ExceptionHelper.create(GeneralCodes.ServerIsBuzy, e.getMessage());
        }
    }

    /**
     * 活动下线
     */
    public Pair<Boolean, String> activityCancel(String requestJson) throws BizError {
        String body = buildBody(ACTIVITY_CANCEL_INTERFACE, requestJson);
        try {
            JSONObject jsonObject = send(body);
            Integer code = jsonObject.getInt(RESULT_CODE);
            if (!Objects.equals(code, 200)) {
                log.error("ICRMProxy activityCancel send icrm fail request: {} , result: {} ", body, jsonObject);
                return Pair.of(Boolean.FALSE, jsonObject.getStr(RESULT_MESSAGE));
            }
            log.info("ICRMProxy activityCancel is success, request: {}, result: {}", body, jsonObject);
            return Pair.of(Boolean.TRUE, jsonObject.getStr(RESULT_MESSAGE));
        } catch (Exception e) {
            log.error("ICRMProxy activityCancel send icrm fail request: {} ", body, e);
            throw ExceptionHelper.create(GeneralCodes.ServerIsBuzy, e.getMessage());
        }
    }


    public String getToken() throws BizError {
        String token = "";
        try {
            Map<String, Object> paramsMap = buildGetAdfsTokenParamsMap();
            log.info("ICRMProxy getToken paramsMap={}", paramsMap);
            String resp = HttpUtil.post(adfsUrl, paramsMap);
            log.info("ICRMProxy getToken resp={}", resp);
            token = JSONUtil.parseObj(resp).getStr("access_token");
        } catch (Exception e) {
            log.error("ICRMProxy getToken error", e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, e.getMessage());
        }
        return token;
    }

    private Map<String, Object> buildGetAdfsTokenParamsMap() {
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("client_id", clientId);
        paramsMap.put("client_secret", clientSecret);
        paramsMap.put("resource", resource);
        paramsMap.put("grant_type", grantType);
        paramsMap.put("username", "xiaomi\\xiaomichannel01");
        paramsMap.put("password", password);
        return paramsMap;
    }

    public JSONObject send(String body) throws BizError {
        try {
            long st1 = System.currentTimeMillis();
            String token = getToken();
            log.info("ICRMProxy get token body={} cost:{} ms", body, System.currentTimeMillis() - st1);
            long st2 = System.currentTimeMillis();
            // 发送HTTP POST请求
            HttpResponse response = HttpRequest.post(icrmUrl)
                    .header("Authorization", token)
                    .header("Content-Type", "application/json; charset=utf-8")
                    .body(body)
                    .execute();
            // 返回响应体的字符串表示
            log.info("ICRMProxy send end body={}， result={} cost:{} ms", body, JSONUtil.parseObj(response.body()), System.currentTimeMillis() - st2);
            JSONObject jsonObject = JSONUtil.parseObj(response.body());
            Integer code = jsonObject.getInt(RESULT_CODE);
            JSONObject message = getValue(jsonObject, RESULT_MESSAGE);
            if (!Objects.equals(code, 200) && message != null && message.containsKey(PROMOTION_CODE)) {
                String errorMsg = "ICRM:" + IcrmErrorEnum.getEnumByCode(message.getInt(PROMOTION_CODE)).getTranslateKey();
                if (message.containsKey(RESULT_DATA) && !message.isNull(RESULT_DATA)) {
                    errorMsg = errorMsg + ":" + message.get(RESULT_DATA);//拼接上有问题的sku
                }
                jsonObject.put(RESULT_MESSAGE, errorMsg);
            }
            return jsonObject;
        } catch (Exception e) {
            log.error("ICRMProxy send error ", e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, e.getMessage());
        }

    }

    private JSONObject getValue(JSONObject json, String key){
        try {
            return json.getJSONObject(key);
        } catch (Exception e) {
            return null;
        }
    }
}
