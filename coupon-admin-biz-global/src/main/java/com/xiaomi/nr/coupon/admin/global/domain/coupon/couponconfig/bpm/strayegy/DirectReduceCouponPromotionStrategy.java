package com.xiaomi.nr.coupon.admin.global.domain.coupon.couponconfig.bpm.strayegy;

import com.xiaomi.com.i18n.area.Area;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.PromotionRuleVO;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.PromotionTypeEnum;
import org.springframework.stereotype.Component;

@Component
public class DirectReduceCouponPromotionStrategy implements CouponPromotionStrategy {

    public static final String template = " %s Reduce by %s Item";
    @Override
    public String formatSalesValue(PromotionRuleVO promotionRuleVO, String areaId) {
        return String.format(template,Area.of(areaId).moneyFormatSymbol(promotionRuleVO.getPromotionValue()),promotionRuleVO.getBottomCount() >0 ? promotionRuleVO.getBottomCount() : 1);
    }

    @Override
    public PromotionTypeEnum getPromotionTypeEnum() {
        return PromotionTypeEnum.DirectReduce;
    }

    @Override
    public String buildPromotionPriceShow(Long marketPrice, PromotionRuleVO promotionRuleVO, String areaId) {
        return Area.of(areaId).moneyFormatSymbol(Math.max(marketPrice - promotionRuleVO.getPromotionValue(), 0));
    }
}
