package com.xiaomi.nr.coupon.admin.global.domain.coupon.couponconfig.check.globalretail;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.UseChannelVO;
import com.xiaomi.nr.coupon.admin.api.enums.TranslationEnum;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.check.CouponConfigValidator;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponBaseInfo;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.UseChannelsEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.error.ErrCode;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * @author: zhangliwei6
 * @date: 2025/5/30 14:17
 * @description:
 */
@Component
public class DirectReduceGlobalValidator implements CouponConfigValidator {

    @Override
    public void validate(CouponConfigItem couponConfigItem) throws Exception {
        CouponBaseInfo info = couponConfigItem.getCouponBaseInfo();
        // 国际的areaId必填
        if (StringUtils.isEmpty(info.getAreaId())) {
            throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_CONFIG_CREATE_CHECK_AREA.getTranslateContent());
        }

        // 国际的审批流id必填
        if (Objects.isNull(info.getWorkflowId())) {
            throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_CONFIG_CREATE_CHECK_WORKFLOW.getTranslateContent());
        }

        // 判断立减金额是否大于0
        if (info.getPromotionValue() <= 0) {
            throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_CONFIG_CREATE_CHECK_AMOUNT.getTranslateContent());
        }

        // 使用渠道 useChannel，使用平台 usePlatForm，使用门店 useStore
        if (MapUtils.isEmpty(info.getUseChannel())) {
            throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_CONFIG_CREATE_CHECK_CHANNEL.getTranslateContent());
        }

        // 国际只有直营店、授权店
        for (Integer channel : info.getUseChannel().keySet()) {
            if (Objects.isNull(channel)) continue;
            if (UseChannelsEnum.DIRECTSALE_STORE.getValue() != channel && UseChannelsEnum.AUTHORIZED_STORE.getValue() != channel) {
                throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_CONFIG_INSERT_ERROR_MATCH.getTranslateContent());
            }
        }

        // 3c商品指定门店校验
        for (Map.Entry<Integer, UseChannelVO> entry : info.getUseChannel().entrySet()) {
            if (!entry.getValue().isAll() && CollectionUtils.isEmpty(entry.getValue().getLimitIds())) {
                throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_CONFIG_CREATE_CHECK_STORE.getTranslateContent());
            }
        }
    }
}
