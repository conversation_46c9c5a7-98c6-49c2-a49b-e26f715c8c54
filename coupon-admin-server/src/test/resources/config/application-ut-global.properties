#server
app.name=coupon-admin
server.type=ut
server.port=8086
server.debug=true
server.connection-timeout=1000
i18n.group=GLOBAL

#dubbo global config
dubbo.group=sg_staging
dubbo.protocol.id=dubbo
dubbo.protocol.name=dubbo
dubbo.protocol.port=-1
dubbo.protocol.payload=11557050
dubbo.registry.address=nacos://sgp.nacos.test.b2c.srv:80
nacos.config.addrs=sgp.nacos.test.b2c.srv:80

#i8n area nacos sdk
i18n.area.app.env=sg_staging

youpin.log.group=sg_staging
log.path=/home/<USER>/log

spring.xmpulsenatl-datasource.name=xm_pulse_natl
spring.xmpulsenatl-datasource.url=jdbc:h2:mem:xm_pulse_natl;MODE=mysql;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
spring.xmpulsenatl-datasource.driver-class-name=org.h2.Driver
spring.xmpulsenatl-datasource.username=root
spring.xmpulsenatl-datasource.password=root


spring.xmpulsecoupon-datasource.name=xm_pulse_natl
spring.xmpulsecoupon-datasource.url=jdbc:h2:mem:xm_pulse_natl;MODE=mysql;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
spring.xmpulsecoupon-datasource.driver-class-name=org.h2.Driver
spring.xmpulsecoupon-datasource.username=root
spring.xmpulsecoupon-datasource.password=root


spring.xmbdmall-datasource.name=xm_mall
spring.xmbdmall-datasource.url=jdbc:h2:mem:xm_mall;MODE=mysql;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
spring.xmbdmall-datasource.driver-class-name=org.h2.Driver
spring.xmbdmall-datasource.username=root
spring.xmbdmall-datasource.password=root


spring.xmpulse-datasource.name=xm_pulse
spring.xmpulse-datasource.url=jdbc:h2:mem:xm_pulse;MODE=mysql;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
spring.xmpulse-datasource.driver-class-name=org.h2.Driver
spring.xmpulse-datasource.username=root
spring.xmpulse-datasource.password=root


spring.xmpulse-slave-datasource.name=xm_pulse
spring.xmpulse-slave-datasource.url=jdbc:h2:mem:xm_pulse;MODE=mysql;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
spring.xmpulse-slave-datasource.driver-class-name=org.h2.Driver
spring.xmpulse-slave-datasource.username=root
spring.xmpulse-slave-datasource.password=root


spring.xmpms-slave-datasource.name=xm_pms
spring.xmpms-slave-datasource.url=jdbc:h2:mem:xm_pms;MODE=mysql;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
spring.xmpms-slave-datasource.driver-class-name=org.h2.Driver
spring.xmpms-slave-datasource.username=root
spring.xmpms-slave-datasource.password=root


spring.xmstore-datasource.name=xm_store
spring.xmstore-datasource.url=jdbc:h2:mem:xm_store;MODE=mysql;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
spring.xmstore-datasource.driver-class-name=org.h2.Driver
spring.xmstore-datasource.username=root
spring.xmstore-datasource.password=root


spring.adsdata-datasource.name=nrdc_bi
spring.adsdata-datasource.url=jdbc:h2:mem:nrdc_bi;MODE=mysql;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
spring.adsdata-datasource.driver-class-name=org.h2.Driver
spring.adsdata-datasource.username=root
spring.adsdata-datasource.password=root


spring.xmpulse-tidb-slave-datasource.name=xm_pulse
spring.xmpulse-tidb-slave-datasource.url=jdbc:h2:mem:xm_pulse;MODE=mysql;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
spring.xmpulse-tidb-slave-datasource.driver-class-name=org.h2.Driver
spring.xmpulse-tidb-slave-datasource.username=root
spring.xmpulse-tidb-slave-datasource.password=root

spring.nr-point-admin-datasource.name=nr_point_admin
spring.nr-point-admin-datasource.url=jdbc:h2:mem:nr_point_admin;MODE=mysql;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
spring.nr-point-admin-datasource.driver-class-name=org.h2.Driver
spring.nr-point-admin-datasource.username=root
spring.nr-point-admin-datasource.password=root

spring.user-point-datasource.name=pulse_user
spring.user-point-datasource.url=jdbc:h2:mem:pulse_user;MODE=mysql;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
spring.user-point-datasource.driver-class-name=org.h2.Driver
spring.user-point-datasource.username=root
spring.user-point-datasource.password=root


logging.level.com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.couponconfig=DEBUG
logging.level.com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.goods=DEBUG
logging.level.com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.coupon=DEBUG
logging.level.com.xiaomi.nr.coupon.admin.infrastructure.repository.tidbdao=DEBUG

spring.pulse.redis.host=127.0.0.1
spring.pulse.redis.port=6379
spring.pulse.redis.password=root


spring.newcoupon.redis.host=127.0.0.1
spring.newcoupon.redis.port=6379
spring.newcoupon.redis.password=root
spring.newcoupon.redis.connect-timeout-millis=1000
spring.newcoupon.redis.read-timeout-millis=3000
spring.newcoupon.redis.database=0
spring.newcoupon.redis.jedis-pool.max-idle=50
spring.newcoupon.redis.jedis-pool.min-idle=20
spring.newcoupon.redis.jedis-pool.max-total=50
spring.newcoupon.redis.jedis-pool.test-while-idle=true
spring.newcoupon.redis.jedis-pool.time-between-eviction-runs-millis=30000
spring.newcoupon.redis.jedis-pool.min-evictable-idle-time-millis=150000
spring.newcoupon.redis.jedis-pool.num-tests-per-eviction-run=-1
spring.newcoupon.redis.jedis-pool.max-wait-millis=3000

spring.pointadmin.redis.host=127.0.0.1
spring.pointadmin.redis.port=6379
spring.pointadmin.redis.password=root


spring.karos.misc.redis.host=127.0.0.1
spring.karos.misc.redis.port=6379
spring.karos.misc.redis.password=root


#领券活动缓存会用到
spring.misc.redis.host=127.0.0.1
spring.misc.redis.port=6379
spring.misc.redis.password=root


spring.coupon.es.host=tjstaging.api.es.srv:80
spring.coupon.es.username=xiaomi
spring.coupon.es.password=xiaomi
spring.coupon.es.index=sg_nr_coupon_config_staging

keycenter.addr=keycenter-test.b2c.srv:9988
keycenter.sid=keycenter-test

#---bpm---
bpm.dubbo.group=global_staging
dubbo.nr-bpm.appId=xm-yp-upc-0181
dubbo.nr-bpm.appKey=GDAyQIdH0sw4qw+EMawoc6XJyzMlEAIOsTyFXn5FaaxOqgVQxuLjYh1wFh56OaW+WGIYEuYNCtvLvU9FudA08EiB2mnq/xgQ0UzGiGx0Tjq9GwBJXAxVshgUrGqi9OkbQelSzTeuEroQjKSF7dEA
dubbo.nr-bpm.appKey@kc-sid=mi_newretail_promotion_global
bpm.coupon.definitionKey=mi_online_coupon
bpm.coupon.newRetailKey=newretail_freight_coupon
bpm.task.fillTaskRetailKey=newretail_fill_coupon
bpm.coupon.subsidyCouponKey=super_subsidy_coupon

#---bpm---
store.group=sg_staging
user.group=sg_staging
gms.dubbo.group=sg_staging
gis.dubbo.group=sg_staging
gis.dubbo.group.offline=sg_staging
order.dubbo.group=sg_staging
aries.dubbo.group=sg_staging
job.dubbo.group=sg_staging

miShop.activity.event.api=http://event-be.test.mi.com

fds.accessKey=AKB7FI7MHQH2BBQQDW
fds.secretKey=GDCgr5ObWcyg8mUoXIzuDUlDF8nR3TFo2Pd+tEJ7ppo0pw94NVjJy1lFV3sPHKSAHmUYErybVUy3V0MeiHGSQuZPGguE/xgQMrN5dzbYTtycZAcmuQa4GhgUM88f1CKusGZ1YIFK1mdx4ov3RBsA
fds.secretKey@kc-sid=mi_newretail_promotion_global
fds.endpoint=staging-cnbj2-fds.api.xiaomi.net
fds.bucketName=sg-nr-coupon-bucket

coupon.review.url=https://work-be.test.mi.com/main-coupon-manage/coupon/list?type=review&id=
coupon.global.review.url=https://work-test.g.mi.com/main-global-promotion-admin/coupon/goods?bpmId=
coupon.update.url=https://work-be.test.mi.com/main-coupon-manage/coupon/list?type=editCoupon&id=
postCoupon.review.url=https://work-be.test.mi.com/main-coupon-manage/coupon/freightCouponList?type=review&id=
postCoupon.update.url=https://work-be.test.mi.com/main-coupon-manage/coupon/freightCouponList?type=editCoupon&id=
subsidyCoupon.review.url=https://work-be.test.mi.com/main-coupon-manage/coupon/subsidyCouponList?type=review&id=
subsidyCoupon.update.url=https://work-be.test.mi.com/main-coupon-manage/coupon/subsidyCouponList?type=editCoupon&id=

userApi.url=http://api.d.mi.com
userApi.key=a219beff5c4baa56

bpm.secret=55NegnAUktDTfPqwL0OnQFxRcaXsKi92
bpm.url=http://global-xmmionegw.test.mi.com/mtop/bpm/process/create
bpm.url.cancel=http://global-xmmionegw.test.mi.com/mtop/bpm/process/cancel

rocketmq.name-server=staging-cnbj2-rocketmq.namesrv.api.xiaomi.net:9876
rocketmq.producer.group=nr_coupon_postoffice_producer_group
rocketmq.producer.sendMessageTimeout=300000
rocketmq.producer.access-key=AKB7FI7MHQH2BBQQDW
rocketmq.producer.secret-key=GDCgr5ObWcyg8mUoXIzuDUlDF8nR3TFo2Pd+tEJ7ppo0pw94NVjJy1lFV3sPHKSAHmUYErybVUy3V0MeiHGSQuZPGguE/xgQMrN5dzbYTtycZAcmuQa4GhgUM88f1CKusGZ1YIFK1mdx4ov3RBsA
rocketmq.producer.secret-key@kc-sid=mi_newretail_promotion_global
postoffice.topic=CI96578_Youpin_Committee_Msg_Listener

#------品类券新品提醒场景id------
postoffice.newGoodsNotifySceneId=218
postoffice.pointLowStockNotifySceneId=261

nr.rocketmq.name-server=staging-cnbj2-rocketmq.namesrv.api.xiaomi.net:9876
nr.rocketmq.producer.group=nr-coupon
nr.rocketmq.producer.access-key=AKB7FI7MHQH2BBQQDW
nr.rocketmq.producer.secret-key=GDCgr5ObWcyg8mUoXIzuDUlDF8nR3TFo2Pd+tEJ7ppo0pw94NVjJy1lFV3sPHKSAHmUYErybVUy3V0MeiHGSQuZPGguE/xgQMrN5dzbYTtycZAcmuQa4GhgUM88f1CKusGZ1YIFK1mdx4ov3RBsA
nr.rocketmq.producer.secret-key@kc-sid=mi_newretail_promotion_global
nr.rocketmq.producer.sendMessageTimeout=300000

#优惠券即将过期提醒push
nr.rocketmq.producer.coupon.expire.topic=nr_coupon_expire_push_test
#优惠券修改提醒push
nr.rocketmq.producer.coupon.change.topic=sg_staging_nr_coupon_config_change_topic


#飞书消息必填 环境标识
env=test

rpc.etcd.host=http://etcd.test.mi.com

# ems config
oaucf.ems.url=https://ems-finance.test.mioffice.cn
oaucf.ems.appId=expense_management_test
oaucf.ems.appSecret=aea004ec9c661e0ac26a66051c3d7e4e

hdfs.url=hdfs://tjwqstaging-hdd

aes.password=M6EED09BCC6539AD

neptune.hotload.appId=22871eed7c454b2390d1001d4bd88226
neptune.hotload.appKey=GBAMnBwpXQYDZhBKOCT+PL2eGBJiFXM3R+pAXZvuulaRCwW+uP8YEC7OJ695f0gfrGm3bUSmNaQYFE6/UaEi0jOhMgb8bjArLgWVy3tzAA==
neptune.hotload.appKey@kc-sid=mi_newretail_promotion_global
neptune.hotLoad.isProd=false
neptune.hotload.isSg=true
neptune.hotload.period=60

job.admin.addresses=http://sg-job.test.be.mi.com/
job.accessToken=GCC6YUoT9nF/pG/2CApB4cbLE2pdgAkC6lqWDDoQ/rDw3hgSBCVBsGQeSLSCi2jUADuAmY3/GBBzWcT8UyRCK41cslMJ51puGBTdaEIwvE932P9ebLf/PqDLq7eEtwA=
job.accessToken@kc-sid=mi_newretail_promotion_global
job.executor.appname=global-promotion-admin
job.executor.ip=
job.executor.port=-1
job.executor.logpath=
job.executor.logretentiondays=30
job.task.jobKey=couponOfferNrJobKey
job.task.projectId=852

# icrm url
adfs.url=https://adfs.mioffice.cn/adfs/oauth2/token
adfs.client_id=213bb3aa-d235-4080-b945-76cf4fa3c1d4
adfs.client_secret=nATkYP7Bed9QWA7mCNdKebsLnFJacLzSUdEm5BR3
adfs.resource=https://icrmuat.be.test.mi.com/api/data/v9.1/
adfs.grantType=password
adfs.username=xiaomi\\xiaomichannel01
adfs.password=Xmh78190qr01
icrm.url=https://icrmuat.be.test.mi.com/api/data/v9.1/xiaomi_PromotionInterfaceActions

unittest.mock.datasource[0].beanName=xmPulseNatlDatasource
unittest.mock.datasource[0].ddlSql=db/schema/xm_pulse_natl.sql
unittest.mock.datasource[0].dmlSql=db/case/xm_pulse_natl.sql
unittest.mock.datasource[1].beanName=xmPulseNatlDatasource
unittest.mock.datasource[1].ddlSql=db/schema/other.sql