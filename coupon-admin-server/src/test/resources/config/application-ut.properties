#server
app.name=coupon-admin
server.type=ut
server.port=8086
server.debug=true
server.connection-timeout=1000
i18n.group=CN

dubbo.group=ut
dubbo.protocol.id=dubbo
dubbo.protocol.name=dubbo
dubbo.protocol.port=-1
dubbo.protocol.payload=11557050
dubbo.registry.address=nacos://nacos.test.b2c.srv:80
nacos.config.addrs=nacos.test.b2c.srv:80

youpin.log.group=dev
log.path=/tmp

talos.topic=cnzone_newretail_common_test
talos.sendpoint=http://staging-cnbj2-talos.api.xiaomi.net
talos.access.key=AKTAFN57IDDEY7VBBP
talos.access.secret=GDAz37cI7SbSMQ10JYFhtP/7HITSFKGLvONy27vYK7Du8eRdsSZaO2r5FGR2QGVq7jwYEiEeAZPnMExirYKutGrcjUyR/xgQHvyHWbQbSgO9eisdSQHbhhgURUM6w2HSfto+rWQbz3qygM/ac6gA
talos.access.secret@kc-sid=mi_newretail_risk.g


spring.xmpulsenatl-datasource.name=xm_pulse_natl
spring.xmpulsenatl-datasource.url=jdbc:h2:mem:xm_pulse_natl;MODE=mysql;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
spring.xmpulsenatl-datasource.driver-class-name=org.h2.Driver
spring.xmpulsenatl-datasource.username=root
spring.xmpulsenatl-datasource.password=root


spring.xmpulsecoupon-datasource.name=xm_pulse_natl
spring.xmpulsecoupon-datasource.url=jdbc:h2:mem:xm_pulse_natl;MODE=mysql;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
spring.xmpulsecoupon-datasource.driver-class-name=org.h2.Driver
spring.xmpulsecoupon-datasource.username=root
spring.xmpulsecoupon-datasource.password=root


spring.xmbdmall-datasource.name=xm_mall
spring.xmbdmall-datasource.url=jdbc:h2:mem:xm_mall;MODE=mysql;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
spring.xmbdmall-datasource.driver-class-name=org.h2.Driver
spring.xmbdmall-datasource.username=root
spring.xmbdmall-datasource.password=root


spring.xmpulse-datasource.name=xm_pulse
spring.xmpulse-datasource.url=jdbc:h2:mem:xm_pulse;MODE=mysql;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
spring.xmpulse-datasource.driver-class-name=org.h2.Driver
spring.xmpulse-datasource.username=root
spring.xmpulse-datasource.password=root


spring.xmpulse-slave-datasource.name=xm_pulse
spring.xmpulse-slave-datasource.url=jdbc:h2:mem:xm_pulse;MODE=mysql;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
spring.xmpulse-slave-datasource.driver-class-name=org.h2.Driver
spring.xmpulse-slave-datasource.username=root
spring.xmpulse-slave-datasource.password=root


spring.xmpms-slave-datasource.name=xm_pms
spring.xmpms-slave-datasource.url=jdbc:h2:mem:xm_pms;MODE=mysql;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
spring.xmpms-slave-datasource.driver-class-name=org.h2.Driver
spring.xmpms-slave-datasource.username=root
spring.xmpms-slave-datasource.password=root


spring.xmstore-datasource.name=xm_store
spring.xmstore-datasource.url=jdbc:h2:mem:xm_store;MODE=mysql;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
spring.xmstore-datasource.driver-class-name=org.h2.Driver
spring.xmstore-datasource.username=root
spring.xmstore-datasource.password=root


spring.adsdata-datasource.name=nrdc_bi
spring.adsdata-datasource.url=jdbc:h2:mem:nrdc_bi;MODE=mysql;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
spring.adsdata-datasource.driver-class-name=org.h2.Driver
spring.adsdata-datasource.username=root
spring.adsdata-datasource.password=root


spring.xmpulse-tidb-slave-datasource.name=xm_pulse
spring.xmpulse-tidb-slave-datasource.url=jdbc:h2:mem:xm_pulse;MODE=mysql;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
spring.xmpulse-tidb-slave-datasource.driver-class-name=org.h2.Driver
spring.xmpulse-tidb-slave-datasource.username=root
spring.xmpulse-tidb-slave-datasource.password=root

spring.nr-point-admin-datasource.name=nr_point_admin
spring.nr-point-admin-datasource.url=jdbc:h2:mem:nr_point_admin;MODE=mysql;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
spring.nr-point-admin-datasource.driver-class-name=org.h2.Driver
spring.nr-point-admin-datasource.username=root
spring.nr-point-admin-datasource.password=root

spring.user-point-datasource.name=pulse_user
spring.user-point-datasource.url=jdbc:h2:mem:pulse_user;MODE=mysql;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
spring.user-point-datasource.driver-class-name=org.h2.Driver
spring.user-point-datasource.username=root
spring.user-point-datasource.password=root


logging.level.com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.couponconfig=DEBUG
logging.level.com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.goods=DEBUG
logging.level.com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.coupon=DEBUG
logging.level.com.xiaomi.nr.coupon.admin.infrastructure.repository.tidbdao=DEBUG

spring.pulse.redis.host=127.0.0.1
spring.pulse.redis.port=6379
spring.pulse.redis.password=root
spring.pulse.redis.connect-timeout-millis=1000
spring.pulse.redis.read-timeout-millis=3000
spring.pulse.redis.database=0
spring.pulse.redis.jedis-pool.max-idle=50
spring.pulse.redis.jedis-pool.min-idle=20
spring.pulse.redis.jedis-pool.max-total=50
spring.pulse.redis.jedis-pool.test-while-idle=true
spring.pulse.redis.jedis-pool.time-between-eviction-runs-millis=30000
spring.pulse.redis.jedis-pool.min-evictable-idle-time-millis=150000
spring.pulse.redis.jedis-pool.num-tests-per-eviction-run=-1
spring.pulse.redis.jedis-pool.max-wait-millis=3000


spring.newcoupon.redis.host=127.0.0.1
spring.newcoupon.redis.port=6379
spring.newcoupon.redis.password=root
spring.newcoupon.redis.connect-timeout-millis=1000
spring.newcoupon.redis.read-timeout-millis=3000
spring.newcoupon.redis.database=0
spring.newcoupon.redis.jedis-pool.max-idle=50
spring.newcoupon.redis.jedis-pool.min-idle=20
spring.newcoupon.redis.jedis-pool.max-total=50
spring.newcoupon.redis.jedis-pool.test-while-idle=true
spring.newcoupon.redis.jedis-pool.time-between-eviction-runs-millis=30000
spring.newcoupon.redis.jedis-pool.min-evictable-idle-time-millis=150000
spring.newcoupon.redis.jedis-pool.num-tests-per-eviction-run=-1
spring.newcoupon.redis.jedis-pool.max-wait-millis=3000

spring.pointadmin.redis.host=127.0.0.1
spring.pointadmin.redis.port=6379
spring.pointadmin.redis.password=root
spring.pointadmin.redis.connect-timeout-millis=1000
spring.pointadmin.redis.read-timeout-millis=3000
spring.pointadmin.redis.database=0
spring.pointadmin.redis.jedis-pool.max-idle=50
spring.pointadmin.redis.jedis-pool.min-idle=20
spring.pointadmin.redis.jedis-pool.max-total=50
spring.pointadmin.redis.jedis-pool.test-while-idle=true
spring.pointadmin.redis.jedis-pool.time-between-eviction-runs-millis=30000
spring.pointadmin.redis.jedis-pool.min-evictable-idle-time-millis=150000
spring.pointadmin.redis.jedis-pool.num-tests-per-eviction-run=-1
spring.pointadmin.redis.jedis-pool.max-wait-millis=3000


spring.karos.misc.redis.host=127.0.0.1
spring.karos.misc.redis.port=6379
spring.karos.misc.redis.password=root
spring.karos.misc.redis.connect-timeout-millis=1000
spring.karos.misc.redis.read-timeout-millis=3000
spring.karos.misc.redis.database=0
spring.karos.misc.redis.jedis-pool.max-idle=50
spring.karos.misc.redis.jedis-pool.min-idle=20
spring.karos.misc.redis.jedis-pool.max-total=50
spring.karos.misc.redis.jedis-pool.test-while-idle=true
spring.karos.misc.redis.jedis-pool.time-between-eviction-runs-millis=30000
spring.karos.misc.redis.jedis-pool.min-evictable-idle-time-millis=150000
spring.karos.misc.redis.jedis-pool.num-tests-per-eviction-run=-1
spring.karos.misc.redis.jedis-pool.max-wait-millis=3000


#领券活动缓存会用到
spring.misc.redis.host=127.0.0.1
spring.misc.redis.port=6379
spring.misc.redis.password=root
spring.misc.redis.connect-timeout-millis=1000
spring.misc.redis.read-timeout-millis=3000
spring.misc.redis.database=0
spring.misc.redis.jedis-pool.max-idle=50
spring.misc.redis.jedis-pool.min-idle=20
spring.misc.redis.jedis-pool.max-total=50
spring.misc.redis.jedis-pool.test-while-idle=true
spring.misc.redis.jedis-pool.time-between-eviction-runs-millis=30000
spring.misc.redis.jedis-pool.min-evictable-idle-time-millis=150000
spring.misc.redis.jedis-pool.num-tests-per-eviction-run=-1
spring.misc.redis.jedis-pool.max-wait-millis=3000


spring.coupon.es.host=tjstaging.api.es.srv:80
spring.coupon.es.username=xiaomi
spring.coupon.es.password=xiaomi
spring.coupon.es.index=nr_coupon_center_coupon_config


keycenter.addr=keycenter-test.b2c.srv:9988
keycenter.sid=keycenter-test


#---bpm����---
bpm.dubbo.group=test
dubbo.nr-bpm.appId=xm-yp-upc-0181
dubbo.nr-bpm.appKey=GDAa0oNL1T6+J8Zb6gj7oWwbXe7+dJ0clMdMfdOk5+bvGoNt0daueaDXS74xRlVqymsYEnv8HzQ9n0AotEgwe1b7GMLi/xgQU8lpn/HWQaK4ULv3+iJQkBgUoCPWrDzt7gr3bZQ06k2JDk75SqMA
dubbo.nr-bpm.appKey@kc-sid=mi_newretail_risk.g
bpm.coupon.definitionKey=mi_online_coupon
bpm.coupon.newRetailKey=newretail_freight_coupon
bpm.task.fillTaskRetailKey=newretail_fill_coupon
bpm.coupon.subsidyCouponKey=super_subsidy_coupon
#---bpm����---


store.group=staging
gms.dubbo.group=staging
gis.dubbo.group=staging
gis.dubbo.group.offline=staging
order.dubbo.group=staging
aries.dubbo.group=staging
cis.dubbo.group=staging


miShop.activity.event.api=http://event-be.test.mi.com


fds.accessKey=AKB7FI7MHQH2BBQQDW
fds.secretKey=GDBsq9RzEFWxqWcJzRH4TQFalJO1hn0zd27nv3cyO7T2En6OYSC/456cY6j/hyDLcrcYEjJ0phsquUdhgbxxpaZOZXew/xgQnQOLhsWdQPGzI2oWYZD9oxgUE7NREtydh2tVy81gx28y1ODkwbsA
fds.secretKey@kc-sid=mi_newretail_risk.g
fds.endpoint=staging-cnbj2-fds.api.xiaomi.net
fds.bucketName=nr-coupon-bucket

coupon.review.url=https://work-be.test.mi.com/main-coupon-manage/coupon/list?type=review&id=
coupon.global.review.url=https://work-test.g.mi.com/main-global-promotion-admin/coupon/goods?bpmId=
coupon.update.url=https://work-be.test.mi.com/main-coupon-manage/coupon/list?type=editCoupon&id=
postCoupon.review.url=https://work-be.test.mi.com/main-coupon-manage/coupon/freightCouponList?type=review&id=
postCoupon.update.url=https://work-be.test.mi.com/main-coupon-manage/coupon/freightCouponList?type=editCoupon&id=
subsidyCoupon.review.url=https://work-be.test.mi.com/main-coupon-manage/coupon/subsidyCouponList?type=review&id=
subsidyCoupon.update.url=https://work-be.test.mi.com/main-coupon-manage/coupon/subsidyCouponList?type=editCoupon&id=

userApi.url=http://api.d.mi.com
userApi.key=a219beff5c4baa56



rocketmq.name-server=staging-cnbj2-rocketmq.namesrv.api.xiaomi.net:9876
rocketmq.producer.group=nr_coupon_postoffice_producer_group
rocketmq.producer.sendMessageTimeout=300000
rocketmq.producer.access-key=AKB7FI7MHQH2BBQQDW
rocketmq.producer.secret-key=GDA5kiTgld61hxW3G0dLFpKsNTU8eqNnq5Y35Sxylz0C+188L9cG254wXCVRvSoV/DwYEtuSI8VLCEZhrYQP3AtGFq92/xgQ1VjPB6DnSniNxVqRUp4aXBgUdoPZPney5DtE95Bm40PFrOIVl4kA
rocketmq.producer.secret-key@kc-sid=mi_newretail_risk.g
postoffice.topic=CI96578_Youpin_Committee_Msg_Listener
#------品类券新品提醒场景id------
postoffice.newGoodsNotifySceneId=218
postoffice.pointLowStockNotifySceneId=261

bpm.secret=
bpm.url=
bpm.url.cancel=

nr.rocketmq.name-server=staging-cnbj2-rocketmq.namesrv.api.xiaomi.net:9876
nr.rocketmq.producer.group=nr-coupon
nr.rocketmq.producer.access-key=AKB7FI7MHQH2BBQQDW
nr.rocketmq.producer.secret-key=GDDGzV/xwzmqwlz6gpMKY2ICbBcJAopdOd1pjs1ry6IP/zThU1wsIj5xW0u6h/7sg2cYEsE/jHpl/kOLon2L6FaAryie/xgQMBlvvaR0QR+pKEfnCRw5dRgUEdemi9+tFCIscMc/WB3yOkvj520A
nr.rocketmq.producer.secret-key@kc-sid=mi_newretail_risk.g
nr.rocketmq.producer.sendMessageTimeout=300000

#优惠券即将过期提醒push
nr.rocketmq.producer.coupon.expire.topic=nr_coupon_expire_push_test
#优惠券修改提醒push
nr.rocketmq.producer.coupon.change.topic=nr_coupon_config_change


#飞书消息必填 环境标识
env=local

rpc.etcd.host=http://etcd.test.mi.com

# ems config
oaucf.ems.url=https://ems-finance.test.mioffice.cn
oaucf.ems.appId=expense_management_test
oaucf.ems.appSecret=aea004ec9c661e0ac26a66051c3d7e4e

hdfs.url=hdfs://tjwqstaging-hdd

aes.password=M6EED09BCC6539AD

unittest.mock.datasource[0].beanName=xmPulseNatlDatasource
unittest.mock.datasource[0].ddlSql=db/schema/xm_pulse_natl.sql
unittest.mock.datasource[0].dmlSql=db/case/xm_pulse_natl.sql
unittest.mock.datasource[1].beanName=xmPulseNatlDatasource
unittest.mock.datasource[1].ddlSql=db/schema/other.sql
