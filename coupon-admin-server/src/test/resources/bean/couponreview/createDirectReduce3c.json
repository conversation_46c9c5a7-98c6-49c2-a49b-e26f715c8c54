{"couponConfigVO": {"id": 0, "name": "测试56000", "couponDesc": "不适用于秒杀、拼团、套装、众筹、第三方产品、虚拟产品等，涉及产品以优惠券实际使用情况为准。", "sendSceneType": 1, "sendScene": "136A560EF374DC3F6945EB3325AE28CD", "startFetchTime": "Jun 4, 2025 12:00:00 AM", "endFetchTime": "Jun 25, 2035 11:59:59 PM", "useTermVO": {"useTimeType": 1, "startUseTime": "Jun 4, 2025 12:00:00 AM", "endUseTime": "Jun 26, 2035 11:59:59 PM", "useDuration": 0, "timeGranularity": 2}, "useChannel": {"1": {"all": true}, "2": {"all": true, "limitIds": []}, "3": {"all": true, "limitIds": []}, "4": {"all": true, "limitIds": []}}, "promotionRuleVO": {"promotionType": 4, "promotionValue": 1000, "bottomPrice": 0, "bottomCount": 1, "bottomType": 2, "maxReduce": 0}, "distributionRuleVO": {"applyCount": 10, "fetchLimit": 10}, "extProp": {"postFree": 2, "share": 2, "area": 2, "proMember": 2, "specialStore": 2, "publicPromotion": 2, "checkoutStage": 2, "displayDate": 1, "annualType": 0}, "areaIds": [], "goodsRuleVO": {"scopeType": 1, "autoUpdateGoods": 2, "goodsDepartments": [1], "goodsInclude": {"sku": [20856], "package": [], "suit": [], "ssu": []}, "goodsExclude": {}, "categoryIds": [], "goodsSuitableVOs": [{"goodsAmount": 1, "categoryTextList": "电视/小米电视/小米电视1"}, {"goodsAmount": 2, "categoryTextList": "手机/小米系列/小米9 SE"}], "goodsDiscountLevelVO": {"discountLevel": 8.5, "hasLowLevelGoods": false}}, "sendPurpose": 8, "costShare": {"1": 100}, "cost": 100.0, "departmentId": 0, "status": 0, "source": 2, "couponType": 3, "shipmentId": -1, "sendMode": 0}, "applyAttachment": [{"name": "配送时效录入模板 (5).csv", "url": "https://staging-cnbj2-fds.api.xiaomi.net/nr-inner/202503311306_d8e1c7ccbd7342ccff9c2b2a51e142c5.csv"}], "token": "208f32b9c99eed7833fc7dae9d354f64", "bizType": 0}