--  投放场景
INSERT INTO `nr_coupon_scene` (`id`, `id_generation_type`, `name`, `relation_scene_id`, `scene_code`, `scene_desc`, `send_mode`, `assign_mode`, `status`, `apply_mark`, `creator`, `create_time`, `modifier`, `update_time`, `coupon_type`, `ext_props`, `biz_platform`, `area_id`) VALUES
     ('1', '1', 'bingo营销活动', '1', '********************************', '支持bingo活动后台发券，支持灌券任务', '1', '2,1', '1', '支持灌券任务、bingo活动后台发券', 'zhujingyu1', '2022-03-17 08:57:54', 'zhujingyu1', '1678109960', '1,2,3', 'postFree,share', '0', ''),
     ('2', '1', '店长券活动', '1', '7C0938DFA6023303195221E669AFDE24', '通过店长券活动后台发放，用户可在移动pos产品站领取', '1', '1,2', '1', 'XM2106:新零售/零售中台-营销系统', 'zhujingyu1', '2022-03-31 14:50:55', 'zhujingyu1', '1677550886', '1,3', 'specialStore,share,postFree', '0', ''),
     ('3', '1', '门店下单赠券活动', '1', '0FBBAE2FD347172EFF6669E3BA1F82D4', '通过oms下单赠券活动发放，用户在门店购买指定商品获得的优惠券', '1', '1', '1', '1011：直营店/专卖店\nXM2058：授权店\nXM2057：直供店', 'zhujingyu1', '2022-03-31 14:51:28', 'wangrongyao', '1656571231', '1,2', 'postFree,share', '0', ''),
     ('4', '1', '门店换新活动', '1', '92BA19A96694E3FD53690E3FFA03981D', '用户参与门店换新活动，当旧机回收单完成后下发换新优惠券', '1', '1', '1', 'XM2095:新零售/员工激励账户', 'zhujingyu1', '2022-03-31 14:51:53', 'wangrongyao', '1656571238', '1', 'postFree,share', '0', ''),
     ('5', '1', '其他灌券活动（美团赠品使用）', '100', '7FEC45995702920286B3E8D1850CAB73', '近期仅限美团下单赠品使用，使用时提前和产品沟通确认是否可用', '1', '2', '1', 'bd发放，仅供B.D/OMS老数据迁移使用，线上数据不支持勾选', 'zhujingyu1', '2022-03-31 14:52:27', 'wangrongyao', '1659426223', '1', 'postFree,share', '0', ''),
     ('6', '1', '门店异业合作', '2', '504E45C3444F637331C8E6995F5725BE', '通过门店异业活动后台发放，用户可在移动pos产品站领取', '1', '1', '1', 'XM2106:新零售/零售中台-营销系统', 'zhujingyu1', '2022-03-31 14:52:58', 'wangrongyao', '1656571256', '1', 'postFree,share', '0', ''),
     ('7', '1', '小米金融合作', '2', 'C08278EEA7CF3A376D49E7DA1BF269CA', '用户参与小米金融消费活动获得的券', '1', '1', '1', '1009:小米金融/小米支付', 'zhujingyu1', '2022-03-31 14:53:25', 'wangrongyao', '1656571262', '1', 'postFree,share', '0', ''),
     ('8', '1', '小米社区合作', '2', '809B71DB0C5D3D69000DFF0042B5DED7', '用户参与小米社区活动获得的券，在小米社区app领取', '1', '1', '1', 'XM2097:软件与体验部_用户平台部/用户平台部_米粉APP', 'zhujingyu1', '2022-03-31 14:54:00', 'wangrongyao', '1656571269', '1', 'postFree,share', '0', ''),
     ('9', '1', '服务部合作', '2', '5AD763EFACA237DA4726F63E9BF22EF6', '用户参与服务部活动获得的券', '1', '2', '1', '用户参与服务部活动获得的券', 'zhujingyu1', '2022-03-31 14:54:51', 'wangrongyao', '1656571278', '1', 'postFree,share', '0', ''),
     ('10', '1', '大市场部合作', '2', '94CFD51E9EBB986E0F15EF6669499431', '用户参与大市场部活动活动获得的券', '1', '2', '1', '用户参与大市场部活动活动获得的券', 'zhujingyu1', '2022-03-31 14:55:24', 'wangrongyao', '1656571310', '1', 'postFree,share', '0', ''),
     ('11', '1', '门店补差', '3', '818ADB0168079DA36E5BAFA1D36267C6', '用户申请门店订单补差时，通过移动pos补差券活动后台发券。上线后，立即同步到oms补差关系表里', '1', '1,2', '1', '1011：直营店/专卖店\nXM2058：授权店', 'zhujingyu1', '2022-03-31 14:55:57', 'wangrongyao', '1658396456', '1', 'auGoods', '0', ''),
     ('12', '1', '客诉补偿', '3', '566427EBF2814E7F70FA44EDF8900257', '用户申请客诉补偿时，通过灌券系统或客服系统发券', '1', '1,2', '1', '用户申请客诉补偿时，通过灌券系统或客服系统发券', 'zhujingyu1', '2022-03-31 14:56:17', 'wangrongyao', '1656571320', '1', 'postFree,share', '0', ''),
     ('13', '1', '换新保服务', '3', '86ED65E673EC3C8E429D4A011F9A7C0B', '用户购买换新保服务，设备发生故障申请售后时，通过售后系统发券。', '1', '1,2', '1', 'XM2072:信息部/售后系统', 'zhujingyu1', '2022-03-31 14:56:42', 'wangrongyao', '1661253208', '1', 'postFree,share', '0', ''),
     ('14', '1', '新员工券', '100', '3C4675D2084760F374C666AB6AD9D224', '仅小米新员工可领，通过员工券系统发券', '1', '1,2', '1', 'XM2050:信息部/员工券系统', 'zhujingyu1', '2022-03-31 14:57:15', 'wangrongyao', '1661161301', '1', 'postFree', '0', ''),
     ('15', '1', '校园招聘券', '100', 'A306A1AD9AB1C2925F7DBEA051858C09', '用户参与校园招聘活动获得的券，通过优惠码形式发放', '2', '', '1', '用户参与校园招聘活动获得的券，通过优惠码形式发放', 'zhujingyu1', '2022-03-31 14:57:36', 'wangrongyao', '1656571296', '1', 'postFree,share', '0', ''),
     ('16', '1', '测试系统专用', '100', 'DCCC8C085E79839661D9B93E9A996EC8', '测试系统专用，通过测试平台发券', '1', '1,2', '1', '测试专用，通过测试平台发券', 'zhujingyu1', '2022-03-31 14:57:57', 'hanyuxia', '1659420410', '1,2', 'area,share,postFree', '0', ''),
     ('17', '2', '营销互动平台（海葵-即时抽奖）', '1', '7C783BEBB0D1C882E09DA5031B8EAEBF', '通过海葵系统即时抽奖活动发券，用户可参与抽奖后领取优惠券', '1', '1,2', '1', '通过营销互动平台（海葵）发券，用户可参与抽奖活动领取优惠券，关美娟申请', 'hejiapeng', '2022-05-17 12:15:00', 'zhujingyu1', '1677550768', '1,3', 'postFree,share', '0', ''),
     ('18', '2', 'FRIEND会员营销活动', '1', '04223AE7F2ABC3F5999EBB51142C3C6C', '适用于FRIEND会员频道页、产品站推广会员券使用。不支持优惠券转赠', '1', '1,2', '1', '新零售部-新零售技术部-线上产品中心-用户产品组-卢洋，<EMAIL>，用于会员频道页和产品站推广', 'wangrongyao', '2022-05-20 14:59:13', 'zhujingyu1', '1678109953', '1,3', 'postFree', '0', ''),
     ('19', '2', '电信补贴活动', '2', '6C307B2B070579B91000083E06E50D1E', '用户参与电信活动获得的优惠券', '1', '1', '1', '运营商平台', 'wangjun21', '2022-06-13 10:52:44', 'wangrongyao', '1660031042', '1', 'postFree,share', '0', ''),
     ('20', '2', '购物车营销活动', '1', '985A02FD9E1ED1ED3F26FD381CC20E36', '小米商城购物车底部营销区域展示', '1', '1', '1', '新零售部-新零售技术部-线上产品中心-业务平台产品组-寇宇琼', 'wangrongyao', '2022-06-14 16:33:00', 'wangrongyao', '1656571034', '1', 'postFree,share', '0', ''),
     ('21', '1', '测试运费券-预上线验证', '100', '5B8D452B265AC5A439DF98F37551C3D6', '测试testing', '1', '1,2', '2', '运费券 预上线验证', 'tangxiaoyan', '2022-06-24 15:51:26', 'tangxiaoyan', '1656057400', '2', '', '0', ''),
     ('22', '2', '云店社群营销活动', '1', '8D5D96C7D9E680EF29FF1F2FC87B8DF8', '通过云店销售助手领券活动发放，用户通过店员在企业微信分享获得的优惠券', '1', '1', '1', '业务研发中心-线上业务研发组-卢华，新零售技术部-产品运营中心-用户运营组-王存', 'zhujingyu1', '2022-06-24 15:51:44', 'zhujingyu1', '1678959018', '1,2,3', 'share', '0', ''),
     ('23', '2', '店员销售竞赛激励活动', '100', '562669B784DE6506726B92BBCE1D145F', '店员通过销售竞赛积分兑换的优惠券', '1', '1', '1', '中国区-新零售部-新零售技术部-线下产品中心-导购营销组-杨曼\n中国区-新零售部-新零售技术部-业务研发中心-零售通研发组-李钊，通过零售通app 发放给店员的优惠券', 'wangrongyao', '2022-08-30 15:06:28', 'wangrongyao', '1669192046', '1', 'postFree,share', '0', ''),
     ('24', '1', '剑鱼营销活动', '1', '18E36EBAD6DE398A4CEB8505F3F1AD18', '用于剑鱼搭建的领券组件去发放优惠券', '1', '1', '1', '通过剑鱼系统发放优惠券，新零售部-新零售技术部-线上产品中心-导购营销产品组-国翠微', 'wangrongyao', '2022-08-31 19:31:46', '', '0', '1', 'postFree,share', '0', ''),
     ('25', '1', '大众点评活动-优惠码', '2', '0B4F889166E20FC702E1DD7199BD42BA', '用户通过大众点评app获得优惠码', '2', '', '1', '杨向坤', 'wangrongyao', '2022-09-07 15:21:47', 'wangrongyao', '1663313593', '1', '', '0', ''),
     ('26', '1', '百度地图活动-优惠码', '2', '3479F424D1A4B4FE9CAF105FE5D60108', '用户通过百度地图app获得优惠码', '2', '', '1', '中国区-新零售部-小米之家-门店运营中心-店效提升-邢一鸣', 'wangrongyao', '2022-09-16 15:22:25', 'wangrongyao', '1663313586', '1', '', '0', ''),
     ('27', '1', '客服营销活动-优惠码', '100', '0FC210E240C14011B971CD1E77503147', '通过客服外呼意向用户，发给用户券码', '2', '', '1', '新零售部-小米网-品类运营中心-手机-李鹏程\n新零售部-小米网-服务运营中心-体验&增值&质量组-朱梦雪', 'wangrongyao', '2022-10-19 15:56:44', '', '0', '1', '', '0', ''),
     ('28', '1', '操盘激励活动', '100', '12E1DC3DAF1E3AFB8BB18626DFBE3484', '销售运营使用，通过采用实物兑付方式，对店员进行销售激励', '1', '2', '1', '中国区-销售运营一部-销售管理部-区域管理组-Jingmeng1 Wang 王京蒙', 'wangrongyao', '2022-11-23 16:26:49', 'wangrongyao', '1669192879', '1', 'postFree', '0', ''),
     ('29', '1', '测试投放场景1', '101', 'AE0DC48615111768FABA368DBF12B8A2', '预发环境测试场景', '1', '2', '1', '预发环境测试投放场景', 'v-zhangruizhi', '2023-10-24 17:12:16', '', '0', '1', '', '3', ''),
     ('30', '1', '汽车售后基础保养测试1', '102', '10A2D7989BD58B744DA924215C4FE1EF', '汽车售后基础保养测试1', '1', '1,2', '1', '汽车售后基础保养测试1', 'liwenlong3', '2024-04-01 17:37:56', '', '0', '11', '', '4', ''),
     ('31', '1', '汽车售后基础保养测试2', '102', '571892A86FA758F386834D0EE8A5063F', '汽车售后基础保养测试2', '1', '1,2', '2', '汽车售后基础保养测试2', 'liwenlong3', '2024-04-01 17:38:16', '', '0', '11', '', '4', ''),
     ('32', '1', '汽车售后服务券测试', '102', '71DDFC7EF7D954C21CCB5FDB71A03B08', '汽车售后', '1', '2,1', '2', '预发测试', 'v-langpengyi', '2024-06-06 16:21:43', '', '0', '11,12', '', '4', ''),
     ('33', '1', '线上测试汽车售后服务券', '102', '4D2B2ED6DBB77FC9696A49608A3AEB2F', '测试', '1', '1,2', '1', '测试不限次抵扣卡', 'v-langpengyi', '2024-06-13 10:29:39', '', '0', '11,12', '', '4', ''),
     ('34', '1', '购买服务包发券（作废）（勿动）', '102', '1006B46BE0F9409F9861AA18BEB8B711', '购买服务包发券，可使用时间不可编辑，该场景不能和其他场景添加到同一个券批次内', '1', '1', '2', '服务包上线，配置指定发券场景', 'zhangtianji', '2024-06-13 11:02:18', 'zhangtianji', '1718265444', '11,12', '', '4', ''),
     ('35', '2', '购买服务包发券', '102', '08B818C8A63BD1849391A0430CDDCBA8', '购买服务包发券，可使用时间不可编辑，该场景不能和其他场景添加到同一个券批次内', '1', '1', '1', '服务包上线，配置指定发券场景', 'zhangtianji', '2024-06-13 15:58:01', '', '0', '11,12', 'customUseTime', '4', ''),
     ('36', '2', '西瓜商超投放-优惠码', '2', '5FD6B4A75B5447780697308D3113F44E', '通过西瓜平台投放优惠码到商超系统', '2', '', '1', '新增异业合作业务场景', 'zhangcongyue1', '2024-06-17 11:09:47', '', '0', '1', '', '0', ''),
     ('37', '2', '车商城产品站普发券', '103', '0669C15DC47BDFF1599DE3570CD0F272', '上线并生效后，本优惠券即会自动在相应商品的产品站内展示', '1', '1', '1', '汽车部-智能座舱与APP部-汽车APP部-运营中心，韩馥霞，车商城发放优惠券使用', 'gaocaiyuan', '2024-10-28 10:49:16', '', '0', '1', 'publicPromotion', '5', ''),
     ('38', '2', '车商城灌券专用券', '103', 'E4B2EE72D1687E4B3BC263ADDB18C13F', '本优惠券不会在产品站内展示，需通过【灌券管理】功能将券发放给用户', '1', '2', '1', '汽车部-智能座舱与APP部-汽车APP部-运营中心，韩馥霞，用于车商城发券', 'gaocaiyuan', '2024-10-28 10:50:12', '', '0', '1', '', '5', ''),
     ('39', '1', '商城物流赔付', '3', '4CFDCDE8ED7A5834A6CDFB871479DFD9', '基于物流履约问题，支持用户申请物流赔付，对用户进行一定的优惠券补偿。', '1', '1', '1', '信息技术部销服研发部中台产品-王晓静-商城物流四赔需求，给用户发放优惠券', 'wangxiaojing3', '2024-11-05 09:57:08', 'wangxiaojing3', '1730771877', '1', '', '0', ''),
     ('40', '2', '礼品发放（可包邮、通过灌券发放）', '103', '1DC2FBE1B76AE8E10CBD23381492A66A', '用于给车主等用户发放礼品，需要0元订单、包邮的业务场景', '1', '2', '1', '汽车部-智能座舱与APP部-汽车APP部-运营中心 韩馥霞 创建运营活动需要申请', 'gaocaiyuan', '2024-12-22 21:38:54', 'gaocaiyuan', '1734934086', '1', 'postFree', '5', ''),
     ('41', '1', '测试OK气门芯礼品券', '103', '7D2508A9318FB398818CAB369A6998FE', '测试OK气门芯礼品券', '1', '2', '1', '测试OK气门芯礼品券 测试场景创建', 'p-wushaopeng', '2024-12-23 11:02:15', '', '0', '1', 'postFree', '5', ''),
     ('42', '1', '驾驶学院抵扣大定金额', '101', '6246B801B240CBDBEFF1B0331BFBFB2C', '驾驶学院抵扣大定金额', '1', '2', '1', '驾驶学院抵扣大定金额', 'xiachaojie', '2025-01-09 11:52:14', '', '0', '1', '', '3', ''),
     ('43', '2', 'Ultra首任车主权益-按需保养券', '102', 'A52BB47F1A97E9DE954515463DD7B483', 'Ultra购车后，发放按需保养券', '1', '1,2', '1', 'Ultra首任车主权益-发券', 'zhangtianji', '2025-02-06 17:32:05', 'wanghaotian7', '1738893388', '11,12', 'customUseTime', '4', ''),
     ('44', '2', 'Ultra首任车主权益-补胎券', '102', 'BA3D69CF78978452D4A0D84152E33713', 'Ultra购车发放补胎券', '1', '1,2', '1', 'Ultra购车发放补胎券', 'zhangtianji', '2025-02-06 17:32:59', '', '0', '11,12', '', '4', ''),
     ('45', '2', 'Ultra会员入会礼盒-0元抵扣券', '104', 'E14D88941F5672AEF3263C4BB50EBB2E', 'Ultra会员入会礼盒-0元抵扣券', '1', '1,2', '1', 'Ultra会员入会礼盒-0元抵扣券', 'zhangtianji', '2025-02-06 17:34:58', 'zhangtianji', '1740555689', '1', 'postFree', '5', ''),
     ('46', '2', '购买耗材包发券', '102', '3A82BCD62800D50CB782FFFF83542C64', '购买耗材包发券', '1', '1,2', '1', '购买耗材包发券', 'zhangtianji', '2025-02-06 17:35:50', 'zhangtianji', '1739096584', '11,12', 'customUseTime', '4', ''),
     ('47', '2', '商城省钱卡', '1', '9E3F69DD0B6801721251F2B8EAE18545', '商城省钱卡发放优惠券服务专用', '1', '1', '1', '商城新增省钱卡业务线，对应业务产品@赵晓坤', 'zhangcongyue1', '2025-04-09 15:26:16', '', '0', '1', 'customUseTime', '0', ''),
     ('48', '2', '购买竞速版套装/赛道限量版发轮胎券', '102', '3F8EC16F5DD7C62352F13F781B73EE59', '购买竞速版套装/赛道限量版发轮胎券', '1', '1,2', '1',
     '购买竞速版套装/赛道限量版发轮胎券', 'zhangtianji', '2025-04-15 11:06:30', '', '0', '11,12', 'customUseTime', '4', ''),
     ('49', '1', 'bingo营销活动-下线', '1', '00000000000000000000000000000000', '支持bingo活动后台发券，支持灌券任务', '1', '2,1', '0',
     '支持灌券任务、bingo活动后台发券', 'zhujingyu1', '2022-03-17 08:57:54', 'zhujingyu1', '1678109960', '1,2,3', 'postFree,share',
     '0', ''),
     ('77', '1', '印尼门店营销', '21', '089346F2D4E6E4F56BFA5274074651B7', '印尼门店营销', '1', '1', '1', '印尼门店营销', 'p-xueqizheng', '2025-06-13 10:11:09', '', '0', '1', '', '6', 'ID');


-- 服务场景
INSERT INTO `nr_coupon_service_scene` (`id`, `name`, `coupon_type`, `deduct_rule`, `mutual_rule`, `status`, `creator`, `create_time`, `support_order_type`) VALUES
    ('1', '基础保养', '11', '1', '1', '1', 'liwenlong3', '2024-03-05 20:23:26', '1,2'),
    ('2', '漆面修复', '11', '2', '2', '1', 'liwenlong3', '2024-03-05 20:23:52', '1,2'),
    ('3', '上门补胎', '12', '2', '1', '1', 'liwenlong3', '2024-06-06 10:50:56', '1,2'),
    ('4', '按需保养', '11', '1', '1', '1', 'wanghaotian7', '2025-02-05 11:33:35', '1,2'),
    ('5', '耗材券', '11', '2', '2', '1', 'wanghaotian7', '2025-02-05 11:33:35', '1,2,4'),
    ('6', '玻璃无忧', '11', '2', '1', '1', 'liujixiang', '2025-02-28 16:10:26', '1');


-- 车商城测试 查看券详情
INSERT INTO `nr_coupon_config` (`id`, `name`, `status`, `coupon_desc`, `budget_apply_no`, `line_num`, `budget_create_time`, `br_apply_no`, `promotion_type`, `send_scene`, `send_purpose`, `start_fetch_time`, `end_fetch_time`, `use_time_type`, `start_use_time`, `end_use_time`, `use_duration`, `use_channel`, `use_platform`, `use_store`, `bottom_type`, `bottom_price`, `bottom_count`, `promotion_value`, `max_reduce`, `scope_type`, `goods_include`, `goods_departments`, `goods_exclude`, `category_ids`, `apply_count`, `release_count`, `fetch_limit`, `ext_prop`, `area_ids`, `cost_share`, `creator`, `create_time`, `update_time`, `department_id`, `source`, `code`, `send_channel`, `coupon_type`, `time_granularity`, `shipment_id`, `auto_update_goods`, `biz_platform`, `service_type`, `fetch_limit_type`, `times_limit`, `public_promotion`) VALUES
    ('213221', '23115立减2元', '1', '不适用于服务包、虚拟产品等，适用商品以优惠券实际使用情况为准', '', '0', '', 'dd13fb6f85cb4e3db9e7375751d61f92', '4', '0669C15DC47BDFF1599DE3570CD0F272', '8', '1745424000', '1748707199', '1', '1745424000', '1748707199', '0', '6', '{}', '{"6":{"all":true}}', '2', '0', '1', '200', '0', '1', '{"sku":[],"packages":[],"ssu":[2192100011],"suit":[]}', '1', '', '', '100', '0', '100', '{"postFree":2,"share":2,"area":2,"proMember":2,"specialStore":2,"publicPromotion":1,"checkoutStage":2,"displayDate":1,"annualType":0}', '', '{"1":100}', 'lianghuan3', '1745476884', '2025-05-07 14:12:53', '0', '2', '2', 'other', '1', '2', '-1', '2', '5', '0', '1', '1', '1');
-- 车商城测试 修改优惠券 + 上下线
INSERT INTO `nr_coupon_config` (`id`, `name`, `status`, `coupon_desc`, `budget_apply_no`, `line_num`, `budget_create_time`, `br_apply_no`, `promotion_type`, `send_scene`, `send_purpose`, `start_fetch_time`, `end_fetch_time`, `use_time_type`, `start_use_time`, `end_use_time`, `use_duration`, `use_channel`, `use_platform`, `use_store`, `bottom_type`, `bottom_price`, `bottom_count`, `promotion_value`, `max_reduce`, `scope_type`, `goods_include`, `goods_departments`, `goods_exclude`, `category_ids`, `apply_count`, `release_count`, `fetch_limit`, `ext_prop`, `area_ids`, `cost_share`, `creator`, `create_time`, `update_time`, `department_id`, `source`, `code`, `send_channel`, `coupon_type`, `time_granularity`, `shipment_id`, `auto_update_goods`, `biz_platform`, `service_type`, `fetch_limit_type`, `times_limit`, `public_promotion`) VALUES
    ('216279', '车商城商品券修改前', '2', '不适用于服务包、虚拟产品等，适用商品以优惠券实际使用情况为准。', '', '0', '', 'dddd457afdab4822a965332846e5492c', '4', '0669C15DC47BDFF1599DE3570CD0F272', '8', '1746720000', '2062238400', '1', '1746720000', '2062238400', '0', '6', '{}', '{"6":{"all":true}}', '2', '0', '1', '20000', '0', '1', '{"sku":[],"packages":[],"ssu":[2221000457],"suit":[]}', '1', '', '', '4', '0', '4', '{"postFree":2,"share":2,"area":2,"proMember":2,"specialStore":2,"publicPromotion":1,"checkoutStage":2,"displayDate":1,"annualType":0}', '', '{"1":100}', 'p-jianwei', '1746782445', '2025-05-12 16:34:06', '0', '2', '2', 'other', '1', '2', '-1', '2', '5', '0', '1', '1', '1');

INSERT INTO `nr_coupon_review_rel` (`creator`, `review_group`, `create_time`, `remarks`, `add_user`) VALUES
('zhangliwei6', 'group1', '2025-06-26 12:13:24', '', 'zhangliwei6')