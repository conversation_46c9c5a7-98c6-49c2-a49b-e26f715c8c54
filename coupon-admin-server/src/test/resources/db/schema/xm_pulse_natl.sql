CREATE TABLE IF NOT EXISTS `nr_coupon_config` (
                                    `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '优惠券编号',
                                    `name` varchar(50) NOT NULL DEFAULT '' COMMENT '优惠券类型名称',
                                    `status` tinyint(1) NOT NULL DEFAULT '2' COMMENT '状态, 1:上线, 2:下线, 3:终止',
                                    `coupon_desc` varchar(255) NOT NULL DEFAULT '' COMMENT '优惠券描述信息，包含使用范围描述，金额描述等',
                                    `budget_apply_no` varchar(100) DEFAULT '' COMMENT '预算申请单号',
                                    `line_num` bigint(20) DEFAULT '0' COMMENT '预算行号',
                                    `budget_create_time` varchar(100) DEFAULT '' COMMENT '预算单创建时间',
                                    `br_apply_no` varchar(100) DEFAULT '' COMMENT 'br申请单号（唯一标识）',
                                    `promotion_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '优惠类型, 1:满减, 2:满折, 3:N元券, 4:立减',
                                    `send_scene` varchar(255) NOT NULL DEFAULT '' COMMENT '投放场景',
                                    `send_purpose` tinyint(1) NOT NULL DEFAULT '0' COMMENT '投放目的',
                                    `start_fetch_time` int(10) NOT NULL DEFAULT '0' COMMENT '开始领取时间',
                                    `end_fetch_time` int(10) NOT NULL DEFAULT '0' COMMENT '结束领取时间',
                                    `use_time_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '使用有效期类型 1 固定有效期,2 相对有效期',
                                    `start_use_time` int(10) NOT NULL DEFAULT '0' COMMENT '开始使用时间',
                                    `end_use_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '结束使用时间',
                                    `use_duration` int(10) NOT NULL DEFAULT '0' COMMENT '有效时长(单位小时)',
                                    `use_channel` varchar(50) NOT NULL DEFAULT '' COMMENT '使用渠道 1:小米商城 2:直营店 3:专卖店 4:授权店 5:堡垒店',
                                    `use_platform` varchar(255) NOT NULL DEFAULT '' COMMENT '使用平台',
                                    `use_store` mediumtext COMMENT '使用门店',
                                    `bottom_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '门槛类型 1:满元, 2:满件, 3:每满元, 4:每满件, 5:满元且满件',
                                    `bottom_price` bigint(20) NOT NULL DEFAULT '0' COMMENT '满元门槛值 单位分',
                                    `bottom_count` int(10) NOT NULL DEFAULT '0' COMMENT '满件门槛值 单位个',
                                    `promotion_value` bigint(20) NOT NULL DEFAULT '0' COMMENT '优惠值(单位分/折)',
                                    `max_reduce` int(10) NOT NULL DEFAULT '0' COMMENT '最大减免金额 单位分',
                                    `scope_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '商品范围类型 1 商品券 2 品类券',
                                    `goods_include` longtext COMMENT '券可用商品',
                                    `goods_departments` varchar(255) NOT NULL DEFAULT '' COMMENT '商品渠道',
                                    `goods_exclude` longtext COMMENT '排除商品',
                                    `category_ids` mediumtext COMMENT '类目Id列表',
                                    `apply_count` int(10) NOT NULL DEFAULT '0' COMMENT '可发放总量',
                                    `release_count` int(10) DEFAULT '0' COMMENT '释放总量',
                                    `fetch_limit` int(10) NOT NULL DEFAULT '0' COMMENT '每人领取限制',
                                    `ext_prop` varchar(255) NOT NULL DEFAULT '' COMMENT '附加属性 1可包邮 2可转增 3指定地区',
                                    `area_ids` varchar(255) NOT NULL DEFAULT '' COMMENT '可使用地区',
                                    `cost_share` text COMMENT '成本分摊',
                                    `creator` varchar(100) NOT NULL DEFAULT '' COMMENT '创建人邮箱',
                                    `create_time` int(10) NOT NULL DEFAULT '0' COMMENT '创建时间',
                                    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后一次修改时间',
                                    `department_id` int(10) NOT NULL DEFAULT '0' COMMENT '创建部门',
                                    `source` tinyint(1) NOT NULL DEFAULT '2' COMMENT '券来源 1 老券迁移 2乾坤建券',
                                    `code` tinyint(1) NOT NULL DEFAULT '2' COMMENT '老数据有码券',
                                    `send_channel` varchar(100) NOT NULL DEFAULT 'other' COMMENT '老数据发放渠道',
                                    `coupon_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '优惠券类型 1:商品券 2:运费券',
                                    `time_granularity` tinyint(1) NOT NULL DEFAULT '1' COMMENT '配置相对时间粒度，1-小时， 2 - 天',
                                    `shipment_id` int(11) NOT NULL DEFAULT '-1' COMMENT '履约部门，-1代表所有部门',
                                    `auto_update_goods` tinyint(1) NOT NULL DEFAULT '2' COMMENT '自动更新新品，1-是， 2-否',
                                    `biz_platform` tinyint(1) NOT NULL DEFAULT '0' COMMENT '所属业务 0 零售业务 3 整车销售 5 汽车售后',
                                    `service_type` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '服务场景',
                                    `fetch_limit_type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '限领类型 1限领 2不限领',
                                    `times_limit` tinyint(1) NOT NULL DEFAULT '1' COMMENT '次数限制 1-限制 2-不限制',
                                    `public_promotion` tinyint(1) NOT NULL DEFAULT '2' COMMENT '是否公开推广：1-是; 2-否',
                                    `area_id` varchar(100) NOT NULL DEFAULT 'CN' COMMENT '地区id',
                                    `workflow_id` bigint(20) DEFAULT NULL COMMENT '审批id唯一标识',
                                    PRIMARY KEY (`id`),
                                    KEY `idx_end_use_time` (`end_use_time`),
                                    KEY `idx_update_time` (`update_time`),
                                    KEY `idx_edn_fetch_time` (`end_fetch_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='优惠券类型表';

CREATE TABLE IF NOT EXISTS `nr_coupon_config_log` (
                                        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                        `config_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '优惠券id',
                                        `review_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '优惠券审核id',
                                        `operation_type` int(11) NOT NULL DEFAULT '0' COMMENT '操作类型 1 创建, 2 修改, 3 上线, 4 下线, 5 终止',
                                        `operator` varchar(128) NOT NULL DEFAULT '' COMMENT '操作人',
                                        `operation_content` mediumtext COMMENT '修改信息',
                                        `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `area_id` varchar(10) NOT NULL DEFAULT 'CN' COMMENT '地区id',
                                        PRIMARY KEY (`id`),
                                        KEY `idx_coupon` (`config_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='优惠券日志表';

CREATE TABLE IF NOT EXISTS `nr_coupon_opt_record` (
                                        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                        `opt_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '操作类型 1-创建券 2-修改券',
                                        `opt_info` longtext COMMENT '操作信息',
                                        `opt_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态 0默认 1成功 2失败',
                                        `retry_time` tinyint(1) NOT NULL DEFAULT '0' COMMENT '重试次数',
                                        `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                        PRIMARY KEY (`id`),
                                        KEY `idx_status_time` (`opt_status`,`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='操作记录表';

CREATE TABLE IF NOT EXISTS `nr_coupon_review` (
                                    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                    `config_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '优惠券id',
                                    `coupon_name` varchar(50) NOT NULL DEFAULT '' COMMENT '优惠券名称',
                                    `promotion_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '优惠类型',
                                    `promotion_value` bigint(20) NOT NULL DEFAULT '0' COMMENT '优惠值(单位分/折)',
                                    `bottom_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '门槛类型',
                                    `bottom_price` bigint(20) NOT NULL DEFAULT '0' COMMENT '使用门槛',
                                    `bottom_count` int(10) NOT NULL DEFAULT '0' COMMENT '满件门槛值 单位个',
                                    `apply_count` bigint(20) NOT NULL DEFAULT '0' COMMENT '可发放总量',
                                    `start_use_time` int(10) NOT NULL DEFAULT '0' COMMENT '开始使用时间',
                                    `end_use_time` int(10) NOT NULL DEFAULT '0' COMMENT '截止使用时间',
                                    `config_compress` blob COMMENT '券审核压缩信息',
                                    `bpm_key` varchar(128) NOT NULL DEFAULT '' COMMENT 'BPM审批流唯一标识',
                                    `bpm_reason` varchar(128) NOT NULL DEFAULT '' COMMENT 'BPM审批流返回原因',
                                    `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '审核状态：1 待审核, 2 审核中, 3 审核通过, 4 已驳回 5 已撤回 6 已过期',
                                    `department_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '部门id',
                                    `apply_mark` varchar(50) NOT NULL DEFAULT '' COMMENT '申请备注',
                                    `apply_attachment` varchar(1000) NOT NULL DEFAULT '' COMMENT '优惠券申请上传附件',
                                    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    `creator` varchar(64) NOT NULL DEFAULT '' COMMENT '创建人',
                                    `approved_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '审核时间',
                                    `approved_id` varchar(64) NOT NULL DEFAULT '' COMMENT '审核人id',
                                    `coupon_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '优惠券类型，1-商品券， 2 - 运费券',
                                    `biz_platform` tinyint(1) NOT NULL DEFAULT '0' COMMENT '所属业务 0 零售业务 3 整车销售 5 汽车售后',
                                    `area_id` varchar(10) NOT NULL DEFAULT 'CN' COMMENT '地区id',
                                    `workflow_id` bigint(20) DEFAULT NULL COMMENT '审批流id',
                                    PRIMARY KEY (`id`),
                                    UNIQUE KEY `uk_bpm_key` (`bpm_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='优惠券审核表';

CREATE TABLE IF NOT EXISTS `nr_coupon_review_rel` (
                                        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                        `creator` varchar(50) NOT NULL DEFAULT '' COMMENT '提交审批人邮箱前缀',
                                        `review_group` varchar(32) NOT NULL DEFAULT '' COMMENT '一级审批组',
                                        `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '生成时间',
                                        `remarks` varchar(255) NOT NULL COMMENT '备注',
                                        `add_user` varchar(50) NOT NULL COMMENT '创建人',
                                        PRIMARY KEY (`id`),
                                        UNIQUE KEY `uk_creator` (`creator`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='优惠券审批关系表';

CREATE TABLE IF NOT EXISTS `nr_coupon_scene` (
                                   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '场景ID',
                                   `id_generation_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '场景ID生成方式 1:自动生成, 2:手动生成',
                                   `name` varchar(32) NOT NULL DEFAULT '' COMMENT '投放场景名称',
                                   `relation_scene_id` tinyint(1) NOT NULL DEFAULT '0' COMMENT '关联场景ID 1:官方营销活动, 2:外部企业合作, 3:售后服务, 100:其他场景',
                                   `scene_code` varchar(32) NOT NULL DEFAULT '' COMMENT '投放场景编码',
                                   `scene_desc` varchar(100) NOT NULL DEFAULT '' COMMENT '场景描述',
                                   `send_mode` tinyint(1) NOT NULL DEFAULT '1' COMMENT '投放方式 1:优惠券, 2:兑换码',
                                   `assign_mode` varchar(32) NOT NULL DEFAULT '' COMMENT '发放方式(以逗号分隔) 1:外部系统发券, 2:内部系统灌券',
                                   `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '可用状态 1:上线 2:下线',
                                   `apply_mark` varchar(256) NOT NULL DEFAULT '' COMMENT '申请备注',
                                   `creator` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人',
                                   `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `modifier` varchar(30) NOT NULL DEFAULT '' COMMENT '修改人邮箱',
                                   `update_time` int(10) NOT NULL DEFAULT '0' COMMENT '修改时间',
                                   `coupon_type` varchar(32) NOT NULL DEFAULT '1' COMMENT '优惠券类型，1-商品券， 2 - 运费券',
                                   `ext_props` varchar(255) DEFAULT NULL COMMENT '特殊规则 包邮postFree,转增share,指定区域area,专店专用specialStore',
                                   `biz_platform` tinyint(1) NOT NULL DEFAULT '0' COMMENT '所属业务 0 零售业务 3 整车销售 5 汽车售后',
                                   `area_id` varchar(10) NOT NULL DEFAULT 'CN' COMMENT '地区id',
                                   PRIMARY KEY (`id`),
                                   UNIQUE KEY `uk_scene_code` (`scene_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='优惠券场景表';

CREATE TABLE IF NOT EXISTS `nr_coupon_scene_permission` (
                                              `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键自增',
                                              `scene_id` int(10) NOT NULL DEFAULT '0' COMMENT '渠道Id',
                                              `app_id` varchar(16) NOT NULL DEFAULT '0' COMMENT 'appId',
                                              `app_name` varchar(50) DEFAULT NULL,
                                              `app_contact` varchar(50) DEFAULT NULL,
                                              `channel` tinyint(1) NOT NULL DEFAULT '0' COMMENT '调用渠道  默认0 , 1商城 2门店',
                                              `creator` varchar(30) NOT NULL DEFAULT '' COMMENT '创建人',
                                              `add_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
                                              `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '1 有效 2 停用',
                                              `modifier` varchar(30) NOT NULL DEFAULT '' COMMENT '修改人邮箱',
                                              `update_time` int(10) NOT NULL DEFAULT '0' COMMENT '修改时间',
                                              `area_id` varchar(10) NOT NULL DEFAULT 'CN' COMMENT '地区id',
                                              PRIMARY KEY (`id`),
                                              UNIQUE KEY `uk_scene_appId` (`scene_id`,`app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='优惠券场景授权表';

CREATE TABLE IF NOT EXISTS `nr_coupon_service_scene` (
                                           `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '服务场景id',
                                           `name` varchar(200) NOT NULL DEFAULT '' COMMENT '服务场景名称',
                                           `coupon_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '优惠券类型 1:商品券 2:运费券 3：超级补贴券  11：服务抵扣券 12：不限次服务卡',
                                           `deduct_rule` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '抵扣规则: 1-全部抵扣; 2-部分抵扣',
                                           `mutual_rule` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '互斥规则：1-互斥; 2-叠加',
                                           `status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '状态: 1-生效; 2-失效',
                                           `creator` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人',
                                           `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                           `support_order_type` varchar(50) NOT NULL DEFAULT '' COMMENT '支持工单类型：1-到店维保；2-上门维保；3-售前维修；4-赛道整备',
                                           PRIMARY KEY (`id`),
                                           KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='服务场景表';

CREATE TABLE IF NOT EXISTS `nr_coupon_sketch` (
                                    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '草稿ID',
                                    `coupon_name` varchar(50) NOT NULL DEFAULT '' COMMENT '优惠券名称',
                                    `start_fetch_time` int(10) NOT NULL DEFAULT '0' COMMENT '开始使用时间',
                                    `end_fetch_time` int(10) NOT NULL DEFAULT '0' COMMENT '截止使用时间',
                                    `promotion_value` bigint(20) NOT NULL DEFAULT '0' COMMENT '优惠值(单位分/折)',
                                    `bottom_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '门槛类型 1 满元 2 满件 3每满元 4每满件',
                                    `bottom_price` bigint(20) NOT NULL DEFAULT '0' COMMENT '使用门槛',
                                    `apply_count` bigint(20) NOT NULL DEFAULT '0' COMMENT '可发放总量',
                                    `config_compress` text COMMENT '券草稿压缩信息',
                                    `apply_attachment` varchar(2000) NOT NULL DEFAULT '' COMMENT '申请附件地址',
                                    `delete_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否状态 1 有效 2 无效',
                                    `department_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '部门id',
                                    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    `creator` varchar(64) NOT NULL DEFAULT '' COMMENT '创建人',
                                    `coupon_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '优惠券类型，1-商品券， 2 - 运费券',
                                    `biz_platform` tinyint(1) NOT NULL DEFAULT '0' COMMENT '所属业务 0 零售业务 3 整车销售 4 汽车售后 5 车商城',
                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='优惠券草稿表';

CREATE TABLE `nr_coupon_sub_task` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `task_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '任务ID',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态,0等待,1锁定,2运行中,3失败,4成功,5错误,-1取消',
  `type` int(11) NOT NULL DEFAULT '0' COMMENT '任务类型 11 灌券 ，21 优惠码',
  `params` varchar(400) NOT NULL DEFAULT '' COMMENT '任务参数',
  `offset` bigint(20) NOT NULL DEFAULT '0' COMMENT '执行偏移量',
  `start_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '任务开始时间',
  `finish_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '任务结束时间',
  `update_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '任务更新时间',
  `retry_times` tinyint(3) NOT NULL DEFAULT '0' COMMENT '重试次数',
  `alarm_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '报警状态: 0-无需报警、1-失败报警、2-已报警',
  `version` int(10) NOT NULL DEFAULT '0' COMMENT '乐观锁版本号',
  `priority` tinyint(4) NOT NULL DEFAULT '0' COMMENT '优先级',
  `config_id` int(10) NOT NULL COMMENT '优惠券id',
  `biz_platform` tinyint(1) NOT NULL DEFAULT '0' COMMENT '优惠券所属业务平台: 0-零售业务;3-汽车业务;4-汽车售后 6-国际新零售',
  `area_id` varchar(10) NOT NULL DEFAULT 'CN' COMMENT '地区id',
  PRIMARY KEY (`id`),
  KEY `idx_taskid` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='优惠券子任务表';

CREATE TABLE IF NOT EXISTS `nr_coupon_task` (
                                  `task_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '任务ID',
                                  `task_name` varchar(50) NOT NULL DEFAULT '' COMMENT '任务名称',
                                  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态,0等待,1就绪,2运行中,3失败,4成功,5错误,-1撤销',
                                  `type` tinyint(3) NOT NULL DEFAULT '0' COMMENT '任务类型 11 灌券 ，21  优惠码',
                                  `priority` tinyint(1) NOT NULL DEFAULT '0' COMMENT '优先级',
                                  `parent_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '父任务ID',
                                  `params` varchar(400) NOT NULL DEFAULT '' COMMENT '任务参数',
                                  `source` varchar(100) NOT NULL DEFAULT '' COMMENT '任务来源',
                                  `offset` bigint(20) NOT NULL DEFAULT '0' COMMENT '执行位置',
                                  `process_rate` int(10) NOT NULL DEFAULT '0' COMMENT '任务进度',
                                  `process_desc` varchar(200) NOT NULL DEFAULT '' COMMENT '进度描述',
                                  `create_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '任务创建时间',
                                  `start_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '任务开始时间',
                                  `finish_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '任务结束时间',
                                  `result` varchar(200) NOT NULL DEFAULT '' COMMENT '执行结果',
                                  `config_id` int(10) NOT NULL DEFAULT '0' COMMENT '配置ID',
                                  `update_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '任务更新时间',
                                  `retry_times` tinyint(1) NOT NULL DEFAULT '0' COMMENT '重试次数',
                                  `alarm_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '报警状态 : 0-无需报警、2-已报警 ',
                                  `department_id` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人归属部门',
                                  `creator` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人',
                                  `version` int(10) NOT NULL DEFAULT '0' COMMENT '幂等版本号',
                                  `biz_platform` tinyint(1) NOT NULL DEFAULT '0' COMMENT '优惠券所属业务平台: 0-零售业务;3-汽车业务;4-汽车售后',
                                  `area_id` varchar(10) NOT NULL DEFAULT 'CN' COMMENT '地区id',
                                  `workflow_id` bigint(20) DEFAULT NULL COMMENT '审批流id',
                                  PRIMARY KEY (`task_id`),
                                  KEY `idx_parent` (`parent_id`),
                                  KEY `idx_status_ctime` (`status`,`create_time`),
                                  KEY `idx_biz_platform` (`biz_platform`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='优惠券任务表';

CREATE TABLE `nr_coupon_task_review` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `task_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '任务Id',
  `task_name` varchar(50) NOT NULL DEFAULT '' COMMENT '任务名称',
  `config_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '优惠券Id',
  `config_name` varchar(50) NOT NULL DEFAULT '0' COMMENT '优惠券名称',
  `area_id` varchar(10) NOT NULL DEFAULT 'CN' COMMENT '地区id',
  `plan_count` bigint(20) NOT NULL DEFAULT '0' COMMENT '可发放总量',
  `compress_info` longtext COMMENT '灌券审核压缩信息',
  `bpm_key` varchar(128) NOT NULL DEFAULT '' COMMENT 'BPM审批流唯一标识',
  `bpm_reason` varchar(128) NOT NULL DEFAULT '' COMMENT 'BPM审批流返回原因',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '审核状态：1 待审核, 2 审核中, 3 审核通过, 4 已驳回 5 已撤回 6 已过期',
  `department_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '部门id',
  `apply_attachment` varchar(1000) NOT NULL DEFAULT '' COMMENT '灌券申请上传附件',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator` varchar(64) NOT NULL DEFAULT '' COMMENT '创建人',
  `approved_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '审核时间',
  `approved_id` varchar(64) NOT NULL DEFAULT '' COMMENT '审核人id',
  `workflow_id` bigint(20) DEFAULT NULL COMMENT '审批流id',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bpm_key1` (`bpm_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='优惠券灌券审核表';

CREATE TABLE IF NOT EXISTS `nr_coupon_code` (
                                  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                  `code` varchar(255) NOT NULL DEFAULT '' COMMENT '加密的券码',
                                  `code_md` varchar(32) NOT NULL DEFAULT '' COMMENT '索引',
                                  `status` tinyint(3) NOT NULL DEFAULT '1' COMMENT '状态 1 未兑换 2 已兑换',
                                  `config_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '对应的配置ID',
                                  `task_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '对应的任务ID',
                                  `exchange_time` int(10) NOT NULL DEFAULT '0' COMMENT '兑换时间',
                                  `user_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '兑换账户ID',
                                  `coupon_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '兑换后的券ID',
                                  `end_time` int(10) NOT NULL DEFAULT '0' COMMENT '截止兑换时间',
                                  `create_time` int(10) NOT NULL DEFAULT '0' COMMENT '生成时间',
                                  PRIMARY KEY (`id`),
                                  UNIQUE KEY `idx_code` (`code`),
                                  KEY `idx_coupon_id` (`coupon_id`),
                                  KEY `idx_user_id` (`user_id`),
                                  KEY `idx_code_md` (`code_md`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='优惠券码表';

CREATE TABLE `nr_coupon_mission` (
                                     `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '任务Id',
                                     `stat` varchar(10) NOT NULL DEFAULT 'add' COMMENT '任务状态',
                                     `name` varchar(256) NOT NULL DEFAULT '' COMMENT '任务名称',
                                     `group_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '对应人群id',
                                     `send_num` int(10) NOT NULL DEFAULT '0' COMMENT '生成数量',
                                     `type_id` int(10) NOT NULL DEFAULT '0' COMMENT '优惠券类型id',
                                     `coupon_start_time` int(10) NOT NULL DEFAULT '0' COMMENT '优惠券有效开始时间',
                                     `coupon_end_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '优惠券有效结束时间',
                                     `send_time` int(10) NOT NULL DEFAULT '0' COMMENT '发券开始时间',
                                     `send_end_time` int(10) NOT NULL DEFAULT '0' COMMENT '发券结束时间',
                                     `admin_id` int(10) NOT NULL DEFAULT '0' COMMENT '添加人id',
                                     `admin_name` varchar(64) NOT NULL DEFAULT '' COMMENT '添加人',
                                     `add_time` int(10) NOT NULL DEFAULT '0' COMMENT '添加时间',
                                     `area_id` varchar(10) NOT NULL DEFAULT 'CN' COMMENT '地区id',
                                     `download` varchar(256) NOT NULL DEFAULT '' COMMENT '下载地址',
                                     `mission_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '发放任务的类型',
                                     `coupon_days` int(10) NOT NULL DEFAULT '0' COMMENT '优惠券有效天数',
                                     `max_num` int(10) NOT NULL DEFAULT '0' COMMENT '最大发放数量',
                                     `now_num` int(10) NOT NULL DEFAULT '0' COMMENT '已经发送数量',
                                     `approved_id` int(10) NOT NULL DEFAULT '0' COMMENT '幂等所用审核任务ID',
                                     `last_update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
                                     PRIMARY KEY (`id`),
                                     KEY `ix_add_time` (`add_time`),
                                     KEY `ix_stat` (`stat`),
                                     KEY `ix_area_id` (`area_id`),
                                     KEY `idx_last_update_time` (`last_update_time`),
                                     KEY `idx_coupon_end_time` (`coupon_end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='发券任务';

CREATE TABLE `nr_coupon_workflow` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `workflow_id` bigint(20) NOT NULL COMMENT '审批id唯一标识',
  `workflow_translate_key` varchar(100) NOT NULL DEFAULT '' COMMENT '审批流名字翻译key',
  `bpm_key` varchar(100) NOT NULL DEFAULT '' COMMENT 'bpm key',
  `area_id` varchar(10) NOT NULL DEFAULT 'CN' COMMENT '地区id',
  `channels` varchar(100) NOT NULL DEFAULT '' COMMENT '渠道，多个以,分割',
  `promotion_types` varchar(100) NOT NULL DEFAULT '' COMMENT '促销类型，多个以,分割',
  `extend` text COMMENT '扩展数据',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_workflow_id` (`workflow_id`),
  KEY `idx_area_id` (`area_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='审核流信息表';