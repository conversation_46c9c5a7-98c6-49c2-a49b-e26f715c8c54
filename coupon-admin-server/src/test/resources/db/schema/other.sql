CREATE TABLE `mall_activity_market_coupon` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `coupon_id` int(10) unsigned NOT NULL COMMENT 'shopapi优惠券ID',
  `company_id` int(10) unsigned NOT NULL DEFAULT '10000' COMMENT '企业id',
  `task_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '最新审核任务ID',
  `coupon_type` int(5) unsigned NOT NULL DEFAULT '0' COMMENT '优惠券类型1现金2折扣3抵扣',
  `coupon_name` varchar(64) NOT NULL DEFAULT '' COMMENT '优惠券名称',
  `coupon_desc` text NOT NULL COMMENT '优惠券可用范围描述',
  `start_date` datetime NOT NULL COMMENT '优惠券开始时间',
  `end_date` datetime NOT NULL COMMENT '优惠券结束时间',
  `send_limit` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建数量',
  `left_num` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '发放剩余量',
  `is_code` int(2) unsigned NOT NULL DEFAULT '0' COMMENT '发放方式，1接口+手动发放，2券码发放',
  `policy` text NOT NULL COMMENT '策略信息json',
  `deduct_type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '抵扣类型:0=>0元抵扣，1=>0.01元抵扣',
  `select_type` enum('ignore','deduction','add') NOT NULL DEFAULT 'add' COMMENT '选择货品/套装的方式，添加/排除或者抵扣',
  `all_goods` enum('1','0') NOT NULL DEFAULT '0' COMMENT '是否全部商品',
  `goods_include` longtext NOT NULL COMMENT '货品信息json',
  `goods_include_ids` longtext COMMENT '包含/排除商品id,多个用逗号分隔',
  `class_ids` text NOT NULL COMMENT '所选品类ID',
  `class_update_time` int(10) NOT NULL DEFAULT '0' COMMENT '品类更新时间',
  `client` text NOT NULL COMMENT '可用范围,多个逗号隔开',
  `postfree` int(2) unsigned NOT NULL DEFAULT '2' COMMENT '免邮费',
  `is_share` int(2) unsigned NOT NULL DEFAULT '2' COMMENT '可分享',
  `check_price` int(2) unsigned NOT NULL DEFAULT '2' COMMENT '直降商品和满减商品不参加',
  `check_package` int(2) unsigned NOT NULL DEFAULT '2' COMMENT '套装部分商品可用券',
  `send_channel` varchar(64) NOT NULL DEFAULT 'other' COMMENT '发放渠道: other-其他，store_manager-店长券',
  `audit_status` varchar(64) NOT NULL DEFAULT 'approved' COMMENT '审核状态(approved,cancel）',
  `is_finance` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否包含财务分摊信息',
  `finance_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '财务分摊类型：0->不包含分摊, 1->按金额分摊, 2->按百分比分摊',
  `finance_allocate` text NOT NULL COMMENT '财务成本分摊',
  `update_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '品类更新状态，0->最新，1->需更新',
  `add_time` datetime NOT NULL COMMENT '创建时间',
  `add_user` varchar(32) NOT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `update_user` varchar(32) NOT NULL COMMENT '更新人',
  `class_ids_version` text NOT NULL COMMENT '所选品类对应版本号(mall_real_class_sku.id)',
  `use_channel` varchar(64) NOT NULL DEFAULT '' COMMENT '使用渠道(关联client_id): mi_shop：小米商城渠道, mi_home：小米之家渠道, mi_authorized：小米授权店渠道',
  `create_channel` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0-bd创建, 1-乾坤创建',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_coupon_id` (`coupon_id`),
  KEY `idx_a_e` (`audit_status`,`end_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='营销优惠券表';

CREATE TABLE `mall_market_coupon_apply` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `mission_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '心动发券任务ID',
  `coupon_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '优惠券ID',
  `task_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '最新审核任务ID',
  `apply_department` varchar(64) NOT NULL DEFAULT '' COMMENT '格式为申请部门ID：小组名',
  `apply_desc` text NOT NULL COMMENT '用途描述',
  `apply_desc_img` varchar(255) NOT NULL COMMENT '申请说明图片',
  `attachment` text NOT NULL COMMENT '附件信息json',
  `mission_type` tinyint(2) unsigned NOT NULL DEFAULT '0' COMMENT '发放类别',
  `send_num` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '发放数量',
  `coupon_info` text NOT NULL COMMENT '优惠券有效期、名单等',
  `audit_status` varchar(32) NOT NULL DEFAULT 'approved' COMMENT '申请审核状态',
  `add_time` datetime NOT NULL COMMENT '创建时间',
  `add_user` varchar(32) NOT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `update_user` varchar(32) NOT NULL COMMENT '更新人',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='营销优惠券发放记录';

CREATE TABLE `tbl_coupon_class` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `site_id` int(10) unsigned NOT NULL DEFAULT '1' COMMENT '站点ID',
  `class_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '券类型id',
  `only_check` tinyint(2) NOT NULL COMMENT '1-参数校验 2-参数校验且落库',
  `name` varchar(64) NOT NULL DEFAULT '' COMMENT '券名称',
  `range_desc` varchar(512) NOT NULL DEFAULT '' COMMENT '可用范围',
  `coupon_type` tinyint(2) NOT NULL COMMENT '券类型 1-现金券 2-折扣券 3-抵扣券',
  `send_limit` int(11) NOT NULL COMMENT '最大可发送数量',
  `is_code` tinyint(2) NOT NULL COMMENT '明码券 无码券(优惠券)',
  `start_date` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '全局开始时间',
  `end_date` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '全局结束时间',
  `check_package` int(11) NOT NULL DEFAULT '0' COMMENT '套装部分可用券 (是: 1, 否: 0)',
  `check_price` int(11) NOT NULL DEFAULT '0' COMMENT '直降商品不可参加 (是: 1, 否: 0)',
  `client` varchar(512) NOT NULL DEFAULT '' COMMENT '应用范围',
  `goods_include` mediumtext NOT NULL COMMENT '可参数商品列表 (level为两级: goods/package',
  `policy` text NOT NULL COMMENT '优惠策略',
  `status` tinyint(2) unsigned NOT NULL COMMENT '0-已创建 1-可用 2-已过期 3-驳回 4-已申请 5-已取消',
  `select_type` tinyint(2) unsigned NOT NULL DEFAULT '1' COMMENT '1:包含全部商品 2：包含部分商品 3排斥部分商品',
  `created_at` int(11) unsigned NOT NULL DEFAULT '0',
  `created_by` bigint(20) unsigned NOT NULL DEFAULT '0',
  `updated_at` int(11) unsigned NOT NULL DEFAULT '0',
  `updated_by` bigint(20) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_tbl_coupon_class_on_class_id` (`class_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `tbl_coupon_info` (
  `class_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '优惠券类型id',
  `mission_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '发券任务ID',
  `site_id` int(10) unsigned NOT NULL DEFAULT '1' COMMENT '站点id',
  `name` varchar(64) NOT NULL DEFAULT '' COMMENT '补偿券名称',
  `expire_day` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '有效天数',
  `remark` varchar(64) NOT NULL DEFAULT '' COMMENT '备注',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 0: 已失效 1: 生效中',
  `reduce_money` decimal(9,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '优惠券优惠扣减金额',
  `updated_by` bigint(20) unsigned NOT NULL DEFAULT '0',
  `updated_at` int(11) unsigned NOT NULL DEFAULT '0',
  `created_by` bigint(20) unsigned NOT NULL DEFAULT '0',
  `created_at` int(11) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`class_id`),
  KEY `idx_site_money` (`site_id`,`reduce_money`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='优惠券信息表';