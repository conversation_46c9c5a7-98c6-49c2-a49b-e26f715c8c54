package com.xiaomi.nr.coupon.admin.deprecated.infrastructure.repository;

import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.PointConfigRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * <AUTHOR>
 * @date 2023/12/15 15:05
 */
@Slf4j
@ActiveProfiles("dev")
@SpringBootTest(classes = CouponAdminBootstrap.class)
public class PointConfigRepositoryTest {
    @Autowired
    private PointConfigRepository pointConfigRepository;

/*    @Test
    public void updateTest() throws BizError {
        log.info("================================================");

        CarPointsBatchConfigPo configPo = new CarPointsBatchConfigPo();

        pointConfigRepository.update(configPo);

        log.info("================================================");
    }*/
}