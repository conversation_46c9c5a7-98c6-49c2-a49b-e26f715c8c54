package com.xiaomi.nr.coupon.admin.deprecated.application.dubbo.pointadmin;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.BudgetInfoDto;
import com.xiaomi.nr.coupon.admin.api.dto.PageInfo;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.scene.request.OperateSceneRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.scene.response.SceneTypeVO;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.*;
import com.xiaomi.nr.coupon.admin.api.service.pointadmin.DubboPointDataService;
import com.xiaomi.nr.coupon.admin.api.service.pointadmin.DubboPointLogService;
import com.xiaomi.nr.coupon.admin.api.service.pointadmin.DubboPointSceneService;
import com.xiaomi.nr.coupon.admin.api.service.pointadmin.DubboUserPointsService;
import com.xiaomi.nr.coupon.admin.common.application.dubbo.DubboPointServiceImpl;
import com.xiaomi.nr.coupon.admin.common.application.dubbo.pointadmin.DubboPointAdminServiceImpl;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.enums.pointadmin.PointBatchPeriodStatusEnum;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.rpc.RpcContext;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/7 10:05
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponAdminBootstrap.class)
@ActiveProfiles("dev")
public class DubboPointAdminServiceImplTest {
    @Autowired
    private DubboPointAdminServiceImpl dubboPointAdminServiceImpl;

    @Autowired
    private DubboPointServiceImpl dubboPointServiceImpl;

    @Autowired
    private DubboPointSceneService dubboPointSceneService;

    @Autowired
    private DubboPointLogService dubboPointLogService;


    @Autowired
    private DubboPointDataService dubboPointDataService;


    @Autowired
    private DubboUserPointsService dubboUserPointsService;

    @Test
    public void savePointBatchTest() {
        log.info("=======================================================");

        // RpcContext.getContext().getAttachments().put("$upc_account", "liwenlong3");

        SavePointBatchRequest request = new SavePointBatchRequest();
        request.setBatchName("简威3333");
        request.setSceneCode("58E3C9037BD28470D5344188A48A700E");
        request.setBudgetId(1L);
        request.setApplyCount(100L);
        request.setWarningRatio(20);
        request.setStartTime(1750262400L);
        request.setEndTime(1753113599L);
        request.setUseTimeType(1);
        request.setOperator("liwenlong3");

        Result<SavePointBatchResponse> res = dubboPointAdminServiceImpl.savePointBatch(request);

        log.info("savePointBatchTest res = {}", res);

        log.info("=======================================================");
    }

    @Test
    public void updatePointBatchTest() {
        log.info("=======================================================");

        SavePointBatchRequest request = new SavePointBatchRequest();
        request.setBatchId(23L);
        request.setBatchName("积分发放周期验证090101修改02");
        request.setSceneCode("2C7A40AA905780CDCED1A440399E1996");
        request.setBudgetId(1002L);
        request.setApplyCount(600L);
        request.setWarningRatio(20);
        request.setStartTime(1706785800L);
        request.setEndTime(1707141599L);
        request.setUseTimeType(1);
        request.setOperator("liwenlong3");

        dubboPointAdminServiceImpl.updatePointBatch(request);

        log.info("=======================================================");
    }

    @Test
    public void addSsuBlacklistTest() {
        log.info("=======================================================");

        RpcContext.getContext().setAttachment("$upc_account","liwenlong3");

        AddSsuBlacklistRequest request = new AddSsuBlacklistRequest();
        List<Long> ssuIdList = Arrays.asList(2230000295L);
        request.setSsuIdList(ssuIdList);

        Result<Void> res = dubboPointAdminServiceImpl.addSsuBlacklist(request);

        log.info("addSsuBlacklistTest res = {}", res);

        log.info("=======================================================");
    }

    @Test
    public void deleteSsuBlacklistTest() {
        log.info("=======================================================");

        DeleteSsuBlacklistRequest request = new DeleteSsuBlacklistRequest();
        request.setSsuId(600000451L);

        Result<Void> res = dubboPointAdminServiceImpl.deleteSsuBlacklist(request);

        log.info("=======================================================");
    }

    @Test
    public void ssuBlacklistTest() {
        log.info("=======================================================");

        SsuBlacklistRequest request = new SsuBlacklistRequest();
        Result<BasePageResponse<SsuBlacklistDto>> res = dubboPointAdminServiceImpl.ssuBlacklist(request);
        log.info("ssuBlacklistTest res = {}", res);

        log.info("=======================================================");
    }

    @Test
    public void testPointBatchList() {

        PointBatchListRequest req = new PointBatchListRequest();
        req.setKeyWord("进行中");
        req.setPeriodStatus(PointBatchPeriodStatusEnum.IN_PROGRESS.getCode());

        Result<List<PointBatchConfigData>> result = dubboPointAdminServiceImpl.pointBatchList(req);
        log.info("result is {}", GsonUtil.toJson(result));

        req = new PointBatchListRequest();
        req.setKeyWord("7");
        req.setPeriodStatus(PointBatchPeriodStatusEnum.IN_PROGRESS.getCode());

        result = dubboPointAdminServiceImpl.pointBatchList(req);
        log.info("result is {}", GsonUtil.toJson(result));

    }


    @Test
    public void testGetPointBatchList() {

        GetPointBatchListRequest req = new GetPointBatchListRequest();
        req.setWithBalanceCnt(true);
        req.setOnlyAvailable(true);

        // 添加 sceneCode 字段
//        req.setSceneCode("58E3C9037BD28470D5344188A48A700E");
        // 添加 sceneCodeList 字段
        List<String> sceneCodeList = new ArrayList<>();
        sceneCodeList.add("585BBF0290555CEEB0852357EC495ECD");
        sceneCodeList.add("9ADBF831E250D7FEE454699897D8EFA0");
        sceneCodeList.add("1BB001591FFD42EA9B9C9E58FE588717");
        sceneCodeList.add("DCA941976EFDEB78CCAC86997C7776EB");
        sceneCodeList.add("58E3C9037BD28470D5344188A48A700E");

        req.setSceneCodeList(sceneCodeList);

        Result<GetPointBatchListResponse> result = dubboPointServiceImpl.getPointBatchList(req);
        log.info("result is {}", GsonUtil.toJson(result));

    }

    @Test
    public void testChangePointBatchStatus() {

        ChangePointBatchStatusRequest req = new ChangePointBatchStatusRequest();
        req.setBatchId(7L);
        req.setOptType(2);
        req.setOperator("caoxiaopeng1");

        dubboPointAdminServiceImpl.changePointBatchStatus(req);
        req.setOptType(1);
        dubboPointAdminServiceImpl.changePointBatchStatus(req);

    }

    @Test
    public void renewPointBatchConfigTest() {
        log.info("=======================================================");

        dubboPointAdminServiceImpl.renewPointBatchConfig();

        log.info("=======================================================");
    }

    @Test
    public void pointBatchDetailTest() {
        log.info("=======================================================");

        PointBatchDetailRequest request = new PointBatchDetailRequest();
        request.setBatchId(11L);

        Result<PointBatchDetailDto> res = dubboPointAdminServiceImpl.pointBatchDetail(request);

        log.info("pointBatchDetailTest res = {}", res);

        log.info("=======================================================");
    }

    @Test
    public void pointSceneCreateTest() {
        log.info("=======================================================");

        RpcContext.getContext().setAttachment("$upc_account","hjp");

        PointSceneRequest request = new PointSceneRequest();

        PointSceneDto pointSceneDTO = new PointSceneDto();
        pointSceneDTO.setName("测试积分场景");
        pointSceneDTO.setSceneDesc("测试场景使用");
        pointSceneDTO.setStatus(1);
        pointSceneDTO.setParentId(1);
        request.setPointSceneDTO(pointSceneDTO);
        request.setType(0);

        Result<Boolean> res = dubboPointSceneService.createOrUpdateScene(request);

        log.info("pointSceneCreateTest res = {}", res);

        request.setSceneId(3L);
        request.getPointSceneDTO().setSceneDesc("测试场景使用编辑");
        request.getPointSceneDTO().setId(3L);
        request.setType(1);
        Result<Boolean> res2 = dubboPointSceneService.createOrUpdateScene(request);

        log.info("pointSceneCreateTest res = {}", res2);
        log.info("=======================================================");
    }

    @Test
    public void pointSceneOpenCreateTest() {
        log.info("=======================================================");

        RpcContext.getContext().setAttachment("$upc_account","jzm");

        PointSceneOpenRequest request = new PointSceneOpenRequest();

        request.setType(1);
        //一级场景
        request.setCreateParentScene(false);
        request.setParentSceneName("j_test2");

        request.setSceneName("二级场景CC");
        //request.setStatus(1L);

        request.setAppName("111测试用-111");
        request.setAssignRecordDesc("发放记录文案-编辑");
        request.setExpireRecordDesc("过期记录文案-编辑");
        request.setDelayRecordDesc("延迟记录文案-编辑");
        request.setSceneCode("45E5B6A6E9311C1A08E6443ABE1675C1");
        request.setParentId(31);

        // 已有/新建 一级场景
        /*request.setType(0);
        request.setCreateParentScene(false);
        request.setParentSceneName("333");*/
        // 新建不传一级场景id
        //request.setParentId(2);
        Result<Boolean> res = dubboPointSceneService.createOrUpdateSceneOpen(request);
        log.info("pointSceneOpenCreateTest res = {}", res);

        // 编辑二级场景 设置二级场景id
//        request.setType(1);
//        request.setCreateParentScene(false);
//        request.setSceneId(3L);
//        request.setParentId(1);
//        request.setSceneDesc("测试场景使用编辑");
//
//        Result<Boolean> res2 = dubboPointSceneService.createOrUpdateSceneOpen(request);
//        log.info("pointSceneOpenCreateTest res2 = {}", res2);

        log.info("=======================================================");
    }

    @Test
    public void operateSceneTest(){
        RpcContext.getContext().setAttachment("$upc_account","hjp");
        OperateSceneRequest request = new OperateSceneRequest();
        request.setSceneId(3L);
        request.setType(1);
        Result<Boolean> res = dubboPointSceneService.operateScene(request);

        log.info("pointSceneCreateTest res = {}", res);
        log.info("=======================================================");
    }

    @Test
    public void searchSceneWithCatTest(){

        SceneWithCatRequest request = new SceneWithCatRequest();
        request.setOnlyValid(false);
        Result<SceneWithCatResponse> res = dubboPointSceneService.searchSceneWithCat(request);

        log.info("pointSceneCreateTest res = {}", res);
        log.info("=======================================================");
    }


    @Test
    public void queryLogListTest() {
        log.info("=======================================================");

        PointLogRequest request = new PointLogRequest();
        request.setBatchId(16L);

        Result<BasePageResponse<PointBatchLogDto>> res = dubboPointLogService.queryLogList(request);

        log.info("=======================================================");
    }

    @Test
    public void searchScenePageTest(){

        SceneWithCatPageRequest request = new SceneWithCatPageRequest();
        request.setOnlyValid(true);
        request.setPageNo(2);
//        request.setPageSize(15);
//        request.setKeyWord("测试");

        Result<BasePageResponse<SceneTypeVO>> res = dubboPointSceneService.searchScenePage(request);


        log.info("pointSceneCreateTest res = {}", res);
        log.info("=======================================================");
    }


    @Test
    public void pointBatchStatTest() {
        log.info("=======================================================");

        PointsBatchDataStatRequest request = new PointsBatchDataStatRequest();
        request.setBatchId(18L);

        Result<PointsBatchDataStatResponse> res = dubboPointDataService.pointBatchStat(request);

        log.info("=======================================================");
    }


    @Test
    public void cancelUserPointTest() {
        log.info("=======================================================");

        UserPointCancelRequest request = new UserPointCancelRequest();
        request.setMid(3150067909L);
        request.setPointId(1010428462L);

        Result<Void> res = dubboUserPointsService.cancelUserPoint(request);

        log.info("=======================================================");
    }

    @Test
    public void queryBudgetList() {
        log.info("======================================================");
        PointQueryBudgetListRequest request = new PointQueryBudgetListRequest();
        request.setSendScene("585BBF0290555CEEB0852357EC495ECD");
        Result<PageInfo<BudgetInfoDto>> result = dubboPointAdminServiceImpl.queryBudgetList(request);
        log.info("queryBudgetList result = {}", GsonUtil.toJson(result));
        log.info("======================================================");
    }
}