package com.xiaomi.nr.coupon.admin.unittest.api.service.couponadmin;

import cn.hutool.json.JSONObject;
import com.xiaomi.mit.unittest.field.autoconfig.SetupBean;
import com.xiaomi.newretail.bpm.api.model.callback.OnStatusChangedRequest;
import com.xiaomi.newretail.bpm.api.model.callback.OnStatusChangedResponse;
import com.xiaomi.newretail.bpm.api.model.callback.ProcessAction;
import com.xiaomi.newretail.bpm.api.model.dto.ProcessCreateDTO;
import com.xiaomi.newretail.common.tools.utils.GsonUtil;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponConfigVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.UseChannelVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.request.CouponCreateReviewRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.response.CouponReviewResponse;
import com.xiaomi.nr.coupon.admin.api.enums.TranslationEnum;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponBpmCallBackService;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponReviewService;
import com.xiaomi.nr.coupon.admin.common.domain.coupon.couponreview.CouponReviewService;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.UseChannelsEnum;
import com.xiaomi.nr.coupon.admin.enums.icrm.IcrmEnum;
import com.xiaomi.nr.coupon.admin.global.domain.coupon.couponconfig.bpm.GlobalBpmProxy;
import com.xiaomi.nr.coupon.admin.global.domain.icrm.ICRMProxy;
import com.xiaomi.nr.coupon.admin.unittest.BaseUnitTest;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.rpc.RpcContext;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.BDDMockito.willReturn;
import static org.mockito.Mockito.when;

/**
 * @author: zhangliwei6
 * @date: 2025/9/3 15:21
 * @description:
 */
@Slf4j
@SetupBean("bean/couponreview/conditionDiscountGlobal.json")
public class ConditionDiscountGlobalTest extends BaseUnitTest {

    @Autowired
    private DubboCouponReviewService dubboCouponReviewService;

    @Autowired
    private DubboCouponBpmCallBackService dubboCouponBpmCallBackService;

    @Autowired
    private CouponReviewService couponReviewService;

    private static CouponCreateReviewRequest conditionDiscountGlobal;

    private CouponCreateReviewRequest request;

    private CouponConfigVO couponConfigVO;

    private CouponConfigVO originCouponConfigVO;

    private static String bpmKey;

    private static Long id;

    @BeforeEach
    public void beforeEach() {
        RpcContext.getContext().getAttachments().put("$upc_account", "zhangliwei6");
        request = GsonUtil.parseObj(GsonUtil.toJsonStr(conditionDiscountGlobal), CouponCreateReviewRequest.class);
        couponConfigVO = request.getCouponConfigVO();
        originCouponConfigVO = conditionDiscountGlobal.getCouponConfigVO();
    }

    @Test
    public void test_createCouponReview_paramError() {
        couponConfigVO.setWorkflowId(null);
        Result<CouponReviewResponse> result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals(TranslationEnum.COUPON_CONFIG_CREATE_CHECK_WORKFLOW.getTranslateContent(),
                result.getMessage());
        couponConfigVO.setWorkflowId(originCouponConfigVO.getWorkflowId());

        couponConfigVO.getPromotionRuleVO().setPromotionValue(-1L);
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals(TranslationEnum.COUPON_CONFIG_CREATE_CHECK_DISCOUNT.getTranslateContent(),
                result.getMessage());

        couponConfigVO.getPromotionRuleVO().setPromotionValue(1L);
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals(TranslationEnum.COUPON_TASK_COMMIT_ERROR_ARGS.getTranslateContent(),
                result.getMessage());
        couponConfigVO.getPromotionRuleVO().setPromotionValue(originCouponConfigVO.getPromotionRuleVO().getPromotionValue());

        couponConfigVO.getUseChannel().clear();
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals(TranslationEnum.COUPON_CONFIG_CREATE_CHECK_CHANNEL.getTranslateContent(),
                result.getMessage());
        UseChannelVO useChannelVO = new UseChannelVO();
        useChannelVO.setAll(false);
        couponConfigVO.getUseChannel().put(UseChannelsEnum.XIAOMI_SHOP.getValue(), useChannelVO);

        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals(TranslationEnum.COUPON_CONFIG_INSERT_ERROR_MATCH.getTranslateContent(),
                result.getMessage());
        couponConfigVO.getUseChannel().clear();

        couponConfigVO.getUseChannel().put(UseChannelsEnum.DIRECTSALE_STORE.getValue(), useChannelVO);
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals(TranslationEnum.COUPON_CONFIG_CREATE_CHECK_STORE.getTranslateContent(),
                result.getMessage());
        useChannelVO.setAll(true);
    }

    @SpyBean
    private ICRMProxy icrmProxy;

    @MockBean
    private GlobalBpmProxy globalBpmProxy;

    @Test
    public void test_createCouponReview_check() throws BizError {
        request.setCheckOrSubmit(IcrmEnum.PREVIEW_CHECK.getCode());

        // mock icrm
        JSONObject mockResp = new JSONObject();
        mockResp.set("code", 200);
        when(icrmProxy.send(anyString())).thenReturn(mockResp);

        Result<CouponReviewResponse> result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals(GeneralCodes.OK.getCode(), result.getCode());
    }

    @Test
    public void test_createCouponReview_save() throws Exception {
        // mock icrm
        JSONObject mockResp = new JSONObject();
        mockResp.set("code", 200);
        mockResp.set("message", "200");
        willReturn(mockResp).given(icrmProxy).send(anyString());

        // mock bpm
        bpmKey = UUID.randomUUID().toString().toLowerCase();
        when(globalBpmProxy.submitReview(any(ProcessCreateDTO.class))).thenReturn(bpmKey);

        Result<CouponReviewResponse> result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals(GeneralCodes.OK.getCode(), result.getCode());

        OnStatusChangedRequest callbackRequest = new OnStatusChangedRequest();
        callbackRequest.setProcessInstanceId(bpmKey);
        callbackRequest.setAction(ProcessAction.Accept);
        callbackRequest.setFinished(Boolean.TRUE);
        callbackRequest.setOperator(RpcContext.getContext().getAttachment("$upc_account"));

        // 审核完
        Result<OnStatusChangedResponse>  callbackResult = dubboCouponBpmCallBackService.onStatusChanged(callbackRequest);
        Assertions.assertEquals(GeneralCodes.OK.getCode(), callbackResult.getCode());
    }
}
