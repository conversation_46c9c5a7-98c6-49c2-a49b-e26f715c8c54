package com.xiaomi.nr.coupon.admin.deprecated.coupon;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponConfigListVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.GetCouponByIdRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.GetCouponByIdResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.GetCouponByTypeIdRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.GetCouponByTypeIdResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.GetCouponCodeRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.GetCouponCodeResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.GoodConfigIdsRequest;
import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.*;
import com.xiaomi.nr.coupon.admin.api.dto.mission.CouponMissionListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.mission.MissionDto;
import com.xiaomi.nr.coupon.admin.api.dto.mission.PageResponse;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.api.enums.BizSubTypeEnum;
import com.xiaomi.nr.coupon.admin.api.service.DubboCouponService;
import com.xiaomi.nr.coupon.admin.api.service.DubboMissionService;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponToolService;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.EventContext;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.UseChannelsEnum;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.BdCouponSyncRepository;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.po.CouponConfigPoConvert;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.po.GoodsItemPo;
import com.xiaomi.nr.coupon.admin.infrastructure.rpc.goods.GmsProxyService;
import com.xiaomi.nr.coupon.admin.infrastructure.rpc.WatermelonCouponServiceProxy;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.global.dev.base.HeraContextKeyValueHolder;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponAdminBootstrap.class)
@ActiveProfiles("staging")
public class DubboCouponServiceTest {

    @Autowired
    private DubboCouponToolService dubboCouponToolService;

    @Autowired
    private DubboMissionService dubboMissionService;

    @Autowired
    private BdCouponSyncRepository bdCouponSyncRepository;

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    @Autowired
    private GmsProxyService gmsProxyService;

    @Autowired
    private CouponConfigPoConvert couponConfigPoConvert;

    @Autowired
    private DubboCouponService dubboCouponService;

    @Autowired
    private WatermelonCouponServiceProxy watermelonCouponServiceProxy;



    @Test
    public void  loadAllCouponConfigTest(){
        GenAllConfigRedisCacheRequest request = new GenAllConfigRedisCacheRequest();
        request.setItemIds(Lists.newArrayList(30101L));
        dubboCouponToolService.loadAllCouponConfig(request);
    }


    @Test
    public void loadAllGoodsCouponTest(){
        dubboCouponToolService.loadAllGoodsCoupon(true);
    }

    @Test
    public void moveOldCouponByIdsTest(){
        GenAllConfigRedisCacheRequest request = new GenAllConfigRedisCacheRequest();
        request.setSource("test");
        request.setItemIds(Lists.newArrayList(12868L));
        dubboCouponToolService.moveOldCouponByIds(request);
    }

    @Test
    public void modifyOldCouponByIdsTest(){
        updateCoupondataRequest request = new updateCoupondataRequest();
        request.setConfigIds(Lists.newArrayList(12873L));
        request.setSendScene("504E45C3444F637331C8E6995F5725BE");
        request.setUseTimeType(1);
        request.setUseDuration(48);
        request.setStartTime(1651852800L);
        request.setEndTime(1651935600L);
        //dubboCouponService.updateOldCouponByIds(request);
    }

    @Test
    public void laodAllMissionTest(){
        CouponMissionListRequest request = new CouponMissionListRequest();

        request.setSendChannel("diff_business");
        request.setPageSize(50);
        request.setLastMissionId(0L);
        request.setMissionId(0L);
        request.setAppId("XM2106");
        Result<PageResponse<MissionDto>> responseResult= dubboMissionService.getCouponMissionList(request);

        System.out.println(responseResult);
    }


    @Test
    public void loadMallCoupon() throws Exception {

        EventContext eventContext = new EventContext();

        CouponConfigPO couponConfigPO = couponConfigRepository.searchCouponById(30098L);

        GoodsItemPo goodsItemPo = couponConfigPoConvert.serializeGoodsItemPo(couponConfigPO);

        if(CollectionUtils.isNotEmpty(goodsItemPo.getSkuList())) {
            eventContext.setSkuInfoDtos(gmsProxyService.queryListBySkuIds(goodsItemPo.getSkuList(), false,false, false, Lists.newArrayList(BizSubTypeEnum.ORDINARY_3C.getCode())));
        }

        if(CollectionUtils.isNotEmpty(goodsItemPo.getPackageList())){
            eventContext.setBatchedInfoDtos(gmsProxyService.queryListByPackageIds(goodsItemPo.getPackageList(), false));
        }

        eventContext.setGoodsItemPo(goodsItemPo);

        // 保证幂等
        if(bdCouponSyncRepository.checkMallMarketCoupon(couponConfigPO.getId())){
            return;
        }

        bdCouponSyncRepository.updateMallMarketCoupon(couponConfigPO, eventContext);
    }


    @Test
    public void test() {
        String urlencodePassword = new String(Base64.getUrlEncoder().encode(String.format("%s:%s", "NRCenter", "OJWpDKrs0vppm3np").getBytes()));
        String basicAuth = String.format("Basic %s", urlencodePassword);
        //Header[] headers = new Header[] {new BasicHeader("Authorization", basicAuth)};
        //System.out.println(headers);
    }

    @Test
    public void testGetConfigListByScene() {
        try {
            BatchGetConfigInfoRequest request = new BatchGetConfigInfoRequest();
            request.setSceneCode("7C0938DFA6023303195221E669AFDE24");
            request.setOnlyAvailable(true);
            Result<BatchGetConfigInfoReponse> res = dubboCouponService.batchGetCouponConfigInfo(request);
            List<CouponConfigInfoDTO> couponConfigs = res.getData().getCouponConfigs();
            for (CouponConfigInfoDTO couponConfigInfoDTO : couponConfigs) {
                System.out.println(couponConfigInfoDTO.getConfigId());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testGetConfigListBySceneGlobal() {
        try {
            BatchGetConfigInfoRequest request = new BatchGetConfigInfoRequest();
            request.setSceneCode("DCCC8C085E79839661D9B93E9A996EC8");
            request.setOnlyAvailable(true);
            HeraContextKeyValueHolder.put("mone-retail-dev-mode", "true");
            HeraContextKeyValueHolder.put("mone-retail-area-for-global", "HK");
            HeraContextKeyValueHolder.put("mone-retail-language-for-global", "zh-CN");
            Result<BatchGetConfigInfoReponse> res = dubboCouponService.batchGetCouponConfigInfo(request);
            List<CouponConfigInfoDTO> couponConfigs = res.getData().getCouponConfigs();
            for (CouponConfigInfoDTO couponConfigInfoDTO : couponConfigs) {
                System.out.println(couponConfigInfoDTO.getConfigId());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testElaryWaringCoupon(){
        dubboCouponToolService.couponEarlyWarning();
    }

    @Test
    public void testGetCouponConfigInfo(){
        GetConfigInfoRequest req = new GetConfigInfoRequest();
        req.setConfigId(302L);
        CouponConfigInfoDTO couponConfigInfo = dubboCouponService.getCouponConfigInfo(req).getData().getCouponConfigInfoDTO();
        Assert.assertEquals(couponConfigInfo.getFetchLimitType().longValue(), 1);
        System.out.println("couponConfigInfo is " + GsonUtil.toJson(couponConfigInfo));
        req.setConfigId(148219L);
        couponConfigInfo = dubboCouponService.getCouponConfigInfo(req).getData().getCouponConfigInfoDTO();
        Assert.assertEquals(couponConfigInfo.getFetchLimitType().longValue(), 2);
        System.out.println("couponConfigInfo is " + GsonUtil.toJson(couponConfigInfo));
    }

    @Test
    public void testBatchGetCouponConfigInfo(){
        BatchGetConfigInfoRequest req = new BatchGetConfigInfoRequest();
        req.setBizType(BizPlatformEnum.CAR_AFTER_SALE.getCode());
        req.setConfigIds(Lists.newArrayList(147601L, 148219L));
        List<CouponConfigInfoDTO> couponConfigInfoDTOList = dubboCouponService.batchGetCouponConfigInfo(req).getData().getCouponConfigs();
        System.out.println("couponConfigInfoDTOList is " + GsonUtil.toJson(couponConfigInfoDTOList));
    }

    @Test
    public void testSyncCouponConfig() throws BizError {
        CouponConfigPO po = couponConfigRepository.searchCouponById(159735L);
        watermelonCouponServiceProxy.syncCouponTemplate(po);
    }

    @Test
    public void getCouponCodeInfoTest() {
        GetCouponCodeRequest request = new GetCouponCodeRequest();
        ArrayList<String> couponIndexList = Lists.newArrayList("3e0429b467298b1887b9a109ddb3d1f8", "8befd4b63966086ad99c513b27128215", "8aaff2a2d3714bc3d6e7332d45814b76");


        request.setCouponIndexList(couponIndexList);

        Result<GetCouponCodeResponse> response = dubboCouponService.getCouponCodeInfo(request);

        log.info("getCouponCodeInfoTest response = {}", GsonUtil.toJson(response));
    }

    @Test
    public void testGetCouponById() {
        GetCouponByIdRequest request = new GetCouponByIdRequest();
        ArrayList<String> idList = Lists.newArrayList("1000056880", "1000196548");

        request.setIdList(idList); // 设置测试的优惠券ID
        Result<GetCouponByIdResponse> response = dubboCouponService.getCouponById(request);

        log.info("testGetCouponById response = {}", GsonUtil.toJson(response));
    }

    @Test
    public void getCouponByTypeIdTest() {
        GetCouponByTypeIdRequest request = new GetCouponByTypeIdRequest();

        ArrayList<String> typeIdList = Lists.newArrayList("145795");
        request.setTypeIdList(typeIdList);

        request.setStartTime(1724998105L);
        request.setEndTime(1724998125L);

        Result<GetCouponByTypeIdResponse> response = dubboCouponService.getCouponByTypeId(request);

        log.info("getCouponByTypeIdTest response = {}", GsonUtil.toJson(response));
    }

    @Test
    public void testGetGoodsCouponConfigRel() {
        GoodsConfigRelRequest req = new GoodsConfigRelRequest();
        req.setBizPlatform(BizPlatformEnum.CAR_SHOP.getCode());
        req.setUseChannel(Lists.newArrayList(UseChannelsEnum.CAR_SHOP.getValue()));
        GoodsItem goodsItem = new GoodsItem();
        goodsItem.setId(2230000331L);
        goodsItem.setLevel(GoodsLevelEnum.Ssu.getValue());
        req.setGoodsItems(Lists.newArrayList(goodsItem));

        GoodsConfigRelResponse resp = dubboCouponService.getGoodsCouponConfigRel(req).getData();
        log.info("resp is {}", GsonUtil.toJson(resp));
    }

    @Test
    public void searchGoodConfigIdTest() {
        GoodConfigIdsRequest req = new GoodConfigIdsRequest();
        req.setLevel("sku");
        req.setItemId(5407L);
        req.setUseAbleType(1);

        Result<BasePageResponse<CouponConfigListVO>> resp = dubboCouponToolService.searchGoodConfigId(req);

        log.info("searchGoodConfigIdTest resp = {}", GsonUtil.toJson(resp));
    }

    @Test
    public void testGetGoodsCouponConfigRel_0429() {
        GoodsConfigRelRequest req = new GoodsConfigRelRequest();
        req.setBizPlatform(BizPlatformEnum.RETAIL.getCode());
        GoodsItem goodsItem = new GoodsItem();
        goodsItem.setId(21035L);
        goodsItem.setLevel(GoodsLevelEnum.Sku.getValue());
        req.setGoodsItems(Lists.newArrayList(goodsItem));

        GoodsConfigRelResponse resp = dubboCouponService.getGoodsCouponConfigRel(req).getData();

        // 208743
        log.info("testGetGoodsCouponConfigRel_0429 resp = {}", GsonUtil.toJson(resp));
    }
}
