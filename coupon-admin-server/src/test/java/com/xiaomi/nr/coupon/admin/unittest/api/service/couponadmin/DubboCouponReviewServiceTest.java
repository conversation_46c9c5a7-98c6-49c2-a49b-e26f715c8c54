package com.xiaomi.nr.coupon.admin.unittest.api.service.couponadmin;

import com.xiaomi.mit.unittest.field.autoconfig.SetupBean;
import com.xiaomi.newretail.bpm.api.model.callback.OnStatusChangedRequest;
import com.xiaomi.newretail.bpm.api.model.callback.OnStatusChangedResponse;
import com.xiaomi.newretail.bpm.api.model.callback.ProcessAction;
import com.xiaomi.newretail.bpm.api.model.dto.ProcessCreateDTO;
import com.xiaomi.newretail.bpm.api.provider.BpmService;
import com.xiaomi.newretail.common.tools.utils.GsonUtil;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.request.CouponCreateReviewRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.request.CouponUpdateReviewRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.response.CouponReviewResponse;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.api.enums.TranslationEnum;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponBpmCallBackService;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponReviewService;
import com.xiaomi.nr.coupon.admin.common.domain.coupon.couponreview.CouponReviewService;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.BottomTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.PromotionTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.UseChannelsEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.UseTimeTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.po.CouponConfigReviewPO;
import com.xiaomi.nr.coupon.admin.retail.domain.coupon.couponconfig.bpm.BpmProxy;
import com.xiaomi.nr.coupon.admin.unittest.BaseUnitTest;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.rpc.RpcContext;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;

/**
 * @author: zhangliwei6
 * @date: 2025/6/4 10:19
 * @description:
 */
@Slf4j
@SetupBean("bean/couponreview/createDirectReduce3c.json")
public class DubboCouponReviewServiceTest extends BaseUnitTest {

    @Autowired
    private DubboCouponReviewService dubboCouponReviewService;

    @Autowired
    private DubboCouponBpmCallBackService dubboCouponBpmCallBackService;

    @Autowired
    private CouponReviewService couponReviewService;

    @Autowired
    private BpmProxy bpmProxy;

    private static CouponCreateReviewRequest createDirectReduce3c;

    private static String bpmKey;

    private static Long id;

    @BeforeEach
    public void beforeEach() {
        RpcContext.getContext().getAttachments().put("$upc_account", "zhangliwei6");
    }

    @Test
    @Order(1)
    public void test_createCouponReview_directReduceCar() {
        // mock
        BpmService mockBpmService = PowerMockito.mock(BpmService.class);
        Whitebox.setInternalState(bpmProxy, mockBpmService);

        CouponCreateReviewRequest request = GsonUtil.parseObj(GsonUtil.toJsonStr(createDirectReduce3c), CouponCreateReviewRequest.class);

        bpmKey = UUID.randomUUID().toString().toLowerCase();
        doReturn(Result.success(bpmKey)).when(mockBpmService).processCreate(any(ProcessCreateDTO.class));

        Result<CouponReviewResponse> result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals(GeneralCodes.OK.getCode(), result.getCode());

        OnStatusChangedRequest callbackRequest = new OnStatusChangedRequest();
        callbackRequest.setProcessInstanceId(bpmKey);
        callbackRequest.setAction(ProcessAction.Accept);
        callbackRequest.setFinished(Boolean.FALSE);
        callbackRequest.setOperator(RpcContext.getContext().getAttachment("$upc_account"));

        // 审核中
        Result<OnStatusChangedResponse> callbackResult = dubboCouponBpmCallBackService.onStatusChanged(callbackRequest);
        Assertions.assertEquals(GeneralCodes.OK.getCode(), callbackResult.getCode());

        // 审核完
        callbackRequest.setFinished(Boolean.TRUE);
        callbackResult = dubboCouponBpmCallBackService.onStatusChanged(callbackRequest);
        Assertions.assertEquals(GeneralCodes.OK.getCode(), callbackResult.getCode());

    }

    @Test
    @Order(2)
    public void test_createCouponReview_refused() {
        // mock
        BpmService mockBpmService = PowerMockito.mock(BpmService.class);
        Whitebox.setInternalState(bpmProxy, mockBpmService);

        CouponCreateReviewRequest request = GsonUtil.parseObj(GsonUtil.toJsonStr(createDirectReduce3c), CouponCreateReviewRequest.class);

        doReturn(Result.success(bpmKey + "1")).when(mockBpmService).processCreate(any(ProcessCreateDTO.class));

        Result<CouponReviewResponse> result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals(GeneralCodes.OK.getCode(), result.getCode());

        OnStatusChangedRequest callbackRequest = new OnStatusChangedRequest();
        callbackRequest.setAction(ProcessAction.Refuse);
        callbackRequest.setFinished(Boolean.FALSE);
        callbackRequest.setOperator(RpcContext.getContext().getAttachment("$upc_account"));

        // 异常
        Result<OnStatusChangedResponse> callbackResult = dubboCouponBpmCallBackService.onStatusChanged(callbackRequest);
        Assertions.assertEquals(GeneralCodes.OK.getCode(), callbackResult.getCode());
        Assertions.assertEquals(GeneralCodes.InternalError.getCode(), callbackResult.getData().getCode());

        // 拒绝
        callbackRequest.setProcessInstanceId(bpmKey + "1");
        callbackResult = dubboCouponBpmCallBackService.onStatusChanged(callbackRequest);
        Assertions.assertEquals(GeneralCodes.OK.getCode(), callbackResult.getCode());

        // 重复审核
        callbackResult = dubboCouponBpmCallBackService.onStatusChanged(callbackRequest);
        Assertions.assertEquals(GeneralCodes.OK.getCode(), callbackResult.getCode());
        Assertions.assertEquals(GeneralCodes.InternalError.getCode(), callbackResult.getData().getCode());
    }

    @Test
    @Order(3)
    public void test_updateCommonValidator() {
        CouponUpdateReviewRequest request = GsonUtil.parseObj(GsonUtil.toJsonStr(createDirectReduce3c), CouponUpdateReviewRequest.class);

        Result<CouponReviewResponse> result = dubboCouponReviewService.updateCouponReview(request);
        Assertions.assertEquals(TranslationEnum.COUPON_TASK_INSERT_NOT_NULL_CONFIG.getTranslateContent(),
                result.getMessage());

        request.getCouponConfigVO().setId(99999999L);
        result = dubboCouponReviewService.updateCouponReview(request);
        Assertions.assertEquals(TranslationEnum.COUPON_CONFIG_UPDATE_NULL_CONFIG.getTranslateContent(),
                result.getMessage());

        // bpmKey查id
        CouponConfigReviewPO couponConfigReviewPO = couponReviewService.selectByBpmKey(bpmKey);
        Assertions.assertNotNull(couponConfigReviewPO);
        id = couponConfigReviewPO.getConfigId();
        request.getCouponConfigVO().setId(id);

        request.getCouponConfigVO().setSendScene("7C0938DFA6023303195221E669AFDE24");
        result = dubboCouponReviewService.updateCouponReview(request);
        Assertions.assertEquals(TranslationEnum.COUPON_CONFIG_UPDATE_ERROR_SCENE.getTranslateContent(),
                result.getMessage());
        request.getCouponConfigVO().setSendScene(createDirectReduce3c.getCouponConfigVO().getSendScene());

        request.getCouponConfigVO().getUseTermVO().setUseTimeType(UseTimeTypeEnum.RELATIVE.getValue());
        request.getCouponConfigVO().getUseTermVO().setUseDuration(1);
        result = dubboCouponReviewService.updateCouponReview(request);
        Assertions.assertEquals(TranslationEnum.COUPON_CONFIG_UPDATE_ERROR_TIME.getTranslateContent(),
                result.getMessage());
        request.getCouponConfigVO().getUseTermVO().setUseTimeType(createDirectReduce3c.getCouponConfigVO().getUseTermVO().getUseTimeType());
        request.getCouponConfigVO().getUseTermVO().setUseDuration(createDirectReduce3c.getCouponConfigVO().getUseTermVO().getUseDuration());

        request.getCouponConfigVO().getPromotionRuleVO().setPromotionType(PromotionTypeEnum.ConditionReduce.getValue());
        result = dubboCouponReviewService.updateCouponReview(request);
        Assertions.assertEquals(TranslationEnum.COUPON_CONFIG_UPDATE_CHANGE_TYPE.getTranslateContent(),
                result.getMessage());
        request.getCouponConfigVO().getPromotionRuleVO().setPromotionType(createDirectReduce3c.getCouponConfigVO().getPromotionRuleVO().getPromotionType());

        request.getCouponConfigVO().getPromotionRuleVO().setBottomType(BottomTypeEnum.OverYuan.getValue());
        result = dubboCouponReviewService.updateCouponReview(request);
        Assertions.assertEquals(TranslationEnum.COUPON_CONFIG_UPDATE_CHANGE_BOTTOM.getTranslateContent(),
                result.getMessage());
        request.getCouponConfigVO().getPromotionRuleVO().setBottomType(createDirectReduce3c.getCouponConfigVO().getPromotionRuleVO().getBottomType());

        request.setBizType(BizPlatformEnum.CAR_SHOP.getCode());
        request.getCouponConfigVO().getUseChannel().clear();
        request.getCouponConfigVO().getUseChannel().put(UseChannelsEnum.CAR_SHOP.getValue(), null);
        result = dubboCouponReviewService.updateCouponReview(request);
        Assertions.assertEquals(TranslationEnum.COUPON_CONFIG_CHANGE_CHECK_TYPE.getTranslateContent(),
                result.getMessage());
    }

    @Test
    @Order(4)
    public void test_updateCouponReview_directReduceCar() {
        // mock
        BpmService mockBpmService = PowerMockito.mock(BpmService.class);
        Whitebox.setInternalState(bpmProxy, mockBpmService);

        CouponUpdateReviewRequest request = GsonUtil.parseObj(GsonUtil.toJsonStr(createDirectReduce3c), CouponUpdateReviewRequest.class);
        request.getCouponConfigVO().setId(id);

        // 直接修改
        Result<CouponReviewResponse> result = dubboCouponReviewService.updateCouponReview(request);
        Assertions.assertEquals(GeneralCodes.OK.getCode(), result.getCode());

        // 审核修改
        doReturn(Result.success(bpmKey + "2")).when(mockBpmService).processCreate(any(ProcessCreateDTO.class));
        request.getCouponConfigVO().getPromotionRuleVO().setPromotionValue(999L);
        result = dubboCouponReviewService.updateCouponReview(request);
        Assertions.assertEquals(GeneralCodes.OK.getCode(), result.getCode());

        OnStatusChangedRequest callbackRequest = new OnStatusChangedRequest();
        callbackRequest.setProcessInstanceId(bpmKey + "2");
        callbackRequest.setAction(ProcessAction.Accept);
        callbackRequest.setFinished(Boolean.TRUE);
        callbackRequest.setOperator(RpcContext.getContext().getAttachment("$upc_account"));

        Result<OnStatusChangedResponse> callbackResult = dubboCouponBpmCallBackService.onStatusChanged(callbackRequest);
        Assertions.assertEquals(GeneralCodes.OK.getCode(), callbackResult.getCode());
    }
}
