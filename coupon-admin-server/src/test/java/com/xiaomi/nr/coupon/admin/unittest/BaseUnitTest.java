package com.xiaomi.nr.coupon.admin.unittest;

import com.xiaomi.mit.unittest.configuration.UTAutoConfiguration;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.global.dev.base.HeraContextKeyValueHolder;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

/**
 * @description 国内测试基类
 * <AUTHOR>
 * @date 2025-05-08 20:17
*/
@Slf4j
@ActiveProfiles("ut-global")
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {UTAutoConfiguration.class, CouponAdminBootstrap.class})
public abstract class BaseUnitTest {

    @BeforeEach
    public void test() {
        HeraContextKeyValueHolder.put("mone-retail-dev-mode", "true");
        HeraContextKeyValueHolder.put("mone-retail-area-for-global", "ID");
        HeraContextKeyValueHolder.put("mone-retail-language-for-global", "zh-CN");
    }
}
