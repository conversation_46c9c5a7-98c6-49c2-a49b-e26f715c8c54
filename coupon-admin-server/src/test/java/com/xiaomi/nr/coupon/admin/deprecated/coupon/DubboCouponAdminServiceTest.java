package com.xiaomi.nr.coupon.admin.deprecated.coupon;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.BudgetInfoDto;
import com.xiaomi.nr.coupon.admin.api.dto.PageInfo;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponConfigListVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponQueryBudgetListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponInfoRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponUpdateStatusRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.response.CouponInfoResponse;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponAdminService;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.auth.AuthCouponAdminService;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponCreateEvent;
import com.xiaomi.nr.coupon.admin.common.domain.coupon.couponconfig.event.posthandler.OmsDataSyncPostHandler;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.CouponConfigMapper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.StringUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.rpc.RpcContext;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponAdminBootstrap.class)
@ActiveProfiles("sg_staging")
public class DubboCouponAdminServiceTest {

    @Autowired
    private DubboCouponAdminService adminService;
    @Autowired
    private AuthCouponAdminService authCouponAdminService;

    @Autowired
    private CouponConfigMapper mapper;

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    @Autowired
    private OmsDataSyncPostHandler omsDataSyncPostHandler;

    @Test
    public void  couponDetailTestFor3c(){

        CouponInfoRequest request = new CouponInfoRequest();
        request.setId(51L);
        Result<CouponInfoResponse> response = adminService.couponConfigDetail(request);
        Assert.assertNotNull(response);
        log.info(GsonUtil.toJson(response));
    }

    @Test
    public void couponDetailTestForCarShop(){

        CouponInfoRequest request = new CouponInfoRequest();
        request.setId(182788L);
        request.setAppId("XM2111");
        String token = StringUtil.getToken(request,"XM2111","10506aa82e59096eb55095c44c70cea4");
        request.setToken(token);
        Result<CouponInfoResponse> response = adminService.couponConfigDetail(request);
        Assert.assertNotNull(response);
        log.info("couponDetailTestForCarShop res = {}", GsonUtil.toJson(response));
    }


    @Test
    public void couponListTest(){

        CouponListRequest request = new CouponListRequest();
        request.setPageNo(1);
        request.setCouponType(1);
        request.setStoreId("MITW00014");
        request.setPageSize(10);

        Result<BasePageResponse<CouponConfigListVO>> result = adminService.couponConfigList(request);
        Assert.assertNotNull(result.getData());
        System.out.println(result);
    }
    @Test
    public void authCouponListTest() throws BizError {

        CouponListRequest request = new CouponListRequest();
        request.setPageNo(1);
        request.setCouponType(1);
        request.setStatus(1);
//        request.setStoreId("MITW00014");
        request.setPageSize(10);

        Result<BasePageResponse<CouponConfigListVO>> result = authCouponAdminService.authCouponConfigList(request);
        Assert.assertNotNull(result.getData());
        System.out.println(result);
    }

    @Test
    public void authCouponDetailTest() throws BizError {

        CouponInfoRequest request = new CouponInfoRequest();
        request.setId(206L);

        Result<CouponInfoResponse> result = authCouponAdminService.queryConfigDetail(request);
        Assert.assertNotNull(result.getData());
        System.out.println(result);
    }

    @Test
    public void authCouponExportTest() throws BizError {

        CouponListRequest request = new CouponListRequest();
        request.setPageNo(1);
        request.setCouponType(1);
        request.setPageSize(10);

        Result<String> result = authCouponAdminService.export2TaskCenter(request);
        Assert.assertNotNull(result.getData());
        System.out.println(result);
    }




    @Test
    public void updateStatTest(){
        CouponUpdateStatusRequest request = new CouponUpdateStatusRequest();
        request.setId(14L);
        request.setOperateType(2);
        RpcContext.getContext().getAttachments().put("$upc_account", "taiyang1");
        System.out.println(adminService.updateStatus(request));
    }


    @Test
    public void t1() {
        CouponConfigPO couponConfigPO = couponConfigRepository.searchCouponById(30227L);
        CouponCreateEvent couponCreateEvent = new CouponCreateEvent();
        couponCreateEvent.setData(couponConfigPO);
        omsDataSyncPostHandler.createPost(couponCreateEvent);
    }

    @Test
    public void queryBudgetList() {
        log.info("======================================================");
        CouponQueryBudgetListRequest request = new CouponQueryBudgetListRequest();
        Result<PageInfo<BudgetInfoDto>> result = adminService.queryBudgetList(request);
        log.info("queryBudgetList result = {}", GsonUtil.toJson(result));
        log.info("======================================================");
    }

}
