package com.xiaomi.nr.coupon.admin.deprecated.coupon;

import com.xiaomi.nr.coupon.admin.api.dto.ecard.request.GetEcardInfoRequest;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.request.GetEcardLogRequest;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.response.GetEcardInfoResponse;
import com.xiaomi.nr.coupon.admin.api.dto.ecard.response.GetEcardLogResponse;
import com.xiaomi.nr.coupon.admin.api.service.DubboEcardService;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date 2024/9/2 09:54
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponAdminBootstrap.class)
@ActiveProfiles("staging")
public class DubboEcardServiceTest {
    @Autowired
    private DubboEcardService dubboEcardService;

    @Test
    public void getEcardLogTest() {
        GetEcardLogRequest request = new GetEcardLogRequest();
        request.setUserId(3150419349L);
        request.setLogType(1);
        request.setRefundNo(24082800183541L);

        Result<GetEcardLogResponse> response = dubboEcardService.getEcardLog(request);

        log.info("getEcardLogTest response = {}", GsonUtil.toJson(response));
    }

    @Test
    public void getEcardInfoTest() {
        GetEcardInfoRequest request = new GetEcardInfoRequest();
        ArrayList<Long> cardIdList = Lists.newArrayList(20007309079136L, 20007182204696L);
        request.setCardIdList(cardIdList);

        Result<GetEcardInfoResponse> response = dubboEcardService.getEcardInfo(request);

        log.info("getEcardInfoTest  response = {}", GsonUtil.toJson(response));
    }
}
