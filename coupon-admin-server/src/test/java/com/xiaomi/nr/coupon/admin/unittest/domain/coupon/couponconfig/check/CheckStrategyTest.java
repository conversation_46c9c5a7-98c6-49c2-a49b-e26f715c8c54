package com.xiaomi.nr.coupon.admin.unittest.domain.coupon.couponconfig.check;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mi.oa.infra.oaucf.ems.resp.br.BrResp;
import com.mi.oa.infra.oaucf.ems.resp.br.BudgetApplyDetailResp;
import com.mi.oa.infra.oaucf.ems.resp.br.BudgetApplyResp;
import com.mi.oa.infra.oaucf.ems.service.BrBudgetApplyService;
import com.xiaomi.goods.gis.api.GoodsInfoNewService;
import com.xiaomi.goods.gis.dto.goodsInfoNew.GoodsMultiInfoDTO;
import com.xiaomi.goods.gis.dto.goodsInfoNew.GoodsMultiInfoRequest;
import com.xiaomi.goods.gis.dto.goodsInfoNew.GoodsMultiInfoResponse;
import com.xiaomi.goods.gms.api.batch.BatchedInfoService;
import com.xiaomi.goods.gms.api.sku.SkuInfoService;
import com.xiaomi.goods.gms.dto.batch.BatchedInfoDto;
import com.xiaomi.goods.gms.dto.batch.BatchedMapResponse;
import com.xiaomi.goods.gms.dto.batch.BatchedRequest;
import com.xiaomi.goods.gms.dto.sku.SkuInfoDto;
import com.xiaomi.goods.gms.dto.sku.SkuInfoResponse;
import com.xiaomi.goods.gms.dto.sku.SkuRequest;
import com.xiaomi.mit.unittest.field.autoconfig.SetupBean;
import com.xiaomi.newretail.common.tools.utils.GsonUtil;
import com.xiaomi.nr.coupon.admin.api.dto.BudgetInfoDto;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponConfigVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.UseChannelVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.request.CouponCreateReviewRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.response.CouponReviewResponse;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.api.enums.BizSubTypeEnum;
import com.xiaomi.nr.coupon.admin.api.enums.BudgetFeeTypeEnum;
import com.xiaomi.nr.coupon.admin.api.enums.TranslationEnum;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponReviewService;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.*;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsItemTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.goods.GoodsLevelEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.nacosconfig.NacosSwitchConfig;
import com.xiaomi.nr.coupon.admin.infrastructure.rpc.BrProxy;
import com.xiaomi.nr.coupon.admin.infrastructure.rpc.goods.GisProxyService;
import com.xiaomi.nr.coupon.admin.infrastructure.rpc.goods.GmsProxyService;
import com.xiaomi.nr.coupon.admin.unittest.BaseUnitTest;
import com.xiaomi.nr.order.common.enums.product.ShipmentTypeEnum;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.rpc.RpcContext;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;

/**
 * @author: zhangliwei6
 * @date: 2025/6/9 16:27
 * @description:
 */
@Slf4j
@SetupBean("bean/couponreview/*.json")
public class CheckStrategyTest extends BaseUnitTest {

    @Autowired
    private DubboCouponReviewService dubboCouponReviewService;

    private static CouponCreateReviewRequest createDirectReduce3c;

    private CouponCreateReviewRequest request;

    private CouponConfigVO couponConfigVO;

    private CouponConfigVO originCouponConfigVO;

    @BeforeEach
    public void beforeEach() {
        RpcContext.getContext().getAttachments().put("$upc_account", "zhangliwei6");
        request = GsonUtil.parseObj(GsonUtil.toJsonStr(createDirectReduce3c), CouponCreateReviewRequest.class);
        couponConfigVO = request.getCouponConfigVO();
        originCouponConfigVO = createDirectReduce3c.getCouponConfigVO();
    }

    @Test
    public void test_commonValidator() {
        couponConfigVO.getUseTermVO().setUseTimeType(UseTimeTypeEnum.RELATIVE.getValue());

        // common check
        couponConfigVO.setName(null);
        Result<CouponReviewResponse> result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals(TranslationEnum.COUPON_CONFIG_INSERT_NULL_NAME.getTranslateContent(), result.getMessage());
        couponConfigVO.setName(originCouponConfigVO.getName());

        couponConfigVO.setCouponDesc(null);
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals(TranslationEnum.COUPON_CONFIG_INSERT_NULL_EXPLAIN.getTranslateContent(), result.getMessage());
        couponConfigVO.setCouponDesc(originCouponConfigVO.getCouponDesc());

        couponConfigVO.setSendScene(null);
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals(TranslationEnum.COUPON_CONFIG_INSERT_NULL_SCENE.getTranslateContent(), result.getMessage());
        couponConfigVO.setSendScene(originCouponConfigVO.getSendScene());

        couponConfigVO.setStartFetchTime(null);
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals(TranslationEnum.COUPON_CONFIG_INSERT_ERROR_TIME.getTranslateContent(), result.getMessage());
        couponConfigVO.setStartFetchTime(originCouponConfigVO.getStartFetchTime());

        couponConfigVO.getUseTermVO().setUseTimeType(0);
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals(TranslationEnum.COUPON_CONFIG_INSERT_NULL_USE.getTranslateContent(), result.getMessage());
        couponConfigVO.getUseTermVO().setUseTimeType(originCouponConfigVO.getUseTermVO().getUseTimeType());

        couponConfigVO.getUseTermVO().setUseTimeType(UseTimeTypeEnum.RELATIVE.getValue());
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals(TranslationEnum.COUPON_CONFIG_INSERT_NULL_TIME_2.getTranslateContent(), result.getMessage());
        couponConfigVO.getUseTermVO().setUseDuration(1);

        String mockSendScene = "11111111111111111111111111111111";
        couponConfigVO.setSendScene(mockSendScene);
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals(TranslationEnum.COUPON_CONFIG_INSERT_ERROR_SCENE.getTranslateContent() + mockSendScene,
                result.getMessage());
        couponConfigVO.setSendScene(originCouponConfigVO.getSendScene());

        mockSendScene = "00000000000000000000000000000000";
        couponConfigVO.setSendScene(mockSendScene);
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals(TranslationEnum.COUPON_CONFIG_INSERT_STATUS_SCENE.getTranslateContent() + mockSendScene,
                result.getMessage());
        couponConfigVO.setSendScene(originCouponConfigVO.getSendScene());

        couponConfigVO.getDistributionRuleVO().setApplyCount(-1);
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals(TranslationEnum.COUPON_CONFIG_INSERT_ERROR_APPLY_COUNT.getTranslateContent(), result.getMessage());
        couponConfigVO.getDistributionRuleVO().setApplyCount(originCouponConfigVO.getDistributionRuleVO().getApplyCount());

        couponConfigVO.getExtProp().setArea(1);
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals(TranslationEnum.COUPON_CONFIG_INSERT_ERROR_AREA.getTranslateContent(), result.getMessage());
        couponConfigVO.getExtProp().setArea(originCouponConfigVO.getExtProp().getArea());
    }

    @Autowired
    private BrProxy brProxy;

    @Test
    public void test_bizPlatformValidator() {
        // mock
        BrBudgetApplyService mockBrBudgetApplyService = PowerMockito.mock(BrBudgetApplyService.class);
        Whitebox.setInternalState(brProxy, mockBrBudgetApplyService);

        // retail
        HashMap<Integer, UseChannelVO> useChannelVOHashMap = new HashMap<>();
        useChannelVOHashMap.put(9, null);
        couponConfigVO.setUseChannel(useChannelVOHashMap);
        Result<CouponReviewResponse> result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals(TranslationEnum.COUPON_CONFIG_INSERT_ERROR_MATCH.getTranslateContent(), result.getMessage());
        couponConfigVO.setUseChannel(originCouponConfigVO.getUseChannel());

        // carShop
        request.setBizType(BizPlatformEnum.CAR_SHOP.getCode());
        BrResp<BudgetApplyResp> brResp = new BrResp<>();
        BudgetApplyResp resp = new BudgetApplyResp();
        brResp.setBody(resp);
        BudgetApplyDetailResp detailResp = new BudgetApplyDetailResp();
        detailResp.setFeeType(BudgetFeeTypeEnum.CARSHOP1.getCode());
        resp.setDetailList(Lists.newArrayList(detailResp));
        doReturn(brResp).when(mockBrBudgetApplyService).queryDetailAndMin(any());

        BudgetInfoDto budgetInfoDto = new BudgetInfoDto();
        budgetInfoDto.setLineNum(12345L);
        budgetInfoDto.setBudgetApplyNo("BR202409190034");
        couponConfigVO.setBudgetInfoDto(budgetInfoDto);

        useChannelVOHashMap = new HashMap<>();
        useChannelVOHashMap.put(9, null);
        couponConfigVO.setUseChannel(useChannelVOHashMap);
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals(TranslationEnum.COUPON_CONFIG_INSERT_ERROR_MATCH.getTranslateContent(), result.getMessage());
        couponConfigVO.setUseChannel(originCouponConfigVO.getUseChannel());
    }

    @Test
    public void test_promotionTypeValidator() {
        // directReduce create check
        couponConfigVO.setId(1L);
        Result<CouponReviewResponse> result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals(TranslationEnum.COUPON_TASK_INSERT_NULL_CONFIG.getTranslateContent(),
                result.getMessage());

        // ConditionDiscount
        couponConfigVO.getPromotionRuleVO().setPromotionType(PromotionTypeEnum.ConditionDiscount.getValue());
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("券id必须为空", result.getMessage());
        couponConfigVO.setId(0L);

        couponConfigVO.getPromotionRuleVO().setBottomType(BottomTypeEnum.OverYuan.getValue());
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("门槛值满元不能为空", result.getMessage());

        couponConfigVO.getPromotionRuleVO().setBottomType(BottomTypeEnum.OverCount.getValue());
        couponConfigVO.getPromotionRuleVO().setBottomCount(0);
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("门槛值满件不能为空", result.getMessage());
        couponConfigVO.getPromotionRuleVO().setBottomCount(originCouponConfigVO.getPromotionRuleVO().getBottomCount());

        couponConfigVO.getPromotionRuleVO().setBottomType(0);
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("不支持的门槛类型", result.getMessage());

        couponConfigVO.getPromotionRuleVO().setBottomType(BottomTypeEnum.OverYuan.getValue());
        couponConfigVO.getPromotionRuleVO().setBottomPrice(1L);
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("最大减免金额必须大于0", result.getMessage());

        // ConditionReduce
        couponConfigVO.getPromotionRuleVO().setPromotionType(PromotionTypeEnum.ConditionReduce.getValue());
        couponConfigVO.setId(1L);
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("券id必须为空", result.getMessage());

        // GiftBuy
        couponConfigVO.getPromotionRuleVO().setPromotionType(PromotionTypeEnum.GIFT.getValue());
        request.setBizType(BizPlatformEnum.CAR_SHOP.getCode());
        HashMap<Integer, UseChannelVO> useChannelVOHashMap = new HashMap<>();
        useChannelVOHashMap.put(6, null);
        couponConfigVO.setUseChannel(useChannelVOHashMap);

        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("券id必须为空", result.getMessage());
        couponConfigVO.setId(0L);

        couponConfigVO.getPromotionRuleVO().setPromotionValue(1L);
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("商品金额应直减至0元", result.getMessage());

        // NyuanBuy
        couponConfigVO.getPromotionRuleVO().setPromotionType(PromotionTypeEnum.NyuanBuy.getValue());
        couponConfigVO.setId(1L);
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("券id必须为空", result.getMessage());

        couponConfigVO.setId(0L);
        couponConfigVO.getPromotionRuleVO().setPromotionValue(10000000000L);
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("n元券最大为99999999.99元", result.getMessage());
    }

    @Test
    public void test_couponTypeValidator() {
        // postFree  check
        couponConfigVO.setCouponType(CouponTypeEnum.POSTFREE.getValue());
        Result<CouponReviewResponse> result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("运费券目前只支持直营店和专卖店！", result.getMessage());
        couponConfigVO.getUseChannel().remove(UseChannelsEnum.XIAOMI_SHOP.getValue());
        couponConfigVO.getUseChannel().remove(UseChannelsEnum.AUTHORIZED_STORE.getValue());

        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("运费券履约方式只支持门店闪送！", result.getMessage());
        couponConfigVO.setShipmentId(ShipmentTypeEnum.STORE_FLASH.getMallId());

        couponConfigVO.getPromotionRuleVO().setBottomType(BottomTypeEnum.OverCount.getValue());
        couponConfigVO.getPromotionRuleVO().setPromotionValue(1L);
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("运费券当前只支持配置立减10元的优惠类型！", result.getMessage());
        couponConfigVO.getPromotionRuleVO().setPromotionValue(originCouponConfigVO.getPromotionRuleVO().getPromotionValue());

        couponConfigVO.getExtProp().setArea(GivenAreaEnum.YES.getType());
        couponConfigVO.setAreaIds(Lists.newArrayList(1L));
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("运费券特殊规则只支持配置是否转赠！", result.getMessage());
        couponConfigVO.getExtProp().setArea(originCouponConfigVO.getExtProp().getArea());

        couponConfigVO.setCouponType(originCouponConfigVO.getCouponType());
        couponConfigVO.setUseChannel(originCouponConfigVO.getUseChannel());
        couponConfigVO.setShipmentId(originCouponConfigVO.getShipmentId());

        // goods | subsidy  check
        couponConfigVO.setCouponType(CouponTypeEnum.GOODS.getValue());
        couponConfigVO.setCostShare(null);
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("成本分摊不可为空", result.getMessage());
        couponConfigVO.setCostShare(originCouponConfigVO.getCostShare());
    }

    @Test
    public void test_createSpecialValidator() {
        // directDeduce3c
        couponConfigVO.getUseChannel().clear();
        Result<CouponReviewResponse> result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("使用渠道不能为空", result.getMessage());

        UseChannelVO useChannelVO = new UseChannelVO();
        useChannelVO.setAll(false);
        couponConfigVO.getUseChannel().put(UseChannelsEnum.XIAOMI_SHOP.getValue(), useChannelVO);
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("指定门店不能为空", result.getMessage());
        useChannelVO.setAll(true);

        couponConfigVO.getPromotionRuleVO().setPromotionValue(-1L);
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("优惠值必须大于0", result.getMessage());
        couponConfigVO.getPromotionRuleVO().setPromotionValue(originCouponConfigVO.getPromotionRuleVO().getPromotionValue());

        // directReduceGlobal
        request.setBizType(BizPlatformEnum.GLOBAL_NEW_RETAIL.getCode());
        couponConfigVO.getPromotionRuleVO().setPromotionType(PromotionTypeEnum.DirectReduce.getValue());

        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals(TranslationEnum.COUPON_CONFIG_CREATE_CHECK_WORKFLOW.getTranslateContent(),
                result.getMessage());
        couponConfigVO.setWorkflowId(1L);

        couponConfigVO.getPromotionRuleVO().setPromotionValue(-1L);
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals(TranslationEnum.COUPON_CONFIG_CREATE_CHECK_AMOUNT.getTranslateContent(),
                result.getMessage());
        couponConfigVO.getPromotionRuleVO().setPromotionValue(originCouponConfigVO.getPromotionRuleVO().getPromotionValue());

        couponConfigVO.getUseChannel().clear();
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals(TranslationEnum.COUPON_CONFIG_CREATE_CHECK_CHANNEL.getTranslateContent(),
                result.getMessage());
        couponConfigVO.getUseChannel().put(UseChannelsEnum.XIAOMI_SHOP.getValue(), useChannelVO);

        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals(TranslationEnum.COUPON_CONFIG_INSERT_ERROR_MATCH.getTranslateContent(),
                result.getMessage());
        couponConfigVO.getUseChannel().clear();

        useChannelVO.setAll(false);
        couponConfigVO.getUseChannel().put(UseChannelsEnum.DIRECTSALE_STORE.getValue(), useChannelVO);
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals(TranslationEnum.COUPON_CONFIG_CREATE_CHECK_STORE.getTranslateContent(),
                result.getMessage());

        // directReduceCarSHop
        request.setBizType(BizPlatformEnum.CAR_SHOP.getCode());

        couponConfigVO.getUseChannel().clear();
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("使用渠道不能为空", result.getMessage());
        couponConfigVO.getUseChannel().put(UseChannelsEnum.CAR_SHOP.getValue(), useChannelVO);

        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("指定门店不能为空", result.getMessage());
        useChannelVO.setAll(true);

        couponConfigVO.getPromotionRuleVO().setPromotionValue(-1L);
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("优惠值必须大于0", result.getMessage());

        // giftBuyCarShop
        couponConfigVO.getPromotionRuleVO().setPromotionType(PromotionTypeEnum.GIFT.getValue());

        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("商品金额应直减至0元", result.getMessage());
    }

    @Autowired
    private GmsProxyService gmsProxyService;

    @Autowired
    private GisProxyService gisProxyService;

    @Autowired
    private NacosSwitchConfig nacosSwitchConfig;

    @Test
    public void test_couponScopeValidator() {
        // mock
        SkuInfoService mockSkuInfoService = PowerMockito.mock(SkuInfoService.class);
        Whitebox.setInternalState(gmsProxyService, mockSkuInfoService);
        Whitebox.setInternalState(gisProxyService, mockSkuInfoService);

        BatchedInfoService mockBatchedInfoService = PowerMockito.mock(BatchedInfoService.class);
        Whitebox.setInternalState(gmsProxyService, mockBatchedInfoService);
        Whitebox.setInternalState(gisProxyService, mockBatchedInfoService);

        GoodsInfoNewService mockGoodsInfoNewService = PowerMockito.mock(GoodsInfoNewService.class);
        Whitebox.setInternalState(gisProxyService, mockGoodsInfoNewService);

        // goods
        couponConfigVO.getGoodsRuleVO().setScopeType(CouponScopeTypeEnum.Goods.getValue());
        couponConfigVO.getGoodsRuleVO().setGoodsInclude(null);
        Result<CouponReviewResponse> result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("通过选择商品建券，商品信息不能为空", result.getMessage());
        couponConfigVO.getGoodsRuleVO().setGoodsInclude(originCouponConfigVO.getGoodsRuleVO().getGoodsInclude());

        // nyuan
        couponConfigVO.getPromotionRuleVO().setPromotionType(PromotionTypeEnum.NyuanBuy.getValue());

        // sku
        doReturn(Result.success(null)).when(mockSkuInfoService).listSku(any(SkuRequest.class));
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("商品信息未找到", result.getMessage());

        SkuInfoResponse skuInfoResponse = new SkuInfoResponse();
        HashMap<Long, List<SkuInfoDto>> skuMap = new HashMap<>();
        skuInfoResponse.setSkuMap(skuMap);
        SkuInfoDto skuInfoDto = new SkuInfoDto();
        skuInfoDto.setProductId(1L);
        skuInfoDto.setSku(1L);
        skuInfoDto.setMarketPrice(900L);
        skuInfoDto.setBizSubType(BizSubTypeEnum.CARRIER_PAN_COMPLETE.getCode());
        skuMap.put(1L, Lists.newArrayList(skuInfoDto));
        doReturn(Result.success(skuInfoResponse)).when(mockSkuInfoService).listSku(any(SkuRequest.class));
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("非授权店渠道只允许配置普通3C商品", result.getMessage());
        skuInfoDto.setBizSubType(BizSubTypeEnum.ORDINARY_3C.getCode());

        // pidSet > 1
        SkuInfoDto skuInfoDto1 = new SkuInfoDto();
        skuInfoDto1.setProductId(2L);
        skuInfoDto1.setSku(2L);
        skuInfoDto1.setMarketPrice(900L);
        skuInfoDto1.setBizSubType(BizSubTypeEnum.ORDINARY_3C.getCode());
        skuMap.put(2L, Lists.newArrayList(skuInfoDto1));
        doReturn(Result.success(skuInfoResponse)).when(mockSkuInfoService).listSku(any(SkuRequest.class));
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("N元券只能设置在同一个pid维度下的商品，请检查后提交！", result.getMessage());

        // package
        couponConfigVO.getGoodsRuleVO().getGoodsInclude().put(GoodsLevelEnum.Package.getValue(),
                Lists.newArrayList(2L));

        doReturn(Result.success(null)).when(mockBatchedInfoService).listBatched(any(BatchedRequest.class));
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("套装信息未找到", result.getMessage());

        // package data
        BatchedMapResponse batchedMapResponse = new BatchedMapResponse();
        HashMap<Long, BatchedInfoDto> batchedMap = new HashMap<>();
        BatchedInfoDto batchedInfoDto = new BatchedInfoDto();
        batchedInfoDto.setBatchedId(1L);
        batchedInfoDto.setMarketPriceMax(900L);
        batchedMap.put(1L, batchedInfoDto);
        batchedMapResponse.setBatchedMap(batchedMap);
        doReturn(Result.success(batchedMapResponse)).when(mockBatchedInfoService).listBatched(any(BatchedRequest.class));

        // ssu + suit
        couponConfigVO.getGoodsRuleVO().getGoodsInclude().put(GoodsLevelEnum.Ssu.getValue(), Lists.newArrayList(3L));
        couponConfigVO.getGoodsRuleVO().getGoodsInclude().put(GoodsLevelEnum.Suit.getValue(), Lists.newArrayList(4L));

        doReturn(Result.success(null)).when(mockGoodsInfoNewService).getGoodsMultiInfo(any(GoodsMultiInfoRequest.class));
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("商品信息未找到", result.getMessage());

        // ssu suit data
        GoodsMultiInfoResponse goodsMultiInfoResponse = new GoodsMultiInfoResponse();
        HashMap<Long, GoodsMultiInfoDTO> goodsMap = new HashMap<>();
        GoodsMultiInfoDTO goodsMultiInfoDTO = new GoodsMultiInfoDTO();
        goodsMultiInfoDTO.setProductId(1L);
        goodsMultiInfoDTO.setGoodsId(1L);
        goodsMultiInfoDTO.setMarketPrice(900L);
        goodsMultiInfoDTO.setItemType(GoodsItemTypeEnum.SUIT.getValue());
        goodsMap.put(1L, goodsMultiInfoDTO);
        goodsMultiInfoResponse.setGoodsMap(goodsMap);
        doReturn(Result.success(goodsMultiInfoResponse)).when(mockGoodsInfoNewService).getGoodsMultiInfo(any(GoodsMultiInfoRequest.class));

        // lowPrice
        skuInfoResponse.getSkuMap().remove(2L);
        doReturn(Result.success(skuInfoResponse)).when(mockSkuInfoService).listSku(any(SkuRequest.class));

        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertTrue(result.getMessage().startsWith("优惠券门槛"));
        Assertions.assertTrue(result.getMessage().endsWith("无法使用N元券，请检查后提交！"));

        // gift
        couponConfigVO.getPromotionRuleVO().setPromotionType(PromotionTypeEnum.GIFT.getValue());
        request.setBizType(BizPlatformEnum.CAR_SHOP.getCode());
        couponConfigVO.getUseChannel().clear();
        couponConfigVO.getUseChannel().put(UseChannelsEnum.CAR_SHOP.getValue(), null);
        couponConfigVO.getPromotionRuleVO().setPromotionValue(0L);

        nacosSwitchConfig.setCarShopGiftSsuWhitelist(Collections.emptyList());
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("商品白名单为空", result.getMessage());
        nacosSwitchConfig.setCarShopGiftSsuWhitelist(Lists.newArrayList("1"));

        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("有商品不在“礼品券可用商品白名单”内，请检查适用商品，或联系产品同学添加白名单", result.getMessage());
        nacosSwitchConfig.setCarShopGiftSsuWhitelist(Lists.newArrayList("1", "2", "3", "4"));

        doReturn(Result.success(null)).when(mockGoodsInfoNewService).getGoodsMultiInfo(any(GoodsMultiInfoRequest.class));
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("商品信息未找到", result.getMessage());
        nacosSwitchConfig.setCarShopGiftSsuWhitelist(new ArrayList<>());

        // thirdCategories
        couponConfigVO.getGoodsRuleVO().setScopeType(CouponScopeTypeEnum.Categories.getValue());
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("通过选择品类建券，分类信息不能为空", result.getMessage());
        couponConfigVO.getGoodsRuleVO().setCategoryIds(Sets.newHashSet(1L));

        couponConfigVO.getGoodsRuleVO().setGoodsInclude(null);
        result = dubboCouponReviewService.createCouponReview(request);
        Assertions.assertEquals("通过选择品类建券，商品信息不能为空", result.getMessage());
    }
}
