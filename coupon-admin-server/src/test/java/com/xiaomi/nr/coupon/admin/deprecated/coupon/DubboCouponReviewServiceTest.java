package com.xiaomi.nr.coupon.admin.deprecated.coupon;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponReviewListVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.WorkFlowConfigVo;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.request.CouponReviewListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.request.CouponReviewRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.response.CouponCommonConfigResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.response.CouponReviewDetailResponse;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponReviewService;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.common.domain.coupon.couponreview.CouponTaskReviewService;
import com.xiaomi.nr.coupon.admin.common.domain.coupon.couponreview.CouponReviewService;
import com.xiaomi.nr.coupon.admin.global.domain.coupon.couponconfig.bpm.GlobalCouponTaskPageGenerator;
import com.xiaomi.nr.coupon.admin.global.domain.coupon.couponconfig.bpm.GlobalPageGenerator;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.po.CouponConfigReviewPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.po.CouponTaskReviewPO;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.global.dev.base.HeraContextKeyValueHolder;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/9/26
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponAdminBootstrap.class)
@ActiveProfiles("sg_staging")
public class DubboCouponReviewServiceTest {

    @Autowired
    private DubboCouponReviewService dubboCouponReviewService;

    @Test
    public void testCouponReviewList() {
        CouponReviewListRequest request = new CouponReviewListRequest();
        request.setCouponType(1);
        Result<BasePageResponse<CouponReviewListVO>> response = dubboCouponReviewService.couponReviewList(request);
        log.info("testCouponReviewList res = {}", GsonUtil.toJson(response));
    }

    @Test
    public void testCouponReviewDetail() {
        CouponReviewRequest request = new CouponReviewRequest();
        request.setReviewId(44L);
        CouponReviewDetailResponse response = dubboCouponReviewService.couponReviewDetail(request).getData();

        log.info("testCouponReviewDetail res = {}", GsonUtil.toJson(response));
    }

    @Test
    public void testGetCommonConfigInfoList() {
        Result<List<WorkFlowConfigVo>> workFlowConfigVoList = null;
        try {
            Result<CouponCommonConfigResponse> workFlowConfigVoList1 = dubboCouponReviewService.getCommonConfigInfoList();
            log.info("workFlowConfigVoList1 = {}", GsonUtil.toJson(workFlowConfigVoList1));
        } catch (Exception e) {
            log.info("e:",e);
            throw new RuntimeException(e);
        }

        log.info("testCouponReviewDetail res = {}", GsonUtil.toJson(workFlowConfigVoList));
    }
    @Test
    public void testWorkflowList2() throws Exception {
        CouponConfigReviewPO reviewPO = couponReviewService.selectById(127l);
        String s = globalPageGenerator.handlerHtml(reviewPO);

        log.info("testCouponReviewDetail res = {}", s);
    }

    @Test
    public void testWorkflowList3(){
        CouponTaskReviewPO reviewPO = couponTaskReviewService.getTaskReviewPO(22364L);
        String s = null;
        try {
            s = globalCouponTaskPageGenerator.buildBpmHtmlJson(reviewPO);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        log.info("testCouponReviewDetail res = {}", s);
    }

    @Autowired
    private CouponTaskReviewService couponTaskReviewService;

    @Autowired
    private CouponReviewService couponReviewService;

    @Autowired
    private GlobalCouponTaskPageGenerator globalCouponTaskPageGenerator;

    @Autowired
    private GlobalPageGenerator globalPageGenerator;
}

