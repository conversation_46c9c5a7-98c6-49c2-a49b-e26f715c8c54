package com.xiaomi.nr.coupon.admin.deprecated.coupon;

import com.xiaomi.nr.coupon.admin.api.dto.ecard.request.ListEcardIdRequest;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponsync.CouponSyncService;
import com.xiaomi.nr.coupon.admin.util.StringUtil;
import com.xiaomi.nr.coupon.api.dto.coupon.AssignMissionItemDto;
import com.xiaomi.nr.coupon.api.dto.coupon.AssignRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponAdminBootstrap.class)
// @ActiveProfiles("dev")
public class couponServiceTest {

    @Autowired
    private CouponSyncService couponSyncService;

    @Test
    public void assignCouponTest() throws Exception {

        AssignRequest assign = new AssignRequest();
        assign.setOrgCode("123");
        assign.setAppId("XM2106");
        assign.setUserId(315879879845L);
        assign.setSendChannel("other");
        assign.setRequestId("123526467");
        List<AssignMissionItemDto> assignMissionItemDtos = new ArrayList<>();
        assign.setItems(assignMissionItemDtos);


    }


    @Test
    public void tokenTest() throws Exception {
        ListEcardIdRequest request = new ListEcardIdRequest();
        List<Long> list = new ArrayList<>();
        list.add(123L);
        request.setAppId("XM2106");
        request.setEcardIdList(list);


        String result = StringUtil.getToken(request,"XM2106","123");

        String token = DigestUtils.md5Hex("appId=XM2106&ecardIdList=[123]&secret=123");

        if (StringUtils.equals(result,token)){
            System.out.println("************************************");
        }
        //getFiledName(request);


    }

    private String[] getFiledName(Object o){
        Field[] fields=o.getClass().getDeclaredFields();
        String[] fieldNames=new String[fields.length];
        for(int i=0;i<fields.length;i++){
            fieldNames[i]=fields[i].getName();
            System.out.println(fields[i]);
        }
        return fieldNames;
    }



    @Test
    public void syncXiguaMarketCouponMessageTest() throws Exception {
        couponSyncService.syncXiguaMarketCouponMessage();
    }
}
