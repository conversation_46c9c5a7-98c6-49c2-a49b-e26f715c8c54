package com.xiaomi.nr.coupon.admin.deprecated.point;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.UserPointsListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.pointadmin.UsersPointDto;
import com.xiaomi.nr.coupon.admin.api.service.pointadmin.DubboUserPointsService;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.domain.pointadmin.PointBatchScheduleService;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/12/13
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponAdminBootstrap.class)
@ActiveProfiles("dev")
public class PointBatchScheduleServiceTest {

    @Resource
    private PointBatchScheduleService pointBatchScheduleService;

    @Resource
    private DubboUserPointsService dubboUserPointsService;

    @Test
    public void riskWarning() {
        pointBatchScheduleService.riskWarning();
    }

    @Test
    public void budgetRelease() {
        pointBatchScheduleService.budgetRelease();
    }

    @Test
    public void userPointList() {
        UserPointsListRequest req = new UserPointsListRequest();
        req.setMid(2270998577L);
        Result<BasePageResponse<UsersPointDto>> resp = dubboUserPointsService.getUserPointsList(req);
        System.out.println(GsonUtil.toJson(resp));
    }
}
