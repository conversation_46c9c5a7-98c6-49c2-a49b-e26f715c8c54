package com.xiaomi.nr.coupon.admin.deprecated.task;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponTaskListVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.*;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.response.CouponCodeTaskListVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.response.CouponConfigDescResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.response.CouponFillTaskDetailResponse;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponTaskService;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.domain.coupon.fillcoupontask.FillCouponService;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.task.FillCouponTaskMapper;
import com.xiaomi.nr.coupon.admin.infrastructure.rpc.http.UserApiService;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Collection;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponAdminBootstrap.class)
@ActiveProfiles("sg_staging")
public class DubboCouponTaskServiceTest {

    @Autowired
    private DubboCouponTaskService dubboCouponTaskService;


    @Autowired
    private FillCouponTaskMapper mapper;

    @Autowired
    private FillCouponService fillCouponService;



    @Test
    public void userTagTest(){
        //userTagProxyService.getUserTagInfo();
    }

    @Test
    public void taskListTest(){

        CouponFillTaskListRequest request = new CouponFillTaskListRequest();
        //request.setCouponType(1);
        request.setBizType(6);
        request.setAreaId("HK");
        Result<BasePageResponse<CouponTaskListVO>> result = dubboCouponTaskService.couponFillTaskList(request);
        Assert.assertNotNull(result);
        System.out.println(result);

    }


    @Test
    public void taskDetailTest(){

        CouponFillTaskDetailRequest request = new CouponFillTaskDetailRequest();
        request.setTaskId(29L);

        Result<CouponFillTaskDetailResponse> result =  dubboCouponTaskService.taskDetail(request);
        Assert.assertNotNull(result);
        System.out.println(result);

    }
    @Test
    public void taskDetailTest2(){

        CouponConfigDescRequest request = new CouponConfigDescRequest();
        request.setId(1L);
        request.setAreaId("PL");

        Result<CouponConfigDescResponse> couponConfigDescResponseResult = dubboCouponTaskService.queryCouponConfigDesc(request);
        Assert.assertNotNull(couponConfigDescResponseResult);
        System.out.println(couponConfigDescResponseResult);

    }


    @Test
    public void retryTask(){
        ReStartTaskRequest r = new ReStartTaskRequest();
        r.setTaskId(10L);
        dubboCouponTaskService.taskRetry(r);
    }

    @Test
    public void testCodeTaskList() {
        try {
            CouponCodeTaskListRequest request = new CouponCodeTaskListRequest();
            //request.setConfigId(14780L);
            //request.setStatus(4);
            request.setCouponName("满件");
            Result<BasePageResponse<CouponCodeTaskListVO>> res = dubboCouponTaskService.couponCodeTaskList(request);
            Collection<CouponCodeTaskListVO> list = res.getData().getList();
            System.out.println("===================");
            System.out.println(list.size());
            for (CouponCodeTaskListVO couponCodeTaskListVO : list) {
                System.out.println(couponCodeTaskListVO);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Autowired
    private UserApiService userApi;
    @Test
    public void trest() throws BizError {
        System.out.println(userApi.userBatchData("45773"));
    }

    @Test
    public void testDownLoadCode() {
        CouponCodeDownloadRequest request = new CouponCodeDownloadRequest();
        request.setConfigId(30318L);
        request.setIsAdmin(1);
        dubboCouponTaskService.downloadCode(request);
    }

}
