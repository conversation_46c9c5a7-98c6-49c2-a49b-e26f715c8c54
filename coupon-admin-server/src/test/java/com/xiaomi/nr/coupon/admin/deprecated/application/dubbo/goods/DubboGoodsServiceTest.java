package com.xiaomi.nr.coupon.admin.deprecated.application.dubbo.goods;

import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.GoodsRuleVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.PromotionRuleVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.UseTermVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.goods.request.GoodsDiscountLevelRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.goods.response.GoodsDiscountLevelResponse;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboGoodsService;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025-01-21 20:21
*/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponAdminBootstrap.class)
@ActiveProfiles("dev")
public class DubboGoodsServiceTest {

    @Autowired
    private DubboGoodsService goodsService;

    @Test
    public void getGoodsDiscountLevelTest() {
        GoodsDiscountLevelRequest request = new GoodsDiscountLevelRequest();
        PromotionRuleVO promotionRuleVO = new PromotionRuleVO();
        promotionRuleVO.setPromotionType(5);
        promotionRuleVO.setPromotionValue(1L);
        request.setPromotionRuleVO(promotionRuleVO);
        GoodsRuleVO goodsRuleVO = new GoodsRuleVO();
        goodsRuleVO.setScopeType(1);
        goodsRuleVO.setGoodsDepartments(Lists.newArrayList(8));
        Map<String, List<Long>> goodsInclude = new HashMap<>();
        goodsInclude.put("sku", Lists.newArrayList());
        goodsInclude.put("package", Lists.newArrayList());
        goodsInclude.put("suit", Lists.newArrayList(600020252L, 600020257L));
        goodsInclude.put("ssu", Lists.newArrayList());
        goodsRuleVO.setGoodsInclude(goodsInclude);
        goodsRuleVO.setGoodsExclude(new HashMap<>());
        goodsRuleVO.setCategoryIds(new HashSet<>());
        request.setGoodsRuleVO(goodsRuleVO);
        request.setUseChannel(Lists.newArrayList(6));
        request.setSendScene("98EDA5B9CA1D6239C457BCD65D27A4F1");
        request.setDiscountLevel(new BigDecimal("8.5"));
        request.setCouponType(1);
        UseTermVO useTermVO = new UseTermVO();
        useTermVO.setUseTimeType(2);
        useTermVO.setUseDuration(120);
        useTermVO.setTimeGranularity(2);
        request.setUseTermVO(useTermVO);
        Result<GoodsDiscountLevelResponse> result =  goodsService.getGoodsDiscountLevel(request);
        log.info("result:{}", GsonUtil.toJson(result));

    }
}
