package com.xiaomi.nr.coupon.admin.deprecated.task;


import com.xiaomi.newretail.bpm.api.model.callback.OnStatusChangedRequest;
import com.xiaomi.newretail.bpm.api.model.callback.OnStatusChangedResponse;
import com.xiaomi.newretail.bpm.api.model.callback.ProcessAction;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.response.CouponReviewInfoResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CouponFillReviewCancelRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CouponFillReviewDetailRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CreateCouponFillReviewRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CreateFillCouponTaskRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.response.CouponFillReviewCancelResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.response.CouponFillReviewDetailResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.response.CreateCouponFillReviewResponse;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponTaskReviewService;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.common.application.dubbo.coupon.DubboCouponCallBackServiceImpl;
import com.xiaomi.nr.coupon.admin.common.application.dubbo.coupon.convert.TaskReviewConvert;
import com.xiaomi.nr.coupon.admin.common.infrastructure.repository.mysqldao.CouponTaskReviewRepository;
import com.xiaomi.nr.coupon.admin.domain.coupon.fillcoupontask.FillCouponConvert;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.task.po.FillCouponTaskPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.po.CouponTaskReviewPO;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.rpc.RpcContext;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.PostConstruct;

/**
 * @Description:
 * @Date: 2022.05.
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponAdminBootstrap.class)
@ActiveProfiles("sg_staging")
public class ReviewTest {


    @Autowired
    private DubboCouponCallBackServiceImpl dubboCouponBpmCallBackService;


    @Autowired
    private DubboCouponTaskReviewService taskReviewService;


    @Test
    public void testQueryReviewInfo() {
        String bpmKey = "7b68ce89-e25b-11ec-b402-0242ac190002";
        Result<CouponReviewInfoResponse> res = dubboCouponBpmCallBackService.queryReviewInfo(bpmKey);
        CouponReviewInfoResponse response = res.getData();
        System.out.println(response.getCouponTypeText());
    }
    @Test
    public void testCallBack() {
        OnStatusChangedRequest request = new OnStatusChangedRequest();
        request.setProcessInstanceId("d68fd8f8-e933-11ef-bb29-c25797a75927");
        request.setAction(ProcessAction.Accept);
        request.setOperator("p-xueqizheng");
        request.setFinished(true);
        log.info(GsonUtil.toJson(request));
        log.info(GsonUtil.toJson(request));
        Result<OnStatusChangedResponse> result = null;
        try {
            result = dubboCouponBpmCallBackService.onStatusChanged(request);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        System.out.println("result:" + GsonUtil.toJson(result));
    }



    @Test
    public void testReview() {
        CreateCouponFillReviewRequest request = new CreateCouponFillReviewRequest();
        request.setTaskName("测试123");
        request.setBatchId(52631L);
        request.setConfigId(30620L);
        request.setBatchName("AB测非米】-低-中-高_拆分-拆分包2");
        request.setPlanCount(90L);
        request.setUserGroupSize(123);

        System.out.println(GsonUtil.toJson(taskReviewService.createTaskReview(request)));
    }

    @Test
    public void testTaskReview() {
        String content = "{\n" +
                "        \"taskName\": \"xqz-test-coupon-0317-2\",\n" +
                "        \"configId\": \"83000\",\n" +
                "        \"userGroupType\": 1,\n" +
                "        \"customizeType\": 2,\n" +
//                "        \"workflowId\": 100001,\n" +
                "        \"areaId\": \"HK\",\n" +
                "        \"uidList\": [\n" +
                "            \"3150442913\"\n" +
                "        ],\n" +
                "        \"planCount\": 4,\n" +
                "        \"token\": \"d314f9fce27a95d2a7fef19485b120c9\"\n" +
                "    }";
        CreateCouponFillReviewRequest request = GsonUtil.fromJson(content, CreateCouponFillReviewRequest.class);
        Result<CreateCouponFillReviewResponse> taskReview = taskReviewService.createTaskReview(request);

        System.out.println(GsonUtil.toJson(taskReview));
    }

    @Test
    public void testTaskReview2() {
        CouponFillReviewCancelRequest request = new CouponFillReviewCancelRequest();
        request.setReviewId(22369L);
        request.setAreaId("HK");
        Result<CouponFillReviewCancelResponse> responseResult = taskReviewService.cancelTaskReview(request);


        System.out.println(GsonUtil.toJson(responseResult));
    }
    @Test
    public void testTaskReview22() {
        CouponFillReviewCancelRequest request = new CouponFillReviewCancelRequest();
        request.setReviewId(22369L);
        request.setAreaId("HK");
        Result<CouponFillReviewCancelResponse> responseResult = taskReviewService.cancelTaskReviewHttp(request);


        System.out.println(GsonUtil.toJson(responseResult));
    }
    @Test
    public void testTaskReview3() {
        CouponFillReviewDetailRequest request = new CouponFillReviewDetailRequest();
        request.setId(22396L);
        Result<CouponFillReviewDetailResponse> responseResult = taskReviewService.taskReviewDetail(request);


        System.out.println(GsonUtil.toJson(responseResult));
    }    @Test
    public void testTaskReview4() throws Exception {

        try {
            CouponTaskReviewPO reviewPO = couponTaskReviewRepository.getReviewByBpmKey("f5bc4997-199e-11f0-b297-5686d827db7d");
            //灌券任务落库
            CreateFillCouponTaskRequest fillCouponTaskRequest = taskReviewConvert.convertToCouponTaskRequest(reviewPO.getCompressInfo());

            FillCouponTaskPO taskPO = fillCouponConvert.convertFillCouponTaskPO(fillCouponTaskRequest);


            System.out.println(GsonUtil.toJson(taskPO));
        } catch (Exception e) {
            log.info("异常：",e);
        }
        System.out.println("end...");
    }

    @Autowired
    private CouponTaskReviewRepository couponTaskReviewRepository;

    @Autowired
    private TaskReviewConvert taskReviewConvert;

    @Autowired
    private FillCouponConvert fillCouponConvert;





    @PostConstruct
    private void init() {
        RpcContext.getContext().getAttachments().put("$upc_account", "p-xueqizheng");
    }

}
