package com.xiaomi.nr.coupon.admin.deprecated.infrastructure.rpc.goods;

import com.xiaomi.goods.gms.dto.sku.SkuInfoDto;
import com.xiaomi.nr.coupon.admin.api.enums.BizSubTypeEnum;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.infrastructure.rpc.goods.GmsProxyService;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @date 2024/2/2 16:12
 */
@Slf4j
@ActiveProfiles("dev")
@SpringBootTest(classes = CouponAdminBootstrap.class)
public class GmsProxyServiceTest {
    @Autowired
    private GmsProxyService gmsProxyService;

    @Test
    public void queryListBySsuIdsTest() {
        log.info("============================");
    }

    @Test
    public void testQueryListBySkuIds() throws Exception {
        List<Long> skuIds = Arrays.asList(4999L, 5407L);
        boolean isNeedStock = false;
        boolean isNeedCategory = true;
        boolean needMiSupportShipment = false;
        List<Integer> bizSubTypeList = Arrays.asList(BizSubTypeEnum.ORDINARY_3C.getCode(), BizSubTypeEnum.CARRIER_PAN_COMPLETE.getCode());

        List<SkuInfoDto> result = gmsProxyService.queryListBySkuIds(skuIds, isNeedStock, isNeedCategory, needMiSupportShipment, bizSubTypeList);

        System.out.println(GsonUtil.toJson(result));
        assertNotNull(result);
        assertEquals(result.size(), 2);
    }
}