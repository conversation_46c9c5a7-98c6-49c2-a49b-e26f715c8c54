package com.xiaomi.nr.coupon.admin.deprecated.schedule;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.schedule.RefreshCouponUserWhiteListReq;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.schedule.RefreshCouponUserWhiteListResp;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.common.application.dubbo.couponadmin.DubboCouponScheduleServiceImpl;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @description 
 * <AUTHOR>
 * @date 2025-04-01 15:58
*/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponAdminBootstrap.class)
@ActiveProfiles("dev")
public class CouponScheduleServiceTest {

    @Autowired
    private DubboCouponScheduleServiceImpl dubboCouponScheduleService;

    @Test
    public void testRefreshCouponUserWhiteList() {
        RefreshCouponUserWhiteListReq req = new RefreshCouponUserWhiteListReq();
        req.setFullRefresh(false);
        Result<RefreshCouponUserWhiteListResp> resp = dubboCouponScheduleService.refreshCouponUserWhiteList(req);
        log.info("refreshCouponUserWhiteList result:{}", GsonUtil.toJson(resp));
    }
}
