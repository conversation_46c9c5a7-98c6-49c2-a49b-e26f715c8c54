package com.xiaomi.nr.coupon.admin.deprecated.sketch;

import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.*;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.sketch.request.CouponCreateSketchRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.sketch.request.CouponSketchDeleteRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.sketch.request.CouponSketchListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.sketch.request.CouponSketchRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.sketch.response.CouponCreateSketchResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.sketch.response.CouponSketchDetailResponse;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponSketchService;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.*;
import com.xiaomi.nr.coupon.admin.enums.sketch.CouponSketchOptType;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Description: 优惠券草稿
 * @Date: 2022.03.11 17:12
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ActiveProfiles({"staging"})
@SpringBootTest(
        classes = {
                CouponAdminBootstrap.class,
                DubboCouponSketchServiceTest.class
        })
public class DubboCouponSketchServiceTest {

    @Autowired
    private DubboCouponSketchService dubboCouponSketchService;

    @Test
    public void testCreateSketch() {
        try {
            CouponCreateSketchRequest request = new CouponCreateSketchRequest();
            CouponConfigVO couponConfigVO = generateConfigVO();

            PromotionRuleVO promotionRuleVO = new PromotionRuleVO();
            promotionRuleVO.setPromotionType(PromotionTypeEnum.DirectReduce.getValue());
            couponConfigVO.setPromotionRuleVO(promotionRuleVO);

            request.setCouponConfigVO(couponConfigVO);
            request.setBizType(BizPlatformEnum.GLOBAL_NEW_RETAIL.getCode());
            //request.setApplyAttachment(Arrays.asList(" "));
            request.setType(CouponSketchOptType.CREATE.getCode());
            Result<CouponCreateSketchResponse> result = dubboCouponSketchService.createSketch(request);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Test
    public void testUpdateSketch() {
        try {
            CouponCreateSketchRequest request = new CouponCreateSketchRequest();
            CouponConfigVO couponConfigVO = generateConfigVO();
            request.setType(CouponSketchOptType.UPDATE.getCode());
            Result<CouponCreateSketchResponse> result = dubboCouponSketchService.createSketch(request);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }




    private CouponConfigVO generateConfigVO() {
        CouponConfigVO couponConfigVO = new CouponConfigVO();
        couponConfigVO.setName("rrrrrrrrrrrr");
        couponConfigVO.setCouponDesc("hcsxcd");
        couponConfigVO.setSendSceneType(7);
        couponConfigVO.setSendScene("DCCC8C085E79839661D9B93E9A996EC8");
        couponConfigVO.setStartFetchTime(new Date(2022 - 1900, 5, 7));
        couponConfigVO.setEndFetchTime(new Date(2022 - 1900, 5, 15));


        UseTermVO useTermVO = new UseTermVO();
        useTermVO.setUseTimeType(UseTimeTypeEnum.ABSOLUTE.getValue());
        //useTermVO.setUseTimeType(UseTimeTypeEnum.RELATIVE.getValue());
        useTermVO.setStartUseTime(new Date(2022 - 1900, 6, 25));
        useTermVO.setEndUseTime(new Date(2022 - 1900, 6, 26));
        //固定有效时，使用时长会不会生效
        //useTermVO.setUseDuration(2400);
        couponConfigVO.setUseTermVO(useTermVO);


        Map<Integer, UseChannelVO> channelMap = new HashMap<>();
        UseChannelVO useChannelVO = new UseChannelVO();
        useChannelVO.setAll(true);
        useChannelVO.setLimitIds(new ArrayList<>());
        channelMap.put(UseChannelEnum.MiAuthorized.getCode(), useChannelVO);
        couponConfigVO.setUseChannel(channelMap);


        PromotionRuleVO promotionRuleVO = new PromotionRuleVO();
        promotionRuleVO.setPromotionType(PromotionTypeEnum.ConditionReduce.getValue());
        promotionRuleVO.setPromotionValue(2000L);
        promotionRuleVO.setBottomPrice(100L);
        promotionRuleVO.setBottomCount(2);
        promotionRuleVO.setBottomType(2);
        promotionRuleVO.setMaxReduce(2000L);
        couponConfigVO.setPromotionRuleVO(promotionRuleVO);


        DistributionRuleVO distributionRuleVO = new DistributionRuleVO();
        distributionRuleVO.setApplyCount(500);
        distributionRuleVO.setFetchLimit(4);
        couponConfigVO.setDistributionRuleVO(distributionRuleVO);

        ExtPropVO extPropVO = new ExtPropVO();
        extPropVO.setPostFree(Integer.parseInt(IsPostFreeEnum.No.getMysqlValue()));
        extPropVO.setShare(Integer.parseInt(IsShareEnum.No.getMysqlValue()));
        extPropVO.setArea(GivenAreaEnum.NO.getType());
        couponConfigVO.setExtProp(extPropVO);

        couponConfigVO.setAreaIds(new ArrayList<>());

        GoodsRuleVO goodsRuleVO = new GoodsRuleVO();
        goodsRuleVO.setScopeType(CouponScopeTypeEnum.Goods.getValue());
        Map<String, List<Long>> goodMap = new HashMap<>();
        List<Long> skuList = Arrays.asList(1234567L, 1234568L);
        goodMap.put("sku", skuList);
        goodsRuleVO.setGoodsInclude(goodMap);
        //goodsRuleVO.setGoodsExclude();
        //goodsRuleVO.setCategoryIds(Arrays.asList(1L, 2L));

        couponConfigVO.setGoodsRuleVO(goodsRuleVO);

        couponConfigVO.setSendPurpose(SendPurposeEnum.Active.getValue());
        Map<Integer, Integer> costMap = new HashMap<>();
        costMap.put(12, 80);
        couponConfigVO.setCostShare(costMap);
        couponConfigVO.setCost(BigDecimal.TEN);
        couponConfigVO.setCreator("liuwei");
        couponConfigVO.setDepartmentId(100);
        //couponConfigVO.setStatus();
        couponConfigVO.setSource(2);
        return couponConfigVO;
    }

    @Test
    public void testList() {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss" );
            CouponSketchListRequest request = new CouponSketchListRequest();
            request.setStartFetchTime(new Date(2022 - 1900, 4, 12, 16, 0));
            request.setEndFetchTime(new Date(2022 - 1900, 5, 12, 16, 0));
            Result<BasePageResponse<CouponSketchListVO>> result = dubboCouponSketchService.querySketchList(request);
            BasePageResponse<CouponSketchListVO> data = result.getData();
            System.out.println(data.getList().size());
            System.out.println(data.getTotalCount());
            System.out.println(data.getTotalPage());
            Iterator<CouponSketchListVO> iterator = data.getList().iterator();
            while (iterator.hasNext()) {
                CouponSketchListVO couponSketchListVO = iterator.next();
                System.out.println(sdf.format(couponSketchListVO.getStartFetchTime()) + "--" + sdf.format(couponSketchListVO.getEndFetchTime()));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testDetail() {
        try {
            CouponSketchRequest request = new CouponSketchRequest();
            request.setSketchId(1);
            Result<CouponSketchDetailResponse> result = dubboCouponSketchService.querySketchDetail(request);
            CouponSketchDetailResponse response = result.getData();
            System.out.println(response.getCouponConfigVO());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testDelete() {
        try {
            CouponSketchDeleteRequest request = new CouponSketchDeleteRequest();
            request.setSketchId(1);
            dubboCouponSketchService.deleteSketch(request);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
