package com.xiaomi.nr.coupon.admin.deprecated.coupon;

import com.google.common.collect.Maps;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.BudgetInfoDto;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.*;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponConfigDTO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponInfoRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponUpdateStatusRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.OperateCouponConfigRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.response.CouponDetailResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.response.SaveCouponConfigResponse;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.CouponAdminService;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.common.application.dubbo.coupon.convert.CouponAdminConvert;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.BottomTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponConfigStatusEnum;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponAdminBootstrap.class)
@ActiveProfiles("sg_staging")
public class CouponAdminServiceTest {

    @Autowired
    private CouponAdminService couponAdminService;

    @Test
    public void testCouponConfigList(){
        CouponListRequest req = new CouponListRequest();
        // req.setId(137068L);
        req.setBizPlatform(BizPlatformEnum.CAR_AFTER_SALE.getCode());

        Result<BasePageResponse<CouponConfigListVO>> res = couponAdminService.couponConfigList(req);
        System.out.println("res is " + res);
    }


    @Test
    public void testCouponConfigDetail(){
        CouponInfoRequest req = new CouponInfoRequest();
        req.setId(58L);
        Result<CouponDetailResponse> res = couponAdminService.couponConfigDetail(req);
        System.out.println("res is " + res);
    }

    @Test
    public void testUpdateConfigStatus(){
        CouponUpdateStatusRequest req = new CouponUpdateStatusRequest();
        req.setId(105383L);
        req.setOperateType(CouponConfigStatusEnum.ONLINE.getCode());
        req.setOperator("caoxiaopeng1");
        Result<String> res = couponAdminService.updateConfigStatus(req);
        System.out.println("res is " + res);
    }

    @Test
    public void testSaveCouponConfig(){
        log.info("==================================================================");

        // OperateCouponConfigRequest req = mockOperateCouponConfigReq();
        // System.out.println("req is " + req);
        //
        // SaveCouponConfigResponse res = couponAdminService.saveCouponConfig(req).getData();
        // System.out.println("res is " + res);

        OperateCouponConfigRequest request = new OperateCouponConfigRequest();

        CouponConfigDTO couponConfigDTO = new CouponConfigDTO();
        couponConfigDTO.setId(0L);
        couponConfigDTO.setCouponDesc("test coupon desc");
        couponConfigDTO.setName("券名称3");
        couponConfigDTO.setSendScene("test sendScene");
        couponConfigDTO.setCouponType(1);
        couponConfigDTO.setStatus(0);
        couponConfigDTO.setSendPurpose(0);
        couponConfigDTO.setSendScene("11BFB15C0E2BA4D03DFF93D95C843D0E");
        couponConfigDTO.setUseChannel(Maps.newHashMap());
        couponConfigDTO.setAreaIds(Lists.newArrayList());

        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, 2023);
        cal.set(Calendar.MONTH, Calendar.OCTOBER);
        cal.set(Calendar.DAY_OF_MONTH, 10);
        couponConfigDTO.setStartFetchTime(cal.getTime());

        cal.set(Calendar.YEAR, 2023);
        cal.set(Calendar.MONTH, Calendar.OCTOBER);
        cal.set(Calendar.DAY_OF_MONTH, 20);
        couponConfigDTO.setEndFetchTime(cal.getTime());

        HashMap<Integer, Integer> costShare = new HashMap<>();
        costShare.put(21, 100);
        couponConfigDTO.setCostShare(costShare);

        UseTermVO useTermVO = new UseTermVO();
        // 相对时间粒度 天
        useTermVO.setTimeGranularity(2);
        // 使用有效期类型 1 固定有效,2 相对有效期
        useTermVO.setUseTimeType(2);
        useTermVO.setUseDuration(240);
        couponConfigDTO.setUseTermVO(useTermVO);

        PromotionRuleVO promotionRuleVO = new PromotionRuleVO();
        promotionRuleVO.setPromotionType(4);
        promotionRuleVO.setPromotionValue(10L);
        couponConfigDTO.setPromotionRuleVO(promotionRuleVO);

        DistributionRuleVO distributionRuleVO = new DistributionRuleVO();
        distributionRuleVO.setApplyCount(10L);
        distributionRuleVO.setFetchLimit(1L);
        couponConfigDTO.setDistributionRuleVO(distributionRuleVO);

        GoodsRuleVO goodsRuleVO = new GoodsRuleVO();
        couponConfigDTO.setGoodsRuleVO(goodsRuleVO);

        ExtPropVO extPropVO = new ExtPropVO();
        couponConfigDTO.setExtProp(extPropVO);

        request.setBizPlatform(3);
        request.setOperator("liwenlong3");
        request.setCouponConfigDTO(couponConfigDTO);

        log.info("testSaveCouponConfig request = {}", GsonUtil.toJson(request));


        Result<SaveCouponConfigResponse> res = couponAdminService.saveCouponConfig(request);

        log.info("testSaveCouponConfig res = {}", GsonUtil.toJson(res));

        log.info("==================================================================");
    }

    @Test
    public void testUpdateCouponConfig() throws BizError {
        OperateCouponConfigRequest req = mockOperateCouponConfigReq();
        req.getCouponConfigDTO().setId(103756);
        req.getCouponConfigDTO().getPromotionRuleVO().setBottomType(BottomTypeEnum.OverCount.getValue());
        req.getCouponConfigDTO().setName("MS11 100元立减券cxp2");
        System.out.println("req is " + req);

        Result<Void> res = couponAdminService.updateCouponConfig(req);
        if(res.getCode() != GeneralCodes.OK.getCode()) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "业务平台非法");
        }
    }

    public OperateCouponConfigRequest mockOperateCouponConfigReq() {
        String reqJson =
                "{" +
                "    \"couponConfigDTO\": {" +
                "        \"name\": \"MS11 100元立减券cxp\"," +
                "        \"couponDesc\": \"不适用于秒杀、拼团、套装、众筹、第三方产品、虚拟产品等，涉及产品以优惠券实际使用情况为准。\"," +
                "        \"sendScene\": \"11BFB15C0E2BA4D03DFF93D95C843D0E\"," +
                "        \"sendSceneType\": 1," +
                "        \"startFetchTime\": \"2023-10-06T16:00:00.000Z\"," +
                "        \"endFetchTime\": \"2023-10-31T15:59:59.000Z\"," +
                "        \"useTermVO\": {" +
                "            \"useTimeType\": 1," +
                "            \"startUseTime\": \"2023-10-06T16:00:00.000Z\"," +
                "            \"endUseTime\": \"2023-10-31T15:59:59.000Z\"," +
                "            \"timeGranularity\": 2" +
                "        }," +
                "        \"useChannel\": {" +
                "            \"1\": {" +
                "                \"all\": true" +
                "            }," +
                "            \"2\": {" +
                "                \"all\": true," +
                "                \"limitIds\": []" +
                "            }," +
                "            \"3\": {" +
                "                \"all\": true," +
                "                \"limitIds\": []" +
                "            }," +
                "            \"4\": {" +
                "                \"all\": true," +
                "                \"limitIds\": []" +
                "            }" +
                "        }," +
                "        \"promotionRuleVO\": {" +
                "            \"promotionType\": 4," +
                "            \"promotionValue\": 10000," +
                "            \"bottomPrice\": 0," +
                "            \"maxReduce\": 0" +
                "        }," +
                "        \"distributionRuleVO\": {" +
                "            \"applyCount\": 1," +
                "            \"fetchLimit\": 1" +
                "        }," +
                "        \"extProp\": {" +
                "            \"postFree\": 2," +
                "            \"share\": 2," +
                "            \"area\": 2," +
                "            \"specialStore\": 1," +
                "            \"proMember\": 2" +
                "        }," +
                "        \"sendPurpose\": 4," +
                "        \"areaIds\": []," +
                "        \"couponType\": 1," +
                "        \"shipmentId\": -1," +
                "        \"cost\": 100," +
                "        \"goodsRuleVO\": {" +
                "            \"scopeType\": 1," +
                "            \"goodsDepartments\": [" +
                "                1" +
                "            ]," +
                "            \"goodsInclude\": {" +
                "                \"sku\": []," +
                "                \"package\": []," +
                "                \"ssu\": [" +
                "                    600002292, 600002274" +
                "                ]" +
                "            }," +
                "            \"goodsExclude\": []," +
                "            \"categoryIds\": []," +
                "            \"goodsDiscountLevelVO\": {" +
                "                \"discountLevel\": 8.5," +
                "                \"hasLowLevelGoods\": false" +
                "            }," +
                "            \"goodsSuitableVOs\": [" +
                "                {" +
                "                    \"goodsAmount\": 1," +
                "                    \"categoryTextList\": \"手机/小米系列/小米11\"" +
                "                }" +
                "            ]," +
                "            \"autoUpdateGoods\": 2" +
                "        }," +
                "        \"costShare\": {" +
                "            \"1\": 5," +
                "            \"2\": 15," +
                "            \"3\": 10," +
                "            \"4\": 20," +
                "            \"9\": 20," +
                "            \"10\": 30" +
                "        }," +
                "        \"source\": 2," +
                "        \"applyAttachment\": [" +
                "            {" +
                "                \"name\": \"MySQL是怎样运行的：从根儿上理解MySQL.pdf\"," +
                "                \"url\": \"https://staging-cnbj2-fds.api.xiaomi.net/nr-inner/202309251025_8efb18a0c8ab8682946d40033e82c2ce.pdf\"" +
                "            }" +
                "        ]," +
                "        \"token\": \"397bf1bd9472ea44b4248e891ba9e6f5\"" +
                "    }," +
                "    \"bizPlatform\": 3," +
                "    \"operator\": \"caoxiaopeng1\"" +
                "}";
        return GsonUtil.fromJson(reqJson, OperateCouponConfigRequest.class);
    }

    @Test
    public void saveCouponConfigTest1() throws Exception {
        CouponConfigVO configVO = new CouponConfigVO();
        configVO.setName("【测试】车商城套装立减3元");
        BudgetInfoDto budgetInfoDto = new BudgetInfoDto();
        budgetInfoDto.setLineNum(12893L);
        budgetInfoDto.setBudgetApplyNo("BR202409190034");
        budgetInfoDto.setBudgetCreateTime("2024-09-19 17:49:28");
        configVO.setBudgetInfoDto(budgetInfoDto);
        configVO.setCouponDesc("不适用于服务包、虚拟产品等，适用商品以优惠券实际使用情况为准。");
        configVO.setSendScene("E4B2EE72D1687E4B3BC263ADDB18C13F");
        configVO.setSendSceneType(103);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        configVO.setStartFetchTime(sdf.parse("2024-11-21T16:00:00.000Z"));
        configVO.setEndFetchTime(sdf.parse("2024-12-31T15:59:59.000Z"));
        UseTermVO useTermVO = new UseTermVO();
        useTermVO.setUseTimeType(1);
        useTermVO.setStartUseTime(sdf.parse("2024-11-21T16:00:00.000Z"));
        useTermVO.setEndUseTime(sdf.parse("2024-12-31T15:59:59.000Z"));
        useTermVO.setTimeGranularity(2);
        configVO.setUseTermVO(useTermVO);
        Map<Integer, UseChannelVO> useChannel = new HashMap<>();
        UseChannelVO useChannelVO = new UseChannelVO();
        useChannelVO.setAll(true);
        useChannel.put(6, useChannelVO);
        configVO.setUseChannel(useChannel);
        PromotionRuleVO promotionRuleVO = new PromotionRuleVO();
        promotionRuleVO.setPromotionType(4);
        promotionRuleVO.setPromotionValue(300L);
        promotionRuleVO.setBottomPrice(0L);
        promotionRuleVO.setMaxReduce(0L);
        configVO.setPromotionRuleVO(promotionRuleVO);
        DistributionRuleVO distributionRuleVO = new DistributionRuleVO();
        distributionRuleVO.setApplyCount(10);
        distributionRuleVO.setFetchLimit(1);
        configVO.setDistributionRuleVO(distributionRuleVO);
        ExtPropVO extPropVO = new ExtPropVO();
        extPropVO.setPostFree(2);
        extPropVO.setShare(2);
        extPropVO.setSpecialStore(2);
        extPropVO.setArea(2);
        extPropVO.setSpecialStore(2);
        extPropVO.setProMember(2);
        extPropVO.setPublicPromotion(2);
        configVO.setExtProp(extPropVO);
        configVO.setSendPurpose(8);
        configVO.setAreaIds(Collections.emptyList());
        configVO.setCouponType(1);
        configVO.setShipmentId(-1);
        configVO.setCost(new BigDecimal(30));
        GoodsRuleVO goodsRuleVO = new GoodsRuleVO();
        goodsRuleVO.setScopeType(1);
        goodsRuleVO.setGoodsDepartments(Collections.singletonList(1));
        Map<String, List<Long>> goodsInclude = new HashMap<>();
        goodsInclude.put("sku", Collections.emptyList());
        goodsInclude.put("suit", Collections.singletonList(600013314L));
        goodsInclude.put("ssu", Collections.emptyList());
        goodsRuleVO.setGoodsInclude(goodsInclude);
        GoodsDiscountLevelVO goodsDiscountLevelVO = new GoodsDiscountLevelVO();
        goodsDiscountLevelVO.setDiscountLevel(new BigDecimal("8.5"));
        goodsDiscountLevelVO.setHasLowLevelGoods(false);
        goodsRuleVO.setGoodsDiscountLevelVO(goodsDiscountLevelVO);
        goodsRuleVO.setAutoUpdateGoods(2);
        configVO.setGoodsRuleVO(goodsRuleVO);
        Map<Integer, Integer> costShare = new HashMap<>();
        costShare.put(1, 100);
        configVO.setCostShare(costShare);
        configVO.setSource(2);
        OperateCouponConfigRequest operateCouponConfigReq = CouponAdminConvert.convertOperateCouponConfigReq(configVO, 5);
        operateCouponConfigReq.setOperator("wanghaotian7");
        Result<SaveCouponConfigResponse> saveResult = couponAdminService.saveCouponConfig(operateCouponConfigReq);
        log.info("saveCouponConfigTest1 result:{}", GsonUtil.toJson(saveResult));
    }

}
