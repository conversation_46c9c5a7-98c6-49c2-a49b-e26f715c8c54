package com.xiaomi.nr.coupon.admin.deprecated.coupon;

import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.GoodsCouponRefreshService;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.couponconfig.GoodsConfigRedisDao;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @description 
 * <AUTHOR>
 * @date 2024-11-22 15:12
*/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponAdminBootstrap.class)
@ActiveProfiles("dev")
public class GoodsCouponRefreshServiceTest {

    @Autowired
    private GoodsCouponRefreshService goodsCouponRefreshService;

    @Autowired
    private GoodsConfigRedisDao goodsConfigRedisDao;

    @Test
    public void updateRedisGoodsCouponRelTest() throws Exception {
        Boolean fullLoad = false;
        String lastUpdateTime = "1732255566";
        goodsCouponRefreshService.updateRedisGoodsCouponRel(fullLoad, lastUpdateTime);
    }

    @Test
    public void getGoodsCouponRelTest() throws Exception {
        String result = goodsConfigRedisDao.getGoodsCouponRel("ssu", 600013314L);
        log.info("result:{}", result);
    }

}
