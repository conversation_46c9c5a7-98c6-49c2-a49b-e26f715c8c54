package com.xiaomi.nr.coupon.admin.unittest.api.service.couponadmin;

import com.xiaomi.mit.unittest.redis.SetupRedis;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.*;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.*;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.response.CouponDetailResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.response.SaveCouponConfigResponse;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.CouponAdminService;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.*;
import com.xiaomi.nr.coupon.admin.unittest.BaseUnitTest;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;



/**
 * <AUTHOR>
 * @description 券后台接口测试
 * @date 2025-05-09 10:30
 */
@Slf4j
public class CarShopCouponAdminServiceTest extends BaseUnitTest {

    @Autowired
    private CouponAdminService couponAdminService;

    private static final Long NOW_TIMESTAMP = System.currentTimeMillis();

    private static final Long THREE_MONTH = 30 * 24 * 60 * 60 * 1000L;

    /**
     * 创建车商城立减券
     */
    @Test
    public void testSaveCouponConfig_CarShop_DirectReduce() {
        OperateCouponConfigRequest request = new OperateCouponConfigRequest();

        CouponConfigDTO couponConfigDTO = new CouponConfigDTO();

        // 基础信息
        couponConfigDTO.setName("600013366 立减2元.");
        couponConfigDTO.setCouponDesc("不适用于服务包、虚拟产品等，适用商品以优惠券实际使用情况为准。");
        couponConfigDTO.setSendSceneType(103);
        couponConfigDTO.setSendScene("0669C15DC47BDFF1599DE3570CD0F272");

        // 时间信息
        couponConfigDTO.setStartFetchTime(new Date(NOW_TIMESTAMP));
        couponConfigDTO.setEndFetchTime(new Date(NOW_TIMESTAMP + THREE_MONTH));

        // 预算池信息
        couponConfigDTO.setBudgetApplyNo("BR202504280170");
        couponConfigDTO.setLineNum(72134L);
        couponConfigDTO.setBudgetCreateTime("2025-04-28 10:00:00");

        // 投放目的
        couponConfigDTO.setSendPurpose(SendPurposeEnum.Test.getValue());

        // 使用有效期
        UseTermVO useTermVO = new UseTermVO();
        useTermVO.setTimeGranularity(UseTimeGranularityEnum.DAY.getValue());
        useTermVO.setUseTimeType(UseTimeTypeEnum.ABSOLUTE.getValue());
        useTermVO.setStartUseTime(new Date(NOW_TIMESTAMP));
        useTermVO.setEndUseTime(new Date(NOW_TIMESTAMP + THREE_MONTH));
        useTermVO.setUseDuration(0);
        couponConfigDTO.setUseTermVO(useTermVO);

        // 使用渠道
        Map<Integer, UseChannelVO> useChannelMap = new HashMap<>();
        UseChannelVO useChannelVO = new UseChannelVO();
        useChannelVO.setAll(true);
        useChannelMap.put(6, useChannelVO);
        couponConfigDTO.setUseChannel(useChannelMap);

        // 优惠逻辑
        PromotionRuleVO promotionRuleVO = new PromotionRuleVO();
        promotionRuleVO.setPromotionType(PromotionTypeEnum.DirectReduce.getValue());
        promotionRuleVO.setBottomPrice(0L);
        promotionRuleVO.setBottomType(BottomTypeEnum.OverCount.getValue());
        promotionRuleVO.setBottomCount(1);
        promotionRuleVO.setMaxReduce(0L);
        promotionRuleVO.setPromotionValue(200L);
        couponConfigDTO.setPromotionRuleVO(promotionRuleVO);

        // 发放规则
        DistributionRuleVO distributionRuleVO = new DistributionRuleVO();
        distributionRuleVO.setApplyCount(10000);
        distributionRuleVO.setFetchLimit(100);
        couponConfigDTO.setDistributionRuleVO(distributionRuleVO);

        // 特殊规则
        ExtPropVO extPropVO = new ExtPropVO();
        extPropVO.setArea(2);
        extPropVO.setPublicPromotion(1);
        extPropVO.setProMember(2);
        extPropVO.setDisplayDate(1);
        extPropVO.setShare(2);
        couponConfigDTO.setExtProp(extPropVO);

        // 适用商品信息
        GoodsRuleVO goodsRuleVO = new GoodsRuleVO();
        goodsRuleVO.setScopeType(1);
        goodsRuleVO.setAutoUpdateGoods(2);
        goodsRuleVO.setGoodsDepartments(Lists.newArrayList(1));
        goodsRuleVO.setCategoryIds(Collections.emptySet());

        Map<String, List<Long>> goodsIncludeMap = new HashMap<>();
        goodsIncludeMap.put("suit", Lists.newArrayList(600013366L));
        goodsRuleVO.setGoodsInclude(goodsIncludeMap);

        couponConfigDTO.setGoodsRuleVO(goodsRuleVO);

        // 成本分摊
        couponConfigDTO.setCostShare(Collections.singletonMap(1, 100));

        // 其他字段
        couponConfigDTO.setAreaIds(Lists.newArrayList());
        couponConfigDTO.setFetchLimitType(1);
        couponConfigDTO.setCouponType(1);
        couponConfigDTO.setSendMode(1);

        request.setCouponConfigDTO(couponConfigDTO);
        request.setBizPlatform(5);
        request.setOperator("unittest");

        Result<SaveCouponConfigResponse> result = couponAdminService.saveCouponConfig(request);
        log.info("testSaveCouponConfig_CarShop_DirectReduce result:{}", result);
        Assertions.assertEquals(GeneralCodes.OK.getCode(), result.getCode());
        Assertions.assertNotNull(result.getData());
    }


    /**
     * 创建车商城礼品券
     */
    @Test
    public void testSaveCouponConfig_CarShop_GiftBuy() {
        OperateCouponConfigRequest request = new OperateCouponConfigRequest();

        CouponConfigDTO couponConfigDTO = new CouponConfigDTO();

        // 基础信息
        couponConfigDTO.setName("入会礼品券");
        couponConfigDTO.setCouponDesc("不适用于服务包、虚拟产品等，适用商品以优惠券实际使用情况为准。");
        couponConfigDTO.setSendSceneType(104);
        couponConfigDTO.setSendScene("E14D88941F5672AEF3263C4BB50EBB2E");

        // 时间信息
        couponConfigDTO.setStartFetchTime(new Date(NOW_TIMESTAMP));
        couponConfigDTO.setEndFetchTime(new Date(NOW_TIMESTAMP + THREE_MONTH));

        // 预算池信息
        couponConfigDTO.setBudgetApplyNo("BR202504280170");
        couponConfigDTO.setLineNum(72134L);
        couponConfigDTO.setBudgetCreateTime("2025-04-28 10:00:00");

        // 投放目的
        couponConfigDTO.setSendPurpose(SendPurposeEnum.Test.getValue());

        // 使用有效期
        UseTermVO useTermVO = new UseTermVO();
        useTermVO.setTimeGranularity(UseTimeGranularityEnum.DAY.getValue());
        useTermVO.setUseTimeType(UseTimeTypeEnum.RELATIVE.getValue());
        useTermVO.setStartUseTime(new Date(NOW_TIMESTAMP));
        useTermVO.setEndUseTime(new Date(NOW_TIMESTAMP + THREE_MONTH));
        useTermVO.setUseDuration(30);
        couponConfigDTO.setUseTermVO(useTermVO);

        // 使用渠道
        Map<Integer, UseChannelVO> useChannelMap = new HashMap<>();
        UseChannelVO useChannelVO = new UseChannelVO();
        useChannelVO.setAll(true);
        useChannelMap.put(6, useChannelVO);
        couponConfigDTO.setUseChannel(useChannelMap);

        // 优惠逻辑
        PromotionRuleVO promotionRuleVO = new PromotionRuleVO();
        promotionRuleVO.setPromotionType(PromotionTypeEnum.GIFT.getValue());
        promotionRuleVO.setBottomPrice(0L);
        promotionRuleVO.setBottomType(BottomTypeEnum.OverCount.getValue());
        promotionRuleVO.setBottomCount(1);
        promotionRuleVO.setMaxReduce(0L);
        promotionRuleVO.setPromotionValue(0L);
        couponConfigDTO.setPromotionRuleVO(promotionRuleVO);

        // 发放规则
        DistributionRuleVO distributionRuleVO = new DistributionRuleVO();
        distributionRuleVO.setApplyCount(10000);
        distributionRuleVO.setFetchLimit(1);
        couponConfigDTO.setDistributionRuleVO(distributionRuleVO);

        // 特殊规则
        ExtPropVO extPropVO = new ExtPropVO();
        extPropVO.setArea(2);
        extPropVO.setPublicPromotion(1);
        extPropVO.setProMember(2);
        extPropVO.setDisplayDate(1);
        extPropVO.setShare(2);
        couponConfigDTO.setExtProp(extPropVO);

        // 适用商品信息
        GoodsRuleVO goodsRuleVO = new GoodsRuleVO();
        goodsRuleVO.setScopeType(1);
        goodsRuleVO.setAutoUpdateGoods(2);
        goodsRuleVO.setGoodsDepartments(Lists.newArrayList(1));
        goodsRuleVO.setCategoryIds(Collections.emptySet());

        Map<String, List<Long>> goodsIncludeMap = new HashMap<>();
        goodsIncludeMap.put("ssu", Lists.newArrayList(2130100002L));
        goodsRuleVO.setGoodsInclude(goodsIncludeMap);

        couponConfigDTO.setGoodsRuleVO(goodsRuleVO);

        // 成本分摊
        couponConfigDTO.setCostShare(Collections.singletonMap(1, 100));

        // 其他字段
        couponConfigDTO.setAreaIds(Lists.newArrayList());
        couponConfigDTO.setFetchLimitType(1);
        couponConfigDTO.setCouponType(1);
        couponConfigDTO.setSendMode(1);

        request.setCouponConfigDTO(couponConfigDTO);
        request.setBizPlatform(5);
        request.setOperator("unittest");

        Result<SaveCouponConfigResponse> result = couponAdminService.saveCouponConfig(request);
        log.info("testSaveCouponConfig_CarShop_GiftBuy result:{}", result);
        Assertions.assertEquals(GeneralCodes.OK.getCode(), result.getCode());
        Assertions.assertNotNull(result.getData());
    }

    /**
     * 获取车商城券列表
     */
    @Test
    public void testCouponConfigList_CarShop() {
        CouponListRequest request = new CouponListRequest();
        request.setCouponType(1);
        request.setUseChannel(Lists.newArrayList(6));
        request.setBizPlatform(5);
        Result<BasePageResponse<CouponConfigListVO>> result = couponAdminService.couponConfigList(request);
        log.info("testCouponConfigList_CarShop result:{}", result);
        Assertions.assertEquals(GeneralCodes.OK.getCode(), result.getCode());
        Assertions.assertNotNull(result.getData().getList());
    }

    /**
     * 获取车商城券详情
     */
    @Test
    @SetupRedis(value = "redis/coupon_config_redis.json", templateName = "numberNewCouponRedisTemplate")
    public void testCouponConfigDetail_CarShop() {
        CouponInfoRequest request = new CouponInfoRequest();
        request.setId(213221L);
        Result<CouponDetailResponse> result = couponAdminService.couponConfigDetail(request);
        log.info("testCouponConfigDetail_CarShop result:{}", result);
        Assertions.assertEquals(GeneralCodes.OK.getCode(), result.getCode());
        Assertions.assertNotNull(result.getData());
    }

    @Test
    public void testUpdateCouponConfig_CarShop() {
        OperateCouponConfigRequest request = new OperateCouponConfigRequest();

        String couponName = "车商城商品券-修改后";

        CouponConfigDTO couponConfigDTO = new CouponConfigDTO();

        // 基础信息
        couponConfigDTO.setId(213221L);
        couponConfigDTO.setName(couponName);
        couponConfigDTO.setCouponDesc("不适用于服务包、虚拟产品等，适用商品以优惠券实际使用情况为准。");
        couponConfigDTO.setSendSceneType(103);
        couponConfigDTO.setSendScene("0669C15DC47BDFF1599DE3570CD0F272");

        // 时间信息
        couponConfigDTO.setStartFetchTime(new Date(1746720000000L));
        couponConfigDTO.setEndFetchTime(new Date(2062238400000L));

        // 预算池信息
        couponConfigDTO.setBudgetApplyNo("BR202504280170");
        couponConfigDTO.setLineNum(72134L);
        couponConfigDTO.setBudgetCreateTime("2025-04-28 10:00:00");

        // 投放目的
        couponConfigDTO.setSendPurpose(SendPurposeEnum.Test.getValue());

        // 使用有效期
        UseTermVO useTermVO = new UseTermVO();
        useTermVO.setTimeGranularity(UseTimeGranularityEnum.DAY.getValue());
        useTermVO.setUseTimeType(UseTimeTypeEnum.ABSOLUTE.getValue());
        useTermVO.setStartUseTime(new Date(1746720000000L));
        useTermVO.setEndUseTime(new Date(2062238400000L));
        useTermVO.setUseDuration(0);
        couponConfigDTO.setUseTermVO(useTermVO);

        // 使用渠道
        Map<Integer, UseChannelVO> useChannelMap = new HashMap<>();
        UseChannelVO useChannelVO = new UseChannelVO();
        useChannelVO.setAll(true);
        useChannelMap.put(6, useChannelVO);
        couponConfigDTO.setUseChannel(useChannelMap);

        // 优惠逻辑
        PromotionRuleVO promotionRuleVO = new PromotionRuleVO();
        promotionRuleVO.setPromotionType(PromotionTypeEnum.DirectReduce.getValue());
        promotionRuleVO.setBottomPrice(0L);
        promotionRuleVO.setBottomType(BottomTypeEnum.OverCount.getValue());
        promotionRuleVO.setBottomCount(1);
        promotionRuleVO.setMaxReduce(0L);
        promotionRuleVO.setPromotionValue(20000L);
        couponConfigDTO.setPromotionRuleVO(promotionRuleVO);

        // 发放规则
        DistributionRuleVO distributionRuleVO = new DistributionRuleVO();
        distributionRuleVO.setApplyCount(10000);
        distributionRuleVO.setFetchLimit(100);
        couponConfigDTO.setDistributionRuleVO(distributionRuleVO);

        // 特殊规则
        ExtPropVO extPropVO = new ExtPropVO();
        extPropVO.setArea(2);
        extPropVO.setPublicPromotion(1);
        extPropVO.setProMember(2);
        extPropVO.setDisplayDate(1);
        extPropVO.setShare(2);
        couponConfigDTO.setExtProp(extPropVO);

        // 适用商品信息
        GoodsRuleVO goodsRuleVO = new GoodsRuleVO();
        goodsRuleVO.setScopeType(1);
        goodsRuleVO.setAutoUpdateGoods(2);
        goodsRuleVO.setGoodsDepartments(Lists.newArrayList(1));
        goodsRuleVO.setCategoryIds(Collections.emptySet());

        Map<String, List<Long>> goodsIncludeMap = new HashMap<>();
        goodsIncludeMap.put("ssu", Lists.newArrayList(2221000457L));
        goodsRuleVO.setGoodsInclude(goodsIncludeMap);

        couponConfigDTO.setGoodsRuleVO(goodsRuleVO);

        // 成本分摊
        couponConfigDTO.setCostShare(Collections.singletonMap(1, 100));

        // 其他字段
        couponConfigDTO.setAreaIds(Lists.newArrayList());
        couponConfigDTO.setFetchLimitType(1);
        couponConfigDTO.setCouponType(1);
        couponConfigDTO.setSendMode(1);

        request.setCouponConfigDTO(couponConfigDTO);
        request.setBizPlatform(5);
        request.setOperator("unittest");

        Result<Void> result = couponAdminService.updateCouponConfig(request);
        Assertions.assertEquals(GeneralCodes.OK.getCode(), result.getCode());

        CouponInfoRequest detailRequest = new CouponInfoRequest();
        detailRequest.setId(213221L);
        Result<CouponDetailResponse> detailResult = couponAdminService.couponConfigDetail(detailRequest);
        log.info("testUpdateCouponConfig_CarShop detailResult:{}", detailResult);
        Assertions.assertEquals(GeneralCodes.OK.getCode(), detailResult.getCode());
        Assertions.assertEquals(couponName, detailResult.getData().getCouponConfigVO().getName());
    }

    /**
     * 券配置上下线
     */
    @Test
    public void testUpdateConfigStatus_CarShop() {
        CouponUpdateStatusRequest onlineRequest = new CouponUpdateStatusRequest();
        onlineRequest.setId(216279L);
        onlineRequest.setOperateType(1);
        onlineRequest.setOperator("unittest");
        Result<String> result = couponAdminService.updateConfigStatus(onlineRequest);
        Assertions.assertEquals(GeneralCodes.OK.getCode(), result.getCode());

        CouponInfoRequest detailRequest = new CouponInfoRequest();
        detailRequest.setId(216279L);
        Result<CouponDetailResponse> onlineDetailResult = couponAdminService.couponConfigDetail(detailRequest);
        Assertions.assertEquals(GeneralCodes.OK.getCode(), onlineDetailResult.getCode());
        Assertions.assertEquals(1, onlineDetailResult.getData().getCouponConfigVO().getStatus());

        CouponUpdateStatusRequest offlineRequest = new CouponUpdateStatusRequest();
        offlineRequest.setId(216279L);
        offlineRequest.setOperateType(2);
        offlineRequest.setOperator("unittest");
        result = couponAdminService.updateConfigStatus(offlineRequest);
        Assertions.assertEquals(GeneralCodes.OK.getCode(), result.getCode());
        Result<CouponDetailResponse> offlineDetailResult = couponAdminService.couponConfigDetail(detailRequest);
        Assertions.assertEquals(GeneralCodes.OK.getCode(), offlineDetailResult.getCode());
        Assertions.assertEquals(2, offlineDetailResult.getData().getCouponConfigVO().getStatus());
    }
}
