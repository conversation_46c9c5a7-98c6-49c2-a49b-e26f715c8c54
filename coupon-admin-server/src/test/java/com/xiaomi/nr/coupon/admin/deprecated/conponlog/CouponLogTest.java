package com.xiaomi.nr.coupon.admin.deprecated.conponlog;

import com.xiaomi.nr.coupon.admin.api.dto.coupon.optlog.request.CouponLogListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.optlog.request.LogDetailRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.optlog.response.CouponLogListResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.optlog.response.ModifyContentVO;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponLogService;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.util.CompressUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

/**
 * @Description:
 * @Date: 2022.03.
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ActiveProfiles({"dev"})
@SpringBootTest(
        classes = {
                CouponAdminBootstrap.class,
                CouponLogTest.class
        })
public class CouponLogTest {

    @Autowired
    private DubboCouponLogService dubboCouponLogService;

    @Test
    public void testLogList() {
        try {
            CouponLogListRequest request = new CouponLogListRequest();
            request.setConfigId(67L);
            CouponLogListResponse data = dubboCouponLogService.queryLogList(request).getData();
            System.out.println(data);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    @Test
    public void testLogDetail() {
        try {
            LogDetailRequest request = new LogDetailRequest();
            request.setLogId(1L);
            List<ModifyContentVO> modifyContentVOList = dubboCouponLogService.queryLogDetail(request).getData().getModifyContentVOList();
            System.out.println("size:" + modifyContentVOList.size());
            for (ModifyContentVO modifyContentVO : modifyContentVOList) {
                System.out.println("===========================");
                System.out.println(modifyContentVO.getModifyType());
                System.out.println(modifyContentVO.getModifyDetail());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
