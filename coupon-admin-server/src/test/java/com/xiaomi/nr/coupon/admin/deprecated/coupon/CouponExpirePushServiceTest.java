package com.xiaomi.nr.coupon.admin.deprecated.coupon;

import com.xiaomi.nr.coupon.admin.api.utils.TimeUtil;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponpush.CouponExpirePushService;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponpush.model.CouponExpireMsg;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.oceanbasedao.OceanBaseCouponMapper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.tidbdao.TidbCouponMapper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.tidbdao.po.CouponPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponAdminBootstrap.class)
@ActiveProfiles("dev")
public class CouponExpirePushServiceTest {

    @Resource
    private CouponExpirePushService couponExpirePushService;

    @Resource
    private TidbCouponMapper tidbCouponMapper;

    @Resource
    private OceanBaseCouponMapper oceanBaseCouponMapper;


    @Test
    public void asyncPushMQTest() {
        List<CouponExpireMsg> messages = new ArrayList<>();
        for(int i=0; i<1000; i++) {
            CouponExpireMsg message1 = new CouponExpireMsg();
            message1.setCouponId(1000100001L+i);
            message1.setCouponType(2);
            message1.setUserId(3150000058L);
            message1.setCouponName("测试的券");
            message1.setEndTime(1655049600L);
            message1.setShareUserId(31500000581L);
            message1.setMessageTime(TimeUtil.getNowUnixSecond());
            messages.add(message1);
        }

        SendResult sendResult = couponExpirePushService.asyncPushBatch(messages, 123L, Integer.toString(CouponExpirePushService.TIME_MARK_12));

        System.out.println("################");
        System.out.println(sendResult);
        System.out.println("################");
    }

    @Test
    public void postFeeExpireTaskTest(){
        long runStartTime = TimeUtil.getNowUnixMillis();
        int timeMark = CouponExpirePushService.TIME_MARK_12;
        try {
            log.info("postFeeExpirePushScheduleTask, start");
            long nowTime = TimeUtil.getNowUnixSecond();
            //12小时后即将过期的
            long filterEndTime = nowTime + CouponExpirePushService.TIME_MARK_12*3600*100;
            couponExpirePushService.postFeeExpiringSoon(Integer.toString(timeMark), filterEndTime);
            log.info("postFeeExpirePushScheduleTask, execute success, runTime={}ms", TimeUtil.sinceMillis(runStartTime));
        } catch (Exception e) {
            log.error("postFeeExpirePushScheduleTask, execute fail, runTime={}ms, {}", TimeUtil.sinceMillis(runStartTime), e);
        }
    }


    @Test
    public void postFeeExpireTaskDataSqlTest(){
        long runStartTime = TimeUtil.getNowUnixMillis();
        try {
            log.info("postFeeExpirePushScheduleTask, start");
            long nowTime = TimeUtil.getNowUnixSecond();
            long configFilterTime = nowTime - 24 * 3600;
            //12小时后即将过期的
            long filterEndTime = nowTime + 12*3600;
            List<Long> configIds = new ArrayList<>();
            int page = 0;

            // List<CouponPo> coupons = tidbCouponMapper.getExpiringSoonData(configFilterTime, filterEndTime, configIds, page * 1000, 1000);
            List<CouponPo> coupons = oceanBaseCouponMapper.getExpiringSoonData(configFilterTime, filterEndTime, configIds, page * 1000, 1000);
            if (coupons == null || coupons.isEmpty()) {
                log.info("postFeeExpirePushScheduleTask, 没有需要处理的运费券ID了, lastEndTime={}, filterEndTime={}, configIds={}", configFilterTime, filterEndTime, configIds);
            }
            log.info("postFeeExpirePushScheduleTask, execute success, runTime={}ms", TimeUtil.sinceMillis(runStartTime));
        } catch (Exception e) {
            log.error("postFeeExpirePushScheduleTask, execute fail, runTime={}ms, {}", TimeUtil.sinceMillis(runStartTime), e);
        }
    }

}
