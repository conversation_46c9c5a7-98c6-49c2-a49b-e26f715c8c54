package com.xiaomi.nr.coupon.admin.deprecated.infrastructure.repository.redisdao.pointadmin.impl;


import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.pointadmin.impl.PointBatchConfigRedisDaoImpl;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.redisdao.pointadmin.po.PointBatchConfigCachePo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2023/12/7 10:31
 */
@Slf4j
@ActiveProfiles("dev")
@SpringBootTest(classes = CouponAdminBootstrap.class)
@RunWith(SpringRunner.class)
public class PointBatchConfigRedisDaoImplTest {
    @Autowired
    private PointBatchConfigRedisDaoImpl pointBatchConfigRedisDaoImpl;

    @Test
    public void getPointBatchConfigCacheTest() {
        log.info("================================================================");

        Long batchId = 3L;
        PointBatchConfigCachePo res = pointBatchConfigRedisDaoImpl.getPointBatchConfigCache(batchId);

        log.info("getPointBatchConfigCacheTest res = {}", res);

        log.info("================================================================");
    }

    @Test
    public void getPointBatchDistributeCacheTest() {
        log.info("================================================================");

        Long batchId = 14L;
        Long res = pointBatchConfigRedisDaoImpl.getPointBatchDistributeCache(batchId);

        log.info("getPointBatchDistributeCacheTest res = {}", res);

        log.info("================================================================");
    }

    @Test
    public void updatePointBatchDistributeCacheTest() throws BizError {
        pointBatchConfigRedisDaoImpl.updatePointBatchDistributeCache(100L, 100);
        Long pointBatchDistributeCache = pointBatchConfigRedisDaoImpl.getPointBatchDistributeCache(100L);
        log.info("pointBatchConfigCache is {}", pointBatchDistributeCache);
        pointBatchDistributeCache = pointBatchConfigRedisDaoImpl.getPointBatchDistributeCache(101L);
        log.info("pointBatchConfigCache is {}", pointBatchDistributeCache);
        pointBatchConfigRedisDaoImpl.getPointBatchDistributeCache(Lists.newArrayList(101L, 100L, 102L));

    }
	@Test
	public void testGetPointBaseBatchConfigCache() {
	    log.info("================================================================");
        for(int i=0; i<100; i++) {
            PointBatchConfigCachePo actual = pointBatchConfigRedisDaoImpl.getPointBatchConfigCache(18L);
            Assert.assertNotNull(actual);
        }
	    log.info("================================================================");
	}
}