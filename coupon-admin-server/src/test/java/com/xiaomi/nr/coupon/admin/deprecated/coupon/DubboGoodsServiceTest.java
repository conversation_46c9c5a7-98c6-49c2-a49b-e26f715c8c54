package com.xiaomi.nr.coupon.admin.deprecated.coupon;

import com.xiaomi.goods.gms.dto.common.PageInfoDto;
import com.xiaomi.goods.gms.dto.sku.SkuInfoDto;
import com.xiaomi.goods.gms.dto.sku.SkuPageRequest;
import com.xiaomi.goods.gms.dto.sku.SkuPageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.PackageInfoVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.SkuInfoVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.SsuInfoVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.goods.request.*;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.goods.response.CategoryListResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.goods.response.PackageListResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.goods.response.SkuListResponse;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.api.enums.BizSubTypeEnum;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboGoodsService;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.UseChannelsEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.rpc.goods.GisProxyService;
import com.xiaomi.nr.coupon.admin.infrastructure.rpc.goods.GmsProxyService;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponAdminBootstrap.class)
@ActiveProfiles("sg_staging")
public class DubboGoodsServiceTest {

    @Autowired
    private DubboGoodsService goodsService;

    @Autowired
    private GisProxyService gisProxyService;

    @Autowired
    private GmsProxyService gmsProxyService;

    @Test
    public void skuInfoTest() throws BizError {
        SkuPageRequest request = new SkuPageRequest();
        request.setIsNeedStock(false);
        //request.setOnSaleOnline(1);
        PageInfoDto page = new PageInfoDto();
        page.setPageNum(1);
        page.setPageSize(10);
        request.setPage(page);
        request.setSku(21096L);
        SkuPageResponse response = gisProxyService.querySkuList(request);
        Assert.assertNotNull(response);
        System.out.println(response);
    }


    @Test
    public void testQueryListBySkuIds() {
        try {
            List<Long> skuIds = new ArrayList<>(Arrays.asList(1002L, 1070L, 37325L, 19254L));
            List<SkuInfoDto> skuInfoDtoList = gmsProxyService.queryListBySkuIds(skuIds, false, true, true, Lists.newArrayList(BizSubTypeEnum.ORDINARY_3C.getCode()));
            System.out.println("size: " + skuInfoDtoList.size());
            for (SkuInfoDto skuInfoDto : skuInfoDtoList) {
                System.out.println(skuInfoDto.getSku() + ":" + skuInfoDto.getMiSupportShipment());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    @Test
    public void terts(){

        PackagePageListRequest request = new PackagePageListRequest();
        request.setCid(1134800001L);
        System.out.println(GsonUtil.toJson(goodsService.queryPackagePageList(request)));
    }

    @Test
    public void testCategory(){
        CategoryListRequest request = new CategoryListRequest();
        request.setServiceId(1);
        Result<CategoryListResponse> result = goodsService.getCategoryList(request);
        String data = GsonUtil.toJson(result);
    }


    @Test
    public void querySkuPageListTest(){
        log.info("==================================================");


        SkuPageListRequest request = new SkuPageListRequest();
        request.setSku(19254L);

        log.info("querySkuPageListTest request = {}", GsonUtil.toJson(request));

        Result<BasePageResponse<SkuInfoVO>> result = goodsService.querySkuPageList(request);

        log.info("querySkuPageListTest result  = {}", GsonUtil.toJson(result));

        log.info("==================================================");
    }

    @Test
    public void getSkuByCategoryIdTest() {
        log.info("==================================================");

        SkuCategoryIdRequest request = new SkuCategoryIdRequest();
        request.setCouponType(CouponTypeEnum.GOODS.getValue());
        request.setCategoryIds(Arrays.asList(293));

        Result<SkuListResponse> result = goodsService.getSkuByCategoryId(request);

        log.info("getSkuByCategoryIdTest result = {}", GsonUtil.toJson(result));

        log.info("==================================================");
    }

    @Test
    public void testGetSkuByCategoryId() {
        SkuCategoryIdRequest request = new SkuCategoryIdRequest();
        request.setCouponType(CouponTypeEnum.GOODS.getValue());
        request.setCategoryIds(Arrays.asList(294,269));

        Result<SkuListResponse> result = goodsService.getSkuByCategoryId(request);

        System.out.println(GsonUtil.toJson(result));
        assertNotNull(result);
        assertTrue(result.getData().getSkuInfoList().size()>=249);
    }

    @Test
    public void querySuitPageListTest() {
        SuitPageListRequest request = new SuitPageListRequest();
        request.setPageNo(1);
        request.setPageSize(10);
        request.setBizPlatform(BizPlatformEnum.RETAIL.getCode());
        request.setStatus(1);

        Result<BasePageResponse<PackageInfoVO>> result = goodsService.querySuitPageList(request);
        log.info(GsonUtil.toJson(result));
    }

    @Test
    public void queryListBySuitIdsTest() {
        PackageListRequest request = new PackageListRequest();
        // request.setFileUrl("https://staging-cnbj2-fds.api.xiaomi.net/nr-coupon-bucket/whttest/套装上传模版文件.xlsx");
        request.setPackageIds(Lists.newArrayList(600013312L));

        Result<PackageListResponse> result = goodsService.queryListBySuitIds(request);
        log.info(GsonUtil.toJson(result));
    }
    @Test
    public void queryGoodsByPageGoodsRequest() {
        PageGoodsRequest request = new PageGoodsRequest();
        request.setPageNo(1);
        request.setPageSize(20);
        request.setChannel(UseChannelsEnum.DIRECTSALE_STORE.getValue());
        Result<BasePageResponse<SsuInfoVO>> result = goodsService.queryGoodsByPageGoodsRequest(request);
        log.info(GsonUtil.toJson(result));
    }
    @Test
    public void queryListBySkuIdsGlobal() {
        SkuListRequest skuListRequest = new SkuListRequest();
        skuListRequest.setChannel(2);
        skuListRequest.setSkuIds(Lists.newArrayList(
                35736L,14274L,22411L,123L));
        Result<SkuListResponse> result = goodsService.queryListBySkuIdsGlobal(skuListRequest);
        log.info(GsonUtil.toJson(result));
    }

}
