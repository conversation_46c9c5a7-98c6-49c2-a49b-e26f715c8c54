package com.xiaomi.nr.coupon.admin.unittest.api.service;

import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.CouponConfigInfoDTO;
import com.xiaomi.nr.coupon.admin.api.dto.couponconfig.GetConfigInfoRequest;
import com.xiaomi.nr.coupon.admin.api.service.DubboCouponService;
import com.xiaomi.nr.coupon.admin.unittest.BaseUnitTest;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.global.dev.base.HeraContextKeyValueHolder;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @author: zhangliwei6
 * @date: 2025/9/5 10:41
 * @description:
 */
@Slf4j
public class DubboCouponServiceTest extends BaseUnitTest {

    @Autowired
    private DubboCouponService dubboCouponService;

    @BeforeEach
    public void before() {
        HeraContextKeyValueHolder.put("mone-retail-dev-mode", "true");
        HeraContextKeyValueHolder.put("mone-retail-area-for-global", "HK");
        HeraContextKeyValueHolder.put("mone-retail-language-for-global", "zh-CN");
    }

    @Test
    public void testGetCouponConfigInfo(){
        GetConfigInfoRequest req = new GetConfigInfoRequest();
        req.setConfigId(373L);
        CouponConfigInfoDTO couponConfigInfo = dubboCouponService.getCouponConfigInfo(req).getData().getCouponConfigInfoDTO();
        Assert.assertEquals(couponConfigInfo.getFetchLimitType().longValue(), 1);
        System.out.println("couponConfigInfo is " + GsonUtil.toJson(couponConfigInfo));
    }
}
