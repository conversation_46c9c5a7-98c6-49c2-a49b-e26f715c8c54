package com.xiaomi.nr.coupon.admin.deprecated.scene;

import com.google.common.collect.Lists;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.scene.request.*;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.scene.response.*;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponSceneService;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponTypeEnum;
import com.xiaomi.nr.coupon.admin.enums.scene.*;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.rpc.RpcContext;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Random;

/**
 * @Description: 优惠券场景
 * @Date: 2022.03.12 15:21
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ActiveProfiles({"sg_staging"})
@SpringBootTest(
        classes = {
                CouponAdminBootstrap.class,
                DubboCouponSceneServiceTest.class
        })
public class DubboCouponSceneServiceTest {

    @Autowired
    private DubboCouponSceneService dubboCouponSceneService;



    private String generateString(int length) {
        String str = "zxcvbnmlkjhgfdsaqwertyuiopQWERTYUIOPASDFGHJKLZXCVBNM1234567890";
        Random random = new Random();
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < length; i++) {
            stringBuilder.append(str.charAt(random.nextInt(str.length() - 1)));
        }
        return stringBuilder.toString();
    }

    @Test
    public void testCreateScene() {
        try {
            RpcContext.getContext().getAttachments().put("$upc_account", "p-xueqizheng");
            CreateOrUpdateSendSceneRequest request = new CreateOrUpdateSendSceneRequest();
            request.setType(CreateOrUpdateSendSceneEnum.CREATE.getCode());
            request.setName("国际店长券测试场景-xqz");
            request.setSceneDesc("国际店长券-xqz场景描述");
            request.setApplyMark("国际店长券-xqz申请备注");
            request.setCouponTypeList(Lists.newArrayList(CouponTypeEnum.GOODS.getValue()));
            request.setIdGenerationType(SceneIdGenerationEnum.SYSTEM.getCode());
            request.setRelationSceneId(SceneEnum.GLOBAL_Official_Marking.getCode());
            request.setSendMode(SceneSendModeEnum.COUPON.getCode());
            request.setAssignMode(Arrays.asList(SceneAssignModeEnum.Coupon_Schedule.getCode(), SceneAssignModeEnum.External_System.getCode()));
            request.setBizPlatform(BizPlatformEnum.GLOBAL_NEW_RETAIL.getCode());
            log.info("request:{}",GsonUtil.toJson(request));

            Result<CreateOrUpdateSendSceneResponse> result = dubboCouponSceneService.createOrUpdateSendScene(request);
            System.out.println("sceneCode is " + result.getData().getSceneCode());
        } catch (Exception e) {
            System.out.println("error");
            throw new RuntimeException(e);
        }
    }

    @Test
    public void testUpdateScene() {
        try {
            RpcContext.getContext().getAttachments().put("$upc_account", "p-xueqizheng");
            CreateOrUpdateSendSceneRequest request = new CreateOrUpdateSendSceneRequest();
            request.setSceneId(45L);
            request.setType(CreateOrUpdateSendSceneEnum.UPDATE.getCode());
            request.setName("国际店长券测试场景-xqz");
            request.setSceneDesc("国际店长券-xqz场景描述");
            request.setApplyMark("国际店长券-xqz申请备注");
            request.setCouponTypeList(Lists.newArrayList(CouponTypeEnum.GOODS.getValue()));
            request.setIdGenerationType(SceneIdGenerationEnum.SYSTEM.getCode());
            request.setRelationSceneId(SceneEnum.GLOBAL_Official_Marking.getCode());
            request.setSendMode(SceneSendModeEnum.COUPON.getCode());
            request.setAssignMode(Arrays.asList(SceneAssignModeEnum.Coupon_Schedule.getCode(), SceneAssignModeEnum.External_System.getCode()));
            request.setBizPlatform(BizPlatformEnum.GLOBAL_NEW_RETAIL.getCode());
            request.setSceneCode("7C904ACC3895B1D25C95714C763BFDB5");

            request.setIdGenerationType(SceneIdGenerationEnum.SYSTEM.getCode());
            request.setSendMode(SceneSendModeEnum.COUPON.getCode());

            Result<CreateOrUpdateSendSceneResponse> result = dubboCouponSceneService.createOrUpdateSendScene(request);
            System.out.println("sceneCode is " + result.getData().getSceneCode());
        } catch (Exception e) {
            System.out.println("error");
            throw new RuntimeException(e);
        }
    }

    @Test
    public void testQuerySceneList() {
        QuerySceneListRequest request = new QuerySceneListRequest();
        request.setBizPlatform(BizPlatformEnum.GLOBAL_NEW_RETAIL.getCode());
        Result<BasePageResponse<SceneListVO>> result = dubboCouponSceneService.querySceneList(request);
        BasePageResponse<SceneListVO> data = result.getData();
        System.out.println("resp is " + data);
    }

    @Test
    public void testSceneDetail() {
        SceneDetailRequest request = new SceneDetailRequest();
        request.setSceneId(45L);
        SceneDetailResponse resp = dubboCouponSceneService.sceneDetail(request).getData();
        System.out.println("resp is " + resp);
    }

    @Test
    public void testOpt() {
        RpcContext.getContext().getAttachments().put("$upc_account", "p-xueqizheng");
        OperateSceneRequest request = new OperateSceneRequest();
        request.setSceneId(45L);
        request.setType(SceneStatusEnum.ONLINE.getCode());
        dubboCouponSceneService.operateScene(request);
    }


    @Test
    public void testCreatePermission() {
        try {
            RpcContext.getContext().getAttachments().put("$upc_account", "p-xueqizheng");
            CreatePermissionRequest request = new CreatePermissionRequest();
            request.setSceneId(45L);
            request.setAppId("11092");
            request.setAppName("小米金融/小米支付1112");
            request.setAppContact("mi..112");
            boolean res = dubboCouponSceneService.createPermission(request).getData();
            System.out.println(res);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void batchInsertPermission() {
        try {
            CreatePermissionRequest request = new CreatePermissionRequest();
            Random random = new Random();
            for (int i = 0; i < 50; i++) {
                request.setSceneId(random.nextInt(20) + 1L);
                request.setAppId(generateString(12));
                request.setAppName(generateString(10));
                request.setAppContact(generateString(8));
                boolean res = dubboCouponSceneService.createPermission(request).getData();
                System.out.println(res);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testPermissionList() {
        try {
            PermissionListRequest request = new PermissionListRequest();
            request.setSceneId(45L);
            request.setPageNo(1);
            request.setPageSize(20);
            BasePageResponse<PermissionListVO> result = dubboCouponSceneService.queryPermissionList(request).getData();
            System.out.println(result.getPageNo());
            System.out.println(result.getPageSize());
            System.out.println(result.getTotalCount());
            System.out.println(result.getTotalPage());
            System.out.println("list size: " + result.getList().size());
            Iterator<PermissionListVO> iterator = result.getList().iterator();
            while (iterator.hasNext()) {
                System.out.println(iterator.next());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Test
    public void testOptPermission() {
        try {
            RpcContext.getContext().getAttachments().put("$upc_account", "p-xueqizheng");
            OperatePermissionRequest request = new OperatePermissionRequest();
            request.setPermissionId(69L);
            request.setSceneId(45L);
            request.setAppId("11092");
            request.setType(ScenePermissionStatusEnum.VALID.getCode());
            dubboCouponSceneService.operatePermission(request);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testSceneCat() {
        Result<SearchSceneWithCatResponse> res = dubboCouponSceneService.searchSceneWithCat();
        SearchSceneWithCatResponse response = res.getData();
        List<CouponSceneTypeVO> couponChannelTypeVOList = response.getCouponChannelTypeVOList();
        System.out.println(couponChannelTypeVOList.size());
        for (CouponSceneTypeVO couponSceneTypeVO : couponChannelTypeVOList) {
            System.out.println(couponSceneTypeVO.getType());
            System.out.println(couponSceneTypeVO.getName());
            System.out.println(couponSceneTypeVO.getChannelVOList().size());
            if (couponSceneTypeVO.getType() == 1) {
                for (SceneCatVO sceneCatVO : couponSceneTypeVO.getChannelVOList()) {
                    System.out.println(sceneCatVO.getSceneCode());
                    System.out.println(sceneCatVO.getSceneName());
                    System.out.println(sceneCatVO.getTips());
                }
            }
            System.out.println("---------------------------------");
        }
    }


    @Test
    public void testSearchSceneWithCatV2() {
        SearchSceneWithCatRequest request = new SearchSceneWithCatRequest();
        request.setCouponType(CouponTypeEnum.GOODS.getValue());
        request.setBizPlatform(BizPlatformEnum.GLOBAL_NEW_RETAIL.getCode());
        Result<SearchSceneWithCatResponse> res = dubboCouponSceneService.searchSceneWithCatV2(request);
        SearchSceneWithCatResponse resp = res.getData();
        System.out.println("resp is " + resp);
    }
}


