package com.xiaomi.nr.coupon.admin.deprecated.application.dubbo.coupon;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;
import com.xiaomi.keycenter.common.iface.DataProtectionException;
import com.xiaomi.newretail.bpm.api.model.dto.ProcessCreateDTO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.*;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.request.CouponCreateReviewRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.request.CouponReviewRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.request.CouponUpdateReviewRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.response.CouponReviewResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.store.request.StoreListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.store.response.StoreListResponse;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponReviewService;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboStoreService;
import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.PromotionTypeEnum;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.nr.coupon.admin.util.HttpClientUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.rpc.RpcContext;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.PostConstruct;
 import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Description: 优惠券场景
 * @Date: 2022.03.12 15:21
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ActiveProfiles({"staging"})
@SpringBootTest(
        classes = {
                CouponAdminBootstrap.class,
                DubboCouponReviewServiceTest.class
        })
public class DubboCouponReviewServiceTest {

    @Autowired
    private DubboCouponReviewService dubboCouponReviewService;
    @Autowired
    private DubboStoreService dubboStoreService;

    @Test
    public void createCouponReview() {
        RpcContext.getContext().getAttachments().put("$upc_account", "p-xueqizheng");
        CouponCreateReviewRequest request = new CouponCreateReviewRequest();
        CouponConfigVO couponConfigVO = new CouponConfigVO();
        //couponConfigVO.setId()
        couponConfigVO.setName("test");
        couponConfigVO.setCouponDesc("测试");
        couponConfigVO.setSendScene("1DC2FBE1B76AE8E10CBD23381492A66A");
        couponConfigVO.setSendSceneType(103);
        couponConfigVO.setStartFetchTime(new Date(2024,12,15,16,0,0));
        couponConfigVO.setEndFetchTime(new Date(2025,1,19,15,59,59));
        UseTermVO useTermVO = new UseTermVO();
        useTermVO.setUseTimeType(1);
        //useTermVO.setUseDuration();
        useTermVO.setStartUseTime(new Date(2024,12,15,16,0,0));
        useTermVO.setEndUseTime(new Date(2024,12,15,16,0,0));
        useTermVO.setTimeGranularity(2);
        couponConfigVO.setUseTermVO(useTermVO);
        Map<Integer, UseChannelVO> useChannel = new HashMap<>();
        UseChannelVO useChannelVO = new UseChannelVO();
        useChannelVO.setAll(true);
        useChannel.put(6,useChannelVO);
        couponConfigVO.setUseChannel(useChannel);
        PromotionRuleVO promotionRuleVO = new PromotionRuleVO();
        promotionRuleVO.setPromotionType(5);
        promotionRuleVO.setPromotionValue(0L);
        promotionRuleVO.setBottomPrice(0L);
        //promotionRuleVO.getBottomCount();
        promotionRuleVO.setBottomType(1);
        promotionRuleVO.setMaxReduce(0L);
        couponConfigVO.setPromotionRuleVO(promotionRuleVO);
        DistributionRuleVO distributionRuleVO = new DistributionRuleVO();
        distributionRuleVO.setApplyCount(1000);
        distributionRuleVO.setFetchLimit(10);
        couponConfigVO.setDistributionRuleVO(distributionRuleVO);
        ExtPropVO extProp = new ExtPropVO();
        extProp.setPostFree(1);
        extProp.setShare(2);
        extProp.setArea(2);
        extProp.setSpecialStore(2);
        extProp.setProMember(2);
        extProp.setPublicPromotion(2);
        couponConfigVO.setExtProp(extProp);
        couponConfigVO.setSendPurpose(8);
        //couponConfigVO.setAreaIds();
        couponConfigVO.setCouponType(1);
        couponConfigVO.setShipmentId(-1);
        couponConfigVO.setCost(new BigDecimal(0));
        GoodsRuleVO goodsRuleVO = new GoodsRuleVO();
        goodsRuleVO.setScopeType(1);
        List<Integer> goodsDepartments = new ArrayList<>();
        goodsDepartments.add(8);
        goodsRuleVO.setGoodsDepartments(goodsDepartments);
        Map<String, List<Long>> goodsInclude = new HashMap<>();
        List<Long> goodsIds = new ArrayList<>();
        goodsIds.add(2130100002L);
        goodsInclude.put("ssu",goodsIds);
        goodsRuleVO.setGoodsInclude(goodsInclude);
        //goodsRuleVO.setGoodsExclude();
        //goodsRuleVO.setCategoryIds();
        GoodsDiscountLevelVO goodsDiscountLevelVO = new GoodsDiscountLevelVO();
        goodsDiscountLevelVO.setDiscountLevel(new BigDecimal(8.5));
        goodsDiscountLevelVO.setHasLowLevelGoods(false);
        goodsRuleVO.setGoodsDiscountLevelVO(goodsDiscountLevelVO);
        List<GoodsSuitableVO> goodsSuitableVOs = new ArrayList<>();
        GoodsSuitableVO goodsSuitableVO = new GoodsSuitableVO();
        goodsSuitableVO.setGoodsAmount(1);
        goodsSuitableVO.setCategoryTextList("其他/手机配件/手机自研壳模套");
        goodsSuitableVOs.add(goodsSuitableVO);
        goodsRuleVO.setGoodsSuitableVOs(goodsSuitableVOs);
        goodsRuleVO.setAutoUpdateGoods(2);
        couponConfigVO.setGoodsRuleVO(goodsRuleVO);
        Map<Integer, Integer> costShare = new HashMap<>();
        costShare.put(1,100);
        couponConfigVO.setCostShare(costShare);
        couponConfigVO.setSource(2);
        //couponConfigVO.
        List<ApplyAttachmentVO> applyAttachment = new ArrayList<>();
        ApplyAttachmentVO applyAttachmentVO = new ApplyAttachmentVO();
        applyAttachmentVO.setName("预算信息模板.xlsx");
        applyAttachmentVO.setUrl("https://staging-cnbj2-fds.api.xiaomi.net/nr-inner/202411201012_cb7fc473067a4fc732bff98a6bc80ab5.xlsx");
        applyAttachment.add(applyAttachmentVO);

        request.setCouponConfigVO(couponConfigVO);
        request.setApplyAttachment(applyAttachment);
        request.setBizType(5);
        request.setToken("b698ad5d894cc6995145414937b7c1fc");
        //request.setSketchId();

       Result<CouponReviewResponse> result =  dubboCouponReviewService.createCouponReview(request);
       System.out.println(result);
    }
    @Test
    public void createCouponReview2() {
        String content = "{\"couponConfigVO\":{\"id\":0,\"name\":\"自动化测试勿动测试校验规则礼品券\",\"couponDesc\":\"仅限特定活动，不适用于服务包、虚拟产品等，适用商品以优惠券实际使用情况为准。\",\"budgetInfoDto\":{},\"sendSceneType\":104,\"sendScene\":\"98EDA5B9CA1D6239C457BCD65D27A4F1\",\"startFetchTime\":\"Jul 4, 2040 12:00:00 AM\",\"endFetchTime\":\"Jul 13, 2040 11:59:59 PM\",\"useTermVO\":{\"useTimeType\":2,\"useDuration\":24,\"timeGranularity\":2},\"useChannel\":{\"6\":{\"all\":true}},\"promotionRuleVO\":{\"promotionType\":5,\"promotionValue\":0,\"bottomPrice\":0,\"bottomCount\":1,\"bottomType\":2,\"maxReduce\":0},\"distributionRuleVO\":{\"applyCount\":20,\"fetchLimit\":20},\"extProp\":{\"postFree\":1,\"share\":2,\"area\":2,\"proMember\":2,\"specialStore\":2,\"publicPromotion\":2,\"checkoutStage\":2,\"displayDate\":1,\"annualType\":0},\"areaIds\":[],\"goodsRuleVO\":{\"scopeType\":1,\"autoUpdateGoods\":2,\"goodsDepartments\":[8],\"goodsInclude\":{\"sku\":[],\"package\":[],\"suit\":[],\"ssu\":[2130100002,2134700011]},\"goodsExclude\":{},\"categoryIds\":[],\"goodsSuitableVOs\":[{\"goodsAmount\":1,\"categoryTextList\":\"其他/手机配件/手机自研壳模套\"},{\"goodsAmount\":1,\"categoryTextList\":\"生态链/生活酷玩/电池\"}],\"goodsDiscountLevelVO\":{\"discountLevel\":8.5,\"hasLowLevelGoods\":false}},\"sendPurpose\":8,\"costShare\":{\"1\":100},\"cost\":2400.0,\"departmentId\":0,\"status\":0,\"source\":2,\"couponType\":1,\"shipmentId\":-1,\"sendMode\":0},\"applyAttachment\":[{\"name\":\"预算信息模板 (1).xlsx\",\"url\":\"https://staging-cnbj2-fds.api.xiaomi.net/nr-inner/202507021304_203d24c4551ad4b1d3f561203ec62e56.xlsx\"}],\"token\":\"b698ad5d894cc6995145414937b7c1fc\",\"bizType\":5}";
        CouponCreateReviewRequest request = GsonUtil.fromJson(content, CouponCreateReviewRequest.class);
        log.info("request:{}",GsonUtil.toJson(request));
        Result<CouponReviewResponse> result = null;
        try {
            result = dubboCouponReviewService.createCouponReview(request);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        System.out.println(result);
    }

    @Test
    public void updateCouponReview2() {


        String content = "{\n" +
                "    \"couponConfigVO\": {\n" +
                "      \"id\": 36,\n" +
                "      \"name\": \"xqz-test-0213_3\",\n" +
                "      \"couponDesc\": \"This is coupon description\",\n" +
                "      \"startFetchTime\": \"2025-02-20T16:00:00.000Z\",\n" +
                "      \"endFetchTime\": \"2025-02-22T16:00:00.000Z\",\n" +
                "      \"sendScene\": \"DCCC8C085E79839661D9B93E9A996EC8\",\n" +
                "      \"useTermVO\": {\n" +
                "        \"useTimeType\": 1,\n" +
                "        \"startUseTime\": \"2025-02-20T16:00:00.000Z\",\n" +
                "        \"endUseTime\": \"2025-02-22T16:00:00.000Z\",\n" +
                "        \"useDuration\": null,\n" +
                "        \"timeGranularity\": null\n" +
                "      },\n" +
                "      \"useChannel\": {\n" +
                "        \"2\": {\n" +
                "          \"all\": true,\n" +
                "          \"limitIds\": []\n" +
                "        }\n" +
                "      },\n" +
                "      \"promotionRuleVO\": {\n" +
                "        \"promotionType\": 4,\n" +
                "        \"promotionValue\": 2\n" +
                "      },\n" +
                "      \"distributionRuleVO\": {\n" +
                "        \"applyCount\": 10,\n" +
                "        \"fetchLimit\": 2\n" +
                "      },\n" +
                "      \"workflowId\": 100001,\n" +
                "      \"couponType\": 1,\n" +
                "      \"goodsRuleVO\": {\n" +
                "        \"scopeType\": 1,\n" +
                "        \"goodsInclude\": {\n" +
                "          \"ssu\": [\n" +
                "            836000384\n" +
                "          ]\n" +
                "        }\n" +
                "      }\n" +
                "    },\n" +
                "    \"bizType\": 6,\n" +
                "    \"applyAttachment\": []\n" +
                "  }";
        CouponUpdateReviewRequest request = GsonUtil.fromJson(content, CouponUpdateReviewRequest.class);
        log.info("request:{}",GsonUtil.toJson(request));
        Result<CouponReviewResponse> result = null;
        try {
            result = dubboCouponReviewService.updateCouponReview(request);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        System.out.println(result);
    }

    @Test
    public void cancelHttpBpm() {


        CouponReviewRequest couponReviewRequest = new CouponReviewRequest();
        couponReviewRequest.setReviewId(32);
        try {
            Result<Void> voidResult = dubboCouponReviewService.cancelCouponReviewHttp(couponReviewRequest);
            log.info("voidResult,{}",GsonUtil.toJson(voidResult));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        System.out.println("end");
    }
    @Test
    public void test() {

        String descGlobal = PromotionTypeEnum.getDescGlobal(PromotionTypeEnum.DirectReduce, 100);
        System.out.println(descGlobal);

    }

    @Test
    public void queryListByStoreIds() {
        try {
            Result<StoreListResponse> storeListResponseResult = dubboStoreService.queryListByStoreIds(new StoreListRequest());
            log.info("storeListResponseResult:{}",GsonUtil.toJson(storeListResponseResult));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }


        System.out.println("123");
    }

    @Test
    public void bindQualificationForPartnerTest() throws DataProtectionException, IOException {
        String content = "{\"key\":\"HongKong_bpm_coupon_activity_direct_store\",\"name\":\"做一个测试数据给国际优惠卷参考0210_测试http的bpm\",\"requestId\":\"coupon_253C9A768296415FB8CF805D4D02CA14\",\"creator\":\"p-xueqizheng\",\"html\":\"\\u003cdiv class\\u003d\\\"card\\\"\\u003e\\r\\n    \\u003cdiv class\\u003d\\\"card-header\\\"\\u003e\\u003cb\\u003e重要信息\\u003c/b\\u003e\\u003c/div\\u003e\\r\\n    \\u003cdiv class\\u003d\\\"card-body\\\"\\u003e\\r\\n        \\u003cdiv class\\u003d\\\"box-content\\\"\\u003e\\r\\n            \\u003cdiv\\u003e\\r\\n                \\u003ctable cellpadding\\u003d\\\"10\\\"\\u003e\\r\\n\\r\\n                    \\u003ctr\\u003e\\r\\n                        \\u003ctd class\\u003d\\\"col-md-2 text-right active\\\"\\u003e\\r\\n                            \\u003cspan style\\u003d\\\"color: red; \\\"\\u003e*\\u003c/span\\u003e优惠券类型：\\r\\n                        \\u003c/td\\u003e\\r\\n                                                \\u003ctd\\u003e商品券\\u003c/td\\u003e\\r\\n                    \\u003c/tr\\u003e\\r\\n                    \\u003ctr\\u003e\\r\\n                        \\u003ctd class\\u003d\\\"col-md-2 text-right active\\\"\\u003e\\u003cspan style\\u003d\\\"color: red; \\\"\\u003e*\\u003c/span\\u003e优惠券名称：\\u003c/td\\u003e\\r\\n                            \\u003ctd\\u003e做一个测试数据给国际优惠卷参考0210_测试http的bpm\\u003c/td\\u003e\\r\\n                    \\u003c/tr\\u003e\\r\\n                    \\u003ctr\\u003e\\r\\n                        \\u003ctd class\\u003d\\\"col-md-2 text-right active\\\"\\u003e\\u003cspan style\\u003d\\\"color: red; \\\"\\u003e*\\u003c/span\\u003e领取时间：\\u003c/td\\u003e\\r\\n                        \\u003ctd style\\u003d\\\"color: red; \\\"\\u003e\\r\\n                                2025-02-10 00:00:00\\r\\n                        \\u003cspan\\u003e至\\u003c/span\\u003e\\r\\n                                2025-02-14 23:59:59\\r\\n                        \\u003c/td\\u003e\\r\\n                    \\u003c/tr\\u003e\\r\\n                    \\u003ctr\\u003e\\r\\n                        \\u003ctd class\\u003d\\\"col-md-2 text-right active\\\"\\u003e\\u003cspan style\\u003d\\\"color: red; \\\"\\u003e*\\u003c/span\\u003e使用时间：\\u003c/td\\u003e\\r\\n                            \\u003ctd style\\u003d\\\"color:red\\\"\\u003e2025-02-10 00:00:00至2025-02-21 23:59:59\\u003c/td\\u003e\\r\\n                    \\u003c/tr\\u003e\\r\\n\\r\\n                    \\u003ctr\\u003e\\r\\n                        \\u003ctd class\\u003d\\\"col-md-2 text-right active\\\"\\u003e\\u003cspan style\\u003d\\\"color: red; \\\"\\u003e*\\u003c/span\\u003e使用渠道：\\u003c/td\\u003e\\r\\n                                \\u003ctd\\u003e直营店\\u003c/td\\u003e\\r\\n                    \\u003c/tr\\u003e\\r\\n\\r\\n                    \\u003ctr\\u003e\\r\\n                        \\u003ctd class\\u003d\\\"col-md-2 text-right active\\\"\\u003e\\u003cspan style\\u003d\\\"color: red; \\\"\\u003e*\\u003c/span\\u003e折扣类型：\\u003c/td\\u003e\\r\\n                        \\u003ctd\\u003e\\r\\n                                立减\\r\\n                            \\u003cbr/\\u003e\\r\\n                                无门槛立减 ¥10.00 元\\r\\n                        \\u003c/td\\u003e\\r\\n                    \\u003c/tr\\u003e\\r\\n\\r\\n                    \\u003ctr\\u003e\\r\\n                        \\u003ctd class\\u003d\\\"col-md-2 text-right active\\\"\\u003e\\r\\n                            \\u003cspan style\\u003d\\\"color: red; \\\"\\u003e*\\u003c/span\\u003e优惠金额：\\r\\n                        \\u003c/td\\u003e\\r\\n                        \\u003ctd\\u003e10.00元\\u003c/td\\u003e\\r\\n                    \\u003c/tr\\u003e\\r\\n\\r\\n                    \\u003ctr\\u003e\\r\\n                        \\u003ctd class\\u003d\\\"col-md-2 text-right active\\\"\\u003e\\u003cspan style\\u003d\\\"color: red; \\\"\\u003e*\\u003c/span\\u003e发放总量：\\u003c/td\\u003e\\r\\n                            \\u003ctd style\\u003d\\\"color:red\\\"\\u003e1,000张\\u003c/td\\u003e\\r\\n                    \\u003c/tr\\u003e\\r\\n                    \\u003ctr\\u003e\\r\\n                        \\u003ctd class\\u003d\\\"col-md-2 text-right active\\\"\\u003e\\u003cspan style\\u003d\\\"color: red; \\\"\\u003e*\\u003c/span\\u003e投放场景：\\u003c/td\\u003e\\r\\n                            \\u003ctd\\u003e\\u003c/td\\u003e\\r\\n                    \\u003c/tr\\u003e\\r\\n                    \\u003ctr\\u003e\\r\\n                        \\u003ctd class\\u003d\\\"col-md-2 text-right active\\\"\\u003e\\u003cspan style\\u003d\\\"color: red; \\\"\\u003e*\\u003c/span\\u003e投放目的：\\u003c/td\\u003e\\r\\n                            \\u003ctd\\u003e用户拉新\\u003c/td\\u003e\\r\\n                    \\u003c/tr\\u003e\\r\\n\\r\\n                    \\u003ctr\\u003e\\r\\n                        \\u003ctd class\\u003d\\\"col-md-2 text-right active\\\"\\u003e\\u003cspan style\\u003d\\\"color: red; \\\"\\u003e*\\u003c/span\\u003e特殊规则：\\u003c/td\\u003e\\r\\n                        \\u003ctd\\u003e有效期是否展示\\u003c/td\\u003e\\r\\n                    \\u003c/tr\\u003e\\r\\n\\r\\n                \\u003c/table\\u003e\\r\\n\\r\\n            \\u003c/div\\u003e\\r\\n        \\u003c/div\\u003e\\r\\n    \\u003c/div\\u003e\\r\\n\\r\\n\\u003cdiv class\\u003d\\\"card-header\\\"\\u003e\\u003cb\\u003e总申请成本\\u003c/b\\u003e\\u003c/div\\u003e\\r\\n    \\u003cdiv class\\u003d\\\"box-content\\\"\\u003e\\r\\n        \\u003cp\\u003e\\r\\n        \\u003ctable cellpadding\\u003d\\\"10\\\"\\u003e\\r\\n            \\u003ctr\\u003e\\r\\n                \\u003ctd class\\u003d\\\"col-md-2 text-right active\\\" style\\u003d\\\"color: red;\\\"\\u003e预估总成本：\\u003c/td\\u003e\\r\\n                \\u003ctd style\\u003d\\\"color: red;\\\"\\u003e ¥ 10,000元\\u003c/td\\u003e\\r\\n            \\u003c/tr\\u003e\\r\\n            \\u003ctr\\u003e\\r\\n                \\u003ctd class\\u003d\\\"col-md-2 text-right active\\\" style\\u003d\\\"color: red;\\\"\\u003e分摊规则：\\u003c/td\\u003e\\r\\n                \\u003ctd style\\u003d\\\"color: red;\\\"\\u003e\\r\\n                    \\u003c/br\\u003e\\r\\n                    销售运营一部 : 100%\\r\\n                \\u003c/td\\u003e\\r\\n            \\u003c/tr\\u003e\\r\\n        \\u003c/table\\u003e\\r\\n        \\u003cp\\u003e\\r\\n    \\u003c/div\\u003e\\r\\n\\u003c/div\\u003e\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\u003cdiv class\\u003d\\\"card-header\\\"\\u003e\\u003cb\\u003e适用商品\\u003c/b\\u003e\\u003c/div\\u003e\\r\\n\\u003cp class\\u003d\\\"box-name\\\"\\u003e\\u003cspan style\\u003d\\\"color: red; \\\"\\u003e适用货品0个，适用套装0个，明细如下：\\u003c/span\\u003e\\u003c/p\\u003e\\r\\n\\u003cdiv class\\u003d\\\"box-content\\\"\\u003e\\r\\n    \\u003cdiv\\u003e\\r\\n        \\u003ctable border\\u003d\\\"1\\\" cellpadding\\u003d\\\"10\\\"\\u003e\\r\\n            \\u003ctr\\u003e\\r\\n                \\u003ctd style\\u003d\\\"color:blue\\\"\\u003e货品类目\\u003c/td\\u003e\\r\\n                \\u003ctd style\\u003d\\\"color:blue\\\"\\u003e货品数量\\u003c/td\\u003e\\r\\n                \\u003ctd style\\u003d\\\"color:blue\\\"\\u003e套装数量\\u003c/td\\u003e\\r\\n            \\u003c/tr\\u003e\\r\\n\\r\\n                \\u003ctr\\u003e\\r\\n                    \\u003ctd\\u003e其他/手机配件/手机自研壳模套\\u003c/td\\u003e\\r\\n                    \\u003ctd\\u003e\\r\\n                        货品：1\\r\\n                    \\u003c/td\\u003e\\r\\n                        \\u003ctd rowspan\\u003d2\\u003e0\\u003c/td\\u003e\\r\\n                \\u003c/tr\\u003e\\r\\n                \\u003ctr\\u003e\\r\\n                    \\u003ctd\\u003e其他/其他/ShareSave\\u003c/td\\u003e\\r\\n                    \\u003ctd\\u003e\\r\\n                        货品：1\\r\\n                    \\u003c/td\\u003e\\r\\n                \\u003c/tr\\u003e\\r\\n\\r\\n\\r\\n        \\u003c/table\\u003e\\r\\n\\r\\n            \\u003ca href\\u003dhttps://staging-cnbj2-fds.api.xiaomi.net\\\\nr-coupon-bucket\\\\bpm/goods/审核13商品.xlsx\\u003e导出商品列表\\u003c/a\\u003e\\r\\n    \\u003c/div\\u003e\\r\\n\\u003c/div\\u003e\\r\\n\\u003c/div\\u003e\\r\\n\\r\\n\\u003cp\\u003e\\r\\n\\r\\n\\r\\n\\u003cdiv class\\u003d\\\"card-header\\\"\\u003e\\u003cb\\u003e审批信息\\u003c/b\\u003e\\u003c/div\\u003e\\r\\n\\u003cdiv class\\u003d\\\"box-content\\\"\\u003e\\r\\n    \\u003cdiv\\u003e\\r\\n        \\u003ctable cellpadding\\u003d\\\"10\\\"\\u003e\\r\\n            \\u003ctr\\u003e\\r\\n                \\u003ctd\\u003e\\u003cspan style\\u003d\\\"color: red; \\\"\\u003e*\\u003c/span\\u003e申请附件\\u003c/td\\u003e\\r\\n                    \\u003ctr\\u003e\\r\\n                        \\u003ctd\\u003e\\r\\n                            \\u003cimg src\\u003dhttps://staging-cnbj2-fds.api.xiaomi.net/nr-inner/202502081600_28e7a2044002526419ec0eba6407b199.jpg /\\u003e\\r\\n                            \\u003ca href\\u003dhttps://staging-cnbj2-fds.api.xiaomi.net/nr-inner/202502081600_28e7a2044002526419ec0eba6407b199.jpg target\\u003d\\\"_blank\\\"\\u003e电磁炮2.jpg\\u003c/a\\u003e\\r\\n                        \\u003c/td\\u003e\\r\\n                    \\u003c/tr\\u003e\\r\\n        \\u003c/tr\\u003e\\r\\n        \\u003c/table\\u003e\\r\\n        \\u003ca href\\u003dhttps://work-test.g.mi.com/main-global-promotion-admin/coupon/goods?bpmId\\u003d13  target\\u003d\\\"_blank\\\"\\u003e详细信息请到业务系统查看\\u003c/a\\u003e\\r\\n    \\u003c/div\\u003e\\r\\n\\u003c/div\\u003e\\r\\n\\u003c/div\\u003e\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\u003c/div\\u003e\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\"}";
        ProcessCreateDTO processCreateDTO = GsonUtil.fromJson(content, ProcessCreateDTO.class);
        // 分配的appKey
        // 生成签名，使用平台生成的secretKey
        Map<String, String> requestMap = Maps.newHashMap();
        requestMap.put("appKey","bpm");
        requestMap.put("params",GsonUtil.toJson(processCreateDTO));
        requestMap.put("timestamp",DateUtil.formatDateTime(DateUtil.date()));
        String sign = HttpClientUtil.signTopRequest(requestMap, "55NegnAUktDTfPqwL0OnQFxRcaXsKi92");
        requestMap.put("sign",sign);
        log.info("requestMap:{}",GsonUtil.toJson(requestMap));
        try {
            String result = HttpClientUtil.doPostV2("http://global-xmmionegw.test.mi.com/mtop/bpm/process/create",
                    GsonUtil.fromJson(GsonUtil.toJson(requestMap),new TypeToken<Map<String, Object>>(){}.getType()), null, 3000, 3000, 5000);
//                    GsonUtil.toJson(requestMap), null, 3000, 3000, 5000);
            log.info("BpmProxy.submitReviewHttp:{}",result);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        log.info("end..............");

    }
    @Test
    public void testSer(){
//        {"2":{"all":true,"limitIds":[]},{"3":{"all":false,"limitIds":["s1","s2","s3"]}}r
        Map<Integer, UseChannelVO> useChannel = GsonUtil.fromJson("{\"3\":{\"all\":false,\"limitIds\":[\"s1\",\"s2\",\"s3\"]}}",new TypeToken<Map<Integer, UseChannelVO>>(){}.getType());
        List<String> storeIdList = useChannel.values().stream().flatMap(e -> {
            if (CollectionUtils.isNotEmpty(e.getLimitIds())) {
                return e.getLimitIds().stream();
            } else {
                return Stream.of("*");
            }
        }).collect(Collectors.toList());
        System.out.println(storeIdList);
    }


    @PostConstruct
    private void init() {
        RpcContext.getContext().getAttachments().put("$upc_account", "p-xueqizheng");
    }
}