package com.xiaomi.nr.coupon.admin.util;

import com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.assertEquals;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CouponAdminBootstrap.class)
@ActiveProfiles("sg_staging")
public class CouponDiscountUtilTest {

    @Test
    public void testDiscountCalculations(){
        String descRule = "{(1000-#discount)/10}% off";
        Long discount = 400L;
        String formattedDesc = CouponDiscountUtil.formatDiscountCouponDesc(descRule, discount);

        assertEquals("60% off", formattedDesc);  // 预期格式化描述
        System.out.println("Case 1 passed: " + formattedDesc);

        // 折扣倍数计算 ({discount/100}折)
        String descRule1 = "{#discount/100}折";
        Long discount1 = 400L;
        String formattedDesc1 = CouponDiscountUtil.formatDiscountCouponDesc(descRule1, discount1);

        assertEquals("4折", formattedDesc1);  // 400/100 = 4折
        System.out.println("passed: " + formattedDesc1);

        //  多语言百分比折扣 (Diskaun {(1000-discount)/10}%)
        String descRule2 = "Diskaun {(1000-#discount)/10}%";
        Long discount2 = 400L;
        String formattedDesc2 = CouponDiscountUtil.formatDiscountCouponDesc(descRule2, discount2);
        assertEquals("Diskaun 60%", formattedDesc2);  // 保持多语言前缀
        System.out.println("passed: " + formattedDesc2 );
    }
}
