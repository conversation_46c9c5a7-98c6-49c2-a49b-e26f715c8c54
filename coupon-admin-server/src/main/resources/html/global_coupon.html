<div class="card">
    <div class="card-header"><b>重要信息</b></div>
    <div class="card-body">
        <div class="box-content">
            <div>
                <table cellpadding="10">

                    <tr>
                        <td class="col-md-2 text-right active">
                            <span style="color: red; ">*</span>优惠券类型：
                        </td>
                        <#if couponTypeText_v2??>
                        <td style="color: red; ">${couponTypeText_v2}</td>
                        <#else>
                        <td>${couponTypeText}</td>
                    </#if>
                    </tr>
                    <tr>
                        <td class="col-md-2 text-right active"><span style="color: red; ">*</span>优惠券名称：</td>
                        <#if name_v2??>
                            <td style="color: red; ">${name_v2}</td>
                        <#else>
                            <td>${name}</td>
                        </#if>
                    </tr>
                    <tr>
                        <td class="col-md-2 text-right active"><span style="color: red; ">*</span>领取时间：</td>
                        <td style="color: red; ">
                            <#if startFetchTime_v2??>
                                ${startFetchTime_v2}
                            <#else>
                                ${startFetchTime}
                            </#if>
                        <span>至</span>
                            <#if endFetchTime_v2??>
                                ${endFetchTime_v2}
                            <#else>
                                ${endFetchTime}
                            </#if>
                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-2 text-right active"><span style="color: red; ">*</span>使用时间：</td>
                        <#if useTimeText_v2??>
                            <td style="color:red">${useTimeText_v2}</td>
                        <#else>
                            <td style="color:red">${useTimeText}</td>
                        </#if>
                    </tr>

                    <tr>
                        <td class="col-md-2 text-right active"><span style="color: red; ">*</span>使用渠道：</td>
                        <#if useChannelTextStr_v2??>
                            <td style="color:red">${useChannelTextStr_v2}</td>
                            <#else>
                                <td>${useChannelTextStr}</td>
                        </#if>
                    </tr>

                    <tr>
                        <td class="col-md-2 text-right active"><span style="color: red; ">*</span>折扣类型：</td>
                        <td>
                            <#if promotionText_v2??>
                                <span style="color: red; ">${promotionText_v2}</span>
                            <#else>
                                ${promotionText}
                            </#if>
                            <br/>
                            <#if bottomText_v2??>
                                <span style="color: red; ">${bottomText_v2}</span>
                            <#else>
                                ${bottomText}
                            </#if>
                        </td>
                    </tr>

                    <tr>
                        <td class="col-md-2 text-right active">
                            <span style="color: red; ">*</span>优惠金额：
                        </td>
                        <#if promotionValue_v2??>
                        <td style="color: red; ">${promotionValue_v2}</td>
                        <#else>
                        <td>${promotionValue}</td>
                    </#if>
                    </tr>

                    <tr>
                        <td class="col-md-2 text-right active"><span style="color: red; ">*</span>发放总量：</td>
                        <#if applyCount_v2??>
                            <td style="color:red">${applyCount_v2}张</td>
                        <#else>
                            <td style="color:red">${applyCount}张</td>
                        </#if>
                    </tr>
                    <tr>
                        <td class="col-md-2 text-right active"><span style="color: red; ">*</span>投放场景：</td>
                        <#if sendSceneText_v2??>
                            <td style="color: red; ">${sendSceneText_v2}</td>
                        <#else>
                            <td>${sendSceneText}</td>
                        </#if>
                    </tr>
                    <tr>
                        <td class="col-md-2 text-right active"><span style="color: red; ">*</span>投放目的：</td>
                        <#if sendPurposeText_v2??>
                            <td style="color: red; ">${sendPurposeText_v2}</td>
                        <#else>
                            <td>${sendPurposeText}</td>
                        </#if>
                    </tr>
                </table>

            </div>
        </div>
    </div>

</div>



<#if goodsNotifyInfo?? && (goodsNotifyInfo.hasLowLevelGoods?string('true','false')) == 'true'>
<div class="card-header"><b>警示信息</b></div>

<#if goodsNotifyInfo.nowDiscountLevel?? && (goodsNotifyInfo.nowDiscountLevel gt 0)>
<p class="box-name">使用优惠券购买商品折扣力度<span style="color: red; ">低于8.5折（当前为${goodsNotifyInfo.nowDiscountLevel}折），请确认审批风险点</span></p>
<#else>

<p class="box-name">使用优惠券购买以下商品<span style="color: red; ">低于8.5折，请确认审批风险点</span></p>
<div class="box-content">
    <div>

        <#if goodsNotifyInfo.skuInfoVOList?? && (goodsNotifyInfo.skuInfoVOList?size > 0) >
        <table border="1" cellpadding="10">
            <tr>
                <td style="color:blue">sku</td>
                <td style="color:blue">商品名称</td>
                <td style="color:blue">平台价（划线价）</td>
                <td style="color:blue">售价</td>
                <td style="color:blue">预估折扣力度</td>
            </tr>

                    <#list goodsNotifyInfo.skuInfoVOList as skuInfoVO>
                    <tr>
                        <td style="color:red">${skuInfoVO.goodsId?c}</td>
                        <td style="color:red">${skuInfoVO.goodsName}</td>
                        <td style="color:red"><#if skuInfoVO.marketPrice??>￥${skuInfoVO.marketPrice}元</#if></td>

                        <td style="color:red">
                            <#list skuInfoVO.goodsChannelVOs as goodsChannelVO>
                                ${goodsChannelVO.useChannelName}：${goodsChannelVO.price}元
                                <br>
                            </#list>
                        </td>

                        <td style="color:red">
                            <#list skuInfoVO.goodsChannelVOs as goodsChannelVO>
                            <#if goodsChannelVO.price??>
                            ${goodsChannelVO.useChannelName}：${goodsChannelVO.discount}折
                            <br>
                            </#if>
                            </#list>
                        </td>
                    </tr>
                    </#list>
        </table>
    </#if>

    <#if goodsNotifyInfo.packageInfoVOList?? && (goodsNotifyInfo.packageInfoVOList?size > 0) >
    <table border="1" cellpadding="10">
        <tr>
            <td style="color:blue">套装id</td>
            <td style="color:blue">套装名称</td>
            <td style="color:blue">平台价（划线价）</td>
            <td style="color:blue">售价</td>
            <td style="color:blue">预估折扣力度</td>
        </tr>

        <#list goodsNotifyInfo.packageInfoVOList as packageInfoVO>
        <tr>
            <td style="color:red">${packageInfoVO.goodsId?c}</td>
            <td style="color:red">${packageInfoVO.goodsName}</td>
            <td style="color:red"><#if packageInfoVO.marketPrice??>￥${packageInfoVO.marketPrice}元</#if></td>

            <td style="color:red">
                <#list packageInfoVO.goodsChannelVOs as goodsChannelVO>
                <#if goodsChannelVO.price??>
                ${goodsChannelVO.useChannelName}：${goodsChannelVO.price}元
                <br>
                </#if>
                </#list>
            </td>

            <td style="color:red">
                <#list packageInfoVO.goodsChannelVOs as goodsChannelVO>
                ${goodsChannelVO.useChannelName}：${goodsChannelVO.discount}折
                <br>
            </#list>
            </td>
        </tr>
        </#list>
    </table>
    </#if>




</div>
</div>
<p>
</#if>

</#if>



<div class="card-header"><b>适用商品</b></div>
<p class="box-name"><span style="color: red; ">适用货品${totalSku}个，适用套装${totalPackage}个，明细如下：</span></p>
<div class="box-content">
    <div>
        <table border="1" cellpadding="10">
            <tr>
                <td style="color:blue">货品类目</td>
                <td style="color:blue">货品数量</td>
                <td style="color:blue">套装数量</td>
            </tr>
            <#if goodsSuitables?? && (goodsSuitables?size > 0)>

                <#list goodsSuitables as goodsSuitable>
                <tr>
                    <td>${goodsSuitable.categoryTextList}</td>
                    <td>
                        货品：${goodsSuitable.goodsAmount}
                    </td>
                    <#if goodsSuitable_index == 0>
                        <td rowspan=${goodsSuitables?size}>${packageAmount}</td>
                    </#if>
                </tr>
                </#list>

            <#else>
                <tr>
                    <td></td>
                    <td></td>
                    <td>${packageAmount}</td>
                </tr>
            </#if>

        </table>
    </div>
</div>
</div>

<p>


<div class="card-header"><b>审批信息</b></div>
<div class="box-content">
    <div>
        <a href=${systemUrl}  target="_blank">详细信息请到业务系统查看</a>
    </div>
</div>
</div>





</div>





