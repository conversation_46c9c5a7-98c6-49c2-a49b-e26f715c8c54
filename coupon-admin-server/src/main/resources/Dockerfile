FROM miserver
MAINTAINER <EMAIL>
RUN mkdir -p /home/<USER>/coupon-admin/
RUN mkdir -p /home/<USER>/log/coupon-admin/
COPY coupon-admin-server-1.0.0-SNAPSHOT.jar /home/<USER>/coupon-admin/
ENTRYPOINT ["java","-jar","-Xms512M","-Xmx512M","-XX:+UseG1GC","-XX:+PrintReferenceGC","-XX:+PrintGCDetails","-XX:+PrintGCDateStamps","-XX:+PrintHeapAtGC","-Xloggc:/home/<USER>/log/coupon-admin/gc.log","/home/<USER>/coupon-admin/coupon-admin-server-1.0.0-SNAPSHOT.jar"]