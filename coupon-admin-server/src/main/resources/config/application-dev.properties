#server
app.name=coupon-admin
server.type=dev
server.port=8080
server.debug=true
server.connection-timeout=1000
i18n.group=CN

dubbo.group=dev
dubbo.protocol.id=dubbo
dubbo.protocol.name=dubbo
dubbo.protocol.port=-1
dubbo.protocol.payload=11557050
dubbo.registry.address=nacos://nacos.test.b2c.srv:80
nacos.config.addrs=nacos.test.b2c.srv:80

youpin.log.group=dev
log.path=/tmp

talos.topic=cnzone_newretail_common_test
talos.sendpoint=http://staging-cnbj2-talos.api.xiaomi.net
talos.access.key=AKTAFN57IDDEY7VBBP
talos.access.secret=GDAz37cI7SbSMQ10JYFhtP/7HITSFKGLvONy27vYK7Du8eRdsSZaO2r5FGR2QGVq7jwYEiEeAZPnMExirYKutGrcjUyR/xgQHvyHWbQbSgO9eisdSQHbhhgURUM6w2HSfto+rWQbz3qygM/ac6gA
talos.access.secret@kc-sid=mi_newretail_risk.g


spring.xmpulsenatl-datasource.name=xm_pulse_natl
spring.xmpulsenatl-datasource.username=pulse_natl_w
spring.xmpulsenatl-datasource.url=*********************************************************************************************
spring.xmpulsenatl-datasource.password=GCDWg/Nvx25twx10eiPcgw/JqQc9XuE0WKfoq8tmaOO7EBgSJQvBAseiRBW4RA4JYltm9Nn/GBCbFA3EqG5Aao099q0QPMpGGBQUFFlX/bb7K8pyShv6O67fQR1mIAA=
spring.xmpulsenatl-datasource.password@kc-sid=mi_newretail_risk.g
spring.xmpulsenatl-datasource.connectionProperties=
spring.xmpulsenatl-datasource.sql-script-encoding=UTF-8
spring.xmpulsenatl-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.xmpulsenatl-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.xmpulsenatl-datasource.initial-size=5
spring.xmpulsenatl-datasource.max-active=20
spring.xmpulsenatl-datasource.min-idle=3
spring.xmpulsenatl-datasource.max-wait=60000
spring.xmpulsenatl-datasource.remove-abandoned=true
spring.xmpulsenatl-datasource.remove-abandoned-timeout=180
spring.xmpulsenatl-datasource.time-between-eviction-runs-millis=60000
spring.xmpulsenatl-datasource.min-evictable-idle-time-millis=300000
spring.xmpulsenatl-datasource.validation-query=SELECT 1 FROM DUAL
spring.xmpulsenatl-datasource.test-while-idle=true
spring.xmpulsenatl-datasource.test-on-borrow=false
spring.xmpulsenatl-datasource.test-on-return=false
spring.xmpulsenatl-datasource.pool-prepared-statements=true
spring.xmpulsenatl-datasource.max-pool-prepared-statement-per-connection-size=50
spring.xmpulsenatl-datasource.filters=stat,wall,slf4j

spring.xmpulsecoupon-datasource.name=xm_pulse_natl
spring.xmpulsecoupon-datasource.username=pulse_natl_w
spring.xmpulsecoupon-datasource.url=*********************************************************************************************
spring.xmpulsecoupon-datasource.password=GCC/YfwKrpJHsObgiqs0CpgOcIdpqLHG7h8ELQNh4am2GhgStOqPwSecTvyP/UMN0EIwhLP/GBDwcBWVDZ5JE5QmOHZoFSFqGBR0JvJ9N7MKuaAHVHZ5jlS1R4NHdwA=
spring.xmpulsecoupon-datasource.password@kc-sid=mi_newretail_risk.g
spring.xmpulsecoupon-datasource.connectionProperties=
spring.xmpulsecoupon-datasource.sql-script-encoding=UTF-8
spring.xmpulsecoupon-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.xmpulsecoupon-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.xmpulsecoupon-datasource.initial-size=5
spring.xmpulsecoupon-datasource.max-active=20
spring.xmpulsecoupon-datasource.min-idle=3
spring.xmpulsecoupon-datasource.max-wait=60000
spring.xmpulsecoupon-datasource.remove-abandoned=true
spring.xmpulsecoupon-datasource.remove-abandoned-timeout=180
spring.xmpulsecoupon-datasource.time-between-eviction-runs-millis=60000
spring.xmpulsecoupon-datasource.min-evictable-idle-time-millis=300000
spring.xmpulsecoupon-datasource.validation-query=SELECT 1 FROM DUAL
spring.xmpulsecoupon-datasource.test-while-idle=true
spring.xmpulsecoupon-datasource.test-on-borrow=false
spring.xmpulsecoupon-datasource.test-on-return=false
spring.xmpulsecoupon-datasource.pool-prepared-statements=true
spring.xmpulsecoupon-datasource.max-pool-prepared-statement-per-connection-size=50
spring.xmpulsecoupon-datasource.filters=stat,wall,slf4j

spring.xmbdmall-datasource.name=xm_mall
spring.xmbdmall-datasource.username=xm_mall_wn
spring.xmbdmall-datasource.url=***************************************************************************************
spring.xmbdmall-datasource.password=GDBX9twx1nCzoYj5WK-9jrQX7lz5fCPrfDZBEvXf_k3TCdFfKe4snFz0BCMukvBEWKEYEq7_OtVGY0DvhbIyvz310WUJ_xgQ2WSxBzhBTOmZ9zPPm36QDBgU4nxdFchgEKqMKh6syb-Dzhno5ZIA
spring.xmbdmall-datasource.password@kc-sid=mi_newretail_risk.g
spring.xmbdmall-datasource.connectionProperties=
spring.xmbdmall-datasource.sql-script-encoding=UTF-8
spring.xmbdmall-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.xmbdmall-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.xmbdmall-datasource.initial-size=5
spring.xmbdmall-datasource.max-active=20
spring.xmbdmall-datasource.min-idle=3
spring.xmbdmall-datasource.max-wait=60000
spring.xmbdmall-datasource.remove-abandoned=true
spring.xmbdmall-datasource.remove-abandoned-timeout=180
spring.xmbdmall-datasource.time-between-eviction-runs-millis=60000
spring.xmbdmall-datasource.min-evictable-idle-time-millis=300000
spring.xmbdmall-datasource.validation-query=SELECT 1 FROM DUAL
spring.xmbdmall-datasource.test-while-idle=true
spring.xmbdmall-datasource.test-on-borrow=false
spring.xmbdmall-datasource.test-on-return=false
spring.xmbdmall-datasource.pool-prepared-statements=true
spring.xmbdmall-datasource.max-pool-prepared-statement-per-connection-size=50
spring.xmbdmall-datasource.filters=stat,wall,slf4j

spring.xmpulse-datasource.name=xm_pulse
spring.xmpulse-datasource.username=pulse_user
spring.xmpulse-datasource.url=**************************************************************************************
spring.xmpulse-datasource.password=GCAeXIADqQkaYQ/+gOYD8qqXJ+hXAMlQV51IGFM3pjxFcRgSGcBkhnfbTYCKqAtr/6IWv5f/GBBhF9Sols9F+o46q/x2RbYEGBS7UTZU92+1SRS6IDQQIhCjv7LrlQA=
spring.xmpulse-datasource.password@kc-sid=mi_newretail_risk.g
spring.xmpulse-datasource.connectionProperties=
spring.xmpulse-datasource.sql-script-encoding=UTF-8
spring.xmpulse-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.xmpulse-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.xmpulse-datasource.initial-size=5
spring.xmpulse-datasource.max-active=100
spring.xmpulse-datasource.min-idle=3
spring.xmpulse-datasource.max-wait=60000
spring.xmpulse-datasource.remove-abandoned=true
spring.xmpulse-datasource.remove-abandoned-timeout=180
spring.xmpulse-datasource.time-between-eviction-runs-millis=60000
spring.xmpulse-datasource.min-evictable-idle-time-millis=300000
spring.xmpulse-datasource.validation-query=SELECT 1 FROM DUAL
spring.xmpulse-datasource.test-while-idle=true
spring.xmpulse-datasource.test-on-borrow=false
spring.xmpulse-datasource.test-on-return=false
spring.xmpulse-datasource.pool-prepared-statements=true
spring.xmpulse-datasource.max-pool-prepared-statement-per-connection-size=50
spring.xmpulse-datasource.filters=stat,wall,slf4j


spring.xmpulse-slave-datasource.name=xm_pulse
spring.xmpulse-slave-datasource.username=pulse_user
spring.xmpulse-slave-datasource.url=**************************************************************************************
spring.xmpulse-slave-datasource.password=GCCPkLjEaYtOu4ryTQkR6GYUwVXg5H2R3pIRiYlqe6/L+BgSI7R1A14EQ9CuTyDUp60xdFf/GBBQVXg4dllFvYTORVR8A4w0GBS3BdTYTc9doRoqXdbbbra4R1uOTQA=
spring.xmpulse-slave-datasource.password@kc-sid=mi_newretail_risk.g
spring.xmpulse-slave-datasource.connectionProperties=
spring.xmpulse-slave-datasource.sql-script-encoding=UTF-8
spring.xmpulse-slave-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.xmpulse-slave-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.xmpulse-slave-datasource.initial-size=5
spring.xmpulse-slave-datasource.max-active=100
spring.xmpulse-slave-datasource.min-idle=3
spring.xmpulse-slave-datasource.max-wait=60000
spring.xmpulse-slave-datasource.remove-abandoned=true
spring.xmpulse-slave-datasource.remove-abandoned-timeout=180
spring.xmpulse-slave-datasource.time-between-eviction-runs-millis=60000
spring.xmpulse-slave-datasource.min-evictable-idle-time-millis=300000
spring.xmpulse-slave-datasource.validation-query=SELECT 1 FROM DUAL
spring.xmpulse-slave-datasource.test-while-idle=true
spring.xmpulse-slave-datasource.test-on-borrow=false
spring.xmpulse-slave-datasource.test-on-return=false
spring.xmpulse-slave-datasource.pool-prepared-statements=true
spring.xmpulse-slave-datasource.max-pool-prepared-statement-per-connection-size=50
spring.xmpulse-slave-datasource.filters=stat,wall,slf4j


spring.xmpms-slave-datasource.name=xm_pms
spring.xmpms-slave-datasource.username=xm_pms_w
spring.xmpms-slave-datasource.url=************************************************************************************
spring.xmpms-slave-datasource.password=GCCiHKpkLP4l6tDLQl5MEil7q8d/kYvYVE5wqL4kuxH/JRgS/Id21fyCS5uS2Zm9XvxsuW//GBBDUTKj/CtBD64cVAVC8IRtGBQsbKj9Z5qw7+jB0j07gnKcQHx7hQA=
spring.xmpms-slave-datasource.password@kc-sid=mi_newretail_risk.g
spring.xmpms-slave-datasource.connectionProperties=
spring.xmpms-slave-datasource.sql-script-encoding=UTF-8
spring.xmpms-slave-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.xmpms-slave-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.xmpms-slave-datasource.initial-size=5
spring.xmpms-slave-datasource.max-active=100
spring.xmpms-slave-datasource.min-idle=3
spring.xmpms-slave-datasource.max-wait=60000
spring.xmpms-slave-datasource.remove-abandoned=true
spring.xmpms-slave-datasource.remove-abandoned-timeout=180
spring.xmpms-slave-datasource.time-between-eviction-runs-millis=60000
spring.xmpms-slave-datasource.min-evictable-idle-time-millis=300000
spring.xmpms-slave-datasource.validation-query=SELECT 1 FROM DUAL
spring.xmpms-slave-datasource.test-while-idle=true
spring.xmpms-slave-datasource.test-on-borrow=false
spring.xmpms-slave-datasource.test-on-return=false
spring.xmpms-slave-datasource.pool-prepared-statements=true
spring.xmpms-slave-datasource.max-pool-prepared-statement-per-connection-size=50
spring.xmpms-slave-datasource.filters=stat,wall,slf4j


spring.xmstore-datasource.name=xm_store
spring.xmstore-datasource.username=xm_store_w
spring.xmstore-datasource.url=**************************************************************************************
spring.xmstore-datasource.password=GDD4d01JgKRQ/oNdzEJ3hxFcD6VjNa7cEWKt0POuV+881EyAfM48j4mxyUKwRWF83hkYEvyHdtX8gkubktmZvV78bLlv/xgQNEtSyyicRJCFfu3w5TsPdhgUV3IJ915ALTSlYVvhyS4x/Q6iY9kA
spring.xmstore-datasource.password@kc-sid=mi_newretail_risk.g
spring.xmstore-datasource.connectionProperties=
spring.xmstore-datasource.sql-script-encoding=UTF-8
spring.xmstore-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.xmstore-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.xmstore-datasource.initial-size=5
spring.xmstore-datasource.max-active=20
spring.xmstore-datasource.min-idle=3
spring.xmstore-datasource.max-wait=60000
spring.xmstore-datasource.remove-abandoned=true
spring.xmstore-datasource.remove-abandoned-timeout=180
spring.xmstore-datasource.time-between-eviction-runs-millis=60000
spring.xmstore-datasource.min-evictable-idle-time-millis=300000
spring.xmstore-datasource.validation-query=SELECT 1 FROM DUAL
spring.xmstore-datasource.test-while-idle=true
spring.xmstore-datasource.test-on-borrow=false
spring.xmstore-datasource.test-on-return=false
spring.xmstore-datasource.pool-prepared-statements=true
spring.xmstore-datasource.max-pool-prepared-statement-per-connection-size=50
spring.xmstore-datasource.filters=stat,wall,slf4j


spring.adsdata-datasource.name=nrdc_bi
spring.adsdata-datasource.username=nrdc_all_rw
spring.adsdata-datasource.url=**********************************************************************************
spring.adsdata-datasource.password=GDBjgFtjj1xea+F2CsnTk3e2bDk4mnHHzDWm95UTzGpc/JQop7KnQct8YuhEmODwZOAYEm2RdXe9VELVnGgd9tbQvCmP/xgQ8q+SK18EQVu2ru3mO3w4IRgUPlM9ffgF2s2N8jMdFNzMAwY1AAMA
spring.adsdata-datasource.password@kc-sid=mi_newretail_risk.g
spring.adsdata-datasource.connectionProperties=
spring.adsdata-datasource.sql-script-encoding=UTF-8
spring.adsdata-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.adsdata-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.adsdata-datasource.initial-size=5
spring.adsdata-datasource.max-active=20
spring.adsdata-datasource.min-idle=3
spring.adsdata-datasource.max-wait=60000
spring.adsdata-datasource.remove-abandoned=true
spring.adsdata-datasource.remove-abandoned-timeout=180
spring.adsdata-datasource.time-between-eviction-runs-millis=60000
spring.adsdata-datasource.min-evictable-idle-time-millis=300000
spring.adsdata-datasource.validation-query=SELECT 1 FROM DUAL
spring.adsdata-datasource.test-while-idle=true
spring.adsdata-datasource.test-on-borrow=false
spring.adsdata-datasource.test-on-return=false
spring.adsdata-datasource.pool-prepared-statements=true
spring.adsdata-datasource.max-pool-prepared-statement-per-connection-size=50
spring.adsdata-datasource.filters=stat,wall,slf4j


spring.xmpulse-tidb-slave-datasource.name=xm_pulse
spring.xmpulse-tidb-slave-datasource.username=pulse_wr
spring.xmpulse-tidb-slave-datasource.url=***********************************************************************************
spring.xmpulse-tidb-slave-datasource.password=GDC9FxMmCauKVcw+VD9604YiEEr5lncHBGXmYP0juy6iy+UX4BYmxnkRO3T465sRe2EYEhnAZIZ3202AiqgLa/+iFr+X/xgQcXLWpW8ZQgammyy7g2Ny6xgUVT01zqgCKNwieGLeXmeMhe/x5JIA
spring.xmpulse-tidb-slave-datasource.password@kc-sid=mi_newretail_risk.g
spring.xmpulse-tidb-slave-datasource.connectionProperties=
spring.xmpulse-tidb-slave-datasource.sql-script-encoding=UTF-8
spring.xmpulse-tidb-slave-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.xmpulse-tidb-slave-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.xmpulse-tidb-slave-datasource.initial-size=5
spring.xmpulse-tidb-slave-datasource.max-active=20
spring.xmpulse-tidb-slave-datasource.min-idle=3
spring.xmpulse-tidb-slave-datasource.max-wait=60000
spring.xmpulse-tidb-slave-datasource.remove-abandoned=true
spring.xmpulse-tidb-slave-datasource.remove-abandoned-timeout=180
spring.xmpulse-tidb-slave-datasource.time-between-eviction-runs-millis=60000
spring.xmpulse-tidb-slave-datasource.min-evictable-idle-time-millis=300000
spring.xmpulse-tidb-slave-datasource.validation-query=SELECT 1 FROM DUAL
spring.xmpulse-tidb-slave-datasource.test-while-idle=true
spring.xmpulse-tidb-slave-datasource.test-on-borrow=false
spring.xmpulse-tidb-slave-datasource.test-on-return=false
spring.xmpulse-tidb-slave-datasource.pool-prepared-statements=true
spring.xmpulse-tidb-slave-datasource.max-pool-prepared-statement-per-connection-size=50
spring.xmpulse-tidb-slave-datasource.filters=stat,wall,slf4j

spring.xmpulse-oceanbase-slave-datasource.username=xm_pulse_st_wr@common_tenant#test_dba_common
spring.xmpulse-oceanbase-slave-datasource.url=jdbc:mysql://************:2883/xm_pulse
spring.xmpulse-oceanbase-slave-datasource.password=GCAuXGCW8zz9falY2PoD1sxZNeZBGPUj9_VuNRogkl3smxgSFvfHoMB_Qvi6rb_6yUrvbcr_GBCiPKuJbLRLk7bsH_fsGTTRGBTFiiB5ZCPh4K_NbGEFvTCQOArG6wA
spring.xmpulse-oceanbase-slave-datasource.password@kc-sid=mi_newretail_risk.g
spring.xmpulse-oceanbase-slave-datasource.connectionProperties=
spring.xmpulse-oceanbase-slave-datasource.sql-script-encoding=UTF-8
spring.xmpulse-oceanbase-slave-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.xmpulse-oceanbase-slave-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.xmpulse-oceanbase-slave-datasource.initial-size=5
spring.xmpulse-oceanbase-slave-datasource.max-active=20
spring.xmpulse-oceanbase-slave-datasource.min-idle=3
spring.xmpulse-oceanbase-slave-datasource.max-wait=60000
spring.xmpulse-oceanbase-slave-datasource.remove-abandoned=true
spring.xmpulse-oceanbase-slave-datasource.remove-abandoned-timeout=180
spring.xmpulse-oceanbase-slave-datasource.time-between-eviction-runs-millis=60000
spring.xmpulse-oceanbase-slave-datasource.min-evictable-idle-time-millis=300000
spring.xmpulse-oceanbase-slave-datasource.validation-query=SELECT 1 FROM DUAL
spring.xmpulse-oceanbase-slave-datasource.test-while-idle=true
spring.xmpulse-oceanbase-slave-datasource.test-on-borrow=false
spring.xmpulse-oceanbase-slave-datasource.test-on-return=false
spring.xmpulse-oceanbase-slave-datasource.pool-prepared-statements=true
spring.xmpulse-oceanbase-slave-datasource.max-pool-prepared-statement-per-connection-size=50
spring.xmpulse-oceanbase-slave-datasource.filters=stat,wall,slf4j

spring.nr-point-admin-datasource.name=nr_point_admin
spring.nr-point-admin-datasource.username=nr_point_admin_wn
spring.nr-point-admin-datasource.url=jdbc:mysql://gaea.test.mysql03.b2c.srv:13616/nr_point_admin?characterEncoding=utf8&useSSL=true
spring.nr-point-admin-datasource.password=GDBXgrTLvS5JRIDc1kDLVdDJz4MoAgi6Gy8b9_JvJqYUxa4nqCENPT1Ty0gN_zRfJS0YEtyZXSFI9UtEiUAniQkbrciU_xgQwVcqHro3R7StFNVaNrUf2hgUloOSqDXCn2jWVHKiMEp_8h5TzT8A
spring.nr-point-admin-datasource.password@kc-sid=mi_newretail_risk.g
spring.nr-point-admin-datasource.connectionProperties=
spring.nr-point-admin-datasource.sql-script-encoding=UTF-8
spring.nr-point-admin-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.nr-point-admin-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.nr-point-admin-datasource.initial-size=5
spring.nr-point-admin-datasource.max-active=20
spring.nr-point-admin-datasource.min-idle=3
spring.nr-point-admin-datasource.max-wait=60000
spring.nr-point-admin-datasource.remove-abandoned=true
spring.nr-point-admin-datasource.remove-abandoned-timeout=180
spring.nr-point-admin-datasource.time-between-eviction-runs-millis=60000
spring.nr-point-admin-datasource.min-evictable-idle-time-millis=300000
spring.nr-point-admin-datasource.validation-query=SELECT 1 FROM DUAL
spring.nr-point-admin-datasource.test-while-idle=true
spring.nr-point-admin-datasource.test-on-borrow=false
spring.nr-point-admin-datasource.test-on-return=false
spring.nr-point-admin-datasource.pool-prepared-statements=true
spring.nr-point-admin-datasource.max-pool-prepared-statement-per-connection-size=50
spring.nr-point-admin-datasource.filters=stat,wall,slf4j

spring.user-point-datasource.name=pulse_user
spring.user-point-datasource.username=user_point_wn
spring.user-point-datasource.url=jdbc:mysql://gaea.test.mysql03.b2c.srv:13616/user_point?characterEncoding=utf8&useSSL=true
spring.user-point-datasource.password=GDDGFOp2cTQj6W7ziAGc1U9XBON8HDRfcuN7EfPbZ6KZrcp77QiwFxHuw8CVFvBbXBQYEsjvpZ873EmAtX76YAFXTxnd_xgQJxJLXznZTpy1cD5K2jyP3RgU7M_6pK-9THLul7YalHhbgwteQXAA
spring.user-point-datasource.password@kc-sid=mi_newretail_risk.g
spring.user-point-datasource.connectionProperties=
spring.user-point-datasource.sql-script-encoding=UTF-8
spring.user-point-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.user-point-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.user-point-datasource.initial-size=5
spring.user-point-datasource.max-active=20
spring.user-point-datasource.min-idle=3
spring.user-point-datasource.max-wait=60000
spring.user-point-datasource.remove-abandoned=true
spring.user-point-datasource.remove-abandoned-timeout=180
spring.user-point-datasource.time-between-eviction-runs-millis=60000
spring.user-point-datasource.min-evictable-idle-time-millis=300000
spring.user-point-datasource.validation-query=SELECT 1 FROM DUAL
spring.user-point-datasource.test-while-idle=true
spring.user-point-datasource.test-on-borrow=false
spring.user-point-datasource.test-on-return=false
spring.user-point-datasource.pool-prepared-statements=true
spring.user-point-datasource.max-pool-prepared-statement-per-connection-size=50
spring.user-point-datasource.filters=stat,wall,slf4j


logging.level.com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.couponconfig=DEBUG
logging.level.com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.goods=DEBUG
logging.level.com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.coupon=DEBUG
logging.level.com.xiaomi.nr.coupon.admin.infrastructure.repository.tidbdao=DEBUG

spring.pulse.redis.host=wcc.cache01.test.b2c.srv
spring.pulse.redis.port=22122
spring.pulse.redis.password=GDB39DlQn4itke2YBivvyft4rhpVmUnbZyK49CgLzbUudiHcZODSQThDOPJqQJ8Aq0UYEi8Yl3Fyo0jcoLScbyw1ey3//xgQ5pEnWo/USrObzWYNOJdghxgUEnqW2AXdJEsEaB+MdUCopgr57YYA
spring.pulse.redis.password@kc-sid=mi_newretail_risk.g
spring.pulse.redis.connect-timeout-millis=1000
spring.pulse.redis.read-timeout-millis=3000
spring.pulse.redis.database=0
spring.pulse.redis.jedis-pool.max-idle=50
spring.pulse.redis.jedis-pool.min-idle=20
spring.pulse.redis.jedis-pool.max-total=50
spring.pulse.redis.jedis-pool.test-while-idle=true
spring.pulse.redis.jedis-pool.time-between-eviction-runs-millis=30000
spring.pulse.redis.jedis-pool.min-evictable-idle-time-millis=150000
spring.pulse.redis.jedis-pool.num-tests-per-eviction-run=-1
spring.pulse.redis.jedis-pool.max-wait-millis=3000


spring.newcoupon.redis.host=ares.test.common.cache.srv
spring.newcoupon.redis.port=22127
spring.newcoupon.redis.password=GDCfI3t0QtX2L+3i44Vo3FlpHpn5X4iN3p3tN0h+Jz2owWbK1EYoOnNqkquEQ7hW2jwYEjAi37a9vEy9vUvBRJ4AZGEq/xgQpvpa6h3BQH62O0VRYnrW5xgUX+/8/MotgHLNy/P0J81YenoP+aIA
spring.newcoupon.redis.password@kc-sid=mi_newretail_risk.g
spring.newcoupon.redis.connect-timeout-millis=1000
spring.newcoupon.redis.read-timeout-millis=3000
spring.newcoupon.redis.database=0
spring.newcoupon.redis.jedis-pool.max-idle=50
spring.newcoupon.redis.jedis-pool.min-idle=20
spring.newcoupon.redis.jedis-pool.max-total=50
spring.newcoupon.redis.jedis-pool.test-while-idle=true
spring.newcoupon.redis.jedis-pool.time-between-eviction-runs-millis=30000
spring.newcoupon.redis.jedis-pool.min-evictable-idle-time-millis=150000
spring.newcoupon.redis.jedis-pool.num-tests-per-eviction-run=-1
spring.newcoupon.redis.jedis-pool.max-wait-millis=3000

spring.pointadmin.redis.host=ares.test.common.cache.srv
spring.pointadmin.redis.port=22127
spring.pointadmin.redis.password=GDCfI3t0QtX2L+3i44Vo3FlpHpn5X4iN3p3tN0h+Jz2owWbK1EYoOnNqkquEQ7hW2jwYEjAi37a9vEy9vUvBRJ4AZGEq/xgQpvpa6h3BQH62O0VRYnrW5xgUX+/8/MotgHLNy/P0J81YenoP+aIA
spring.pointadmin.redis.password@kc-sid=mi_newretail_risk.g
spring.pointadmin.redis.connect-timeout-millis=1000
spring.pointadmin.redis.read-timeout-millis=3000
spring.pointadmin.redis.database=0
spring.pointadmin.redis.jedis-pool.max-idle=50
spring.pointadmin.redis.jedis-pool.min-idle=20
spring.pointadmin.redis.jedis-pool.max-total=50
spring.pointadmin.redis.jedis-pool.test-while-idle=true
spring.pointadmin.redis.jedis-pool.time-between-eviction-runs-millis=30000
spring.pointadmin.redis.jedis-pool.min-evictable-idle-time-millis=150000
spring.pointadmin.redis.jedis-pool.num-tests-per-eviction-run=-1
spring.pointadmin.redis.jedis-pool.max-wait-millis=3000


spring.karos.misc.redis.host=wcc.cache01.test.b2c.srv
spring.karos.misc.redis.port=22122
spring.karos.misc.redis.password=GBB2I7G0OV0Memm+7s7ebwWvGBKG1PdXW9xAfKYDAyvmaIUWHv8YEInaY/iAhEARlTnHcOCpCe0YFIPHiOl8pZYS9qcsiPheRGi2z3EdAA==
spring.karos.misc.redis.password@kc-sid=mi_newretail_risk.g
spring.karos.misc.redis.connect-timeout-millis=1000
spring.karos.misc.redis.read-timeout-millis=3000
spring.karos.misc.redis.database=0
spring.karos.misc.redis.jedis-pool.max-idle=50
spring.karos.misc.redis.jedis-pool.min-idle=20
spring.karos.misc.redis.jedis-pool.max-total=50
spring.karos.misc.redis.jedis-pool.test-while-idle=true
spring.karos.misc.redis.jedis-pool.time-between-eviction-runs-millis=30000
spring.karos.misc.redis.jedis-pool.min-evictable-idle-time-millis=150000
spring.karos.misc.redis.jedis-pool.num-tests-per-eviction-run=-1
spring.karos.misc.redis.jedis-pool.max-wait-millis=3000


#领券活动缓存会用到
spring.misc.redis.host=wcc.cache01.test.b2c.srv
spring.misc.redis.port=22122
spring.misc.redis.password=GBD5EZuuMqRZHSGim+H8z8t8GBLBP4x6Zf5Di6J9i+hWgK8onv8YEEDy3tUhD0xzoAsA0JI0H/UYFClSnAp/T1+Jo3vS2pspFWPqa47oAA==
spring.misc.redis.password@kc-sid=mi_newretail_risk.g
spring.misc.redis.connect-timeout-millis=1000
spring.misc.redis.read-timeout-millis=3000
spring.misc.redis.database=0
spring.misc.redis.jedis-pool.max-idle=50
spring.misc.redis.jedis-pool.min-idle=20
spring.misc.redis.jedis-pool.max-total=50
spring.misc.redis.jedis-pool.test-while-idle=true
spring.misc.redis.jedis-pool.time-between-eviction-runs-millis=30000
spring.misc.redis.jedis-pool.min-evictable-idle-time-millis=150000
spring.misc.redis.jedis-pool.num-tests-per-eviction-run=-1
spring.misc.redis.jedis-pool.max-wait-millis=3000


spring.coupon.es.host=tjstaging.api.es.srv:80
spring.coupon.es.username=xiaomi
spring.coupon.es.password=xiaomi
spring.coupon.es.index=nr_coupon_center_coupon_config


keycenter.addr=keycenter-test.b2c.srv:9988
keycenter.sid=keycenter-test


#---bpm����---
bpm.dubbo.group=test
dubbo.nr-bpm.appId=xm-yp-upc-0181
dubbo.nr-bpm.appKey=GDAa0oNL1T6+J8Zb6gj7oWwbXe7+dJ0clMdMfdOk5+bvGoNt0daueaDXS74xRlVqymsYEnv8HzQ9n0AotEgwe1b7GMLi/xgQU8lpn/HWQaK4ULv3+iJQkBgUoCPWrDzt7gr3bZQ06k2JDk75SqMA
dubbo.nr-bpm.appKey@kc-sid=mi_newretail_risk.g
bpm.coupon.definitionKey=mi_online_coupon
bpm.coupon.newRetailKey=newretail_freight_coupon
bpm.task.fillTaskRetailKey=newretail_fill_coupon
bpm.coupon.subsidyCouponKey=super_subsidy_coupon
#---bpm����---


store.group=staging
gms.dubbo.group=staging
gis.dubbo.group=staging
gis.dubbo.group.offline=staging
order.dubbo.group=staging
aries.dubbo.group=staging
cis.dubbo.group=staging
phoenix.dubbo.group=staging


miShop.activity.event.api=http://event-be.test.mi.com


fds.accessKey=AKB7FI7MHQH2BBQQDW
fds.secretKey=GDBsq9RzEFWxqWcJzRH4TQFalJO1hn0zd27nv3cyO7T2En6OYSC/456cY6j/hyDLcrcYEjJ0phsquUdhgbxxpaZOZXew/xgQnQOLhsWdQPGzI2oWYZD9oxgUE7NREtydh2tVy81gx28y1ODkwbsA
fds.secretKey@kc-sid=mi_newretail_risk.g
fds.endpoint=staging-cnbj2-fds.api.xiaomi.net
fds.bucketName=nr-coupon-bucket

coupon.review.url=https://work-be.test.mi.com/main-coupon-manage/coupon/list?type=review&id=
coupon.global.review.url=https://work-test.g.mi.com/main-global-promotion-admin/coupon/goods?bpmId=
coupon.update.url=https://work-be.test.mi.com/main-coupon-manage/coupon/list?type=editCoupon&id=
postCoupon.review.url=https://work-be.test.mi.com/main-coupon-manage/coupon/freightCouponList?type=review&id=
postCoupon.update.url=https://work-be.test.mi.com/main-coupon-manage/coupon/freightCouponList?type=editCoupon&id=
subsidyCoupon.review.url=https://work-be.test.mi.com/main-coupon-manage/coupon/subsidyCouponList?type=review&id=
subsidyCoupon.update.url=https://work-be.test.mi.com/main-coupon-manage/coupon/subsidyCouponList?type=editCoupon&id=

userApi.url=http://api.d.mi.com
userApi.key=a219beff5c4baa56



rocketmq.name-server=staging-cnbj2-rocketmq.namesrv.api.xiaomi.net:9876
rocketmq.producer.group=nr_coupon_postoffice_producer_group
rocketmq.producer.sendMessageTimeout=300000
rocketmq.producer.access-key=AKB7FI7MHQH2BBQQDW
rocketmq.producer.secret-key=GDA5kiTgld61hxW3G0dLFpKsNTU8eqNnq5Y35Sxylz0C+188L9cG254wXCVRvSoV/DwYEtuSI8VLCEZhrYQP3AtGFq92/xgQ1VjPB6DnSniNxVqRUp4aXBgUdoPZPney5DtE95Bm40PFrOIVl4kA
rocketmq.producer.secret-key@kc-sid=mi_newretail_risk.g
postoffice.topic=CI96578_Youpin_Committee_Msg_Listener
#------品类券新品提醒场景id------
postoffice.newGoodsNotifySceneId=218
postoffice.pointLowStockNotifySceneId=261

bpm.secret=
bpm.url=
bpm.url.cancel=

nr.rocketmq.name-server=staging-cnbj2-rocketmq.namesrv.api.xiaomi.net:9876
nr.rocketmq.producer.group=nr-coupon
nr.rocketmq.producer.access-key=AKB7FI7MHQH2BBQQDW
nr.rocketmq.producer.secret-key=GDDGzV/xwzmqwlz6gpMKY2ICbBcJAopdOd1pjs1ry6IP/zThU1wsIj5xW0u6h/7sg2cYEsE/jHpl/kOLon2L6FaAryie/xgQMBlvvaR0QR+pKEfnCRw5dRgUEdemi9+tFCIscMc/WB3yOkvj520A
nr.rocketmq.producer.secret-key@kc-sid=mi_newretail_risk.g
nr.rocketmq.producer.sendMessageTimeout=300000

#优惠券即将过期提醒push
nr.rocketmq.producer.coupon.expire.topic=nr_coupon_expire_push_test
#优惠券修改提醒push
nr.rocketmq.producer.coupon.change.topic=nr_coupon_config_change


#飞书消息必填 环境标识
env=local

rpc.etcd.host=http://etcd.test.mi.com

# ems config
oaucf.ems.url=https://ems-finance.test.mioffice.cn
oaucf.ems.appId=expense_management_test
oaucf.ems.appSecret=aea004ec9c661e0ac26a66051c3d7e4e

hdfs.url=hdfs://tjwqstaging-hdd

aes.password=M6EED09BCC6539AD
