#server
app.name=coupon-admin
server.type=preview
server.port=8080
server.debug=true
server.connection-timeout=1000
i18n.group=CN

dubbo.group=preview
dubbo.protocol.id=dubbo
dubbo.protocol.name=dubbo
dubbo.protocol.port=-1
dubbo.protocol.payload=11557050
dubbo.registry.address=nacos://nacos.systech.b2c.srv:80
nacos.config.addrs=nacos.systech.b2c.srv:80

youpin.log.group=preview
log.path=/home/<USER>/log

talos.topic=cnzone_newretail_pro_retail_online
talos.sendpoint=http://cnbj4-talos.api.xiaomi.net
talos.access.key=AK7YWFGLFOC7HOEFZP
talos.access.secret=GDA8nH3vAOcUQyGNChkG8Lm7ZteoUduP7HukOLku6FWur17ai9KVVuLkpI47HQlOsfQYEiozuhFrCkq4t64Jm7maVj/yARgQsbruMaYnRM2m2ol9hNOqfRgUGbLg13xxAx7TFurM2hpa8edt9AkA
talos.access.secret@kc-sid=mi_newretail_risk.g


spring.xmpulsenatl-datasource.name=xm_pulse
spring.xmpulsenatl-datasource.username=misho_pulse_wn
spring.xmpulsenatl-datasource.url=***********************************************************************************
spring.xmpulsenatl-datasource.password=GDC5VmN7k/h++m4TPXia6+TSXqjJPldoBpalcWuQsU1wBNMK1jEZ5tUsrI1Ewv/+bggYEpTlYK0eeEE9jtq4XYWqrEPuARgQt8Xbz6aIT9qmGjBMxhVa3xgUpUIJK1QE3EcxClwm3O6TmlbludYA
spring.xmpulsenatl-datasource.password@kc-sid=mi_newretail_risk.g
spring.xmpulsenatl-datasource.connectionProperties=
spring.xmpulsenatl-datasource.sql-script-encoding=UTF-8
spring.xmpulsenatl-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.xmpulsenatl-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.xmpulsenatl-datasource.initial-size=5
spring.xmpulsenatl-datasource.max-active=20
spring.xmpulsenatl-datasource.min-idle=3
spring.xmpulsenatl-datasource.max-wait=60000
spring.xmpulsenatl-datasource.remove-abandoned=true
spring.xmpulsenatl-datasource.remove-abandoned-timeout=180
spring.xmpulsenatl-datasource.time-between-eviction-runs-millis=60000
spring.xmpulsenatl-datasource.min-evictable-idle-time-millis=300000
spring.xmpulsenatl-datasource.validation-query=SELECT 1 FROM DUAL
spring.xmpulsenatl-datasource.test-while-idle=true
spring.xmpulsenatl-datasource.test-on-borrow=false
spring.xmpulsenatl-datasource.test-on-return=false
spring.xmpulsenatl-datasource.pool-prepared-statements=true
spring.xmpulsenatl-datasource.max-pool-prepared-statement-per-connection-size=50
spring.xmpulsenatl-datasource.filters=stat,wall,slf4j


spring.xmpulse-datasource.name=xm_pulse
spring.xmpulse-datasource.username=misho_pulse_wn
spring.xmpulse-datasource.url=*******************************************************************************************
spring.xmpulse-datasource.password=GDAcCnmYTkDwXPO3F66D3+r4dPb6hXkq1BlJYOGWxgw/MB4GMbSIZEk4zyFWi9+/ofUYEo6FA/AR9U/HibGzFp2NOw/9ARgQlOFk45KzR1ufCknGQ49HdhgU0pcUbytZZH10Iil9GE8kWc4RbOwA
spring.xmpulse-datasource.password@kc-sid=mi_newretail_risk.g
spring.xmpulse-datasource.connectionProperties=
spring.xmpulse-datasource.sql-script-encoding=UTF-8
spring.xmpulse-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.xmpulse-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.xmpulse-datasource.initial-size=5
spring.xmpulse-datasource.max-active=100
spring.xmpulse-datasource.min-idle=3
spring.xmpulse-datasource.max-wait=60000
spring.xmpulse-datasource.remove-abandoned=true
spring.xmpulse-datasource.remove-abandoned-timeout=180
spring.xmpulse-datasource.time-between-eviction-runs-millis=60000
spring.xmpulse-datasource.min-evictable-idle-time-millis=300000
spring.xmpulse-datasource.validation-query=SELECT 1 FROM DUAL
spring.xmpulse-datasource.test-while-idle=true
spring.xmpulse-datasource.test-on-borrow=false
spring.xmpulse-datasource.test-on-return=false
spring.xmpulse-datasource.pool-prepared-statements=true
spring.xmpulse-datasource.max-pool-prepared-statement-per-connection-size=50
spring.xmpulse-datasource.filters=stat,wall,slf4j


spring.xmpulse-slave-datasource.name=xm_pulse_r
spring.xmpulse-slave-datasource.username=misho_pulse_rn
spring.xmpulse-slave-datasource.url=*******************************************************************************************
spring.xmpulse-slave-datasource.password=GDAdeFT7H3XLF8i4w8of4OyUaFT+8jXX9pe74CiY0+jFSq8Md1Ey7R5YN7orXbko9kEYEqZlqB5bCUzKuEGVhC2fnQyYARgQcReDhC9+Qr2mQs5kR+LjRBgU4sqAbFaJ8/jztad7wikYvXchEc0A
spring.xmpulse-slave-datasource.password@kc-sid=mi_newretail_risk.g
spring.xmpulse-slave-datasource.connectionProperties=
spring.xmpulse-slave-datasource.sql-script-encoding=UTF-8
spring.xmpulse-slave-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.xmpulse-slave-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.xmpulse-slave-datasource.initial-size=5
spring.xmpulse-slave-datasource.max-active=100
spring.xmpulse-slave-datasource.min-idle=3
spring.xmpulse-slave-datasource.max-wait=60000
spring.xmpulse-slave-datasource.remove-abandoned=true
spring.xmpulse-slave-datasource.remove-abandoned-timeout=180
spring.xmpulse-slave-datasource.time-between-eviction-runs-millis=60000
spring.xmpulse-slave-datasource.min-evictable-idle-time-millis=300000
spring.xmpulse-slave-datasource.validation-query=SELECT 1 FROM DUAL
spring.xmpulse-slave-datasource.test-while-idle=true
spring.xmpulse-slave-datasource.test-on-borrow=false
spring.xmpulse-slave-datasource.test-on-return=false
spring.xmpulse-slave-datasource.pool-prepared-statements=true
spring.xmpulse-slave-datasource.max-pool-prepared-statement-per-connection-size=50
spring.xmpulse-slave-datasource.filters=stat,wall,slf4j


spring.xmpms-slave-datasource.name=xm_pms
spring.xmpms-slave-datasource.username=misho_pms_rn
spring.xmpms-slave-datasource.url=*******************************************************************************
spring.xmpms-slave-datasource.password=GDCvWNe7uUzPJQN6vacN4Mg4AzeDCkUJLHYZ7mHs5qExZpK2ayrMs6idFmf2oA6b+owYEnIgShLqNE05uJhxMG4L00t1ARgQXdcnraS4RkG6TR8wlC4IXxgU3gKcRAq58FzATeck/HUB3BGa0/EA
spring.xmpms-slave-datasource.password@kc-sid=mi_newretail_risk.g
spring.xmpms-slave-datasource.connectionProperties=
spring.xmpms-slave-datasource.sql-script-encoding=UTF-8
spring.xmpms-slave-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.xmpms-slave-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.xmpms-slave-datasource.initial-size=5
spring.xmpms-slave-datasource.max-active=100
spring.xmpms-slave-datasource.min-idle=3
spring.xmpms-slave-datasource.max-wait=60000
spring.xmpms-slave-datasource.remove-abandoned=true
spring.xmpms-slave-datasource.remove-abandoned-timeout=180
spring.xmpms-slave-datasource.time-between-eviction-runs-millis=60000
spring.xmpms-slave-datasource.min-evictable-idle-time-millis=300000
spring.xmpms-slave-datasource.validation-query=SELECT 1 FROM DUAL
spring.xmpms-slave-datasource.test-while-idle=true
spring.xmpms-slave-datasource.test-on-borrow=false
spring.xmpms-slave-datasource.test-on-return=false
spring.xmpms-slave-datasource.pool-prepared-statements=true
spring.xmpms-slave-datasource.max-pool-prepared-statement-per-connection-size=50
spring.xmpms-slave-datasource.filters=stat,wall,slf4j

spring.xmpulsecoupon-datasource.name=nr_coupon_admin
spring.xmpulsecoupon-datasource.username=nr_coupon_admin_wn
spring.xmpulsecoupon-datasource.url=***************************************************************************************
spring.xmpulsecoupon-datasource.password=GDAC9RPbs7RyGPRNB6mkdmBOwEzLVYa7JFdxiC19GS/uJapICTTa/rs3JdAgaw7GtuIYEl4DqATYfEYrhGFGyMBS+qiqARgQEfSZmeToTRicpgr+nktSBxgUm9fxyiLyk1uPr2kzqSymJOjchWIA
spring.xmpulsecoupon-datasource.password@kc-sid=mi_newretail_risk.g
spring.xmpulsecoupon-datasource.connectionProperties=
spring.xmpulsecoupon-datasource.sql-script-encoding=UTF-8
spring.xmpulsecoupon-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.xmpulsecoupon-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.xmpulsecoupon-datasource.initial-size=5
spring.xmpulsecoupon-datasource.max-active=100
spring.xmpulsecoupon-datasource.min-idle=3
spring.xmpulsecoupon-datasource.max-wait=60000
spring.xmpulsecoupon-datasource.remove-abandoned=true
spring.xmpulsecoupon-datasource.remove-abandoned-timeout=180
spring.xmpulsecoupon-datasource.time-between-eviction-runs-millis=60000
spring.xmpulsecoupon-datasource.min-evictable-idle-time-millis=300000
spring.xmpulsecoupon-datasource.validation-query=SELECT 1 FROM DUAL
spring.xmpulsecoupon-datasource.test-while-idle=true
spring.xmpulsecoupon-datasource.test-on-borrow=false
spring.xmpulsecoupon-datasource.test-on-return=false
spring.xmpulsecoupon-datasource.pool-prepared-statements=true
spring.xmpulsecoupon-datasource.max-pool-prepared-statement-per-connection-size=50
spring.xmpulsecoupon-datasource.filters=stat,wall,slf4j

spring.xmbdmall-datasource.name=xm_mall
spring.xmbdmall-datasource.username=bd_mall_wn
spring.xmbdmall-datasource.url=*******************************************************************************
spring.xmbdmall-datasource.password=GDDQWow02MVU9W8hSBQbqn8v/tuRXUZ0gOGiqCl0WIJtYOCjlfrz1+jTJP2/Bma18uQYEgON1HVdgEe6i4oR7zZ8aV1DARgQT985S3aJQYeAjLpirBvqHRgUAl5wkO57tIAYHNo7i+mh3LKh1KYA
spring.xmbdmall-datasource.password@kc-sid=mi_newretail_risk.g
spring.xmbdmall-datasource.connectionProperties=
spring.xmbdmall-datasource.sql-script-encoding=UTF-8
spring.xmbdmall-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.xmbdmall-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.xmbdmall-datasource.initial-size=5
spring.xmbdmall-datasource.max-active=20
spring.xmbdmall-datasource.min-idle=3
spring.xmbdmall-datasource.max-wait=60000
spring.xmbdmall-datasource.remove-abandoned=true
spring.xmbdmall-datasource.remove-abandoned-timeout=180
spring.xmbdmall-datasource.time-between-eviction-runs-millis=60000
spring.xmbdmall-datasource.min-evictable-idle-time-millis=300000
spring.xmbdmall-datasource.validation-query=SELECT 1 FROM DUAL
spring.xmbdmall-datasource.test-while-idle=true
spring.xmbdmall-datasource.test-on-borrow=false
spring.xmbdmall-datasource.test-on-return=false
spring.xmbdmall-datasource.pool-prepared-statements=true
spring.xmbdmall-datasource.max-pool-prepared-statement-per-connection-size=50
spring.xmbdmall-datasource.filters=stat,wall,slf4j

spring.xmstore-datasource.name=o2o_sale
spring.xmstore-datasource.username=sale_w
spring.xmstore-datasource.url=*********************************************************************************
spring.xmstore-datasource.password=GDD9Ai/bOyTC1BUIF54+rEKs1E1ek2uboHDiNmAXvnMD3B6uAS2puhkfh36nEVAgIIQYEigt7dt91ELukuOCeyC4h1bkARgQF6Y8ATapTLi9LgTR1CLt9hgU512tusBHPm0rteuFKXff1xielbMA
spring.xmstore-datasource.password@kc-sid=mi_newretail_risk.g
spring.xmstore-datasource.connectionProperties=
spring.xmstore-datasource.sql-script-encoding=UTF-8
spring.xmstore-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.xmstore-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.xmstore-datasource.initial-size=5
spring.xmstore-datasource.max-active=20
spring.xmstore-datasource.min-idle=3
spring.xmstore-datasource.max-wait=60000
spring.xmstore-datasource.remove-abandoned=true
spring.xmstore-datasource.remove-abandoned-timeout=180
spring.xmstore-datasource.time-between-eviction-runs-millis=60000
spring.xmstore-datasource.min-evictable-idle-time-millis=300000
spring.xmstore-datasource.validation-query=SELECT 1 FROM DUAL
spring.xmstore-datasource.test-while-idle=true
spring.xmstore-datasource.test-on-borrow=false
spring.xmstore-datasource.test-on-return=false
spring.xmstore-datasource.pool-prepared-statements=true
spring.xmstore-datasource.max-pool-prepared-statement-per-connection-size=50
spring.xmstore-datasource.filters=stat,wall,slf4j


spring.adsdata-datasource.name=nrdc_bi
spring.adsdata-datasource.username=nrdc_bi_rw
spring.adsdata-datasource.url=jdbc:mysql://**************:9999/nrdc_bi?characterEncoding=utf8&useSSL=true
spring.adsdata-datasource.password=GDBvBWEgU5v1zQc0mUUbJ8GYXFlqyWDN4zRoDUI5R+VwUI0GZBTGsMyLs/+39dgef3MYEo6FA/AR9U/HibGzFp2NOw/9ARgQb8is7JN9Q5CLNWOmqnTLYhgUjyDoQWpjjCnZDmumgZDiGRxI0l0A
spring.adsdata-datasource.password@kc-sid=mi_newretail_risk.g
spring.adsdata-datasource.connectionProperties=
spring.adsdata-datasource.sql-script-encoding=UTF-8
spring.adsdata-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.adsdata-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.adsdata-datasource.initial-size=5
spring.adsdata-datasource.max-active=20
spring.adsdata-datasource.min-idle=3
spring.adsdata-datasource.max-wait=60000
spring.adsdata-datasource.remove-abandoned=true
spring.adsdata-datasource.remove-abandoned-timeout=180
spring.adsdata-datasource.time-between-eviction-runs-millis=60000
spring.adsdata-datasource.min-evictable-idle-time-millis=300000
spring.adsdata-datasource.validation-query=SELECT 1 FROM DUAL
spring.adsdata-datasource.test-while-idle=true
spring.adsdata-datasource.test-on-borrow=false
spring.adsdata-datasource.test-on-return=false
spring.adsdata-datasource.pool-prepared-statements=true
spring.adsdata-datasource.max-pool-prepared-statement-per-connection-size=50
spring.adsdata-datasource.filters=stat,wall,slf4j


spring.xmpulse-tidb-slave-datasource.name=xm_pulse
spring.xmpulse-tidb-slave-datasource.username=pulse_r
spring.xmpulse-tidb-slave-datasource.url=********************************************************************************
spring.xmpulse-tidb-slave-datasource.password=GDAg/qlew4nR/RHnpv3UAaDEzPMPYepgLqiz9yPaJYWdGazGdBhEIHGD/A47QX8yFP0YEvuOKLUGLUhQtq60ZS0m/4RQARgQ71k1lykHSMOwnHz2yYcLERgU+Oho5IIeikJ2o/2KJbpLg4vdR2MA
spring.xmpulse-tidb-slave-datasource.password@kc-sid=mi_newretail_risk.g
spring.xmpulse-tidb-slave-datasource.connectionProperties=
spring.xmpulse-tidb-slave-datasource.sql-script-encoding=UTF-8
spring.xmpulse-tidb-slave-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.xmpulse-tidb-slave-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.xmpulse-tidb-slave-datasource.initial-size=5
spring.xmpulse-tidb-slave-datasource.max-active=20
spring.xmpulse-tidb-slave-datasource.min-idle=3
spring.xmpulse-tidb-slave-datasource.max-wait=60000
spring.xmpulse-tidb-slave-datasource.remove-abandoned=true
spring.xmpulse-tidb-slave-datasource.remove-abandoned-timeout=180
spring.xmpulse-tidb-slave-datasource.time-between-eviction-runs-millis=60000
spring.xmpulse-tidb-slave-datasource.min-evictable-idle-time-millis=300000
spring.xmpulse-tidb-slave-datasource.validation-query=SELECT 1 FROM DUAL
spring.xmpulse-tidb-slave-datasource.test-while-idle=true
spring.xmpulse-tidb-slave-datasource.test-on-borrow=false
spring.xmpulse-tidb-slave-datasource.test-on-return=false
spring.xmpulse-tidb-slave-datasource.pool-prepared-statements=true
spring.xmpulse-tidb-slave-datasource.max-pool-prepared-statement-per-connection-size=50
spring.xmpulse-tidb-slave-datasource.filters=stat,wall,slf4j

spring.xmpulse-oceanbase-slave-datasource.username=xm_pulse_rn@SERVICE:cn_info02_b2c_xm_pulse
spring.xmpulse-oceanbase-slave-datasource.url=***************************************************
spring.xmpulse-oceanbase-slave-datasource.password=GDBjg9aUhf1JNXHuqDX1YV8xaxs-dWebxvCeAXeuPkeZBXzR9B96kHZ26GFKnA1Ow5AYElG3fy-7EEnlvkdnIW7tK-SUARgQfc4GQgm9SaWhQWvdInum4RgUVOyZPH9d3iYW2nf2dleeD-l-L_wA
spring.xmpulse-oceanbase-slave-datasource.password@kc-sid=mi_newretail_risk.g
spring.xmpulse-oceanbase-slave-datasource.connectionProperties=
spring.xmpulse-oceanbase-slave-datasource.sql-script-encoding=UTF-8
spring.xmpulse-oceanbase-slave-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.xmpulse-oceanbase-slave-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.xmpulse-oceanbase-slave-datasource.initial-size=5
spring.xmpulse-oceanbase-slave-datasource.max-active=20
spring.xmpulse-oceanbase-slave-datasource.min-idle=3
spring.xmpulse-oceanbase-slave-datasource.max-wait=60000
spring.xmpulse-oceanbase-slave-datasource.remove-abandoned=true
spring.xmpulse-oceanbase-slave-datasource.remove-abandoned-timeout=180
spring.xmpulse-oceanbase-slave-datasource.time-between-eviction-runs-millis=60000
spring.xmpulse-oceanbase-slave-datasource.min-evictable-idle-time-millis=300000
spring.xmpulse-oceanbase-slave-datasource.validation-query=SELECT 1 FROM DUAL
spring.xmpulse-oceanbase-slave-datasource.test-while-idle=true
spring.xmpulse-oceanbase-slave-datasource.test-on-borrow=false
spring.xmpulse-oceanbase-slave-datasource.test-on-return=false
spring.xmpulse-oceanbase-slave-datasource.pool-prepared-statements=true
spring.xmpulse-oceanbase-slave-datasource.max-pool-prepared-statement-per-connection-size=50
spring.xmpulse-oceanbase-slave-datasource.filters=stat,wall,slf4j

spring.nr-point-admin-datasource.name=nr_point_admin
spring.nr-point-admin-datasource.username=nr_point_admin_wn
spring.nr-point-admin-datasource.url=***********************************************************************************************
spring.nr-point-admin-datasource.password=GDATc9qEN8nBQuT0zmjbrz1t3Zw-2vhVFgbT91ALh6ZmxDlBbXinBlO6fxeyuGyP3FcYEiwnwPTLH0zIsGiUfbc2CgiYARgQOC7iayWjR4qoqoMAVQeo0BgUkNjaykol9H2xV4oN_0sAcouUsSkA
spring.nr-point-admin-datasource.password@kc-sid=mi_newretail_risk.g
spring.nr-point-admin-datasource.connectionProperties=
spring.nr-point-admin-datasource.sql-script-encoding=UTF-8
spring.nr-point-admin-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.nr-point-admin-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.nr-point-admin-datasource.initial-size=5
spring.nr-point-admin-datasource.max-active=20
spring.nr-point-admin-datasource.min-idle=3
spring.nr-point-admin-datasource.max-wait=60000
spring.nr-point-admin-datasource.remove-abandoned=true
spring.nr-point-admin-datasource.remove-abandoned-timeout=180
spring.nr-point-admin-datasource.time-between-eviction-runs-millis=60000
spring.nr-point-admin-datasource.min-evictable-idle-time-millis=300000
spring.nr-point-admin-datasource.validation-query=SELECT 1 FROM DUAL
spring.nr-point-admin-datasource.test-while-idle=true
spring.nr-point-admin-datasource.test-on-borrow=false
spring.nr-point-admin-datasource.test-on-return=false
spring.nr-point-admin-datasource.pool-prepared-statements=true
spring.nr-point-admin-datasource.max-pool-prepared-statement-per-connection-size=50
spring.nr-point-admin-datasource.filters=stat,wall,slf4j

spring.user-point-datasource.name=pulse_user
spring.user-point-datasource.username=user_point_wn
spring.user-point-datasource.url=*******************************************************************************************
spring.user-point-datasource.password=GDDSrA0kMHCuKsWHvxRZ0eJAXn9PWU4PUF68qZZU0qxkcVP4m7BxxkU7EEzMZvfaUNAYEtdbD3g44ku9pw_erYDocNNbARgQDhB7JkLCRXuhc4uYNJPonxgUU5kaHEyH4-T6qG-W2U6FrI3IeKMA
spring.user-point-datasource.password@kc-sid=mi_newretail_risk.g
spring.user-point-datasource.connectionProperties=
spring.user-point-datasource.sql-script-encoding=UTF-8
spring.user-point-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.user-point-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.user-point-datasource.initial-size=5
spring.user-point-datasource.max-active=20
spring.user-point-datasource.min-idle=3
spring.user-point-datasource.max-wait=60000
spring.user-point-datasource.remove-abandoned=true
spring.user-point-datasource.remove-abandoned-timeout=180
spring.user-point-datasource.time-between-eviction-runs-millis=60000
spring.user-point-datasource.min-evictable-idle-time-millis=300000
spring.user-point-datasource.validation-query=SELECT 1 FROM DUAL
spring.user-point-datasource.test-while-idle=true
spring.user-point-datasource.test-on-borrow=false
spring.user-point-datasource.test-on-return=false
spring.user-point-datasource.pool-prepared-statements=true
spring.user-point-datasource.max-pool-prepared-statement-per-connection-size=50
spring.user-point-datasource.filters=stat,wall,slf4j

spring.pulse.redis.host=cache01.b2c.srv
spring.pulse.redis.port=5100
spring.pulse.redis.password=GDDGRoljCaq3Xultfbwy3Oc73iusdFTSqpxJaAILbb5mF4ZjdUsKAHbAFOxfeRnc56gYEigt7dt91ELukuOCeyC4h1bkARgQe+OwXq9pQMCWhIO4QJSQVxgULDoTbuzlzMBD5HcHaM6fR59brMUA
spring.pulse.redis.password@kc-sid=mi_newretail_risk.g
spring.pulse.redis.connect-timeout-millis=1000
spring.pulse.redis.read-timeout-millis=3000
spring.pulse.redis.database=0
spring.pulse.redis.jedis-pool.max-idle=50
spring.pulse.redis.jedis-pool.min-idle=20
spring.pulse.redis.jedis-pool.max-total=50
spring.pulse.redis.jedis-pool.test-while-idle=true
spring.pulse.redis.jedis-pool.time-between-eviction-runs-millis=30000
spring.pulse.redis.jedis-pool.min-evictable-idle-time-millis=150000
spring.pulse.redis.jedis-pool.num-tests-per-eviction-run=-1
spring.pulse.redis.jedis-pool.max-wait-millis=3000


spring.newcoupon.redis.host=ares.b2c.cache.b2c.srv
spring.newcoupon.redis.port=5100
spring.newcoupon.redis.password=GDAYWmRcsm81H5h/FATZ6QFOF9layURejFpLWp9WthMXkK30X0j/6OqRnV5nBWIQTb8YEspC+0uPdENLrd+PIdzhXvP9ARgQ4sOlANygQ4a5Tky8whVm8hgUBc6zwfQb9LLk/CAHAarE8tV68OcA
spring.newcoupon.redis.password@kc-sid=mi_newretail_risk.g
spring.newcoupon.redis.connect-timeout-millis=1000
spring.newcoupon.redis.read-timeout-millis=3000
spring.newcoupon.redis.database=0
spring.newcoupon.redis.jedis-pool.max-idle=50
spring.newcoupon.redis.jedis-pool.min-idle=20
spring.newcoupon.redis.jedis-pool.max-total=50
spring.newcoupon.redis.jedis-pool.test-while-idle=true
spring.newcoupon.redis.jedis-pool.time-between-eviction-runs-millis=30000
spring.newcoupon.redis.jedis-pool.min-evictable-idle-time-millis=150000
spring.newcoupon.redis.jedis-pool.num-tests-per-eviction-run=-1
spring.newcoupon.redis.jedis-pool.max-wait-millis=3000

spring.pointadmin.redis.host=ares.b2c.cache.b2c.srv
spring.pointadmin.redis.port=5100
spring.pointadmin.redis.password=GDAYWmRcsm81H5h/FATZ6QFOF9layURejFpLWp9WthMXkK30X0j/6OqRnV5nBWIQTb8YEspC+0uPdENLrd+PIdzhXvP9ARgQ4sOlANygQ4a5Tky8whVm8hgUBc6zwfQb9LLk/CAHAarE8tV68OcA
spring.pointadmin.redis.password@kc-sid=mi_newretail_risk.g
spring.pointadmin.redis.connect-timeout-millis=1000
spring.pointadmin.redis.read-timeout-millis=3000
spring.pointadmin.redis.database=0
spring.pointadmin.redis.jedis-pool.max-idle=50
spring.pointadmin.redis.jedis-pool.min-idle=20
spring.pointadmin.redis.jedis-pool.max-total=50
spring.pointadmin.redis.jedis-pool.test-while-idle=true
spring.pointadmin.redis.jedis-pool.time-between-eviction-runs-millis=30000
spring.pointadmin.redis.jedis-pool.min-evictable-idle-time-millis=150000
spring.pointadmin.redis.jedis-pool.num-tests-per-eviction-run=-1
spring.pointadmin.redis.jedis-pool.max-wait-millis=3000

spring.karos.misc.redis.host=cache01.b2c.srv
spring.karos.misc.redis.port=5100
spring.karos.misc.redis.password=GDB4iU2cQe1gFHzo+vnH6H3pdqgh97yfduwyNiACqAMjpzkKZm1PJ233gwzgy8SHYOMYEo6FA/AR9U/HibGzFp2NOw/9ARgQ7AXPsqUnS9CZcsO7MzFDpxgUD1IMUdTsLN+gS9MqAF39UnXdJH0A
spring.karos.misc.redis.password@kc-sid=mi_newretail_risk.g
spring.karos.misc.redis.connect-timeout-millis=1000
spring.karos.misc.redis.read-timeout-millis=3000
spring.karos.misc.redis.database=0
spring.karos.misc.redis.jedis-pool.max-idle=50
spring.karos.misc.redis.jedis-pool.min-idle=20
spring.karos.misc.redis.jedis-pool.max-total=50
spring.karos.misc.redis.jedis-pool.test-while-idle=true
spring.karos.misc.redis.jedis-pool.time-between-eviction-runs-millis=30000
spring.karos.misc.redis.jedis-pool.min-evictable-idle-time-millis=150000
spring.karos.misc.redis.jedis-pool.num-tests-per-eviction-run=-1
spring.karos.misc.redis.jedis-pool.max-wait-millis=3000


#领券活动缓存会用到
spring.misc.redis.host=cache01.b2c.srv
spring.misc.redis.port=5201
spring.misc.redis.password=GCC6yVzd5ONof5bm7BIsjW5WGuimDHrU6Ku237737N2+TBgSo2guutYsRXiEKE6lvwuVcVABGBBpYKNplD1GOo5pkWIJat8MGBQ1fcgLYgkbcBMNx/HLh7hxNZKygwA=
spring.misc.redis.password@kc-sid=mi_newretail_risk.g
spring.misc.redis.connect-timeout-millis=1000
spring.misc.redis.read-timeout-millis=3000
spring.misc.redis.database=0
spring.misc.redis.jedis-pool.max-idle=50
spring.misc.redis.jedis-pool.min-idle=20
spring.misc.redis.jedis-pool.max-total=50
spring.misc.redis.jedis-pool.test-while-idle=true
spring.misc.redis.jedis-pool.time-between-eviction-runs-millis=30000
spring.misc.redis.jedis-pool.min-evictable-idle-time-millis=150000
spring.misc.redis.jedis-pool.num-tests-per-eviction-run=-1
spring.misc.redis.jedis-pool.max-wait-millis=3000


spring.coupon.es.host=c3nrcenter.api.es.srv:80
spring.coupon.es.username=NRCenter
spring.coupon.es.password=OJWpDKrs0vppm3np
spring.coupon.es.index=nr_coupon_center_coupon_config_online


keycenter.addr=keycenter-test.b2c.srv:9988
keycenter.sid=keycenter-test


#---bpm-----
bpm.dubbo.group=preview
dubbo.nr-bpm.appId=xm-yp-upc-0086
dubbo.nr-bpm.appKey=GDCr/mroUV0YmSBE7TVd1VCG3G/oTqC5p8RioKld8jWVM82v5r0lk+dSqOQodYgAGYoYEsCUgJsG3Eqaj4wVPjxibmNcARgQJ7KDuz2RSX68gD/Zba8K3RgUuP/us1AHgo++mlzhowU/VNgbHigA
dubbo.nr-bpm.appKey@kc-sid=mi_newretail_risk.g
bpm.coupon.definitionKey=mi_online_coupon
bpm.coupon.newRetailKey=newretail_freight_coupon
bpm.task.fillTaskRetailKey=newretail_fill_coupon
#---bpm-----


bpm.secret=
bpm.url=
bpm.url.cancel=

store.group=preview
gms.dubbo.group=preview
gis.dubbo.group=preview
gis.dubbo.group.offline=preview
order.dubbo.group=preview
aries.dubbo.group=preview
cis.dubbo.group=preview
phoenix.dubbo.group=preview


miShop.activity.event.api=http://event.be.mi.com


fds.accessKey=AKB7FI7MHQH2BBQQDW
fds.secretKey=GDAVLDkr4TzEkcz6MTIjSYzLedQGAKfJLqP+Ga0+Yg/IMqSZmLp3y7xWBIuJnsXqEbIYEigt7dt91ELukuOCeyC4h1bkARgQ3tRDS4pRQJWYu/iYjGu7QRgUW5KAmKoVIm9oMtpLhaoKx/GU8p0A
fds.secretKey@kc-sid=mi_newretail_risk.g
fds.endpoint=cnbj1-fds.api.xiaomi.net
fds.bucketName=nr-coupon-bucket


coupon.review.url=https://work-be.pre.mi.com/main-coupon-manage/coupon/list?type=review&id=
coupon.update.url=https://work-be.pre.mi.com/main-coupon-manage/coupon/list?type=editCoupon&id=
postCoupon.review.url=https://work-be.pre.mi.com/main-coupon-manage/coupon/freightCouponList?type=review&id=
postCoupon.update.url=https://work-be.pre.mi.com/main-coupon-manage/coupon/freightCouponList?type=editCoupon&id=
subsidyCoupon.review.url=https://work-be.pre.mi.com/main-coupon-manage/coupon/subsidyCouponList?type=review&id=
subsidyCoupon.update.url=https://work-be.pre.mi.com/main-coupon-manage/coupon/subsidyCouponList?type=editCoupon&id=


userApi.url=http://api.d.mi.com
userApi.key=a219beff5c4baa56

rocketmq.name-server=youpin-cnbj1-rocketmq.namesrv.api.xiaomi.net:9876
rocketmq.producer.group=nr_coupon_postoffice_producer_group
rocketmq.producer.sendMessageTimeout=300000
rocketmq.producer.access-key=AKB7FI7MHQH2BBQQDW
rocketmq.producer.secret-key=GDDZJgAgyN6PYT8Rwo0haTwS/ZdPxHL64IANhU8w1HzUinpZlN6ib0qm8pckUZj1mI8YEgON1HVdgEe6i4oR7zZ8aV1DARgQPXzA/sxATimNidWSTlUnTBgU8RcLDZDx9ipjtFrrf037dAKPOVkA
rocketmq.producer.secret-key@kc-sid=mi_newretail_risk.g
postoffice.topic=CI96578_Youpin_Committee_Msg_Listener
#------品类券新品提醒场景id------
postoffice.newGoodsNotifySceneId=266
postoffice.pointLowStockNotifySceneId=302


nr.rocketmq.name-server=cnbj1-rocketmq.namesrv.api.xiaomi.net:9876
nr.rocketmq.producer.group=nr-coupon
nr.rocketmq.producer.access-key=AKB7FI7MHQH2BBQQDW
nr.rocketmq.producer.secret-key=GDCjIdRC/p3oGHyW/jpSFq9wkl7uuMPbxXjz/V27HXXx+AHcJNJSSKxBa2aMzVCLa4sYErKdC9unjEdqqa0Begu2BkZJARgQp6+O1WRYS1+jgwne6qjv1xgUMNlPHew/4Axm7UGO7bKEpcGMMkEA
nr.rocketmq.producer.secret-key@kc-sid=mi_newretail_risk.g
nr.rocketmq.producer.sendMessageTimeout=300000

#优惠券即将过期提醒push
nr.rocketmq.producer.coupon.expire.topic=nr_coupon_expire_push_online
#优惠券修改提醒push
nr.rocketmq.producer.coupon.change.topic=nr_coupon_config_change

rpc.etcd.host=http://soa01.etcd.b2c.srv:4001

# ems config
oaucf.ems.url=https://ems.finance.mioffice.cn
oaucf.ems.appId=expense_management_prod
oaucf.ems.appSecret=aea004ec9c661e0al4ic66054lcm7e4e

hdfs.url=hdfs://zjyprc-hadoop

aes.password=M6EED09BCC6539AD

i18n.component.scan=false