#server
app.name=coupon-admin
server.debug=true
server.connection-timeout=1000
spring.profiles.active=@profile_name@
#spring.main.allow-bean-definition-overriding=true

# ems config
oaucf.ems.url=${oaucf.ems.url}
oaucf.ems.appId=${oaucf.ems.appId}
oaucf.ems.appSecret=${oaucf.ems.appSecret}
oaucf.ems.enabled=true

#I18N Area Nacos dataId
nacos.biz.config.dataId=coupon_management_region_config

# icrm url
adfs.url=${adfs.url}
adfs.client_id=${adfs.client_id}
adfs.client_secret=${adfs.client_secret}
adfs.resource=${adfs.resource}
adfs.grantType=${adfs.grantType}
adfs.username=${adfs.username}
adfs.password=${adfs.password}
icrm.url=${icrm.url}