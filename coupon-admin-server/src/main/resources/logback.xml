<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="5 seconds">

    <property resource="application.properties"></property>
    <property resource="config/application-${spring.profiles.active}.properties"></property>

    <property name="MAX_HISTORY" value="30"/>

    <appender name="fileAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/coupon-admin/server.log</file>
        <encoder>
            <pattern>%d|%-5level|%thread|%X{trace_id}|%logger{40}|%L|%msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/coupon-admin/server.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <MaxHistory>${MAX_HISTORY}</MaxHistory>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>500MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
    </appender>

    <appender name="asyncAppender" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>60000</queueSize>
        <appender-ref ref="fileAppender"/>
    </appender>


    <appender name="errorAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/coupon-admin/error.log</file>
        <encoder>
            <pattern>%d|%-5level|%thread|%X{trace_id}|%logger{40}|%L|%msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/coupon-admin/error.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <MaxHistory>${MAX_HISTORY}</MaxHistory>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="dataMoveAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/coupon-admin/data_move.log</file>
        <encoder>
            <pattern>%d|%-5level|%thread|%X{trace_id}|%logger{40}|%L|%msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/coupon-admin/data_move.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <MaxHistory>${MAX_HISTORY}</MaxHistory>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
    </appender>

    <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d|%-5level|%thread|%X{trace_id}|%logger{40}|%L|%msg%n</pattern>
        </encoder>
    </appender>

    <logger name="org.springframework" level="ERROR"/>
    <logger name="ch.qos.logback" level="ERROR"/>
    <logger name="com.xiaomi.data.push.service.state" level="ERROR"/>
    <logger name="org.reflections.Reflections" level="ERROR"/>

    <logger name="com.xiaomi.nr.coupon.admin.infrastructure.repository.CouponMoveRepository" level="INFO" additivity="false">
        <appender-ref ref="dataMoveAppender" />
    </logger>

    <root level="INFO">
        <appender-ref ref="stdout"/>
        <appender-ref ref="errorAppender"/>
        <appender-ref ref="fileAppender"/>
    </root>

</configuration>