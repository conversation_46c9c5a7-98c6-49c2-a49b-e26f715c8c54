package com.xiaomi.nr.coupon.admin.config.es;

import lombok.Data;
import lombok.val;
import org.apache.http.Header;
import org.apache.http.HttpHost;
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder;
import org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager;
import org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor;
import org.apache.http.impl.nio.reactor.IOReactorConfig;
import org.apache.http.message.BasicHeader;
import org.apache.http.nio.reactor.IOReactorException;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

import static org.apache.rocketmq.metamanager.common.Constants.MAX_TOTAL;


/**
 * @description: ES配置类
 * @author: hejiapeng
 * @create: 2019-10-19 15:14
 */

@Configuration
public class ESConfiguration {

    @Value("${spring.coupon.es.host: #{null}}")
    private String esAddr;

    @Value("${spring.coupon.es.username: #{null}}")
    private String username;

    @Value("${spring.coupon.es.password: #{null}}")
    private String password;

    private static final int IO_THREAD_COUNT = 10;
    private static final int CONNECT_TIMEOUT = 500;

    private static final int MAX_TOTAL = 50;
    private static final int DEFAULT_MAX_PERROUTE = 100;



    @Bean(name = "esClient")
    public RestHighLevelClient couponSearchEsClient() throws IOReactorException {
        String[] addrs = esAddr.split(",");
        List<HttpHost> hosts = new ArrayList<>();
        for (String addr : addrs) {
            String[] hostAndPort = addr.split(":");
            int port = Integer.parseInt(hostAndPort[1]);
            HttpHost host = new HttpHost(hostAndPort[0], port);
            hosts.add(host);
        }

        String urlencodePassword = new String(Base64.getUrlEncoder().encode(String.format("%s:%s", username, password).getBytes()));
        String basicAuth = String.format("Basic %s", urlencodePassword);
        Header[] headers = new Header[] {new BasicHeader("Authorization", basicAuth)};

        RestClientBuilder clientBuilder = RestClient.builder(hosts.toArray(new HttpHost[0])).setDefaultHeaders(headers);

        // 设置连接超时和套接字超时时间
/*        clientBuilder.setRequestConfigCallback(requestConfigBuilder -> {
            requestConfigBuilder.setConnectTimeout(500); // 连接超时时间500毫秒秒
            requestConfigBuilder.setSocketTimeout(2000); // 套接字超时时间2秒
            return requestConfigBuilder;
        });*/

        final IOReactorConfig ioReactorConfig = IOReactorConfig.custom().setIoThreadCount(IO_THREAD_COUNT).setConnectTimeout(CONNECT_TIMEOUT).setSoKeepAlive(true).build();

        final PoolingNHttpClientConnectionManager connManager = new PoolingNHttpClientConnectionManager(new DefaultConnectingIOReactor(ioReactorConfig));
        connManager.setMaxTotal(MAX_TOTAL);
        connManager.setDefaultMaxPerRoute(DEFAULT_MAX_PERROUTE);

        clientBuilder.setHttpClientConfigCallback(httpClientBuilder -> httpClientBuilder.setConnectionManager(connManager));


        return new RestHighLevelClient(clientBuilder);
    }
}
