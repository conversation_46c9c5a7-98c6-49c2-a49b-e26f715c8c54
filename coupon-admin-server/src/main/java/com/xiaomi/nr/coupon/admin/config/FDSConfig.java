package com.xiaomi.nr.coupon.admin.config;

import com.xiaomi.infra.galaxy.fds.client.FDSClientConfiguration;
import com.xiaomi.infra.galaxy.fds.client.GalaxyFDS;
import com.xiaomi.infra.galaxy.fds.client.GalaxyFDSClient;
import com.xiaomi.infra.galaxy.fds.client.credential.BasicFDSCredential;
import com.xiaomi.infra.galaxy.fds.client.credential.GalaxyFDSCredential;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Description: fds配置
 * @Date: 2021.10.19 19:46
 */
@Configuration
@Data
public class FDSConfig {

    @Value("${fds.accessKey}")
    private String accessKey;

    @Value("${fds.secretKey}")
    private String secretKey;

    @Value("${fds.endpoint}")
    private String endpoint;

    @Value("${fds.bucketName}")
    private String bucketName;


    @Bean("galaxyFDSClient")
    public GalaxyFDS getGalaxyFDSClient() {
        GalaxyFDSCredential credential = new BasicFDSCredential(
                accessKey, secretKey);
        FDSClientConfiguration fdsConfig = new FDSClientConfiguration(endpoint);
        fdsConfig.enableHttps(true);
        fdsConfig.enableCdnForUpload(false);
        return new GalaxyFDSClient(credential, fdsConfig);
    }
}
