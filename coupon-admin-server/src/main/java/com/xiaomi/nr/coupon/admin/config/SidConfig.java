package com.xiaomi.nr.coupon.admin.config;

import com.xiaomi.nr.coupon.admin.infrastructure.rpc.sdk.SidWrapper;
import lombok.extern.slf4j.Slf4j;
import org.mi.thrift.rpc.XRpc;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;


/**
 * @description: sid链接配置
 * @author: hejiapeng
 * @Date 2024/2/2 17:28
 * @Version: 1.0
 **/

@Slf4j
@Component
@Configuration
public class SidConfig {

    /**
     * host
     */
    @Value("${rpc.etcd.host}")
    private String host;

    /**
     * sid
     *
     * @return sidClient
     */
    @Bean(name = "sidClient")
    public SidWrapper sidClient() throws Exception {
        XRpc rpc = XRpc.getInstance();
        rpc.init(host, "mishop", "nr_coupon");
        return new SidWrapper("sid", (long) 1000);
    }
}
