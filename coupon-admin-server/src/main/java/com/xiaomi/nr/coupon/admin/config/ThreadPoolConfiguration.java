package com.xiaomi.nr.coupon.admin.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 线程池配置
 * */
@Slf4j
@Configuration
@Deprecated
public class ThreadPoolConfiguration {
    /**
     * HDFS写入任务线程池
     * */
    @Bean("insertHdfsAsyncExecutor")
    public Executor insertHdfsAsyncExecutor(){
        ThreadPoolExecutor excutor = new ThreadPoolExecutor(10,25,30, TimeUnit.SECONDS
                , new ArrayBlockingQueue<>(50));
        return excutor;
    }
}