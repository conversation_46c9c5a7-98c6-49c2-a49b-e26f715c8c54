package com.xiaomi.nr.coupon.admin.config.datasource;


import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;
import java.util.Objects;

/**
 * 写入B.D 数据库配置
 */
@Configuration
@MapperScan(basePackages = {"com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.adsTidb"},
        sqlSessionFactoryRef = "adsDataSqlSessionFactory")
public class AdsCouponTidb {

    @Value("${spring.adsdata-datasource.url:#{null}}")
    private String dataSourceUrl;

    @Bean(name = "adsDataDatasource")
    @ConfigurationProperties("spring.adsdata-datasource")
    public DataSource mysqlDataSource() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "adsDataSqlSessionFactory")
    public SqlSessionFactory mysqlSqlSessionFactory(@Qualifier("adsDataDatasource") DataSource dataSource)
            throws Exception {
        final SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);
        sessionFactory.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/*.xml"));
        Objects.requireNonNull(sessionFactory.getObject()).getConfiguration().setMapUnderscoreToCamelCase(true);
        //sessionFactory.getObject().getConfiguration().addInterceptor(new CatMybatisInterceptor(dataSourceUrl));
        return sessionFactory.getObject();
    }
}
