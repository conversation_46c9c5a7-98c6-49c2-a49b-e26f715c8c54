package com.xiaomi.nr.coupon.admin.config;

import com.xiaomi.nr.coupon.admin.config.support.RedisNodeConfig;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/7/4 19:46
 */
@Configuration
public class RedissonConfig {

    @Bean(name = "redissonClient")
    public RedissonClient redissonClient(@Qualifier("newCouponRedisConfig") RedisNodeConfig redisNodeConfig) {
        Config config = new Config();
        SingleServerConfig serverConfig = config.useSingleServer()
                .setAddress("redis://" + redisNodeConfig.getHost() + ":" + redisNodeConfig.getPort())
                .setPassword(redisNodeConfig.getPassword())
                .setConnectTimeout(200)
                .setTimeout(3000)
                .setConnectionMinimumIdleSize(8)
                .setConnectionPoolSize(16)
                .setIdleConnectionTimeout(60000);

        return Redisson.create(config);
    }
}
