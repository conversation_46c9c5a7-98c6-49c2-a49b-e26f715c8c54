package com.xiaomi.nr.coupon.admin.config;


import api.ClientFactory;
import api.config.ConfigKey;
import api.producer.NormalProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.exception.MQClientException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

/**
 * MQ配置
 *
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class RocketMqConfig {

    @Value("${nr.rocketmq.name-server}")
    private String nameServer;

    @Value("${nr.rocketmq.producer.access-key}")
    private String access;

    @Value("${nr.rocketmq.producer.secret-key}")
    private String secret;

    @Value("${nr.rocketmq.producer.group}")
    private String group;

    @Value("${nr.rocketmq.producer.sendMessageTimeout}")
    private String timeout;


    /**
     * rocketMQ产生者
     * @return RocketProducer
     */
    @Bean("xmRocketNormalProducer")
    public NormalProducer xmRocketNormalProducer() {
        Properties properties = new Properties();
        properties.setProperty(ConfigKey.PRODUCER_GROUP, group);
        properties.setProperty(ConfigKey.ACCESS_KEY, access);
        properties.setProperty(ConfigKey.SECRET_KEY, secret);
        properties.setProperty(ConfigKey.NAME_SERVER_ADDR, nameServer);
        properties.setProperty(ConfigKey.ENABLE_MSG_TRACE, "true");
        properties.setProperty(ConfigKey.SEND_MSG_TIMEOUT, timeout);
        NormalProducer normalProducer = ClientFactory.createNormalProducer(properties);
        try {
            normalProducer.start();
        }catch (MQClientException e) {
            log.error("xmRocketNormalProducer rocketMQ init error, error={}", e.getErrorMessage(), e);
        }
        return normalProducer;
    }
}
