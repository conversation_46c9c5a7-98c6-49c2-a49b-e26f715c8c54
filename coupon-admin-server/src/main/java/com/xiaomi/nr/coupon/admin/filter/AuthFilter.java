package com.xiaomi.nr.coupon.admin.filter;


/**
 * <AUTHOR>
 */
//@Slf4j
//@Component
//@Activate(group = Constants.PROVIDER, order = 2)
public class AuthFilter /*implements Filter*/ {

/*
    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        Object[] arguments = invocation.getArguments();
        BeanMap attachments = BeanMap.create(arguments[0]);
        List<String> keys = new ArrayList<>();
        Map<String, String> params = new HashMap<>();
        for (Object key : attachments.keySet()) {
            keys.add(String.valueOf(key));
            params.put(String.valueOf(key), String.valueOf(attachments.get(key)));
        }
        AuthValidator.auth(invoker.getInterface().getName(), invocation.getMethodName(), params, keys);
        return invoker.invoke(invocation);
    }
*/

}