package com.xiaomi.nr.coupon.admin.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * @description 国内环境不加载CfgDataLoad
 * <AUTHOR>
 * @date 2025-05-08 16:57
*/
@Configuration
@ConditionalOnProperty(value = "i18n.component.scan", havingValue = "true")
@ComponentScan(basePackages = {"com.xiaomi.com.i18n.cfg"})
public class I18nComponentScanConfig {
}
