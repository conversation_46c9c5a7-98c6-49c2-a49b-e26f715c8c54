package com.xiaomi.nr.coupon.admin.config.support;

import lombok.Data;

/**
 * jedis 池配置
 *
 * <AUTHOR>
 * @date 2024/7/25
 */
@Data
public class RedisPoolConfig {
    /**
     * 最大空闲连接数
     */
    private int maxIdle;

    /**
     * 最小空闲连接数
     */
    private int minIdle;

    /**
     * 最大连接数
     */
    private int maxTotal;

    /**
     * 最大等待时间（毫秒）
     */
    private long maxWaitMillis;

    /**
     * 空闲连接检测时，是否同时检测连接的有效性，无效连接会被释放，且该值只在timeBetweenEvictionRunsMillis > 0时有效
     */
    private boolean testWhileIdle;

    /**
     * 放连接的扫描间隔（毫秒)
     */
    private long timeBetweenEvictionRunsMillis;

    /**
     * 当连接小于最小空闲时间 将回收链接
     */
    private long minEvictableIdleTimeMillis;

    /**
     * 保活检测，默认-1：检查全部的空闲链接，-2：每次检测1/2比例的的连接
     */
    private int numTestsPerEvictionRun;
}




