package com.xiaomi.nr.coupon.admin.filter;

import com.xiaomi.youpin.dubbo.filter.TraceIdUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.common.Constants;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.Filter;
import org.apache.dubbo.rpc.Invocation;
import org.apache.dubbo.rpc.Invoker;
import org.apache.dubbo.rpc.Result;
import org.apache.dubbo.rpc.RpcException;
import org.slf4j.MDC;

/**
 * traceId
 *
 * <AUTHOR>
 * @date 2021/9/30
 */
@Slf4j
@Activate(group = {Constants.PROVIDER}, order = 1)
public class CouponTraceFilter implements Filter {
    /**
     * 调用
     *
     * @param invoker    调用者
     * @param invocation 调用数据
     * @return 结果
     * @throws RpcException 异常
     */
    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        try {
            String traceId = TraceIdUtils.ins().traceId();
            if (StringUtils.isEmpty(traceId)) {
                traceId = TraceIdUtils.ins().uuid();
                TraceIdUtils.ins().setTraceIdAndSpanId(traceId, StringUtils.EMPTY);
            }
            MDC.put(Constants.TRACE_ID, traceId);
            return invoker.invoke(invocation);
        } finally {
            MDC.clear();
        }
    }
}
