package com.xiaomi.nr.coupon.admin.filter;

import com.xiaomi.newretail.common.tools.base.exception.BaseException;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.Constants;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Activate(group = Constants.PROVIDER, order = 3)
public class CouponExceptionFilter implements Filter {

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        RpcResult rpcResult = new RpcResult();
        try {
            Result result = invoker.invoke(invocation);
            if (result.hasException() && result.getException() != null) {
                rpcResult.setValue(com.xiaomi.youpin.infra.rpc.Result.fail(GeneralCodes.ParamError, result.getException().getMessage()));
                return rpcResult;
            }
            return result;
        } catch (BaseException e) {
            log.info("coupon.AuthFilter.Exception, 应用级失败，class={}, errMsg={}", e.getClass(), e.getMessage());
            rpcResult.setValue(com.xiaomi.youpin.infra.rpc.Result.fail(GeneralCodes.ParamError, e.getMessage()));
        } catch (Exception e) {
            log.error("coupon.AuthFilter.Exception, 应用级异常", e);
            rpcResult.setValue(com.xiaomi.youpin.infra.rpc.Result.fromException(e, "系统繁忙，请稍后再试"));
        } catch (Throwable e) {
            log.error("coupon.AuthFilter.Throwable, 系统级报错", e);
            rpcResult.setValue(com.xiaomi.youpin.infra.rpc.Result.fromException(e, "系统繁忙，请稍后再试！"));
        }
        return rpcResult;
    }
}


