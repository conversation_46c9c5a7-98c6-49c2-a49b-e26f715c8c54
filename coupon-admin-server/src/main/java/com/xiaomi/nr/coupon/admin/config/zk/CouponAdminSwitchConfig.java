package com.xiaomi.nr.coupon.admin.config.zk;

import com.xiaomi.nr.infra.aop.config.JsonConfigMapper;
import com.xiaomi.nr.infra.aop.config.ZKAutoConfig;
import lombok.Data;

import java.util.List;

/**
 * zk开关配置
 */

@Data
public class CouponAdminSwitchConfig {

    private static ZKAutoConfig<CouponAdminSwitchConfig> instance = new ZKAutoConfig("taurus/taurus_admin_switch", new JsonConfigMapper(CouponAdminSwitchConfig.class));

    public static CouponAdminSwitchConfig getSwitchConfig() {
        return instance.get();
    }

    /**
     * 重试次数
     */
    private int fillcouponRetryTimes = 3;

    /**
     * 停止券新建和更改操作
     */
    public boolean stopCouponCreateAndUpdate = false;

    /**
     * 停止灌券任务创建和重试
     */
    private boolean stopFillCoupon = false;

    /**
     * 启动全量刷券到ES
     */
    private boolean applyRunLoadAllConfigToES = false;

    /**
     * 启动从redis获取数据写入redis倒排
     */
    private boolean applyRedisLoadInverted = false;

    /**
     * 删除pid商品价格数量阀值
     */
    private int pidPriceDelThreshold = 500;

    /**
     * 退资源操作白名单用户
     */
    private List<String> whiteUsers;




    /**
     * 全场券黑白名单需求，白名单通知价格监控体系开关
     */
    private boolean whiteNotifyPriceSystem = false;
    /**
     * 全场券黑白名单需求，白名单通知凑单开关
     */
    private boolean whiteNotifyCollectOrder = false;
    /**
     * 全场券黑白名单需求，黑名单通知价格监控体系开关
     */
    private boolean blackNotifyPriceSystem = false;
    /**
     * 全场券黑白名单需求，黑名单通知凑单开关
     */
    private boolean blackNotifyCollectOrder = false;
    /**
     * 全场券黑白名单需求，添加新品使用测试商品开关
     */
    private boolean useTestPid = true;

}
