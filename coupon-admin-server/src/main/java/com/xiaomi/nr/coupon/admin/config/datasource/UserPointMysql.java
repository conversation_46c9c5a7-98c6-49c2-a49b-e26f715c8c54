package com.xiaomi.nr.coupon.admin.config.datasource;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;
import java.util.Objects;

/**
 * point admin
 *
 * <AUTHOR>
 */
@Configuration
@MapperScan(basePackages = {"com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.userpoint"},
        sqlSessionFactoryRef = "userPointSqlSessionFactory")
public class UserPointMysql {

    @Bean(name = "userPointDatasource")
    @ConfigurationProperties("spring.user-point-datasource")
    public DataSource mysqlDataSource() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "userPointSqlSessionFactory")
    public SqlSessionFactory mysqlSqlSessionFactory(@Qualifier("userPointDatasource") DataSource dataSource)
            throws Exception {
        final SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);
        sessionFactory.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/*.xml"));
        Objects.requireNonNull(sessionFactory.getObject()).getConfiguration().setMapUnderscoreToCamelCase(true);
        return sessionFactory.getObject();
    }

    @Bean(name = "userPointTransactionManager")
    public PlatformTransactionManager prodTransactionManager(@Qualifier("userPointDatasource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }
}
