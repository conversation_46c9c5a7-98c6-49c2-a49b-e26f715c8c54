package com.xiaomi.nr.coupon.admin.config.support;

import lombok.Data;

/**
 * redis配置
 *
 * <AUTHOR>
 * @date 2020-04-03 16:59
 */
@Data
public class RedisNodeConfig {
    /**
     * host
     */
    private String host;

    /**
     * 端口
     */
    private int port;

    /**
     * 密码
     */
    private String password;

    /**
     * 连接超时时间（毫秒）
     */
    private long connectTimeoutMillis;

    /**
     * 响应超时时间（毫秒）
     */
    private long readTimeoutMillis;

    /**
     * db库
     */
    private int database;

    /**
     * jedis 池配置
     */
    private RedisPoolConfig jedisPool;
}
