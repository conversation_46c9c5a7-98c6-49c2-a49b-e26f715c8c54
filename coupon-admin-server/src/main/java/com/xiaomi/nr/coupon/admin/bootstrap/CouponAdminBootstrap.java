package com.xiaomi.nr.coupon.admin.bootstrap;

import com.xiaomi.mone.dubbo.docs.EnableDubboApiDocs;
import com.xiaomi.nr.coupon.admin.config.NeptuneConfig;
import com.xiaomi.nr.coupon.admin.util.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.DubboComponentScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 */
@Slf4j
@EnableAutoConfiguration
@ComponentScan(basePackages = {"com.xiaomi.nr.coupon.admin", "com.xiaomi.youpin","com.xiaomi.dubbo.validation",
        "com.xiaomi.com.i18n.area"})
@DubboComponentScan(basePackages = "com.xiaomi.nr.coupon.admin")
@EnableScheduling
@EnableDubboApiDocs
public class CouponAdminBootstrap {
    private static final Logger logger = LoggerFactory.getLogger(CouponAdminBootstrap.class);

    @Autowired
    private NeptuneConfig neptuneConfig;

    /**
     * krb5.conf容器中的路径
     */
    private static final String HDFS_CONFIG_PATH = "/home/<USER>/hdfs/krb5.conf";
    /**
     * s_miotstore.keytab在容器中的路径
     */
    private static final String HDFS_KEY_PATH = "/home/<USER>/hdfs/s_nr_center.keytab";

    public static void main(String... args) {
        try {
            initHdfs();
            SpringApplication.run(CouponAdminBootstrap.class, args);
            logger.info(BannerConstant.BANNER);
        } catch (Throwable throwable) {
            logger.error(throwable.getMessage(), throwable);
            System.exit(-1);
        }
    }

    /**
     * hdfs配置设置
     */
    private static void initHdfs() {
        try {
            FileUtils.copyResourceFile("hdfs/krb5.conf", HDFS_CONFIG_PATH);
            FileUtils.copyResourceFile("hdfs/s_nr_center.keytab", HDFS_KEY_PATH);
            System.setProperty("java.security.krb5.conf", HDFS_CONFIG_PATH);
            System.setProperty("hadoop.property.hadoop.client.keytab.file", HDFS_KEY_PATH);
        } catch (Exception e) {
            log.error("initHdfs fail!", e);
        }
    }
}