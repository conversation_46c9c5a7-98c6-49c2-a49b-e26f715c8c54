package com.xiaomi.nr.coupon.admin.config.datasource;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/7/24 11:19
 */
@Configuration
@MapperScan(basePackages = {"com.xiaomi.nr.coupon.admin.infrastructure.repository.oceanbasedao"}, sqlSessionFactoryRef = "xmPulseOceanBaseSlaveSqlSessionFactory")
public class XmPulseOceanBaseSlave {
    @Value("${spring.xmpulse-oceanbase-slave-datasource.url}")
    private String dataSourceUrl;

    /**
	 * 配置并创建一个名为 "xmPulseOceanBaseSlaveDatasource" 的数据源
	 *
	 * @return 配置好的数据源
	 */
	@Bean(name = "xmPulseOceanBaseSlaveDatasource")
    @ConfigurationProperties("spring.xmpulse-oceanbase-slave-datasource")
    public DataSource mysqlDataSource() {
        return DruidDataSourceBuilder.create().build();
    }

	/**
	 * 创建并配置MyBatis的SqlSessionFactory
	 *
	 * @param dataSource 数据源
	 * @return 配置好的SqlSessionFactory
	 * @throws Exception 如果在创建SqlSessionFactory过程中发生错误
	 */
	@Bean(name = "xmPulseOceanBaseSlaveSqlSessionFactory")
    public SqlSessionFactory mysqlSqlSessionFactory(@Qualifier("xmPulseOceanBaseSlaveDatasource") DataSource dataSource)
            throws Exception {
        final SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);
        sessionFactory.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/*.xml"));
        Objects.requireNonNull(sessionFactory.getObject()).getConfiguration().setMapUnderscoreToCamelCase(true);
        //sessionFactory.getObject().getConfiguration().addInterceptor(new CatMybatisInterceptor(dataSourceUrl));
        return sessionFactory.getObject();
    }
}
