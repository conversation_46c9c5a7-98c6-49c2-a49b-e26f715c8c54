package com.xiaomi.nr.coupon.admin.config;

import com.xiaomi.nr.coupon.admin.config.support.RedisNodeConfig;
import com.xiaomi.nr.coupon.admin.config.support.RedisPoolConfig;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisPassword;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.jedis.JedisClientConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import redis.clients.jedis.JedisPoolConfig;

import java.time.Duration;

/**
 * <AUTHOR>
 */
@Configuration
public class RedisConfig {

    /**
     * 老pulse系统缓存配置
     */
    @Bean("pulseRedisConfig")
    @ConfigurationProperties("spring.pulse.redis")
    public RedisNodeConfig pulseRedisConfig() {
        return new RedisNodeConfig();
    }

    /**
     * 老pulse系统缓存模板
     */
    @Bean(name = "stringPulseTypeRedisTemplate")
    public StringRedisTemplate stringPulseRedisTemplate(@Qualifier("pulseRedisConfig") RedisNodeConfig config) {
        return createStringRedisTemplate(config, new StringRedisSerializer());
    }

    /**
     * 新的券缓存配置
     */
    @Bean("newCouponRedisConfig")
    @ConfigurationProperties("spring.newcoupon.redis")
    public RedisNodeConfig newCouponRedisConfig() {
        return new RedisNodeConfig();
    }

    /**
     * 新的券配置缓存模板 -- 字符串序列化
     */
    @Bean(name = "stringNewCouponRedisTemplate")
    public StringRedisTemplate stringNewCouponRedisTemplate(@Qualifier("newCouponRedisConfig") RedisNodeConfig config) {
        return createStringRedisTemplate(config, new StringRedisSerializer());
    }

    /**
     * 积分管理后台缓存配置
     */
    @Bean(name = "pointAdminRedisConfig")
    @ConfigurationProperties("spring.pointadmin.redis")
    public RedisNodeConfig pointAdminRedisConfig() {
        return new RedisNodeConfig();
    }

    /**
     * 积分管理后台缓存模板 -- 字符串序列化
     */
    @Bean(name = "stringPointAdminRedisTemplate")
    public StringRedisTemplate stringPointAdminRedisTemplate(@Qualifier("pointAdminRedisConfig") RedisNodeConfig config) {
        return createStringRedisTemplate(config, new StringRedisSerializer());
    }

    /**
     * 新的券配置缓存模板 -- 数字序列化
     */
    @Bean(name = "numberNewCouponRedisTemplate")
    public RedisTemplate<String, Number> numberNewCouponRedisTemplate(@Qualifier("newCouponRedisConfig") RedisNodeConfig config) {
        Jackson2JsonRedisSerializer<Number> serializer = new Jackson2JsonRedisSerializer<>(Number.class);
        return createNumberRedisTemplate(config, serializer);
    }

    /**
     * karoMisc缓存配置
     */
    @Bean("karoMiscRedisConfig")
    @ConfigurationProperties("spring.karos.misc.redis")
    public RedisNodeConfig karosRedisRedisConfig() {
        return new RedisNodeConfig();
    }

    /**
     * karoMisc券配置缓存模板
     */
    @Bean(name = "stringKarosRedisTemplate")
    public StringRedisTemplate stringKarosRedisTemplate(@Qualifier("karoMiscRedisConfig") RedisNodeConfig config) {
        return createStringRedisTemplate(config, new StringRedisSerializer());
    }

    /**
     * 领券活动缓存配置
     */
    @Bean("miscRedisConfig")
    @ConfigurationProperties("spring.misc.redis")
    public RedisNodeConfig miscRedisConfig() {
        return new RedisNodeConfig();
    }

    /**
     * 领券活动配置缓存模板
     */
    @Bean(name = "stringMiscRedisTemplate")
    public StringRedisTemplate stringMiscRedisTemplate(@Qualifier("miscRedisConfig") RedisNodeConfig config) {
        return createStringRedisTemplate(config, new StringRedisSerializer());
    }

    /**
     * 创建并配置一个RedisTemplate<String, Number>实例
     *
     * @param config     Redis节点配置，用于创建连接工厂
     * @param serializer 用于值的序列化器
     * @return 配置好的RedisTemplate实例
     */
    private RedisTemplate<String, Number> createNumberRedisTemplate(RedisNodeConfig config, RedisSerializer<?> serializer) {
        // 创建一个新的RedisTemplate实例，指定泛型类型
        RedisTemplate<String, Number> redis = new RedisTemplate<>();
        // 调用通用配置方法
        configureRedisTemplate(redis, config, serializer);
        // 返回配置好的RedisTemplate实例
        return redis;
    }

    /**
     * 创建并配置一个StringRedisTemplate实例
     *
     * @param config     Redis节点配置，用于创建连接工厂
     * @param serializer 用于值的序列化器
     * @return 配置好的StringRedisTemplate实例
     */
    private StringRedisTemplate createStringRedisTemplate(RedisNodeConfig config, RedisSerializer<?> serializer) {
        // 创建一个新的StringRedisTemplate实例
        StringRedisTemplate redis = new StringRedisTemplate();
        // 调用通用配置方法
        configureRedisTemplate(redis, config, serializer);
        // 返回配置好的StringRedisTemplate实例
        return redis;
    }

    /**
     * 通用的RedisTemplate配置方法
     *
     * @param redis      RedisTemplate实例
     * @param config     Redis节点配置，用于创建连接工厂
     * @param serializer 用于值的序列化器
     */
    private void configureRedisTemplate(RedisTemplate<String, ?> redis, RedisNodeConfig config, RedisSerializer<?> serializer) {
        // 设置连接工厂，使用给定的Redis节点配置
        redis.setConnectionFactory(createJedisConnectionFactory(config));

        // 设置键的序列化器为StringRedisSerializer
        redis.setKeySerializer(new StringRedisSerializer());

        // 设置值的序列化器为传入的serializer
        redis.setValueSerializer(serializer);

        // 设置哈希键的序列化器为StringRedisSerializer
        redis.setHashKeySerializer(new StringRedisSerializer());

        // 设置哈希值的序列化器为传入的serializer
        redis.setHashValueSerializer(serializer);

        // 在所有属性设置完成后，调用afterPropertiesSet方法进行初始化
        redis.afterPropertiesSet();
    }

    /**
     * 创建并配置一个JedisConnectionFactory实例
     *
     * @param config 包含Redis节点配置信息的RedisNodeConfig对象
     * @return 配置好的JedisConnectionFactory实例
     * @throws IllegalArgumentException 如果poolConfig为null
     */
    private JedisConnectionFactory createJedisConnectionFactory(RedisNodeConfig config) {
        // 从配置对象中获取Jedis连接池配置
        RedisPoolConfig poolConfig = config.getJedisPool();

        // 如果连接池配置为null，抛出非法参数异常
        if (poolConfig == null) {
            throw new IllegalArgumentException("jedis poolConfig is null");
        }

        // 创建RedisStandaloneConfiguration对象，使用主机和端口信息
        RedisStandaloneConfiguration redisConfig = new RedisStandaloneConfiguration(config.getHost(), config.getPort());

        // 设置Redis的密码
        redisConfig.setPassword(RedisPassword.of(config.getPassword()));

        // 设置Redis的数据库索引
        redisConfig.setDatabase(config.getDatabase());

        // 构建客户端
        JedisClientConfiguration clientConfig = JedisClientConfiguration.builder().usePooling()
                .poolConfig(getJedisPoolConfig(poolConfig))
                .and()
                .connectTimeout(Duration.ofMillis(config.getConnectTimeoutMillis()))
                .readTimeout(Duration.ofMillis(config.getReadTimeoutMillis()))
                .build();

        // 创建JedisConnectionFactory实例，使用redisConfig配置
        final JedisConnectionFactory jedisConnectionFactory = new JedisConnectionFactory(redisConfig, clientConfig);

        // 初始化JedisConnectionFactory的属性
        jedisConnectionFactory.afterPropertiesSet();

        // 返回配置好的JedisConnectionFactory实例
        return jedisConnectionFactory;
    }

    /**
     * 基于jedis的redis连接池配置，根据给定的 RedisPoolConfig 对象，生成并返回一个 JedisPoolConfig 对象
     *
     * @param poolConfig RedisPoolConfig 连接池的配置信息
     * @return JedisPoolConfig 转换后的连接池配置信息
     */
    private JedisPoolConfig getJedisPoolConfig(RedisPoolConfig poolConfig) {
        // 创建一个新的JedisPoolConfig对象
        JedisPoolConfig objectPoolConfig = new JedisPoolConfig();

        // 设置最大连接数
        objectPoolConfig.setMaxTotal(poolConfig.getMaxTotal());

        // 设置最大空闲连接数
        objectPoolConfig.setMaxIdle(poolConfig.getMaxIdle());

        // 设置最小空闲连接数
        objectPoolConfig.setMinIdle(poolConfig.getMinIdle());

        // 设置最大等待时间（毫秒）
        objectPoolConfig.setMaxWaitMillis(poolConfig.getMaxWaitMillis());

        // 连接耗尽时是否阻塞
        objectPoolConfig.setBlockWhenExhausted(true);

        // 设置空闲连接检测的时间间隔（毫秒）
        objectPoolConfig.setTimeBetweenEvictionRunsMillis(poolConfig.getTimeBetweenEvictionRunsMillis());

        // 设置每次检测的连接数
        objectPoolConfig.setNumTestsPerEvictionRun(poolConfig.getNumTestsPerEvictionRun());

        // 设置连接的最小空闲时间（毫秒）
        objectPoolConfig.setMinEvictableIdleTimeMillis(poolConfig.getMinEvictableIdleTimeMillis());

        // 设置空闲时是否测试连接
        objectPoolConfig.setTestWhileIdle(poolConfig.isTestWhileIdle());

        // 创建连接时不测试连接
        objectPoolConfig.setTestOnCreate(false);

        // 归还连接时不测试连接
        objectPoolConfig.setTestOnReturn(false);

        // 返回配置好的JedisPoolConfig对象
        return objectPoolConfig;
    }

}
