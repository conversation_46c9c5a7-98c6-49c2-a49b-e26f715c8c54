package com.xiaomi.nr.coupon.admin.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.RejectedExecutionException;

@Configuration
@Slf4j
@EnableAsync
public class ThreadPoolConfig {

    /**
     * 用于异步执行的线程池
     * */
    @Bean("asyncExecutor")
    public ThreadPoolTaskExecutor asyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(50);
        executor.setMaxPoolSize(200);
        executor.setKeepAliveSeconds(30);
        executor.setQueueCapacity(50);
        executor.setThreadNamePrefix("asyncExecutor-");
        executor.setRejectedExecutionHandler((r, executor1) -> {
            log.error("asyncExecutor reject thread. active thread:{}, queue size:{}", executor1.getActiveCount(), executor1.getQueue().size());
            throw new RejectedExecutionException("Task " + r.toString() + " rejected from asyncExecutor");
        });
        return executor;
    }

    /**
     * 用于查询时异步任务执行的线程池
     * */
    @Bean("queryAsyncExecutor")
    public ThreadPoolTaskExecutor queryAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(50);
        executor.setMaxPoolSize(500);
        executor.setKeepAliveSeconds(30);
        executor.setQueueCapacity(1000);
        executor.setThreadNamePrefix("queryAsyncExecutor-");
        executor.setRejectedExecutionHandler((r, executor1) -> {
            log.error("queryAsyncExecutor reject thread. active thread:{}, queue size:{}", executor1.getActiveCount(), executor1.getQueue().size());
            throw new RejectedExecutionException("Task " + r.toString() + " rejected from queryAsyncExecutor");
        });
        return executor;
    }

    /**
     * 商品查询接口专用
     *
     */
    @Bean("queryGoodsAsyncExecutor")
    public ThreadPoolTaskExecutor queryGoodsAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(20);
        executor.setMaxPoolSize(50);
        executor.setKeepAliveSeconds(30);
        executor.setQueueCapacity(1000);
        executor.setThreadNamePrefix("queryAsyncExecutor-");
        executor.setRejectedExecutionHandler((r, executor1) -> {
            log.error("queryGoodsAsyncExecutor reject thread. active thread:{}, queue size:{}", executor1.getActiveCount(), executor1.getQueue().size());
            throw new RejectedExecutionException("Task " + r.toString() + " rejected from queryGoodsAsyncExecutor");
        });
        return executor;
    }


}
