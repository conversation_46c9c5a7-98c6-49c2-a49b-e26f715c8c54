<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>coupon-admin</artifactId>
        <groupId>com.xiaomi.nr</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>coupon-admin-server</artifactId>

    <dependencies>
        <dependency>
            <artifactId>coupon-admin-biz-global</artifactId>
            <groupId>com.xiaomi.nr</groupId>
            <version>${project.parent.version}</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <artifactId>coupon-admin-biz-retail</artifactId>
            <groupId>com.xiaomi.nr</groupId>
            <version>${project.parent.version}</version>
            <scope>compile</scope>
        </dependency>

        <!--阿里巴巴nacos-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dubbo-registry-nacos</artifactId>
            <version>1.2.1-mone-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba.spring</groupId>
            <artifactId>spring-context-support</artifactId>
            <version>1.0.2</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-spring-context</artifactId>
            <version>0.3.6-mone-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>spring-context</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-context-support</artifactId>
                    <groupId>com.alibaba.spring</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
            <version>1.2.1-mone-v6-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>simpleclient</artifactId>
                    <groupId>io.prometheus</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.5</version>
        </dependency>

        <dependency>
            <groupId>org.codehaus.janino</groupId>
            <artifactId>janino</artifactId>
            <version>3.0.8</version>
        </dependency>

        <!-- keycenter -->
        <dependency>
            <groupId>com.xiaomi.mit</groupId>
            <artifactId>mit-starter</artifactId>
            <version>1.1.8</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi</groupId>
            <artifactId>keycenter-agent-client</artifactId>
            <version>3.5.3</version>
            <exclusions>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- redisson -->
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>3.16.4</version>
        </dependency>

        <!--  单测接入  -->
        <dependency>
            <groupId>com.xiaomi.mit</groupId>
            <artifactId>unittest-tool</artifactId>
            <version>1.0-local-SNAPSHOT</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.junit</groupId>
                <artifactId>junit-bom</artifactId>
                <version>5.11.4</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.xiaomi.nr.coupon.admin.bootstrap.CouponAdminBootstrap</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>conf</nonFilteredFileExtension>
                        <nonFilteredFileExtension>keytab</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.mi.xms</groupId>
                <artifactId>neptune-maven-plugin</artifactId>
                <version>1.0-SNAPSHOT</version>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>translate</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <appId>22871eed7c454b2390d1001d4bd88226</appId>
                    <env>prod</env>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.12</version>
                <executions>
                    <execution>
                        <id>default-prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.5.2</version>
                <configuration>
                    <excludes>
                        <exclude>com.xiaomi.nr.coupon.admin.deprecated.**.*Test.java</exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <profile_name>dev</profile_name>
                <zookeeper_host>staging</zookeeper_host>
                <app_nacos>nacos.test.b2c.srv:80</app_nacos>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <dependencies>
                <dependency>
                    <groupId>com.xiaomi.nr</groupId>
                    <artifactId>coupon-admin-biz-retail</artifactId>
                    <version>${project.parent.version}</version>
                </dependency>
            </dependencies>
        </profile>

        <profile>
            <id>staging</id>
            <activation>
                <property>
                    <name>staging</name>
                    <value>true</value>
                </property>
            </activation>
            <properties>
                <profile_name>staging</profile_name>
                <zookeeper_host>staging</zookeeper_host>
                <app_nacos>nacos.test.b2c.srv:80</app_nacos>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.xiaomi.nr</groupId>
                    <artifactId>coupon-admin-biz-retail</artifactId>
                    <version>${project.parent.version}</version>
                </dependency>
            </dependencies>
        </profile>

        <profile>
            <id>c3</id>
            <properties>
                <profile_name>c3</profile_name>
                <zookeeper_host>c4</zookeeper_host>
                <app_nacos>nacos.systech.b2c.srv:80</app_nacos>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.xiaomi.nr</groupId>
                    <artifactId>coupon-admin-biz-retail</artifactId>
                    <version>${project.parent.version}</version>
                </dependency>
            </dependencies>
        </profile>

        <profile>
            <id>c4</id>
            <properties>
                <profile_name>c4</profile_name>
                <zookeeper_host>c4</zookeeper_host>
                <app_nacos>nacos.systech.b2c.srv:80</app_nacos>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.xiaomi.nr</groupId>
                    <artifactId>coupon-admin-biz-retail</artifactId>
                    <version>${project.parent.version}</version>
                </dependency>
            </dependencies>
        </profile>

        <profile>
            <id>preview</id>
            <properties>
                <profile_name>preview</profile_name>
                <zookeeper_host>c4</zookeeper_host>
                <app_nacos>nacos.systech.b2c.srv:80</app_nacos>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.xiaomi.nr</groupId>
                    <artifactId>coupon-admin-biz-retail</artifactId>
                    <version>${project.parent.version}</version>
                </dependency>
            </dependencies>
        </profile>

        <profile>
            <id>sg_staging</id>
            <properties>
                <profile_name>sg_staging</profile_name>
                <zookeeper_host>tj_staging</zookeeper_host>
                <app_nacos>sgp.nacos.test.b2c.srv:80</app_nacos>
                <neptuneEnv>test</neptuneEnv>
                <neptuneAppId>22871eed7c454b2390d1001d4bd88226</neptuneAppId>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.xiaomi.nr</groupId>
                    <artifactId>coupon-admin-biz-global</artifactId>
                    <version>${project.parent.version}</version>
                </dependency>
            </dependencies>
        </profile>

        <profile>
            <id>sg_online</id>
            <activation>
                <property>
                    <name>sg_online</name>
                </property>
            </activation>
            <properties>
                <profile_name>sg_online</profile_name>
                <zookeeper_host>alsgcommonsrv</zookeeper_host>
                <app_nacos>nacos.systech.b2c.srv:80</app_nacos>
                <neptuneEnv>prod</neptuneEnv>
                <neptuneAppId>22871eed7c454b2390d1001d4bd88226</neptuneAppId>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.xiaomi.nr</groupId>
                    <artifactId>coupon-admin-biz-global</artifactId>
                    <version>${project.parent.version}</version>
                </dependency>
            </dependencies>
        </profile>

        <profile>
            <id>eu_online</id>
            <activation>
                <property>
                    <name>eu_online</name>
                </property>
            </activation>
            <properties>
                <profile_name>eu_online</profile_name>
                <zookeeper_host>azamssrv</zookeeper_host>
                <app_nacos>nacos.systech.b2c.srv:80</app_nacos>
                <neptuneEnv>prod</neptuneEnv>
                <neptuneAppId>22871eed7c454b2390d1001d4bd88226</neptuneAppId>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.xiaomi.nr</groupId>
                    <artifactId>coupon-admin-biz-global</artifactId>
                    <version>${project.parent.version}</version>
                </dependency>
            </dependencies>
        </profile>

        <profile>
            <id>ut</id>
            <properties>
                <profile_name>ut</profile_name>
                <zookeeper_host>staging</zookeeper_host>
                <app_nacos>nacos.test.b2c.srv:80</app_nacos>
                <neptuneEnv>test</neptuneEnv>
                <neptuneAppId>22871eed7c454b2390d1001d4bd88226</neptuneAppId>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.xiaomi.nr</groupId>
                    <artifactId>coupon-admin-biz-global</artifactId>
                    <version>${project.parent.version}</version>
                </dependency>
            </dependencies>
        </profile>

        <profile>
            <id>ut-global</id>
            <properties>
                <profile_name>sg_staging</profile_name>
                <zookeeper_host>tj_staging</zookeeper_host>
                <app_nacos>sgp.nacos.test.b2c.srv:80</app_nacos>
                <neptuneEnv>test</neptuneEnv>
                <neptuneAppId>22871eed7c454b2390d1001d4bd88226</neptuneAppId>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.xiaomi.nr</groupId>
                    <artifactId>coupon-admin-biz-global</artifactId>
                    <version>${project.parent.version}</version>
                </dependency>
            </dependencies>
        </profile>
    </profiles>
</project>
