package com.xiaomi.nr.coupon.admin.common.domain.coupon.bpm;

import com.xiaomi.nr.coupon.admin.api.utils.I18nUtil;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.bpm.BpmPageGenerator;
import com.xiaomi.nr.coupon.admin.enums.BpmPageEnum;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponTypeEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.constant.CommonConstant;
import jodd.util.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: zhangliwei6
 * @date: 2025/5/21 14:59
 * @description:
 */
@Slf4j
@Component
public class BpmPageGeneratorFactory {

    private Map<String, BpmPageGenerator> bpmPageGeneratorMap;

    public BpmPageGeneratorFactory(@Autowired List<BpmPageGenerator> bpmPageGeneratorList) {
        if (CollectionUtils.isEmpty(bpmPageGeneratorList)) {
            bpmPageGeneratorMap = Collections.emptyMap();
        }
        this.bpmPageGeneratorMap =
                bpmPageGeneratorList.stream().collect(Collectors.toMap(bpmPageGenerator -> bpmPageGenerator.getI18n() + StringPool.HASH + bpmPageGenerator.getType(), Function.identity()));
    }

    public BpmPageGenerator getGeneratorForReview(Integer couponType) {
        BpmPageEnum bpmPageEnum = null;
        if (CouponTypeEnum.GOODS.getValue().equals(couponType)) {
            bpmPageEnum = I18nUtil.isI18n() ? BpmPageEnum.Global_Coupon : BpmPageEnum.Coupon;
        } else if (CouponTypeEnum.POSTFREE.getValue().equals(couponType)) {
            bpmPageEnum = BpmPageEnum.Postfree_Coupon;
        } else if (CouponTypeEnum.SUBSIDY.getValue().equals(couponType)) {
            bpmPageEnum = BpmPageEnum.Subsidy_Coupon;
        }
        if (bpmPageEnum == null) {
            return null;
        }
        return bpmPageGeneratorMap.get((I18nUtil.isI18n() ? CommonConstant.AREA_ID_GLOBAL : CommonConstant.AREA_ID_CN) + StringPool.HASH + bpmPageEnum);
    }

    public BpmPageGenerator getGeneratorForTask() {
        return bpmPageGeneratorMap.get((I18nUtil.isI18n() ? CommonConstant.AREA_ID_GLOBAL : CommonConstant.AREA_ID_CN) + StringPool.HASH + BpmPageEnum.TASK_COUPON);
    }
}
