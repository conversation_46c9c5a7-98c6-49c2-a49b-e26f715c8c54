package com.xiaomi.nr.coupon.admin.common.application.dubbo.couponadmin;

import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.xiaomi.mone.dubbo.docs.annotations.ApiDoc;
import com.xiaomi.mone.dubbo.docs.annotations.ApiModule;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.scene.request.*;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.scene.response.*;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.api.enums.TranslationEnum;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponSceneService;
import com.xiaomi.nr.coupon.admin.api.utils.I18nUtil;
import com.xiaomi.nr.coupon.admin.application.dubbo.convert.CouponSceneConvert;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponscene.CouponSceneDomainService;
import com.xiaomi.nr.coupon.admin.infrastructure.login.UserInfoItemFactory;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.CouponSceneRepository;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.scene.po.CouponScenePO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.scene.po.SceneListParam;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.scene.po.ScenePermissionPO;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 优惠券场景
 * @Date: 2022.03.03 17:03
 */
@Slf4j
@Component
@Service(timeout = 3000, group = "${dubbo.group}", version = "1.0")
@ApiModule(value = "优惠券场景服务", apiInterface = DubboCouponSceneService.class)
public class DubboCouponSceneServiceImpl implements DubboCouponSceneService {

    @Autowired
    private CouponSceneDomainService couponSceneDomainService;

    @Autowired
    private CouponSceneRepository couponSceneRepository;

    @Autowired
    private CouponSceneConvert couponSceneConvert;

    /**
     * 场景列表
     *
     * @param request
     * @return
     */
    @Override
    @ApiDoc("场景列表")
    public Result<BasePageResponse<SceneListVO>> querySceneList(QuerySceneListRequest request) {
        try {
            log.info("CouponSceneService.querySceneList begin request:{}", request);

            if(Objects.isNull(request.getAreaId())){
                request.setAreaId(I18nUtil.getGlobalAreaId());
            }

            // 1、查询总数
            SceneListParam sceneListParam = new SceneListParam();
            couponSceneConvert.convertToParam(request, sceneListParam);
            int totalCount = couponSceneRepository.searchSceneCountByListReq(sceneListParam);
            // 2、查询列表
            List<CouponScenePO> couponScenePOList = couponSceneRepository.searchSceneByListReq(sceneListParam);
            int pageSize = request.getPageSize();
            List<SceneListVO> sceneListVOList = couponSceneConvert.convertToSceneVOList(couponScenePOList);
            BasePageResponse<SceneListVO> response = new BasePageResponse<>(request.getPageNo(), pageSize, totalCount
                    , totalCount / pageSize + (totalCount % pageSize == 0 ? 0 : 1), sceneListVOList);
            log.info("CouponSceneService.querySceneList end request:{} response:{}", request, response);
            return Result.success(response);
        } catch (Exception e) {
            log.error("CouponSceneService.querySceneList error request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 新增或编辑场景
     *
     * @param request
     * @return
     */
    @Override
    public Result<CreateOrUpdateSendSceneResponse> createOrUpdateSendScene(CreateOrUpdateSendSceneRequest request) {
        try {
            // 兼容3C
            if (request.getBizPlatform() == null) {
                request.setBizPlatform(BizPlatformEnum.RETAIL.getCode());
            }

            // 1、入参校验
            if (BizPlatformEnum.valueOf(request.getBizPlatform()) == null) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, TranslationEnum.COUPON_TASK_COMMIT_ERROR_BIZ_TYPE.getTranslateContent());
            }

            if(Objects.isNull(request.getAreaId())){
                request.setAreaId(I18nUtil.getGlobalAreaId());
            }

            request.setCreator(UserInfoItemFactory.createUserInfoItem().getValidateEmailPrefix());
            log.info("CouponSceneService.createOrUpdateSendScene begin request:{}", request);
            // 2、更新场景
            CreateOrUpdateSendSceneResponse response = new CreateOrUpdateSendSceneResponse(couponSceneDomainService.createOrUpdateSendScene(request));
            log.info("CouponSceneService.createOrUpdateSendScene end request:{} response:{}", request, response);
            return Result.success(response);
        } catch (Exception e) {
            log.error("CouponSceneService.createOrUpdateSendScene error request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 查询场景详情
     *
     * @param request
     * @return
     */
    @Override
    public Result<SceneDetailResponse> sceneDetail(SceneDetailRequest request) {
        try {
            log.info("CouponSceneService.sceneDetail begin request:{}", request);
            CouponScenePO couponScenePO = couponSceneRepository.searchSceneById(request.getSceneId());
            SceneDetailResponse response = new SceneDetailResponse();
            couponSceneConvert.convertToResponse(couponScenePO, response);
            log.info("CouponSceneService.sceneDetail end request:{} response", request);
            return Result.success(response);
        } catch (Exception e) {
            log.error("CouponSceneService.sceneDetail error request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 上下线场景
     *
     * @param request
     * @return
     */
    @Override
    @Transactional
    public Result<Boolean> operateScene(OperateSceneRequest request) {
        try {
            request.setOperator(UserInfoItemFactory.createUserInfoItem().getValidateEmailPrefix());
            log.info("CouponSceneService.operateScene begin request:{}", request);
            if (request.getSceneId() <= 0) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, TranslationEnum.COUPON_SCENE_OPERATE_STATUS_ERROR.getTranslateContent());
            }
            CouponScenePO couponScenePO = couponSceneRepository.searchSceneById(request.getSceneId());
            if (Objects.equals(couponScenePO.getStatus(), request.getType())) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, TranslationEnum.COUPON_CONFIG_UPDATE_CHANGE_STATUS.getTranslateContent());
            }
            couponSceneRepository.updateStatusById(request.getSceneId(), request.getType());
            log.info("CouponSceneService.operateScene end request:{}", request);
            return Result.success(true);
        } catch (Exception e) {
            log.error("CouponSceneService.operateScene error request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 授权列表
     *
     * @param request
     * @return
     */
    @Override
    public Result<BasePageResponse<PermissionListVO>> queryPermissionList(PermissionListRequest request) {
        try {
            log.info("CouponSceneService.queryPermissionList begin request:{}", request);
            int totalCount = couponSceneRepository.searchPermissionCountBySceneId(request.getSceneId());
            List<ScenePermissionPO> scenePermissionPOList = couponSceneRepository.searchPermissionByListReq(request);
            int pageSize = request.getPageSize();
            List<PermissionListVO> permissionListVOList = couponSceneConvert.convertToPermissionVOList(scenePermissionPOList);
            BasePageResponse<PermissionListVO> response = new BasePageResponse<>(request.getPageNo(), pageSize, totalCount,
                    totalCount / pageSize + (totalCount % pageSize == 0 ? 0 : 1), permissionListVOList);
            log.info("CouponSceneService.queryPermissionList end request:{} response:{}", request, response);
            return Result.success(response);
        } catch (Exception e) {
            log.error("CouponSceneService.queryPermissionList error request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 场景授权
     *
     * @param request
     * @return
     */
    @Override
    public Result<Boolean> createPermission(CreatePermissionRequest request) {
        try {
            request.setCreator(UserInfoItemFactory.createUserInfoItem().getValidateEmailPrefix());
            if(Objects.isNull(request.getAreaId())){
                request.setAreaId(I18nUtil.getGlobalAreaId());
            }
            log.info("CouponSceneService.createPermission begin request:{}", request);
            checkRequest(request);
            List<ScenePermissionPO> scenePermissionPOList = couponSceneRepository.searchPermissionListBySceneIdAppId(request.getSceneId(), request.getAppId() ,request.getAreaId());
            if (CollectionUtils.isNotEmpty(scenePermissionPOList)) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, TranslationEnum.COUPON_SCENE_CREATE_PERMISSION_SAME.getTranslateContent());
            }
            ScenePermissionPO scenePermissionPO = couponSceneConvert.convertToPermissionPO(request);
            couponSceneRepository.insertPermission(scenePermissionPO);
            log.info("CouponSceneService.createPermission end request:{}", request);
            return Result.success(true);
        } catch (Exception e) {
            log.error("CouponSceneService.createPermission error request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 停用或启用授权
     *
     * @param request
     * @return
     */
    @Override
    public Result<Boolean> operatePermission(OperatePermissionRequest request) {
        try {
            request.setModifier(UserInfoItemFactory.createUserInfoItem().getValidateEmailPrefix());

            log.info("CouponSceneService.operatePermission begin request:{}", request);
            ScenePermissionPO scenePermissionPO = couponSceneRepository.searchPermissionByIdSceneIdAppId(request.getPermissionId(),
                    request.getSceneId(), request.getAppId());
            if (StringUtils.isBlank(request.getModifier())) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, TranslationEnum.COUPON_CONFIG_UPDATE_OPERATION_NULL.getTranslateContent());
            }
            if (scenePermissionPO == null) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, TranslationEnum.COUPON_SCENE_OPERATE_PERMISSION_ERROR.getTranslateContent());
            }
            scenePermissionPO.setStatus(request.getType());
            scenePermissionPO.setModifier(request.getModifier());
            scenePermissionPO.setUpdateTime(System.currentTimeMillis() / 1000);
            couponSceneRepository.updatePermissionStatus(scenePermissionPO);
            log.info("CouponSceneService.operatePermission end request:{}", request);
            return Result.success(true);
        } catch (Exception e) {
            log.error("CouponSceneService.operatePermission error request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 查询所有场景并且分类
     *
     * @return
     */
    @Override
    public Result<SearchSceneWithCatResponse> searchSceneWithCat() {
        try {
            log.info("CouponSceneService.searchSceneWithCat begin");
            SearchSceneWithCatResponse response = new SearchSceneWithCatResponse();
            List<CouponScenePO> couponScenePOList = couponSceneRepository.searchAllBriefPO(true, BizPlatformEnum.RETAIL.getCode(),null);
            List<CouponSceneTypeVO> couponChannelTypeVOList = couponSceneConvert.convertCouponSceneTypeVOS(couponScenePOList, -1, BizPlatformEnum.RETAIL.getCode());
            response.setCouponChannelTypeVOList(couponChannelTypeVOList);
            log.info("CouponSceneService.searchSceneWithCat end");
            return Result.success(response);
        } catch (Exception e) {
            log.error("CouponSceneService.searchSceneWithCat error", e);
            return Result.fromException(e);
        }
    }

    /**
     * 查询所有场景并且分类 - 根据支持的券类型查询
     *
     * @param request
     * @return
     */
    @Override
    public Result<SearchSceneWithCatResponse> searchSceneWithCatV2(SearchSceneWithCatRequest request) {
        Stopwatch stopwatch = Stopwatch.createStarted();

        try {
            // 兼容3C 和 车商城
            List<Integer> bizPlatformList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(request.getBizPlatformList())) {
                bizPlatformList.addAll(request.getBizPlatformList());
            } else if (Objects.nonNull(request.getBizPlatform())) {
                bizPlatformList.add(request.getBizPlatform());
            } else if(I18nUtil.isI18n()){
                //能走到这证明没传业务类型
                bizPlatformList.add(BizPlatformEnum.GLOBAL_NEW_RETAIL.getCode());
            }else{
                bizPlatformList.addAll(Lists.newArrayList(BizPlatformEnum.RETAIL.getCode(), BizPlatformEnum.CAR_SHOP.getCode()));
            }

            if(Objects.isNull(request.getAreaId())){
                request.setAreaId(I18nUtil.getGlobalAreaId());
            }

            // 1、入参校验
            if (CollectionUtils.isEmpty(BizPlatformEnum.valueOf(bizPlatformList))
                    || !Objects.equals(bizPlatformList.size(), BizPlatformEnum.valueOf(bizPlatformList).size())) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, TranslationEnum.COUPON_TASK_COMMIT_ERROR_BIZ_TYPE.getTranslateContent());
            }
            // 1、入参校验
            if (Objects.isNull(request.getCouponType())) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, TranslationEnum.COUPON_SCENE_QUERY_TYPE_ERROR.getTranslateContent());
            }

            SearchSceneWithCatResponse response = new SearchSceneWithCatResponse();
            List<CouponScenePO> couponScenePOList = couponSceneRepository.searchAllBriefPO(true, bizPlatformList,request.getAreaId());
            List<CouponSceneTypeVO> couponChannelTypeVOList = couponSceneConvert.convertCouponSceneTypeVOS(couponScenePOList, request.getCouponType(), bizPlatformList);
            response.setCouponChannelTypeVOList(couponChannelTypeVOList);
            log.info("CouponSceneService.searchSceneWithCatV2 execute success, runTime={}ms, request={}", stopwatch.elapsed(TimeUnit.MILLISECONDS), request);
            return Result.success(response);
        } catch (Exception e) {
            log.error("CouponSceneService.searchSceneWithCatV2 error ", e);
            return Result.fromException(e);
        }
    }

    private void checkRequest(CreatePermissionRequest request) throws Exception {
        if (request.getSceneId() == null || request.getSceneId() <= 0) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "场景id不能为空");
        }
        if (StringUtils.isBlank(request.getAppId())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "appid不能为空");
        }
        if (StringUtils.isBlank(request.getAppName())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "接入系统不能为空");
        }
        if (StringUtils.isBlank(request.getAppContact())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "对接人不能为空");
        }
        if (StringUtils.isBlank(request.getCreator())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "创建人或修改人不能为空");
        }
    }
}
