package com.xiaomi.nr.coupon.admin.common.application.dubbo.coupon.convert;


import com.xiaomi.nr.coupon.admin.api.dto.BudgetInfoDto;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponConfigVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponConfigDTO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.OperateCouponConfigRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.util.Objects;

@Slf4j
public class CouponAdminConvert {

    public static OperateCouponConfigRequest convertOperateCouponConfigReq(CouponConfigVO couponConfigVO, Integer bizPlatform) {
        // 转换为DTO
        CouponConfigDTO couponConfigDTO = new CouponConfigDTO();
        BeanUtils.copyProperties(couponConfigVO, couponConfigDTO);

        // 预算信息
        BudgetInfoDto budgetInfoDto = couponConfigVO.getBudgetInfoDto();
        if (Objects.nonNull(budgetInfoDto)) {
            couponConfigDTO.setBudgetApplyNo(budgetInfoDto.getBudgetApplyNo());
            couponConfigDTO.setLineNum(budgetInfoDto.getLineNum());
            couponConfigDTO.setBudgetCreateTime(budgetInfoDto.getBudgetCreateTime());
        }

        OperateCouponConfigRequest operateCouponConfigReq = new OperateCouponConfigRequest();
        operateCouponConfigReq.setCouponConfigDTO(couponConfigDTO);
        operateCouponConfigReq.setBizPlatform(bizPlatform);
        // 抽取创建人
        operateCouponConfigReq.setOperator(couponConfigVO.getCreator());
        return operateCouponConfigReq;
    }

}
