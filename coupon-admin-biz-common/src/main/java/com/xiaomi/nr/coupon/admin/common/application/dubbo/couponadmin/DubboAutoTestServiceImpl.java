package com.xiaomi.nr.coupon.admin.common.application.dubbo.couponadmin;

import com.xiaomi.mone.docs.annotations.dubbo.ApiDoc;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;
import com.xiaomi.nr.coupon.admin.api.dto.autotest.CleanCouponDataVO;
import com.xiaomi.nr.coupon.admin.api.dto.autotest.request.CleanCouponDataRequest;
import com.xiaomi.nr.coupon.admin.api.dto.autotest.request.CleanEcardDataRequest;
import com.xiaomi.nr.coupon.admin.api.dto.autotest.request.CleanRedpacketDataRequest;
import com.xiaomi.nr.coupon.admin.api.dto.autotest.request.UserEcardListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.autotest.response.CouponCreateResponse;
import com.xiaomi.nr.coupon.admin.api.dto.autotest.response.UserEcardListResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponConfigVO;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboAutoTestService;
import com.xiaomi.nr.coupon.admin.application.dubbo.convert.CouponConfigConvert;
import com.xiaomi.nr.coupon.admin.common.domain.coupon.couponconfig.check.CouponConfigCheckService;
import com.xiaomi.nr.coupon.admin.common.infrastructure.repository.CouponAutoTestRepository;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponConfigItemFactory;
import com.xiaomi.nr.coupon.admin.domain.ecard.EcardService;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponConfigStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.login.UserInfoItemFactory;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.UserCouponRepository;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.UserRedPacketRepository;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastructure.rpc.CouponProxyService;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@Service(timeout = 3000, group = "${dubbo.group}", version = "1.0")
@ApiModule(value = "自动化测试服务", apiInterface = DubboAutoTestService.class)
public class DubboAutoTestServiceImpl implements DubboAutoTestService {

    @Autowired
    private CouponConfigCheckService couponCheckService;

    @Autowired
    private CouponAutoTestRepository couponConfigRepository;

    @Autowired
    private UserCouponRepository userCouponRepository;

    @Autowired
    private CouponProxyService couponProxyService;

    @Autowired
    private UserRedPacketRepository userRedPacketRepository;

    @Autowired
    private EcardService ecardService;

    @ApiDoc("创建券")
    @Override
    public Result<CouponCreateResponse> createCoupon(CouponConfigVO couponConfigVO) {
        log.info("DubboAutoTestService createCoupon request:{}", couponConfigVO);
        try {
            String account = UserInfoItemFactory.createUserInfoItem().getValidateEmailPrefix();

            couponConfigVO.setCreator(account);

            CouponConfigItem couponConfigItem = CouponConfigItemFactory.createCouponConfigItem(couponConfigVO, 0);
            //基本信息校验
            couponCheckService.getCheckStrategy(couponConfigItem.getCouponBaseInfo().getPromotionType() + "_" + couponConfigItem.getCouponBaseInfo().getBizPlatform()).createCheck(couponConfigItem);

            //新增券
            CouponConfigPO couponConfigPO = CouponConfigConvert.voConvertPo(couponConfigVO);
            couponConfigPO.setStatus(CouponConfigStatusEnum.ONLINE.getCode());
            long configId = couponConfigRepository.insert(couponConfigPO);

            CouponCreateResponse response = new CouponCreateResponse();
            response.setConfigId(configId);
            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboAutoTestService createCoupon error request:{}", couponConfigVO, e);
            return Result.fromException(e);
        }
    }

    @ApiDoc("清除券")
    @Override
    public Result<Void> cleanCouponData(CleanCouponDataRequest request) {
        try {
            List<CleanCouponDataVO> cleanCouponDataVOS = request.getCleanCouponDataVOS();
            if (CollectionUtils.isEmpty(cleanCouponDataVOS)) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "券信息入参不能为空");
            }

            //券配置、新老券配置缓存、券商品缓存、券es
            List<Long> configIds = cleanCouponDataVOS.stream().map(CleanCouponDataVO::getConfigId).distinct().collect(Collectors.toList());
            couponConfigRepository.delete(configIds);

            //删除用户券
            for (CleanCouponDataVO cleanCouponDataVO : cleanCouponDataVOS) {
                userCouponRepository.deleteCoupon(cleanCouponDataVO.getUserId(), cleanCouponDataVO.getCouponId());
            }
            //toc缓存清理
            couponProxyService.cleanCouponData(configIds);

            return Result.success(null);
        } catch (Exception e) {
            log.error("DubboAutoTestService cleanCouponData error request:{}", request, e);
            return Result.fromException(e);
        }
    }

    @ApiDoc("清除用户红包")
    @Override
    public Result<Void> cleanRedpacketData(CleanRedpacketDataRequest request) {
        try {
            userRedPacketRepository.deleteRedPacket(request.getUserId());
            return Result.success(null);
        } catch (Exception e) {
            log.error("DubboAutoTestService cleanRedpacketData error request:{}", request, e);
            return Result.fromException(e);
        }
    }

    @ApiDoc("查询用户礼品卡")
    @Override
    public Result<UserEcardListResponse> queryUserEcard(UserEcardListRequest request) {
        try {
            return Result.success(ecardService.selectUserEcard(request));
        } catch (Exception e) {
            log.error("DubboAutoTestService queryUserEcard error request:{}", request, e);
            return Result.fromException(e);
        }
    }

    @ApiDoc("清除用户礼品卡")
    @Override
    public Result<Void> cleanEcardData(CleanEcardDataRequest request) {
        try {
            ecardService.cleanEcardData(request);
            return Result.success(null);
        } catch (Exception e) {
            log.error("DubboAutoTestService cleanEcardData error request:{}", request, e);
            return Result.fromException(e);
        }
    }


}
