package com.xiaomi.nr.coupon.admin.common.domain.coupon.couponconfig.event;

import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.common.domain.coupon.couponconfig.CouponPostHandlerService;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponCreateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateStatusEvent;
import com.xiaomi.nr.coupon.admin.enums.optrecord.OPtRecordStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.CouponOptRecordRepository;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.Optional;

@Component
@Slf4j
public class CouponChangeListener {

    @Autowired
    private CouponPostHandlerService couponPostHandlerService;

    @Autowired
    private CouponOptRecordRepository couponOptRecordRepository;

    @Async("asyncExecutor")
    @TransactionalEventListener(fallbackExecution = true)
    public void handleCreateEvent(CouponCreateEvent event) {
        try {
            BizPlatformEnum bizPlatform = BizPlatformEnum.valueOf(event.getBizPlatform());
            couponPostHandlerService.handleCreateEvent(bizPlatform, event);

            couponOptRecordRepository.updateStatusById(event.getRecordId(), OPtRecordStatusEnum.SUCCESS.getValue());
            log.info("handleCreateEvent success configId {} ", Optional.ofNullable(event.getData()).map(CouponConfigPO::getId).orElse(null));
        } catch (Exception e) {
            log.error("handleCreateEvent error event {} ", GsonUtil.toJson(event), e);
            couponOptRecordRepository.updateStatusById(event.getRecordId(), OPtRecordStatusEnum.FAIL.getValue());
        }
    }

    @Async("asyncExecutor")
    @TransactionalEventListener(fallbackExecution = true)
    public void handleUpdateEvent(CouponUpdateEvent event) {
        try {

            BizPlatformEnum bizPlatform = BizPlatformEnum.valueOf(event.getBizPlatform());
            couponPostHandlerService.handleUpdateEvent(bizPlatform, event);

            couponOptRecordRepository.updateStatusById(event.getRecordId(), OPtRecordStatusEnum.SUCCESS.getValue());
            log.info("handleUpdateEvent success configId {} ", Optional.ofNullable(event.getData()).map(CouponConfigPO::getId).orElse(null));
        } catch (Exception e) {
            couponOptRecordRepository.updateStatusById(event.getRecordId(), OPtRecordStatusEnum.FAIL.getValue());
            log.error("handleUpdateEvent error event {} ", GsonUtil.toJson(event), e);
        }
    }


    @Async("asyncExecutor")
    @TransactionalEventListener(fallbackExecution = true)
    public void handleUpdateStatusEvent(CouponUpdateStatusEvent event) {
        try {

            BizPlatformEnum bizPlatform = BizPlatformEnum.valueOf(event.getBizPlatform());
            couponPostHandlerService.handleUpdateStatusEvent(bizPlatform, event);

            couponOptRecordRepository.updateStatusById(event.getRecordId(), OPtRecordStatusEnum.SUCCESS.getValue());
            log.info("handleUpdateStatusEvent success configId {} ", Optional.ofNullable(event.getData()).map(CouponConfigPO::getId).orElse(null));
        } catch (Exception e) {
            couponOptRecordRepository.updateStatusById(event.getRecordId(), OPtRecordStatusEnum.FAIL.getValue());
            log.error("handleUpdateStatusEvent error event {} ", GsonUtil.toJson(event), e);
        }
    }
}
