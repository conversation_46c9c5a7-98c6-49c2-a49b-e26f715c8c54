package com.xiaomi.nr.coupon.admin.common.domain.coupon.couponconfig.event.posthandler;

import com.xiaomi.nr.coupon.admin.api.utils.TimeUtil;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.event.BaseCouponPostHandler;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponCreateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateStatusEvent;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.po.CouponConfigPO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class RedisRefreshPostHandler extends BaseCouponPostHandler {

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    @Override
    public void createPost(CouponCreateEvent event) throws Exception {
        CouponConfigPO couponConfigPO;
        if (event.isCompensateFlag()) {
            couponConfigPO = couponConfigRepository.searchCouponById(event.getData().getId());
        } else {
            couponConfigPO = event.getData();
        }
        // 补偿时，eventContext DataPreparePostHandler已采用表里最新商品配置
        couponConfigRepository.updateCouponConfigCache(couponConfigPO, event.getEventContext());
    }

    @Override
    public void updatePost(CouponUpdateEvent event) throws Exception {
        CouponConfigPO couponConfigPO;
        if (event.isCompensateFlag()) {
            couponConfigPO = couponConfigRepository.searchCouponById(event.getData().getId());
        } else {
            couponConfigPO = event.getData();
            couponConfigPO.setUpdateTime(TimeUtil.getNowTimestamp());
        }
        // 补偿时，eventContext DataPreparePostHandler已采用表里最新商品配置
        couponConfigRepository.updateCouponConfigCache(couponConfigPO, event.getEventContext());
    }


    @Override
    public void updateStatusPost(CouponUpdateStatusEvent event) throws Exception {
        CouponConfigPO couponConfigPO;
        if (event.isCompensateFlag()) {
            couponConfigPO = couponConfigRepository.searchCouponById(event.getData().getId());
        } else {
            couponConfigPO = event.getData();
            couponConfigPO.setUpdateTime(TimeUtil.getNowTimestamp());
        }
        couponConfigRepository.updateCouponBaseInfoCache(couponConfigPO);
    }
}
