package com.xiaomi.nr.coupon.admin.common.infrastructure.repository.mysqldao;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xiaomi.newretail.bpm.api.model.dto.ProcessCreateDTO;
import com.xiaomi.nr.coupon.admin.api.enums.TranslationEnum;
import com.xiaomi.nr.coupon.admin.common.domain.coupon.bpm.BpmPageGeneratorFactory;
import com.xiaomi.nr.coupon.admin.common.domain.coupon.couponreview.entity.CouponTaskReviewContext;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.bpm.IBpmProxy;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.CouponTaskReviewMapper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.entity.SearchTaskReviewListParam;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.entity.SearchTaskReviewListResult;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.po.CouponTaskReviewPO;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * 灌券任务审核
 */
@Slf4j
@Component
public class CouponTaskReviewRepository {

    @Autowired
    private IBpmProxy bpmProxy;

    @Autowired
    private CouponTaskReviewMapper couponTaskReviewMapper;

    @Autowired
    private BpmPageGeneratorFactory bpmPageGeneratorFactory;

    /**
     * 新增灌券任务申请
     */
    @Transactional(transactionManager = "xmPulseCouponTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public void save(CouponTaskReviewContext reviewContext) throws Exception {
        String bpmKey = null;
        try {
            // 审核记录入库
            couponTaskReviewMapper.insert(reviewContext.getReviewPO());

            // 构建bpm审核参数
            ProcessCreateDTO processRequest = bpmPageGeneratorFactory.getGeneratorForTask().createRequest(reviewContext);
            bpmKey = bpmProxy.submitReview(processRequest);

            // 更新审核表记录
            couponTaskReviewMapper.updateBpmKey(reviewContext.getReviewPO().getId(), bpmKey);
        } catch (Exception e) {
            if(StringUtils.isNotBlank(bpmKey)){
                bpmProxy.cancelReview(bpmKey, "system");
            }
            log.info("bpm审批异常：",e);
            throw new Exception(TranslationEnum.COUPON_TASK_COMMIT_ERROR_BPM.getTranslateContent());
        }
    }


    /**
     * 根据bpmKey获取审核信息
     *
     * @param bpmKey
     * @return
     */
    public CouponTaskReviewPO getReviewByBpmKey(String bpmKey) {
        return couponTaskReviewMapper.selectByBpmKey(bpmKey);
    }

    /**
     * 更新审核记录状态等信息
     *
     * @param po
     * @return
     */
    public int updateReviewStatus(CouponTaskReviewPO po) {
        return couponTaskReviewMapper.updateReviewStatus(po);
    }

    /**
     * 根据审核id获取审核信息
     *
     * @param id
     * @return
     */
    public CouponTaskReviewPO getReviewById(long id) {
        return couponTaskReviewMapper.selectById(id);
    }


    /**
     * 灌券任务审核列表查询
     *
     * @param param
     * @return
     */
    public SearchTaskReviewListResult getReviewList(SearchTaskReviewListParam param) {

        PageHelper.startPage(param.getPageNo(), param.getPageSize());
        List<CouponTaskReviewPO> list = couponTaskReviewMapper.selectList(param);

        SearchTaskReviewListResult result = new SearchTaskReviewListResult();
        if (CollectionUtils.isNotEmpty(list)) {
            PageInfo<CouponTaskReviewPO> pageInfo = new PageInfo<>(list);
            result.setData(list);
            result.setTotalCount(pageInfo.getTotal());
            result.setTotalPage(pageInfo.getPages());
        }

        return result;
    }


    public int updateTaskId(Long taskId, String bpmKey) {
        return couponTaskReviewMapper.updateReviewTaskId(taskId, bpmKey);
    }


    public byte[] selectByTaskId(long taskId) {

        CouponTaskReviewPO po = couponTaskReviewMapper.selectByTaskId(taskId);
        if (Objects.isNull(po)) {
            return null;
        }
        return po.getCompressInfo();
    }

    public List<Long> selectByConfigId(long configId) {
        return couponTaskReviewMapper.selectByConfigId(configId);
    }

}
