package com.xiaomi.nr.coupon.admin.common.domain.coupon.couponconfig;

import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.event.BaseCouponPostHandler;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.event.ICouponPostHandlerHolder;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponCreateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateStatusEvent;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: zhangliwei6
 * @date: 2025/5/26 16:56
 * @description:
 */
@Slf4j
@Component
public class CouponPostHandlerService implements ApplicationContextAware {

    private final Map<BizPlatformEnum, ICouponPostHandlerHolder> couponPostHandlerHolderMap;

    private ApplicationContext applicationContext;

    public CouponPostHandlerService(@Autowired List<ICouponPostHandlerHolder> couponPostHandlerHolderList) {
        this.couponPostHandlerHolderMap = couponPostHandlerHolderList.stream().collect(Collectors.toMap(ICouponPostHandlerHolder::getBizPlatformEnum, Function.identity()));
    }

    /**
     * 获取后置处理器
     *
     * @param bizPlatformEnum   业务领域
     * @return                  后置处理器列表
     * @throws BizError         业务异常
     */
    public List<BaseCouponPostHandler> getCouponPostHandlers(BizPlatformEnum bizPlatformEnum) throws BizError {
        if (! couponPostHandlerHolderMap.containsKey(bizPlatformEnum)) {
            throw ExceptionHelper.create(GeneralCodes.InternalError, "非法业务领域");
        }
        return couponPostHandlerHolderMap.get(bizPlatformEnum).getCouponPostHandlers().stream().map(clz -> applicationContext.getBean(clz)).collect(Collectors.toList());
    }

    /**
     * 处理创建事件
     *
     * @param bizPlatformEnum   业务领域
     * @param event             创建事件
     * @throws Exception        异常
     */
    public void handleCreateEvent(BizPlatformEnum bizPlatformEnum, CouponCreateEvent event) throws Exception {
        log.info("handleCreateEvent bizPlatformEnum:{}, event:{}", bizPlatformEnum, event);
        List<BaseCouponPostHandler> couponPostHandlers = getCouponPostHandlers(bizPlatformEnum);
        for (BaseCouponPostHandler couponPostHandler : couponPostHandlers) {
            log.info("handleCreateEvent couponPostHandler:{}", couponPostHandler.getClass().getName());
            couponPostHandler.createPost(event);
        }
    }

    /**
     * 处理更新事件
     *
     * @param bizPlatformEnum   业务领域
     * @param event             创建事件
     * @throws Exception        异常
     */
    public void handleUpdateEvent(BizPlatformEnum bizPlatformEnum, CouponUpdateEvent event) throws Exception {
        log.info("handleUpdateEvent bizPlatformEnum:{}, event:{}", bizPlatformEnum, event);
        List<BaseCouponPostHandler> couponPostHandlers = getCouponPostHandlers(bizPlatformEnum);
        for (BaseCouponPostHandler couponPostHandler : couponPostHandlers) {
            log.info("handleUpdateEvent couponPostHandler:{}", couponPostHandler.getClass().getName());
            couponPostHandler.updatePost(event);
        }
    }

    /**
     * 处理上下线事件
     *
     * @param bizPlatformEnum   业务领域
     * @param event             创建事件
     * @throws Exception        异常
     */
    public void handleUpdateStatusEvent(BizPlatformEnum bizPlatformEnum, CouponUpdateStatusEvent event) throws Exception {
        log.info("handleUpdateStatusEvent bizPlatformEnum:{}, event:{}", bizPlatformEnum, event);
        List<BaseCouponPostHandler> couponPostHandlers = getCouponPostHandlers(bizPlatformEnum);
        for (BaseCouponPostHandler couponPostHandler : couponPostHandlers) {
            log.info("handleUpdateStatusEvent couponPostHandler:{}", couponPostHandler.getClass().getName());
            couponPostHandler.updateStatusPost(event);
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
