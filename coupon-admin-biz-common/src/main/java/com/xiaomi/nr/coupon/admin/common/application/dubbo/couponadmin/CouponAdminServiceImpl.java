package com.xiaomi.nr.coupon.admin.common.application.dubbo.couponadmin;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.xiaomi.mone.dubbo.docs.annotations.ApiDoc;
import com.xiaomi.mone.dubbo.docs.annotations.ApiModule;
import com.xiaomi.nr.coupon.admin.api.dto.BasePageResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponConfigDetailVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponConfigListVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.GoodsRuleVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponInfoRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponListRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.CouponUpdateStatusRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.OperateCouponConfigRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.response.CouponDetailResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.response.SaveCouponConfigResponse;
import com.xiaomi.nr.coupon.admin.api.enums.BizPlatformEnum;
import com.xiaomi.nr.coupon.admin.api.enums.TranslationEnum;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.CouponAdminService;
import com.xiaomi.nr.coupon.admin.application.dubbo.convert.CouponConfigConvert;
import com.xiaomi.nr.coupon.admin.common.domain.coupon.couponconfig.check.CouponConfigCheckService;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.check.CouponConfigCheckStrategy;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.CouponConfigAdminService;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponBaseInfo;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponConfigItem;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.model.CouponConfigItemFactory;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.CouponConfigStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.constant.CommonConstant;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.CouponConfigRepository;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.po.CouponConfigPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysql.coupon.config.po.SearchConfigParam;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @description:
 * @author: hejiapeng
 * @Date 2022/3/3 12:51 下午
 * @Version: 1.0
 **/
@Slf4j
@Component
@Service(timeout = 10000, group = "${dubbo.group}", version = "1.0")
@ApiModule(value = "优惠券管理后台服务", apiInterface = CouponAdminService.class)
public class CouponAdminServiceImpl implements CouponAdminService {

    @Autowired
    private CouponConfigAdminService couponConfigAdminService;

    @Autowired
    private CouponConfigConvert couponConfigConvert;

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    @Autowired
    private CouponConfigCheckService couponCheckService;

    /**
     * 券配置列表
     *
     * @param request request
     * @return response
     */
    @Override
    @ApiDoc("券配置列表")
    public Result<BasePageResponse<CouponConfigListVO>> couponConfigList(CouponListRequest request) {

        Stopwatch stopwatch = Stopwatch.createStarted();

        try {

            // fix biz platform
            List<Integer> bizPlatformList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(request.getBizPlatformList())) {
                bizPlatformList.addAll(request.getBizPlatformList());
            } else if (Objects.nonNull(request.getBizPlatform())) {
                bizPlatformList.add(request.getBizPlatform());
            }

            // 1、入参校验
            if (CollectionUtils.isEmpty(BizPlatformEnum.valueOf(bizPlatformList))
                    || !Objects.equals(bizPlatformList.size(), BizPlatformEnum.valueOf(bizPlatformList).size())) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, TranslationEnum.COUPON_TASK_COMMIT_ERROR_BIZ_TYPE.getTranslateContent());
            }
            request.setBizPlatformList(bizPlatformList);

            // 2、封装查询参数
            SearchConfigParam param = couponConfigConvert.transferToSearchParameter(request);

            // 3、具体查询逻辑
            PageHelper.startPage(request.getPageNo(), request.getPageSize());
            List<CouponConfigPO> couponConfigPOS = couponConfigAdminService.searchCouponConfigList(param);
            log.info("CouponAdminService couponConfigList  couponConfigPOS={}", GsonUtil.toJson(couponConfigPOS));

            // 封装返回值
            List<CouponConfigListVO> data = couponConfigConvert.convertCouponConfigListPOToVO(couponConfigPOS);
            PageInfo<CouponConfigPO> pageInfo = new PageInfo<>(couponConfigPOS);
            BasePageResponse<CouponConfigListVO> response = new BasePageResponse<>(request.getPageNo(), request.getPageSize());
            response.setList(data);
            response.setTotalCount(pageInfo.getTotal());
            response.setTotalPage(pageInfo.getPages());
            log.info("CouponAdminService.couponConfigList execute success, runTime={}ms, request={}", stopwatch.elapsed(TimeUnit.MILLISECONDS), request);
            return Result.success(response);
        } catch (Exception e) {
            log.error("CouponAdminService.couponConfigList request={}, error: ", request, e);
            return Result.fromException(e);
        } finally {
            stopwatch.stop();
        }
    }

    /**
     * 券配置详情
     *
     * @param request request
     * @return response
     */
    @Override
    @ApiDoc("券配置详情")
    public Result<CouponDetailResponse> couponConfigDetail(@Valid CouponInfoRequest request) {
        Stopwatch stopwatch = Stopwatch.createStarted();

        try {
            Long id = request.getId();
            CouponConfigPO po = couponConfigRepository.searchCouponById(id);

            Map<Long, Long> sendCountMap = couponConfigRepository.getCouponSendCount(Collections.singletonList(id));

            GoodsRuleVO goodsRuleVO = couponConfigConvert.convertGoodsRuleVO(po);

            CouponConfigDetailVO couponConfigVO = couponConfigConvert.convertCouponConfigPOToVO(po, goodsRuleVO);

            CouponDetailResponse response = new CouponDetailResponse();
            response.setCouponConfigVO(couponConfigVO);
            response.setSendCount(sendCountMap.get(id));
            response.setTimeStatus(CouponConfigConvert.transferToTimeStatus(po.getStartFetchTime(), po.getEndFetchTime(), po.getStatus()));
            response.setServiceType(po.getServiceType());
            response.setBizPlatform(po.getBizPlatform());

            log.info("CouponAdminService.couponConfigDetail execute success, response={},runTime={}ms, id={}", response, stopwatch.elapsed(TimeUnit.MILLISECONDS), id);
            return Result.success(response);
        } catch (Exception e) {
            log.error("CouponAdminService.couponConfigDetail request={}, error: ", request, e);
            return Result.fromException(e);
        } finally {
            stopwatch.stop();
        }
    }

    /**
     * 保存券配置
     *
     * @param request
     * @return
     */
    @Override
    @ApiDoc("保存券配置")
    public Result<SaveCouponConfigResponse> saveCouponConfig(@Valid OperateCouponConfigRequest request) {
        log.info("CouponAdminServiceImpl.saveCouponConfig request = {}", request);

        Stopwatch stopwatch = Stopwatch.createStarted();

        try {

            // 1、入参校验
            if (BizPlatformEnum.valueOf(request.getBizPlatform()) == null) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, TranslationEnum.COUPON_TASK_COMMIT_ERROR_BIZ_TYPE.getTranslateContent());
            }

            CouponConfigItem couponConfigItem = CouponConfigItemFactory.createCouponConfigItem(request.getCouponConfigDTO(), request.getBizPlatform());
            CouponBaseInfo baseInfo = couponConfigItem.getCouponBaseInfo();
            Integer bizPlatform = Optional.ofNullable(baseInfo.getBizPlatform()).orElse(BizPlatformEnum.RETAIL.getCode());
            CouponConfigCheckStrategy checkStrategy = couponCheckService.getCheckStrategy(baseInfo.getPromotionType() + "_" + bizPlatform);

            if (Objects.isNull(checkStrategy)) {
                log.error("CouponAdminService.saveCouponConfig checkStrategy not exist, request = {}", request);
                throw ExceptionHelper.create(GeneralCodes.ParamError, TranslationEnum.COUPON_TASK_COMMIT_ERROR_ARGS.getTranslateContent());
            }

            checkStrategy.createCheck(couponConfigItem);

            // 2、转换为PO
            CouponConfigPO couponConfigPO = couponConfigConvert.dtoConvertPo(request.getCouponConfigDTO(), request.getBizPlatform());
            couponConfigPO.setCreator(request.getOperator());
            couponConfigPO.setReleaseCount(0L);
            // 3、保存优惠券配置
            long configId = couponConfigAdminService.insertCoupon(couponConfigPO);
            // 4、构造出参
            SaveCouponConfigResponse response = new SaveCouponConfigResponse();
            response.setConfigId(configId);
            log.info("CouponAdminService.saveCouponConfig execute success, runTime={}ms, request={}", stopwatch.elapsed(TimeUnit.MILLISECONDS), request);
            return Result.success(response);
        } catch (Exception e) {
            log.error("CouponAdminService.saveCouponConfig request={}, error: ", request, e);
            return Result.fromException(e);
        } finally {
            stopwatch.stop();
        }
    }

    /**
     * 更新券配置
     *
     * @param request
     * @return
     */
    @Override
    @ApiDoc("更新券配置")
    public Result<Void> updateCouponConfig(@Valid OperateCouponConfigRequest request) {


        Stopwatch stopwatch = Stopwatch.createStarted();

        try {

            // 1、入参校验
            if (BizPlatformEnum.valueOf(request.getBizPlatform()) == null) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "业务平台非法");
            }

            CouponConfigItem couponConfigItem = CouponConfigItemFactory.createCouponConfigItem(request.getCouponConfigDTO(), request.getBizPlatform());
            //基本信息校验
            CouponBaseInfo baseInfo = couponConfigItem.getCouponBaseInfo();
            Integer bizPlatform = Optional.ofNullable(baseInfo.getBizPlatform()).orElse(BizPlatformEnum.RETAIL.getCode());
            CouponConfigCheckStrategy checkStrategy =
                    couponCheckService.getCheckStrategy(baseInfo.getPromotionType() + "_" + bizPlatform);

            if (Objects.isNull(checkStrategy)) {
                log.error("CouponAdminService.updateCouponConfig checkStrategy not exist, request = {}", request);
                throw ExceptionHelper.create(GeneralCodes.ParamError, "入参校验失败");
            }

            checkStrategy.updateCheck(couponConfigItem);

            // 2、转换为PO
            CouponConfigPO couponConfigPO = couponConfigConvert.dtoConvertPo(request.getCouponConfigDTO(), request.getBizPlatform());
            // 3、更新券配置
            couponConfigAdminService.updateCoupon(couponConfigPO, request.getOperator());

            log.info("CouponAdminService.updateCouponConfig execute success, runTime={}ms, request={}", stopwatch.elapsed(TimeUnit.MILLISECONDS), request);
            return Result.success(null);
        } catch (Exception e) {
            log.error("CouponAdminService.updateCouponConfig request={}, error: ", request, e);
            return Result.fromException(e);
        } finally {
            stopwatch.stop();
        }
    }

    /**
     * 修改券配置状态
     *
     * @param request request
     * @return void
     */
    @Override
    @ApiDoc("修改券配置状态")
    public Result<String> updateConfigStatus(@Valid CouponUpdateStatusRequest request) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            // 1、入参校验
            checkUpdateStatusReq(request);

            // 2、更新状态
            couponConfigAdminService.updateStatus(request);

            log.info("CouponConfigService.updateStatus execute success, runTime={}ms, request={}", stopwatch.elapsed(TimeUnit.MILLISECONDS), request);
            return Result.success(CommonConstant.SUCCESS);
        } catch (Exception e) {
            log.error("CouponConfigService.updateStatus request={}, error: ", request, e);
            return Result.fromException(e);
        } finally {
            stopwatch.stop();
        }
    }

    /**
     * 修改券状态参数校验
     *
     * @param req
     * @return
     */
    private void checkUpdateStatusReq(CouponUpdateStatusRequest req) throws BizError {
        if (Objects.isNull(CouponConfigStatusEnum.findByCode(req.getOperateType()))) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, TranslationEnum.COUPON_CONFIG_UPDATE_CHANGE_STATUS.getTranslateContent());
        }
        if (StringUtils.isBlank(req.getOperator())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, TranslationEnum.COUPON_CONFIG_UPDATE_OPERATION_NULL.getTranslateContent());
        }
    }
}
