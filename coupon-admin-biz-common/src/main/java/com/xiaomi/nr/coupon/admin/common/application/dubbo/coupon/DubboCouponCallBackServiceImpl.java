package com.xiaomi.nr.coupon.admin.common.application.dubbo.coupon;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDoc;
import com.xiaomi.mone.dubbo.docs.annotations.ApiModule;
import com.xiaomi.newretail.bpm.api.model.callback.OnStatusChangedRequest;
import com.xiaomi.newretail.bpm.api.model.callback.OnStatusChangedResponse;
import com.xiaomi.newretail.bpm.api.model.callback.ProcessAction;
import com.xiaomi.newretail.bpm.api.model.callback.StatusChangeAction;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponConfigVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.request.OperateCouponConfigRequest;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.config.response.SaveCouponConfigResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.response.CouponReviewInfoResponse;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.task.request.CreateFillCouponTaskRequest;
import com.xiaomi.nr.coupon.admin.api.enums.TranslationEnum;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.CouponAdminService;
import com.xiaomi.nr.coupon.admin.api.service.couponadmin.DubboCouponBpmCallBackService;
import com.xiaomi.nr.coupon.admin.common.application.dubbo.coupon.convert.CouponAdminConvert;
import com.xiaomi.nr.coupon.admin.common.application.dubbo.coupon.convert.TaskReviewConvert;
import com.xiaomi.nr.coupon.admin.common.domain.coupon.bpm.BpmPageGeneratorFactory;
import com.xiaomi.nr.coupon.admin.common.domain.coupon.couponreview.CouponReviewService;
import com.xiaomi.nr.coupon.admin.common.domain.coupon.couponreview.CouponTaskReviewService;
import com.xiaomi.nr.coupon.admin.common.infrastructure.repository.mysqldao.CouponTaskReviewRepository;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.bpm.BpmPageGenerator;
import com.xiaomi.nr.coupon.admin.domain.coupon.fillcoupontask.FillCouponService;
import com.xiaomi.nr.coupon.admin.domain.coupon.goods.GoodsService;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.ReviewStatusEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.po.CouponConfigReviewPO;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.po.CouponTaskReviewPO;
import com.xiaomi.nr.coupon.admin.util.CompressUtil;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 券申请回调
 */
@Slf4j
@Component
@Service(timeout = 3000, group = "${dubbo.group}", version = "1.0")
@ApiModule(value = "bpm审核回调服务", apiInterface = DubboCouponBpmCallBackService.class)
public class DubboCouponCallBackServiceImpl implements DubboCouponBpmCallBackService {

    @Autowired
    private CouponReviewService couponReviewService;

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private CouponTaskReviewService couponTaskReviewService;

    @Autowired
    private CouponTaskReviewRepository couponTaskReviewRepository;

    @Autowired
    private TaskReviewConvert taskReviewConvert;

    @Autowired
    private FillCouponService fillCouponService;

    @Autowired
    private CouponAdminService couponAdminService;

    @Autowired
    private BpmPageGeneratorFactory bpmPageGeneratorFactory;

    /**
     * BPM审核回调
     * @param request
     * @return
     */
    @Override
    @ApiDoc("优惠券审批回调")
    public Result<OnStatusChangedResponse> onStatusChanged(OnStatusChangedRequest request) {
        log.info("DubboCouponBpmCallBackService onStatusChanged request:{}", request);
        try {
            return Result.success(getOnStatusChangedResponse(request));
        } catch (Exception e) {
            log.error("DubboCouponBpmCallBackService onStatusChanged error request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * BPM审核回调
     * @param request
     * @return
     */
    @Override
    @ApiDoc("优惠券审批回调http")
    public OnStatusChangedResponse onStatusChangedHttp(OnStatusChangedRequest request) {
        log.info("DubboCouponBpmCallBackService onStatusChangedHttp request:{}", request);
        //如果是灌卷任务，走灌卷任务回调
        if(Objects.equals("task", request.getExtra().get("type"))){
            Result<OnStatusChangedResponse> onStatusChangedResponseResult = onTaskStatusChanged(request);
            return onStatusChangedResponseResult.getData();
        }
        //如果不是灌卷任务，走原来的优惠卷审批回调
        try {
            return getOnStatusChangedResponse(request);
        } catch (Exception e) {
            log.error("DubboCouponBpmCallBackService onStatusChangedHttp error request:{}", request, e);
            OnStatusChangedResponse onStatusChangedResponse = new OnStatusChangedResponse();
            onStatusChangedResponse.setCode(GeneralCodes.InternalError.getCode());
            onStatusChangedResponse.setAction(StatusChangeAction.STOP);
            return onStatusChangedResponse;
        }
    }

    /**
     * 国际的优惠卷审批回调
     * @param request
     * @return
     */
    private OnStatusChangedResponse getOnStatusChangedResponse(OnStatusChangedRequest request) {
        OnStatusChangedResponse onStatusChangedResponse = new OnStatusChangedResponse();
        onStatusChangedResponse.setCode(GeneralCodes.OK.getCode());
        onStatusChangedResponse.setAction(StatusChangeAction.CONTINUE);
        try {
            ProcessAction action = request.getAction();
            CouponConfigReviewPO reviewPO = couponReviewService.selectByBpmKey(request.getProcessInstanceId());
            if (reviewPO == null) {
                log.error("DubboCouponBpmCallBackService onStatusChanged reviewPO is null. request:{}", request);
                onStatusChangedResponse.setCode(GeneralCodes.InternalError.getCode());
                onStatusChangedResponse.setAction(StatusChangeAction.STOP);
                return onStatusChangedResponse;
            }
            if (ReviewStatusEnum.ToBeReviewed.getValue() != reviewPO.getStatus() && ReviewStatusEnum.UnderReview.getValue() != reviewPO.getStatus()) {
                log.error("DubboCouponBpmCallBackService onStatusChanged status error request:{},reviewPO:{}", request, reviewPO);
                onStatusChangedResponse.setCode(GeneralCodes.InternalError.getCode());
                onStatusChangedResponse.setAction(StatusChangeAction.STOP);
                return onStatusChangedResponse;
            }


            if (ProcessAction.Refuse.equals(action)) {
                reviewPO.setStatus(ReviewStatusEnum.Rejected.getValue());
                reviewPO.setApprovedId(request.getOperator());
                reviewPO.setApprovedTime(new Date());
                reviewPO.setBpmReason(request.getRefuseReason());
                couponReviewService.updateReviewStatus(reviewPO);

                return onStatusChangedResponse;
            } else if (ProcessAction.Accept.equals(action)) {
                Boolean finished = request.getFinished();
                if (!finished) {
                    reviewPO.setStatus(ReviewStatusEnum.UnderReview.getValue());
                    reviewPO.setApprovedId(request.getOperator());
                    reviewPO.setApprovedTime(new Date());
                    couponReviewService.updateReviewStatus(reviewPO);
                    return onStatusChangedResponse;
                }

                // VO反序列化
                CouponConfigVO couponConfigVO = GsonUtil.fromJson(CompressUtil.decompress(reviewPO.getConfigCompress()), CouponConfigVO.class);
                // 入参转换
                OperateCouponConfigRequest operateCouponConfigReq = CouponAdminConvert.convertOperateCouponConfigReq(couponConfigVO, reviewPO.getBizPlatform());

                if (reviewPO.getConfigId() <= 0) {
                    // 保存优惠券
                    Result<SaveCouponConfigResponse> saveResult = couponAdminService.saveCouponConfig(operateCouponConfigReq);
                    if (saveResult.getCode() != GeneralCodes.OK.getCode()) {
                        throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_TASK_COMMIT_ERROR_SAVE.getTranslateContent());
                    }
                    reviewPO.setConfigId(saveResult.getData().getConfigId());
                } else {
                    // 更新优惠券
                    operateCouponConfigReq.setOperator(reviewPO.getCreator());
                    Result<Void> updateResult = couponAdminService.updateCouponConfig(operateCouponConfigReq);
                    if (updateResult.getCode() != GeneralCodes.OK.getCode()) {
                        throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_TASK_COMMIT_ERROR_UPDATE.getTranslateContent());
                    }
                }

                reviewPO.setStatus(ReviewStatusEnum.Approved.getValue());
                reviewPO.setApprovedId(request.getOperator());
                reviewPO.setApprovedTime(new Date());
                couponReviewService.updateReviewStatus(reviewPO);
            } else if (ProcessAction.Cancel.equals(action)) {
                reviewPO.setStatus(ReviewStatusEnum.Canceled.getValue());
                reviewPO.setApprovedId(request.getOperator());
                couponReviewService.updateReviewStatus(reviewPO);
            }
        } catch (Exception e) {
            log.error("DubboCouponBpmCallBackService onStatusChanged error request:{}", request, e);
            onStatusChangedResponse.setCode(GeneralCodes.InternalError.getCode());
            onStatusChangedResponse.setAction(StatusChangeAction.STOP);
        }

        log.info("onStatusChangedResponse,{}",GsonUtil.toJson(onStatusChangedResponse));
        return onStatusChangedResponse;
    }

    @Override
    @ApiDoc("查询审核信息")
    public Result<CouponReviewInfoResponse> queryReviewInfo(String bpmKey) {
        log.info("DubboCouponBpmCallBackService queryReviewInfo bpmKey:{}", bpmKey);
        try {
            CouponConfigReviewPO reviewPO = couponReviewService.selectByBpmKey(bpmKey);
            Map<String, Object> map = Collections.emptyMap();
            BpmPageGenerator generator = bpmPageGeneratorFactory.getGeneratorForReview(reviewPO.getCouponType());
            if(Objects.nonNull(generator)){
                // 国际不调这个接口
                map = generator.convertMap(reviewPO);
            }
            CouponReviewInfoResponse response = GsonUtil.fromJson(GsonUtil.toJson(map), CouponReviewInfoResponse.class);
            CouponConfigVO couponConfigVO = GsonUtil.fromJson(CompressUtil.decompress(reviewPO.getConfigCompress()), CouponConfigVO.class);
            response.setGoodsSuitableInfoList(goodsService.getGoodsPriceVO(couponConfigVO.getGoodsRuleVO().getGoodsInclude()));

            return Result.success(response);
        } catch (Exception e) {
            log.error("DubboCouponBpmCallBackService queryReviewInfo error request:{}", bpmKey, e);
            return Result.fromException(e);
        }
    }

    @Override
    @ApiDoc("灌券任务审批回调")
    public Result<OnStatusChangedResponse> onTaskStatusChanged(OnStatusChangedRequest request) {
        //初始化
        OnStatusChangedResponse response = new OnStatusChangedResponse();
        response.setCode(GeneralCodes.OK.getCode());
        response.setAction(StatusChangeAction.CONTINUE);

        try{
            //获取审核实体
            CouponTaskReviewPO reviewPO = couponTaskReviewRepository.getReviewByBpmKey(request.getProcessInstanceId());

            if (ProcessAction.Accept.equals(request.getAction()) && request.getFinished()) {
                //灌券任务落库
                CreateFillCouponTaskRequest fillCouponTaskRequest = taskReviewConvert.convertToCouponTaskRequest(reviewPO.getCompressInfo());

                Long taskId = fillCouponService.createFillCouponTask(fillCouponTaskRequest);

                if (taskId != null && taskId > 0) {
                    couponTaskReviewRepository.updateTaskId(taskId, request.getProcessInstanceId());
                }
            }
            // 审核记录变更
            couponTaskReviewService.onTaskStatusChanged(request, response, reviewPO);
        }catch (Exception e){
            response.setAction(StatusChangeAction.STOP);
            response.setCode(GeneralCodes.InternalError.getCode());
            log.error("DubboCouponBpmCallBackService onTaskStatusChanged error request:{} error:{}", request, e.getMessage(), e);
        }

        return Result.success(response);
    }
}
