package com.xiaomi.nr.coupon.admin.common.domain.coupon.couponconfig.check;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.check.CouponConfigCheckStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class CouponConfigCheckService {

    private final Map<String, CouponConfigCheckStrategy> checkMap =new HashMap<>();

    public CouponConfigCheckService(@Autowired List<CouponConfigCheckStrategy> checkStrategyList) {
        checkMap.putAll(checkStrategyList.stream().collect(Collectors.toMap(CouponConfigCheckStrategy::getCheckStrategyKey, Function.identity())));
    }

    public CouponConfigCheckStrategy getCheckStrategy(String key) {
        return checkMap.get(key);
    }
}
