package com.xiaomi.nr.coupon.admin.common.domain.coupon.couponconfig.event.posthandler;

import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.event.BaseCouponPostHandler;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponCreateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateEvent;
import com.xiaomi.nr.coupon.admin.domain.coupon.couponconfig.event.entity.CouponUpdateStatusEvent;
import com.xiaomi.nr.coupon.admin.infrastructure.notify.zk.ZkNotifyService;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.CouponConfigRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;

@Slf4j
@Component
public class CacheRefreshPostHandler  extends BaseCouponPostHandler {

    @Autowired
    private ZkNotifyService zkNotifyService;

    @Autowired
    private CouponConfigRepository couponConfigRepository;

    @Override
    public void createPost(CouponCreateEvent event) {
        notifyZk(event.getData().getId());
    }

    @Override
    public void updatePost(CouponUpdateEvent event) {
        notifyZk(event.getData().getId());
    }

    @Override
    public void updateStatusPost(CouponUpdateStatusEvent event) {
        notifyZk(event.getData().getId());
    }

    private void notifyZk(long id) {
        Timestamp version = couponConfigRepository.searchUpdateTimeById(id);
        log.info("CacheRefreshPostHandler createPost configId:{},version:{}", id, version);
        zkNotifyService.updateConfigZkVersion(String.valueOf(version), id);
    }
}
