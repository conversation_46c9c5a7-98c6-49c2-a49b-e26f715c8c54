package com.xiaomi.nr.coupon.admin.common.domain.coupon.couponreview;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.CouponConfigVO;
import com.xiaomi.nr.coupon.admin.api.dto.coupon.review.response.CouponReviewResponse;
import com.xiaomi.nr.coupon.admin.api.enums.TranslationEnum;
import com.xiaomi.nr.coupon.admin.api.utils.I18nUtil;
import com.xiaomi.nr.coupon.admin.common.domain.coupon.bpm.BpmPageGeneratorFactory;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.bpm.BpmPageGenerator;
import com.xiaomi.nr.coupon.admin.core.domain.coupon.couponconfig.bpm.IBpmProxy;
import com.xiaomi.nr.coupon.admin.core.domain.icrm.IICRMProxyService;
import com.xiaomi.nr.coupon.admin.enums.couponconfig.UseChannelsEnum;
import com.xiaomi.nr.coupon.admin.enums.icrm.IcrmEnum;
import com.xiaomi.nr.coupon.admin.infrastructure.error.ErrCode;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.CouponConfigReviewMapper;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.entity.SeachReviewListResult;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.entity.SearchReviewListParam;
import com.xiaomi.nr.coupon.admin.infrastructure.repository.mysqldao.review.po.CouponConfigReviewPO;
import com.xiaomi.nr.coupon.admin.util.CompressUtil;
import com.xiaomi.nr.coupon.admin.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * 券审批
 */
@Component
@Slf4j
public class CouponReviewService {

    @Autowired
    private CouponConfigReviewMapper couponConfigReviewMapper;

    @Autowired
    private IBpmProxy bpmProxy;

    @Autowired
    private BpmPageGeneratorFactory bpmPageGeneratorFactory;

    @Autowired
    private IICRMProxyService icrmProxyService;

    /**
     * 校验
     */
    public void check(CouponConfigVO couponConfigVO, CouponReviewResponse response) throws Exception {
        try {
            if (I18nUtil.isI18n() && couponConfigVO.getUseChannel().keySet().contains(UseChannelsEnum.AUTHORIZED_STORE.getValue())) {
                Pair<Boolean, String> result = icrmProxyService.icrmCheck(couponConfigVO);
                if (Objects.equals(result.getLeft(), Boolean.FALSE)) {
                    response.setSuccess(false);
                    response.setTip(result.getRight());
                }
            }
        } catch (Exception e) {
            log.error("CouponReviewRepository check error", e);
            throw ExceptionHelper.create(ErrCode.COUPON, e.getMessage());
        }

    }

    /**
     * 新增申请
     */
    @Transactional(transactionManager = "xmPulseCouponTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public void save(CouponConfigReviewPO reviewPO) throws Exception {
        String bpmKey = null;
        boolean icrmOccupy = false;
        CouponConfigVO couponConfigVO = GsonUtil.fromJson(CompressUtil.decompress(reviewPO.getConfigCompress()), CouponConfigVO.class);
        try {
            if (I18nUtil.isI18n() && couponConfigVO.getUseChannel().keySet().contains(UseChannelsEnum.AUTHORIZED_STORE.getValue())) {
                icrmProxyService.icrmOccupy(couponConfigVO);
                icrmOccupy = true;
            }

            // 业务平台赋值
            Long insertRows = couponConfigReviewMapper.insert(reviewPO);
            if (insertRows <= 0) {
                log.error("CouponReviewService.save 插入审核记录失败！");
                throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_TASK_INSERT_ERROR_AUDIT.getTranslateContent());
            }
            BpmPageGenerator generator = bpmPageGeneratorFactory.getGeneratorForReview(reviewPO.getCouponType());
            if (generator == null) {
                log.error("CouponReviewService.save 未找到对应的bpm页面生成器！");
                throw ExceptionHelper.create(ErrCode.COUPON, "couponType非法");
            }
            bpmKey = bpmProxy.submitReview(generator.createRequest(reviewPO));

            Long updateRows = couponConfigReviewMapper.updateBpm(reviewPO.getId(), bpmKey);
            if (updateRows <= 0) {
                // 更新bpmKey失败，事务回滚
                log.error("CouponReviewService.save 更新bpmKey失败！");
                throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_CONFIG_UPDATE_CHECK_BPM.getTranslateContent());
            }
        } catch (Exception e) {
            // 撤销bpm
            if (Objects.nonNull(bpmKey)) {
                bpmProxy.cancelReview(bpmKey, "system");
            }
            if (icrmOccupy) {
                log.error("CouponReviewService save rollback ");
                Integer applyType = couponConfigVO.getId() == 0 ? IcrmEnum.COUPON_CREATE.getCode() : IcrmEnum.COUPON_UPDATE.getCode();
                icrmProxyService.icrmNotify(couponConfigVO, IcrmEnum.PROCESS_ACTIVITY_ERROR, applyType);
            }
            log.error("CouponReviewService.save error， reviewPO:{}", reviewPO, e);
            throw ExceptionHelper.create(ErrCode.COUPON, TranslationEnum.COUPON_CONFIG_CHANGE_CHECK_AUDIT.getTranslateContent());
        }

    }


    /**
     * 查询申请数量
     *
     * @param configId
     * @param statusList
     * @return
     */
    public long count(long configId, List<Integer> statusList) {
        return couponConfigReviewMapper.count(configId, statusList);
    }

    /**
     * 更新申请状态
     *
     * @return
     */
    public long updateReviewStatus(CouponConfigReviewPO po) {
        return couponConfigReviewMapper.updateReviewStatus(po);
    }

    /**
     * 根据唯一键查询
     *
     * @param bpmKey
     * @return
     */
    public CouponConfigReviewPO selectByBpmKey(String bpmKey) {
        return couponConfigReviewMapper.selectByBpmKey(bpmKey);
    }


    /**
     * 根据id查询
     *
     * @return
     */
    public CouponConfigReviewPO selectById(long id) {
        return couponConfigReviewMapper.selectById(id);
    }


    /**
     * 根据id查询
     *
     * @return
     */
    public SeachReviewListResult selectList(SearchReviewListParam param) {
        PageHelper.startPage(param.getPageNo(), param.getPageSize());
        List<CouponConfigReviewPO> list = couponConfigReviewMapper.selectList(param);

        SeachReviewListResult result = new SeachReviewListResult();
        if (CollectionUtils.isNotEmpty(list)) {
            PageInfo<CouponConfigReviewPO> pageInfo = new PageInfo<>(list);
            result.setReviewPOList(list);
            result.setTotalCount(pageInfo.getTotal());
            result.setTotalPage(pageInfo.getPages());
        }
        return result;
    }

    /**
     * 根据券id查询申请详情
     *
     * @param configId 券id
     * @return 审核附件地址信息
     */
    public String selectByConfigId(Long configId) {
        return couponConfigReviewMapper.selectByConfigId(configId);
    }


}
